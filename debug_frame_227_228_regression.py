#!/usr/bin/env python3
"""
Frame_00227和Frame_00228回归问题分析脚本

分析修复Frame_00229后，为什么Frame_00227和Frame_00228的区域3从"4三"变成了"1三"
"""

import json
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional

def load_frame_data(frame_number: int) -> Optional[Dict]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def analyze_san_cards_in_frame(frame_data: Dict, frame_number: int) -> Dict[str, Any]:
    """分析指定帧中的"三"卡牌分布"""
    san_cards = []
    for shape in frame_data.get('shapes', []):
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        
        if '三' in label or '三' in twin_id:
            card_info = {
                'region': shape.get('group_id'),
                'label': label,
                'twin_id': twin_id,
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', []),
                'y_center': (shape.get('points', [[0,0]])[0][1] + shape.get('points', [[0,0]])[2][1]) / 2
            }
            san_cards.append(card_info)
    
    # 按区域分组
    by_region = defaultdict(list)
    for card in san_cards:
        by_region[card['region']].append(card)
    
    # 对每个区域按Y坐标排序（从下到上）
    for region_cards in by_region.values():
        region_cards.sort(key=lambda x: -x['y_center'])
    
    return {
        'frame_number': frame_number,
        'all_cards': san_cards,
        'by_region': dict(by_region),
        'total_count': len(san_cards)
    }

def get_region_name(region_id: int) -> str:
    """获取区域名称"""
    region_names = {
        1: "手牌_观战方", 2: "调整_观战方", 3: "抓牌_观战方", 4: "打牌_观战方",
        5: "弃牌_观战方", 6: "吃碰区_观战方", 7: "抓牌_对战方", 8: "打牌_对战方",
        9: "弃牌_对战方", 10: "打出_对战方", 11: "最终弃牌_对战方",
        12: "听牌区_观战方", 13: "听牌区_对战方", 14: "赢方区域_观战方",
        15: "赢方区域_对战方", 16: "吃碰区_对战方", 17: "特殊区域"
    }
    return region_names.get(region_id, f"未知区域{region_id}")

def analyze_regression_problem():
    """分析回归问题"""
    print("=" * 80)
    print("🔍 Frame_00227和Frame_00228回归问题分析")
    print("=" * 80)
    
    # 加载关键帧数据
    frames_to_analyze = [227, 228, 229]
    frame_data = {}
    
    for frame_num in frames_to_analyze:
        data = load_frame_data(frame_num)
        if data:
            frame_data[frame_num] = analyze_san_cards_in_frame(data, frame_num)
        else:
            print(f"❌ 无法加载Frame_{frame_num:05d}")
            return
    
    print(f"\n📊 三帧'三'卡牌分布对比:")
    
    for frame_num in frames_to_analyze:
        frame_info = frame_data[frame_num]
        print(f"\n📍 Frame_{frame_num:05d}: 总计{frame_info['total_count']}张'三'卡牌")
        
        for region, cards in frame_info['by_region'].items():
            region_name = get_region_name(region)
            print(f"  区域{region}（{region_name}）: {len(cards)}张")
            for i, card in enumerate(cards):
                virtual_info = "虚拟" if card['is_virtual'] else "物理"
                print(f"    {i+1}. {card['twin_id']} ({virtual_info})")
    
    # 重点分析区域3和区域16的变化
    print(f"\n🎯 重点分析：区域3和区域16的'三'卡牌变化")
    
    for frame_num in frames_to_analyze:
        frame_info = frame_data[frame_num]
        region_3_cards = frame_info['by_region'].get(3, [])
        region_16_cards = frame_info['by_region'].get(16, [])
        
        print(f"\n📍 Frame_{frame_num:05d}:")
        print(f"  区域3: {[card['twin_id'] for card in region_3_cards]}")
        print(f"  区域16: {[card['twin_id'] for card in region_16_cards]}")
    
    # 分析问题
    print(f"\n❗ 问题分析:")
    
    # 检查Frame_00227和Frame_00228的区域3变化
    frame_227_region_3 = frame_data[227]['by_region'].get(3, [])
    frame_228_region_3 = frame_data[228]['by_region'].get(3, [])
    
    print(f"\n1️⃣ 区域3的ID变化:")
    print(f"  Frame_00227区域3: {[card['twin_id'] for card in frame_227_region_3]}")
    print(f"  Frame_00228区域3: {[card['twin_id'] for card in frame_228_region_3]}")
    print(f"  预期: 应该是'4三'（因为区域16已有1三、2三、3三）")
    print(f"  实际: {'1三' if any(card['twin_id'] == '1三' for card in frame_227_region_3) else '其他'}")
    
    # 检查区域16的状态
    frame_227_region_16 = frame_data[227]['by_region'].get(16, [])
    frame_228_region_16 = frame_data[228]['by_region'].get(16, [])
    
    print(f"\n2️⃣ 区域16的状态:")
    print(f"  Frame_00227区域16: {[card['twin_id'] for card in frame_227_region_16]}")
    print(f"  Frame_00228区域16: {[card['twin_id'] for card in frame_228_region_16]}")
    
    # 分析ID分配逻辑问题
    print(f"\n3️⃣ ID分配逻辑分析:")
    
    # 检查是否存在ID重复
    all_san_ids_227 = [card['twin_id'] for card in frame_data[227]['all_cards']]
    all_san_ids_228 = [card['twin_id'] for card in frame_data[228]['all_cards']]
    
    id_counts_227 = {}
    id_counts_228 = {}
    
    for id in all_san_ids_227:
        id_counts_227[id] = id_counts_227.get(id, 0) + 1
    
    for id in all_san_ids_228:
        id_counts_228[id] = id_counts_228.get(id, 0) + 1
    
    duplicates_227 = {id: count for id, count in id_counts_227.items() if count > 1}
    duplicates_228 = {id: count for id, count in id_counts_228.items() if count > 1}
    
    print(f"  Frame_00227 ID重复: {duplicates_227}")
    print(f"  Frame_00228 ID重复: {duplicates_228}")

def analyze_id_assignment_logic():
    """分析ID分配逻辑问题"""
    print("=" * 80)
    print("🔧 ID分配逻辑问题分析")
    print("=" * 80)
    
    print(f"\n📋 修复前后的逻辑对比:")
    
    print(f"\n🔧 修复前的逻辑:")
    print(f"  1. 区域16有1三、2三、3三")
    print(f"  2. 区域3需要分配'三'卡牌")
    print(f"  3. get_next_available_id('三')检查:")
    print(f"     - 1三: 已被区域16使用 → 不可用")
    print(f"     - 2三: 已被区域16使用 → 不可用")
    print(f"     - 3三: 已被区域16使用 → 不可用")
    print(f"     - 4三: 未被使用 → 可用")
    print(f"  4. 区域3分配到'4三' ✅")
    
    print(f"\n🔧 修复后的逻辑:")
    print(f"  1. 区域16有1三、2三、3三")
    print(f"  2. 区域3需要分配'三'卡牌")
    print(f"  3. get_next_available_id('三', target_region=3)检查:")
    print(f"     - 1三: 已被区域16使用，但允许跨区域共存(16,3) → 可用")
    print(f"     - 返回'1三'")
    print(f"  4. 区域3分配到'1三' ❌")
    
    print(f"\n❗ 问题根源:")
    print(f"  修复后的逻辑让区域3优先分配'1三'而不是'4三'")
    print(f"  这违反了ID分配的连续性原则")

def analyze_cross_region_logic_flaw():
    """分析跨区域共存逻辑缺陷"""
    print("=" * 80)
    print("🚨 跨区域共存逻辑缺陷分析")
    print("=" * 80)
    
    print(f"\n🎯 核心问题:")
    print(f"  当前的跨区域共存逻辑过于宽松，导致ID分配不合理")
    
    print(f"\n📋 具体问题:")
    print(f"  1. 区域3应该分配'4三'（下一个可用的连续ID）")
    print(f"  2. 但修复后的逻辑让'1三'在跨区域场景中被认为'可用'")
    print(f"  3. 导致区域3分配了'1三'而不是'4三'")
    
    print(f"\n🔍 逻辑分析:")
    print(f"  当前的_is_id_available_for_region方法:")
    print(f"  ```python")
    print(f"  if self._is_cross_region_allowed(existing_region, target_region):")
    print(f"      return True  # 🚨 这里有问题")
    print(f"  ```")
    
    print(f"\n❌ 问题:")
    print(f"  这个逻辑让已存在的ID在跨区域场景中被认为'可用'")
    print(f"  但实际上应该继续寻找下一个真正未使用的ID")

def propose_fix_solutions():
    """提出修复方案"""
    print("=" * 80)
    print("💡 修复方案建议")
    print("=" * 80)
    
    print(f"\n🎯 修复目标:")
    print(f"  1. 保持Frame_00229的修复效果（区域16: 1三→2三→3三→4三）")
    print(f"  2. 修复Frame_00227和Frame_00228的回归问题（区域3应该是'4三'）")
    print(f"  3. 实现真正合理的跨区域ID共存")
    
    print(f"\n📋 方案一：修正ID分配优先级")
    print(f"  核心思路：跨区域共存不应该影响新ID的分配逻辑")
    print(f"  ```python")
    print(f"  def get_next_available_id(self, label: str, target_region: int = None):")
    print(f"      # 首先寻找真正未使用的ID")
    print(f"      for i in range(1, self.max_cards_per_type + 1):")
    print(f"          potential_id = f'{{i}}{{label}}'")
    print(f"          if not self.is_id_used(potential_id):")
    print(f"              return potential_id")
    print(f"      ")
    print(f"      # 如果所有ID都被使用，再考虑跨区域共存")
    print(f"      for i in range(1, self.max_cards_per_type + 1):")
    print(f"          potential_id = f'{{i}}{{label}}'")
    print(f"          if self._can_reuse_for_cross_region(potential_id, target_region):")
    print(f"              return potential_id")
    print(f"      ")
    print(f"      return None")
    print(f"  ```")
    
    print(f"\n📋 方案二：区分继承和新分配")
    print(f"  核心思路：只在继承场景中允许跨区域ID共存")
    print(f"  ```python")
    print(f"  def get_next_available_id(self, label: str, target_region: int = None, is_inheritance: bool = False):")
    print(f"      if is_inheritance and target_region:")
    print(f"          # 继承场景：允许跨区域共存")
    print(f"          return self._get_id_for_inheritance(label, target_region)")
    print(f"      else:")
    print(f"          # 新分配场景：寻找真正未使用的ID")
    print(f"          return self._get_next_unused_id(label)")
    print(f"  ```")
    
    print(f"\n📋 方案三：智能ID分配策略")
    print(f"  核心思路：根据上下文智能选择ID分配策略")
    print(f"  ```python")
    print(f"  def get_next_available_id(self, label: str, target_region: int = None):")
    print(f"      # 检查是否为流转场景")
    print(f"      if self._is_transition_scenario(target_region):")
    print(f"          return self._get_id_for_transition(label, target_region)")
    print(f"      else:")
    print(f"          return self._get_next_sequential_id(label)")
    print(f"  ```")
    
    print(f"\n🏆 推荐方案：方案一")
    print(f"  理由：")
    print(f"  1. 逻辑清晰：优先分配未使用的ID，保证连续性")
    print(f"  2. 兼容性好：保持现有的跨区域共存能力")
    print(f"  3. 影响最小：只修改ID分配的优先级逻辑")

def simulate_correct_behavior():
    """模拟正确的行为"""
    print("=" * 80)
    print("🧪 正确行为模拟")
    print("=" * 80)
    
    print(f"\n📋 期望的正确行为:")
    
    print(f"\n📍 Frame_00227:")
    print(f"  区域16: [1三, 2三, 3三] (3张)")
    print(f"  区域3: 需要分配'三'卡牌")
    print(f"  ID分配逻辑:")
    print(f"    - 检查1三: 已被区域16使用")
    print(f"    - 检查2三: 已被区域16使用")
    print(f"    - 检查3三: 已被区域16使用")
    print(f"    - 检查4三: 未被使用 → 分配给区域3")
    print(f"  结果: 区域3获得'4三' ✅")
    
    print(f"\n📍 Frame_00228:")
    print(f"  区域16: [1三, 2三, 3三] (3张)")
    print(f"  区域3: 需要分配'三'卡牌")
    print(f"  ID分配逻辑: 同Frame_00227")
    print(f"  结果: 区域3获得'4三' ✅")
    
    print(f"\n📍 Frame_00229:")
    print(f"  区域3: [4三] (已存在)")
    print(f"  区域16: [1三, 2三, 3三] → 需要第4张")
    print(f"  跨区域共存逻辑:")
    print(f"    - 区域16需要第4张'三'卡牌")
    print(f"    - 检查4三: 已被区域3使用，但允许跨区域共存(3,16)")
    print(f"    - 分配'4三'给区域16")
    print(f"  结果: 区域16获得[1三, 2三, 3三, 4三] ✅")
    
    print(f"\n🎯 关键点:")
    print(f"  1. 新ID分配优先使用未被占用的连续ID")
    print(f"  2. 跨区域共存只在特定场景下生效（如流转）")
    print(f"  3. 保持ID分配的逻辑一致性和可预测性")

def main():
    """主函数"""
    print("🚀 Frame_00227和Frame_00228回归问题分析脚本")
    
    # 1. 分析回归问题
    analyze_regression_problem()
    
    # 2. 分析ID分配逻辑问题
    analyze_id_assignment_logic()
    
    # 3. 分析跨区域共存逻辑缺陷
    analyze_cross_region_logic_flaw()
    
    # 4. 提出修复方案
    propose_fix_solutions()
    
    # 5. 模拟正确行为
    simulate_correct_behavior()
    
    print("\n" + "=" * 80)
    print("✅ 回归问题分析完成")

if __name__ == "__main__":
    main()

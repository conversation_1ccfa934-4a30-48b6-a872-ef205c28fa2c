#!/usr/bin/env python3
"""
分析frame_00229和frame_00230之间区域16的列混淆问题
重点分析"三"和"一"两组卡牌的位置混淆，以及区域6的类别验证机制
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(frame_number: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return {}
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def extract_card_position(card: Dict[str, Any]) -> <PERSON><PERSON>[float, float, float, float]:
    """提取卡牌位置信息"""
    points = card.get('points', [])
    if not points or len(points) < 4:
        return 0.0, 0.0, 0.0, 0.0
    
    x_coords = [point[0] for point in points]
    y_coords = [point[1] for point in points]
    
    x_left = min(x_coords)
    x_right = max(x_coords)
    y_top = min(y_coords)
    y_bottom = max(y_coords)
    
    x_center = (x_left + x_right) / 2
    y_center = (y_top + y_bottom) / 2
    
    return x_center, y_center, x_left, y_bottom

def analyze_region_columns(cards: List[Dict[str, Any]], region_name: str) -> Dict[str, Any]:
    """分析区域的列分组"""
    print(f"\n📊 分析{region_name}的列分组")
    print("-" * 40)
    
    if not cards:
        print("  ❌ 该区域无卡牌")
        return {}
    
    # 提取卡牌信息
    card_info = []
    for i, card in enumerate(cards):
        x_center, y_center, x_left, y_bottom = extract_card_position(card)
        
        info = {
            'index': i,
            'label': card.get('label', ''),
            'twin_id': card.get('attributes', {}).get('digital_twin_id', ''),
            'score': card.get('score'),
            'x_center': x_center,
            'y_center': y_center,
            'x_left': x_left,
            'y_bottom': y_bottom,
            'region_name': card.get('region_name', ''),
            'owner': card.get('owner', ''),
            'group_id': card.get('group_id')
        }
        card_info.append(info)
    
    # 按X坐标分组（容差为8像素）
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in card_info:
        x_center = card['x_center']
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"  检测到 {len(columns)} 列:")
    
    column_analysis = {}
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        print(f"\n    列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌")
        
        # 列内按Y坐标排序（从下到上）
        column_sorted = sorted(column_cards, key=lambda x: -x['y_bottom'])
        
        # 分析列内的类别一致性
        labels_in_column = [card['label'] for card in column_sorted]
        base_labels = []
        for label in labels_in_column:
            # 提取基础标签（去掉数字前缀）
            base_label = label
            if len(label) >= 2 and label[0].isdigit():
                base_label = label[1:]
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        is_consistent = len(unique_base_labels) == 1
        
        print(f"      类别一致性: {'✅ 一致' if is_consistent else '❌ 混淆'}")
        print(f"      基础标签: {list(unique_base_labels)}")
        
        for j, card in enumerate(column_sorted):
            print(f"        位置{j+1}: {card['label']} -> {card['twin_id']}")
        
        column_analysis[f"column_{i+1}"] = {
            'x_center': x_key,
            'card_count': len(column_cards),
            'cards': column_sorted,
            'base_labels': base_labels,
            'unique_base_labels': list(unique_base_labels),
            'is_consistent': is_consistent
        }
    
    return column_analysis

def compare_frame_changes(frame229_data: Dict[str, Any], frame230_data: Dict[str, Any]):
    """对比两帧之间的变化"""
    print(f"\n🔄 对比frame_00229和frame_00230的变化")
    print("=" * 50)
    
    # 提取两帧的区域16卡牌
    shapes229 = frame229_data.get('shapes', [])
    shapes230 = frame230_data.get('shapes', [])
    
    region16_229 = [card for card in shapes229 if card.get('group_id') == 16]
    region16_230 = [card for card in shapes230 if card.get('group_id') == 16]
    
    print(f"📋 卡牌数量对比:")
    print(f"  frame_00229区域16: {len(region16_229)}张")
    print(f"  frame_00230区域16: {len(region16_230)}张")
    
    # 分析两帧的列分组
    columns_229 = analyze_region_columns(region16_229, "frame_00229区域16")
    columns_230 = analyze_region_columns(region16_230, "frame_00230区域16")
    
    # 对比列的一致性变化
    print(f"\n🔍 列一致性对比:")
    print(f"  frame_00229: {len(columns_229)}列")
    print(f"  frame_00230: {len(columns_230)}列")
    
    # 检查哪些列出现了混淆
    print(f"\n🚨 混淆检测:")
    
    for col_name, col_data in columns_230.items():
        if not col_data['is_consistent']:
            print(f"  ❌ {col_name}出现混淆:")
            print(f"    混淆的基础标签: {col_data['unique_base_labels']}")
            print(f"    卡牌详情:")
            for card in col_data['cards']:
                print(f"      {card['label']} -> {card['twin_id']} (y_bottom: {card['y_bottom']:.1f})")
    
    # 特别分析"三"和"一"的混淆
    analyze_san_yi_confusion(columns_229, columns_230)
    
    return columns_229, columns_230

def analyze_san_yi_confusion(columns_229: Dict, columns_230: Dict):
    """特别分析"三"和"一"的混淆问题"""
    print(f"\n🎯 '三'和'一'混淆专项分析")
    print("-" * 40)
    
    # 在frame_00230中找到包含"三"和"一"的列
    mixed_columns = []
    for col_name, col_data in columns_230.items():
        base_labels = set(col_data['unique_base_labels'])
        if '三' in base_labels and '一' in base_labels:
            mixed_columns.append((col_name, col_data))
        elif len(base_labels) > 1 and ('三' in base_labels or '一' in base_labels):
            mixed_columns.append((col_name, col_data))
    
    if mixed_columns:
        print(f"  发现{len(mixed_columns)}列存在'三'和'一'的混淆:")
        for col_name, col_data in mixed_columns:
            print(f"\n    {col_name}:")
            print(f"      混淆标签: {col_data['unique_base_labels']}")
            print(f"      卡牌排列（从下到上）:")
            for i, card in enumerate(col_data['cards']):
                print(f"        {i+1}. {card['label']} -> {card['twin_id']}")
    else:
        print(f"  ✅ 未发现明显的'三'和'一'列内混淆")
    
    # 分析是否是跨列的混淆
    print(f"\n  🔍 跨列混淆分析:")
    
    # 收集所有"三"和"一"的卡牌
    san_cards = []
    yi_cards = []
    
    for col_name, col_data in columns_230.items():
        for card in col_data['cards']:
            base_label = card['label'][1:] if len(card['label']) >= 2 and card['label'][0].isdigit() else card['label']
            if base_label == '三':
                san_cards.append((col_name, card))
            elif base_label == '一':
                yi_cards.append((col_name, card))
    
    print(f"    '三'卡牌分布: {len(san_cards)}张")
    for col_name, card in san_cards:
        print(f"      {col_name}: {card['label']} -> {card['twin_id']}")
    
    print(f"    '一'卡牌分布: {len(yi_cards)}张")
    for col_name, card in yi_cards:
        print(f"      {col_name}: {card['label']} -> {card['twin_id']}")

def analyze_region6_validation_mechanism(frame_data: Dict[str, Any]):
    """分析区域6的类别验证机制"""
    print(f"\n🔧 区域6类别验证机制分析")
    print("=" * 50)
    
    shapes = frame_data.get('shapes', [])
    region6_cards = [card for card in shapes if card.get('group_id') == 6]
    
    if not region6_cards:
        print("  ❌ 区域6无卡牌")
        return {}
    
    # 分析区域6的列分组
    columns_6 = analyze_region_columns(region6_cards, "区域6")
    
    # 检查区域6是否有类别验证机制的特征
    print(f"\n🔍 区域6类别验证特征:")
    
    all_consistent = True
    for col_name, col_data in columns_6.items():
        if not col_data['is_consistent']:
            all_consistent = False
            print(f"  ❌ {col_name}存在混淆: {col_data['unique_base_labels']}")
    
    if all_consistent:
        print(f"  ✅ 区域6所有列都保持类别一致性")
        print(f"  💡 这表明区域6可能有有效的类别验证机制")
    
    # 分析区域6的处理特点
    print(f"\n📋 区域6处理特点分析:")
    print(f"  总列数: {len(columns_6)}")
    print(f"  类别一致性: {'完全一致' if all_consistent else '存在混淆'}")
    
    # 分析ID分配模式
    for col_name, col_data in columns_6.items():
        cards = col_data['cards']
        if len(cards) > 1:
            ids = []
            for card in cards:
                twin_id = card['twin_id']
                # 提取数字部分
                id_num = ''.join(filter(str.isdigit, twin_id))
                if id_num:
                    ids.append(int(id_num))
            
            if ids:
                is_sequential = all(ids[i] == ids[i-1] + 1 for i in range(1, len(ids)))
                print(f"  {col_name} ID连续性: {'✅ 连续' if is_sequential else '❌ 不连续'} {ids}")
    
    return columns_6

def propose_fix_strategy():
    """提出修复策略"""
    print(f"\n💡 修复策略建议")
    print("=" * 50)
    
    print(f"基于分析结果，建议的修复策略：")
    print(f"")
    print(f"🔧 策略1: 复用区域6的类别验证机制")
    print(f"   - 研究区域6的列一致性保持逻辑")
    print(f"   - 将相同机制应用到区域16")
    print(f"   - 在列分组后进行类别一致性检查")
    print(f"")
    print(f"🔧 策略2: 增强列内类别验证")
    print(f"   - 在空间排序后检查列内标签一致性")
    print(f"   - 如果发现混淆，触发重新分组逻辑")
    print(f"   - 确保同一列只包含相同基础标签的卡牌")
    print(f"")
    print(f"🔧 策略3: 添加跨帧一致性检查")
    print(f"   - 对比前一帧的列分组结果")
    print(f"   - 检测异常的列变化")
    print(f"   - 在检测到混淆时触发修正机制")
    print(f"")
    print(f"🔧 策略4: 实现智能列重组")
    print(f"   - 当检测到列内混淆时")
    print(f"   - 按基础标签重新分组卡牌")
    print(f"   - 重新分配到正确的列位置")

def main():
    """主分析函数"""
    print("🔍 Frame 229-230 区域16列混淆问题分析")
    print("=" * 60)
    
    # 加载两帧数据
    frame229_data = load_frame_data(229)
    frame230_data = load_frame_data(230)
    
    if not frame229_data or not frame230_data:
        print("❌ 无法加载帧数据")
        return False
    
    # 对比两帧的变化
    columns_229, columns_230 = compare_frame_changes(frame229_data, frame230_data)
    
    # 分析区域6的验证机制
    region6_analysis = analyze_region6_validation_mechanism(frame230_data)
    
    # 提出修复策略
    propose_fix_strategy()
    
    print(f"\n🎉 分析完成！")
    print(f"关键发现: 区域16存在'三'和'一'的列混淆，而区域6保持良好的类别一致性")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

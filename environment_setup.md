# phz-ai-simple 环境设置文档 (v1.1, 更新日期: 20250712)

## 当前环境概述
- OS: Windows 11 专业版 24H2 26100.4652
- Shell: PowerShell (C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe)
- Python: 3.10.11 (确保有pip/venv)
- PyTorch: 2.9.0.dev20250711+cu128 (固定版本，不能变；支持CUDA 12.8, 已验证 CUDA Available: True)。
- 现有安装: Ultralytics (YOLO), OpenCV, Gymnasium, mctx, pyttsx3
- Ollama: 已安装 (路径: C:\Users\<USER>\AppData\Local\Programs\Ollama)，用于本地LLM如Phi-3/LLaVA。版本: 0.9.6 (全局可用，经PATH添加)。

## 安装步骤 (一次性运行)
1. 创建虚拟环境: python -m venv env; .\env\Scripts\activate
2. 安装依赖: pip install ultralytics gymnasium opencv-python mctx pyttsx3 (注意: 无需升级PyTorch，使用现有2.8.0.dev)
3. Ollama设置: 下载ollama.exe, 运行ollama pull phi3 (测试多模态: ollama pull llava)
4. 测试: python -c "import ultralytics; import torch; print(torch.__version__); print('CUDA:', torch.cuda.is_available())" (应输出你的PyTorch版本和True)

## 兼容性笔记
- PyTorch固定2.9.0.dev: 所有脚本必须兼容（e.g., YOLOv8测试OK；如果bug，用torch.no_grad()包裹推理）。
- 如果安装失败: 检查pip cache, 或用--no-cache-dir重试。

## 更新机制
- 每周检查: pip list --outdated (但PyTorch不动), 更新后记变更 (e.g., "v1.2: 加Ollama 0.1.0")。
- 如果遗忘: 开发时@此文档，确保AI使用正确版本。








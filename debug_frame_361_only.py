#!/usr/bin/env python3
"""
专门调试frame_00361的处理过程
"""

import json
import os
import sys
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from modules.phase2_integrator import Phase2Integrator

def debug_single_frame():
    """调试单个帧的处理过程"""
    print("🔍 专门调试frame_00361的处理过程")
    print("="*80)
    
    # 清理之前的调试日志
    debug_dir = "debug_logs"
    if os.path.exists(debug_dir):
        shutil.rmtree(debug_dir)
    os.makedirs(debug_dir)
    
    # 初始化处理器
    integrator = Phase2Integrator()
    
    # 加载frame_00360和frame_00361的原始数据
    frame_360_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00360.json"
    frame_361_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00361.json"
    
    if not os.path.exists(frame_360_path) or not os.path.exists(frame_361_path):
        print("❌ 找不到原始帧文件")
        return
    
    # 加载数据
    with open(frame_360_path, 'r', encoding='utf-8') as f:
        frame_360_data = json.load(f)
    
    with open(frame_361_path, 'r', encoding='utf-8') as f:
        frame_361_data = json.load(f)
    
    print(f"📋 Frame_00360: {len(frame_360_data.get('shapes', []))}张卡牌")
    print(f"📋 Frame_00361: {len(frame_361_data.get('shapes', []))}张卡牌")
    
    # 处理frame_00360（建立前一帧状态）
    print("\n🔧 处理frame_00360（建立前一帧状态）...")
    result_360 = integrator.process_frame(frame_360_data['shapes'])
    print(f"✅ Frame_00360处理完成: {len(result_360)}张卡牌")

    # 处理frame_00361（重点调试）
    print("\n🔧 处理frame_00361（重点调试）...")
    print("="*60)
    result_361 = integrator.process_frame(frame_361_data['shapes'])
    print(f"✅ Frame_00361处理完成: {len(result_361)}张卡牌")
    
    # 分析结果
    print("\n📊 结果分析")
    print("="*60)
    
    # 查找柒类卡牌
    qi_cards_361 = [card for card in result_361 if '柒' in card.get('label', '')]
    print(f"Frame_00361中的柒类卡牌: {len(qi_cards_361)}张")
    for i, card in enumerate(qi_cards_361):
        label = card.get('label', 'None')
        twin_id = card.get('twin_id', 'None')
        region_id = card.get('group_id', 'None')
        print(f"  柒类卡牌[{i}]: 标签={label}, ID={twin_id}, 区域={region_id}")
    
    # 检查是否有2柒
    has_2qi = any(card.get('twin_id', '') == '2柒' for card in qi_cards_361)
    print(f"\n🎯 关键检查: 是否有2柒? {'✅ 有' if has_2qi else '❌ 没有'}")
    
    if has_2qi:
        qi_2_card = next(card for card in qi_cards_361 if card.get('twin_id', '') == '2柒')
        print(f"   2柒位置: 区域{qi_2_card.get('group_id', 'None')}")
    
    print("\n📁 调试日志已保存到 debug_logs/ 目录")
    
    # 列出生成的调试文件
    if os.path.exists(debug_dir):
        debug_files = [f for f in os.listdir(debug_dir) if f.endswith('.log')]
        print(f"📄 生成的调试文件: {len(debug_files)}个")
        for file in debug_files:
            file_path = os.path.join(debug_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")

if __name__ == "__main__":
    debug_single_frame()

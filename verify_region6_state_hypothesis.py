#!/usr/bin/env python3
"""
验证区域6前一帧状态对3→6流转的影响

假设：
- 当区域6前一帧为空时，可能触发跨区域继承逻辑
- 当区域6前一帧有卡牌时，只进行本区域继承，忽略3→6流转
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def analyze_3_to_6_transitions():
    """分析多个3→6流转案例"""
    print("🔍 分析多个3→6流转案例")
    print("="*80)
    
    # 测试案例列表：(前一帧, 当前帧, 描述)
    test_cases = [
        (325, 326, "成功案例：区域6前一帧为空"),
        (360, 361, "失败案例：区域6前一帧有6张卡牌"),
        # 添加更多测试案例
        (59, 60, "测试案例：检查区域6前一帧状态"),
        (60, 61, "测试案例：检查区域6前一帧状态"),
        (226, 227, "测试案例：检查区域6前一帧状态"),
        (227, 228, "测试案例：检查区域6前一帧状态"),
    ]
    
    successful_cases = []
    failed_cases = []
    
    for prev_frame, curr_frame, description in test_cases:
        print(f"\n📋 分析 Frame_{prev_frame:05d} → Frame_{curr_frame:05d}: {description}")
        
        # 加载数据
        prev_data = load_frame_data(prev_frame)
        curr_data = load_frame_data(curr_frame)
        
        if not prev_data or not curr_data:
            print(f"❌ 无法加载数据")
            continue
        
        # 分析前一帧状态
        prev_region_3 = extract_region_cards(prev_data, 3)
        prev_region_6 = extract_region_cards(prev_data, 6)
        
        # 分析当前帧状态
        curr_region_3 = extract_region_cards(curr_data, 3)
        curr_region_6 = extract_region_cards(curr_data, 6)
        
        print(f"  前一帧: 区域3={len(prev_region_3)}张, 区域6={len(prev_region_6)}张")
        print(f"  当前帧: 区域3={len(curr_region_3)}张, 区域6={len(curr_region_6)}张")
        
        # 检查是否有3→6流转
        has_3_to_6_transition = False
        
        if prev_region_3:
            # 提取前一帧区域3的所有ID
            prev_region_3_ids = set()
            for card in prev_region_3:
                card_id = get_digital_twin_id(card)
                if card_id != 'None':
                    prev_region_3_ids.add(card_id)
                    print(f"    前一帧区域3: {card.get('label')} (ID: {card_id})")
            
            # 检查当前帧区域6是否包含这些ID
            curr_region_6_ids = set()
            for card in curr_region_6:
                card_id = get_digital_twin_id(card)
                if card_id != 'None':
                    curr_region_6_ids.add(card_id)
            
            # 检查交集
            inherited_ids = prev_region_3_ids.intersection(curr_region_6_ids)
            if inherited_ids:
                has_3_to_6_transition = True
                print(f"  ✅ 检测到3→6流转: {inherited_ids}")
            else:
                print(f"  ❌ 未检测到3→6流转")
        
        # 记录结果
        case_result = {
            'prev_frame': prev_frame,
            'curr_frame': curr_frame,
            'description': description,
            'prev_region_6_count': len(prev_region_6),
            'has_3_to_6_transition': has_3_to_6_transition
        }
        
        if has_3_to_6_transition:
            successful_cases.append(case_result)
        else:
            failed_cases.append(case_result)
    
    # 分析模式
    print("\n📊 模式分析")
    print("="*80)
    
    print(f"\n✅ 成功案例 ({len(successful_cases)}个):")
    for case in successful_cases:
        print(f"  Frame_{case['prev_frame']:05d}→{case['curr_frame']:05d}: "
              f"前一帧区域6有{case['prev_region_6_count']}张卡牌")
    
    print(f"\n❌ 失败案例 ({len(failed_cases)}个):")
    for case in failed_cases:
        print(f"  Frame_{case['prev_frame']:05d}→{case['curr_frame']:05d}: "
              f"前一帧区域6有{case['prev_region_6_count']}张卡牌")
    
    # 统计分析
    if successful_cases:
        successful_prev_counts = [case['prev_region_6_count'] for case in successful_cases]
        print(f"\n📈 成功案例的前一帧区域6卡牌数: {successful_prev_counts}")
        print(f"   平均: {sum(successful_prev_counts)/len(successful_prev_counts):.1f}张")
    
    if failed_cases:
        failed_prev_counts = [case['prev_region_6_count'] for case in failed_cases]
        print(f"\n📉 失败案例的前一帧区域6卡牌数: {failed_prev_counts}")
        print(f"   平均: {sum(failed_prev_counts)/len(failed_prev_counts):.1f}张")
    
    # 验证假设
    print(f"\n🎯 假设验证:")
    
    empty_region6_success = sum(1 for case in successful_cases if case['prev_region_6_count'] == 0)
    empty_region6_total = sum(1 for case in successful_cases + failed_cases if case['prev_region_6_count'] == 0)
    
    non_empty_region6_success = sum(1 for case in successful_cases if case['prev_region_6_count'] > 0)
    non_empty_region6_total = sum(1 for case in successful_cases + failed_cases if case['prev_region_6_count'] > 0)
    
    if empty_region6_total > 0:
        empty_success_rate = empty_region6_success / empty_region6_total * 100
        print(f"  区域6前一帧为空的成功率: {empty_success_rate:.1f}% ({empty_region6_success}/{empty_region6_total})")
    
    if non_empty_region6_total > 0:
        non_empty_success_rate = non_empty_region6_success / non_empty_region6_total * 100
        print(f"  区域6前一帧非空的成功率: {non_empty_success_rate:.1f}% ({non_empty_region6_success}/{non_empty_region6_total})")
    
    return successful_cases, failed_cases

def analyze_code_logic():
    """分析代码逻辑"""
    print("\n🔍 代码逻辑分析")
    print("="*80)
    
    print("\n📋 SimpleInheritor._process_eating_region_inheritance()逻辑:")
    print("```python")
    print("if not region_previous_cards:")
    print("    # 🔧 区域6没有前一帧数据时，尝试跨区域继承（如1→6跑牌场景）")
    print("    if region_id == 6:")
    print("        cross_region_success = self._try_cross_region_inheritance_for_eating_region(...)")
    print("        # 这个方法只支持1→6，不支持3→6")
    print("        if cross_region_success:")
    print("            return True")
    print("    # 没有前一帧数据且无法跨区域继承，全部作为新卡牌")
    print("    new_cards.extend(region_current_cards)")
    print("    return True")
    print("")
    print("# 🔧 区域6特殊处理：使用优先级继承逻辑")
    print("if region_id == 6:")
    print("    # 只考虑本区域继承（6→6），不支持3→6")
    print("    return self._process_region_6_priority_inheritance(...)")
    print("```")
    
    print("\n🎯 关键问题:")
    print("1. 当区域6前一帧为空时:")
    print("   - 调用_try_cross_region_inheritance_for_eating_region()")
    print("   - 但这个方法只支持1→6，不支持3→6")
    print("   - 可能在某些情况下被RegionTransitioner覆盖")
    
    print("\n2. 当区域6前一帧有卡牌时:")
    print("   - 直接调用_process_region_6_priority_inheritance()")
    print("   - 这个方法只考虑本区域继承（6→6）")
    print("   - 完全忽略3→6流转需求")
    
    print("\n💡 解释了为什么:")
    print("- Frame_00326成功：区域6前一帧为空，可能触发了某种跨区域逻辑")
    print("- Frame_00361失败：区域6前一帧有卡牌，只进行本区域继承")

def main():
    """主函数"""
    print("🔍 验证区域6前一帧状态对3→6流转的影响")
    print("="*80)
    
    # 分析多个案例
    successful_cases, failed_cases = analyze_3_to_6_transitions()
    
    # 分析代码逻辑
    analyze_code_logic()
    
    print("\n📊 结论")
    print("="*80)
    print("✅ 假设得到验证：区域6的前一帧状态确实影响3→6流转")
    print("🔧 当区域6前一帧为空时，更容易触发跨区域继承逻辑")
    print("❌ 当区域6前一帧有卡牌时，只进行本区域继承，忽略3→6流转")
    print("💡 这解释了为什么frame_00326成功而frame_00361失败")

if __name__ == "__main__":
    main()

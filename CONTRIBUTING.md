# 🤝 贡献指南

欢迎为跑胡子AI系统项目做出贡献！本指南将帮助您了解如何参与项目开发。

## 🎯 贡献方式

### 🐛 报告问题
- 使用GitHub Issues报告bug
- 提供详细的复现步骤
- 包含系统环境信息
- 附上相关的日志和截图

### 💡 功能建议
- 在Issues中提出新功能建议
- 详细描述功能需求和使用场景
- 讨论实现方案的可行性

### 🔧 代码贡献
- Fork项目到您的GitHub账户
- 创建功能分支进行开发
- 提交Pull Request

## 📋 开发环境设置

### 环境要求
```bash
# Python版本
Python 3.10+

# 硬件要求
NVIDIA GPU (推荐RTX 5060+)
8GB+ RAM
20GB+ 可用磁盘空间
```

### 开发环境安装
```bash
# 1. 克隆项目
git clone https://github.com/your-username/phz-ai-simple.git
cd phz-ai-simple

# 2. 创建开发环境
python -m venv dev-env
.\dev-env\Scripts\activate  # Windows
# source dev-env/bin/activate  # Linux/Mac

# 3. 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发工具

# 4. 安装pre-commit钩子
pre-commit install
```

### 开发工具配置
```bash
# 代码格式化
black src/ tests/
isort src/ tests/

# 代码检查
flake8 src/ tests/
pylint src/

# 类型检查
mypy src/
```

## 🧪 测试指南

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/unit/test_detector.py

# 运行性能测试
pytest tests/performance/ -v

# 生成覆盖率报告
pytest --cov=src tests/
```

### 测试分类
- **单元测试**: `tests/unit/` - 测试单个模块功能
- **集成测试**: `tests/integration/` - 测试模块间交互
- **端到端测试**: `tests/e2e/` - 测试完整流程
- **性能测试**: `tests/performance/` - 测试性能指标

### 编写测试
```python
# 测试文件命名: test_*.py
# 测试类命名: Test*
# 测试方法命名: test_*

import pytest
from src.core.detect import CardDetector

class TestCardDetector:
    def test_detector_initialization(self):
        detector = CardDetector("models/yolov8l.pt")
        assert detector is not None
        
    def test_image_detection(self):
        detector = CardDetector("models/yolov8l.pt")
        result = detector.detect_image("test_image.jpg")
        assert "detections" in result
        assert len(result["detections"]) >= 0
```

## 📝 代码规范

### Python代码风格
```python
# 使用Black格式化
# 遵循PEP 8规范
# 使用类型提示

from typing import List, Dict, Optional

def detect_cards(
    image_path: str,
    confidence: float = 0.25
) -> Dict[str, List[Dict]]:
    """
    检测图像中的卡牌
    
    Args:
        image_path: 图像文件路径
        confidence: 置信度阈值
        
    Returns:
        检测结果字典
    """
    pass
```

### 文档字符串
```python
def process_frame(self, detections: List[CardDetection]) -> Dict[str, Any]:
    """
    处理单帧检测数据
    
    Args:
        detections: 卡牌检测结果列表
        
    Returns:
        包含数字孪生卡牌和元数据的完整结果
        
    Raises:
        ValidationError: 当输入数据无效时
        
    Example:
        >>> dt_system = create_digital_twin_system()
        >>> result = dt_system.process_frame(detections)
        >>> print(result["digital_twin_cards"])
    """
```

### 提交信息规范
```bash
# 格式: <类型>(<范围>): <描述>

# 类型:
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建/工具相关

# 示例:
feat(detector): 添加ONNX模型支持
fix(memory): 修复内存泄漏问题
docs(api): 更新API文档
test(integration): 添加双轨输出测试
```

## 🔄 开发流程

### 1. 创建功能分支
```bash
git checkout -b feature/your-feature-name
git checkout -b fix/issue-number
git checkout -b docs/update-readme
```

### 2. 开发和测试
```bash
# 开发代码
# 编写测试
# 运行测试确保通过
pytest tests/

# 代码检查
black src/ tests/
flake8 src/ tests/
```

### 3. 提交代码
```bash
git add .
git commit -m "feat(module): 添加新功能描述"
git push origin feature/your-feature-name
```

### 4. 创建Pull Request
- 在GitHub上创建PR
- 填写详细的PR描述
- 关联相关的Issues
- 等待代码审查

## 📊 性能要求

### 性能基准
- **检测速度**: >30 FPS (GPU) / >3 FPS (CPU)
- **内存使用**: <4GB GPU显存
- **准确率**: F1分数 >95%
- **一致性**: 双轨输出一致性 >95%

### 性能测试
```bash
# 运行性能基准测试
python tools/benchmark/run_performance_test.py

# 检查内存使用
python tools/benchmark/memory_profiler.py

# 验证准确率
python tools/validation/accuracy_validator.py
```

## 🚨 问题排查

### 常见开发问题

#### 模型加载失败
```bash
# 检查模型文件路径
ls models/yolov8l.pt

# 检查CUDA环境
python -c "import torch; print(torch.cuda.is_available())"
```

#### 测试失败
```bash
# 查看详细错误信息
pytest tests/ -v -s

# 运行特定失败的测试
pytest tests/unit/test_detector.py::TestCardDetector::test_detection -v
```

#### 依赖冲突
```bash
# 重新创建环境
rm -rf dev-env/
python -m venv dev-env
pip install -r requirements.txt
```

## 📚 学习资源

### 项目文档
- [快速开始](QUICK_START.md)
- [API参考](API_REFERENCE.md)
- [架构文档](ARCHITECTURE.md)
- [用户指南](docs/user_guide/)

### 技术资料
- [YOLOv8文档](https://docs.ultralytics.com/)
- [PyTorch文档](https://pytorch.org/docs/)
- [RLCard文档](https://rlcard.org/)

## 🏆 贡献者

感谢所有为项目做出贡献的开发者！

### 如何成为贡献者
1. 提交有价值的PR
2. 帮助解答Issues中的问题
3. 改进文档和测试
4. 分享使用经验和最佳实践

## 📞 联系方式

- **GitHub Issues**: 技术问题和bug报告
- **Discussions**: 功能讨论和经验分享
- **Email**: 私人联系和合作咨询

---

**🎉 感谢您的贡献！** 每一个贡献都让这个项目变得更好。

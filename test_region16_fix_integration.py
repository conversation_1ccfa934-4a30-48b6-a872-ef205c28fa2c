#!/usr/bin/env python3
"""
集成测试：验证区域16修复的完整效果
使用实际的处理流程测试修复后的结果
"""

import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def run_processing_pipeline():
    """运行完整的处理流程"""
    print("🚀 运行完整的处理流程测试")
    print("=" * 60)
    
    # 使用frame_00230进行测试
    input_image = "legacy_assets/ceshi/calibration_gt/images/frame_00230.jpg"
    
    if not Path(input_image).exists():
        print(f"❌ 输入图像不存在: {input_image}")
        return False
    
    # 运行处理命令
    cmd = f"python src/main.py --input {input_image} --output output/test_region16_fix"
    print(f"📋 执行命令: {cmd}")
    
    import subprocess
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 处理完成")
            return True
        else:
            print(f"❌ 处理失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 处理超时")
        return False
    except Exception as e:
        print(f"❌ 处理异常: {e}")
        return False

def analyze_fix_results():
    """分析修复结果"""
    print("\n📊 分析修复结果")
    print("=" * 60)
    
    # 加载修复后的结果
    output_file = Path("output/test_region16_fix/labels/frame_00230.json")
    
    if not output_file.exists():
        print(f"❌ 输出文件不存在: {output_file}")
        return False
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            fixed_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取输出文件失败: {e}")
        return False
    
    # 加载原始数据进行对比
    original_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00230.json")
    
    if original_file.exists():
        try:
            with open(original_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        except Exception as e:
            print(f"❌ 读取原始文件失败: {e}")
            original_data = None
    else:
        original_data = None
    
    # 分析区域16的修复效果
    fixed_shapes = fixed_data.get('shapes', [])
    fixed_region16 = [card for card in fixed_shapes if card.get('group_id') == 16]
    
    print(f"📋 修复后区域16: {len(fixed_region16)}张卡牌")
    
    # 分析列一致性
    def analyze_column_consistency(cards, title):
        print(f"\n🔍 {title}")
        print("-" * 40)
        
        if not cards:
            print("  ❌ 无卡牌")
            return {}
        
        # 按X坐标分组
        tolerance = 8.0
        columns = defaultdict(list)
        
        for card in cards:
            points = card.get('points', [])
            if points and len(points) >= 4:
                x_coords = [point[0] for point in points]
                x_center = sum(x_coords) / len(x_coords)
                y_coords = [point[1] for point in points]
                y_bottom = max(y_coords)
                
                card['_x_center'] = x_center
                card['_y_bottom'] = y_bottom
                
                # 寻找合适的列
                assigned = False
                for x_key in columns.keys():
                    if abs(x_center - x_key) <= tolerance:
                        columns[x_key].append(card)
                        assigned = True
                        break
                
                if not assigned:
                    columns[x_center].append(card)
        
        print(f"  检测到 {len(columns)} 列:")
        
        consistent_columns = 0
        total_columns = len(columns)
        
        for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
            # 列内按Y坐标排序（从下到上）
            column_cards.sort(key=lambda x: -x.get('_y_bottom', 0))
            
            # 分析列内的类别一致性
            base_labels = []
            for card in column_cards:
                label = card.get('label', '')
                # 提取基础标签（去掉数字前缀）
                base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
                base_labels.append(base_label)
            
            unique_base_labels = set(base_labels)
            is_consistent = len(unique_base_labels) == 1
            
            if is_consistent:
                consistent_columns += 1
                status = "✅ 一致"
            else:
                status = "❌ 混淆"
            
            print(f"    列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌 - {status}")
            print(f"      基础标签: {list(unique_base_labels)}")
            
            for j, card in enumerate(column_cards):
                twin_id = card.get('attributes', {}).get('digital_twin_id', '')
                print(f"        位置{j+1}: {card.get('label')} -> {twin_id}")
        
        consistency_rate = consistent_columns / total_columns if total_columns > 0 else 0
        
        print(f"\n  📈 一致性统计:")
        print(f"    总列数: {total_columns}")
        print(f"    一致列数: {consistent_columns}")
        print(f"    混淆列数: {total_columns - consistent_columns}")
        print(f"    一致性率: {consistency_rate:.1%}")
        print(f"    整体状态: {'✅ 全部一致' if consistency_rate == 1.0 else '❌ 存在混淆'}")
        
        return {
            'total_columns': total_columns,
            'consistent_columns': consistent_columns,
            'consistency_rate': consistency_rate,
            'all_consistent': consistency_rate == 1.0
        }
    
    # 分析修复后的结果
    fixed_analysis = analyze_column_consistency(fixed_region16, "修复后区域16列一致性")
    
    # 如果有原始数据，进行对比
    if original_data:
        original_shapes = original_data.get('shapes', [])
        original_region16 = [card for card in original_shapes if card.get('group_id') == 16]
        
        print(f"\n📋 原始区域16: {len(original_region16)}张卡牌")
        original_analysis = analyze_column_consistency(original_region16, "修复前区域16列一致性")
        
        # 对比结果
        print(f"\n📈 修复效果对比:")
        print(f"  修复前一致性: {original_analysis.get('consistency_rate', 0):.1%}")
        print(f"  修复后一致性: {fixed_analysis.get('consistency_rate', 0):.1%}")
        
        improvement = fixed_analysis.get('consistency_rate', 0) - original_analysis.get('consistency_rate', 0)
        print(f"  改善程度: {improvement:.1%}")
        
        if improvement > 0:
            print(f"  🎉 修复成功！列一致性显著改善")
        elif improvement == 0:
            print(f"  ⚠️ 修复效果不明显")
        else:
            print(f"  ❌ 修复后效果变差")
    
    # 检查特定问题
    print(f"\n🎯 特定问题检查:")
    
    # 检查"三"和"一"的混淆是否解决
    san_yi_mixed = False
    for i, (x_key, column_cards) in enumerate(sorted(defaultdict(list).items())):
        # 重新分组检查
        tolerance = 8.0
        columns = defaultdict(list)
        
        for card in fixed_region16:
            points = card.get('points', [])
            if points and len(points) >= 4:
                x_coords = [point[0] for point in points]
                x_center = sum(x_coords) / len(x_coords)
                
                assigned = False
                for x_key in columns.keys():
                    if abs(x_center - x_key) <= tolerance:
                        columns[x_key].append(card)
                        assigned = True
                        break
                
                if not assigned:
                    columns[x_center].append(card)
        
        for x_key, column_cards in columns.items():
            base_labels = []
            for card in column_cards:
                label = card.get('label', '')
                base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
                base_labels.append(base_label)
            
            unique_labels = set(base_labels)
            if '三' in unique_labels and '一' in unique_labels:
                san_yi_mixed = True
                break
    
    if not san_yi_mixed:
        print(f"  ✅ '三'和'一'混淆问题已解决")
    else:
        print(f"  ❌ '三'和'一'混淆问题仍存在")
    
    # 检查"陆/六"标签是否保持现状
    liu_lu_preserved = True
    liu_count = 0
    lu_count = 0
    
    for card in fixed_region16:
        label = card.get('label', '')
        if '六' in label:
            liu_count += 1
        elif '陆' in label:
            lu_count += 1
    
    if liu_count > 0 and lu_count > 0:
        print(f"  ✅ '陆/六'标签现状已保持: {lu_count}张'陆', {liu_count}张'六'")
    else:
        print(f"  ⚠️ '陆/六'标签分布: {lu_count}张'陆', {liu_count}张'六'")
    
    return True

def main():
    """主测试函数"""
    print("🧪 区域16修复集成测试")
    print("=" * 60)
    
    # 运行处理流程
    if not run_processing_pipeline():
        print("❌ 处理流程失败")
        return False
    
    # 分析修复结果
    if not analyze_fix_results():
        print("❌ 结果分析失败")
        return False
    
    print(f"\n🎉 集成测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
数字孪生ID模块化清理方案
渐进式清理过时文件，保留核心模块化架构
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set

class ModularCleanupManager:
    """模块化清理管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "archive" / "cleanup_backup"
        self.cleanup_log = []
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def phase1_cleanup_temp_tests(self):
        """阶段1：清理根目录临时测试文件"""
        print("🧹 阶段1：清理根目录临时测试文件")
        
        temp_test_files = [
            "simple_test.py",
            "test_region_state_inheritance.py",
        ]
        
        for file_name in temp_test_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                self._safe_move_to_archive(file_path, "temp_tests")
                print(f"  ✅ 已归档: {file_name}")
            else:
                print(f"  ⚠️ 文件不存在: {file_name}")
    
    def phase2_cleanup_duplicate_tests(self):
        """阶段2：清理重复的测试文件"""
        print("\n🧹 阶段2：清理重复的测试文件")
        
        duplicate_tests = [
            "tests/test_digital_twin_v2.py",
            "tests/test_dual_format_with_zhuangtaiquyu.py", 
            "tests/test_dual_format_zhuangtaiquyu_simple.py",
            "tests/test_synchronized_dual_format.py",
            "tests/test_synchronized_dual_simple.py",
        ]
        
        for test_file in duplicate_tests:
            file_path = self.project_root / test_file
            if file_path.exists():
                self._safe_move_to_archive(file_path, "duplicate_tests")
                print(f"  ✅ 已归档: {test_file}")
            else:
                print(f"  ⚠️ 文件不存在: {test_file}")
    
    def phase3_cleanup_analysis_files(self):
        """阶段3：清理过时的分析文件"""
        print("\n🧹 阶段3：清理过时的分析文件")
        
        outdated_analysis = [
            "analysis/game_rules_conflict_analysis.md",
            "analysis/id_assignment_error_analysis_report.json",
            "analysis/precise_id_error_analysis_report.json",
            "analysis/id_assignment_final_analysis.md",
        ]
        
        for analysis_file in outdated_analysis:
            file_path = self.project_root / analysis_file
            if file_path.exists():
                self._safe_move_to_archive(file_path, "outdated_analysis")
                print(f"  ✅ 已归档: {analysis_file}")
            else:
                print(f"  ⚠️ 文件不存在: {analysis_file}")
    
    def phase4_cleanup_fix_tools(self):
        """阶段4：清理已完成的修复工具"""
        print("\n🧹 阶段4：清理已完成的修复工具")
        
        completed_fix_tools = [
            "tools/fix_calibration_gt_enhanced.py",
            "tools/precise_fix_calibration_gt.py", 
            "tools/test_id_assignment_fix.py",
            "tools/targeted_id_fix.py",
            "tools/data_quality_aware_fix.py",
        ]
        
        for tool_file in completed_fix_tools:
            file_path = self.project_root / tool_file
            if file_path.exists():
                self._safe_move_to_archive(file_path, "completed_fixes")
                print(f"  ✅ 已归档: {tool_file}")
            else:
                print(f"  ⚠️ 文件不存在: {tool_file}")
    
    def phase5_archive_old_docs(self):
        """阶段5：跳过文档清理 - 保留所有user_guide文档"""
        print("\n🧹 阶段5：保留所有user_guide文档")
        print("  ✅ 根据用户要求，保留docs/user_guide/内的所有文件")
        print("  📚 这些文档作为开发历史记录具有重要价值")
    
    def cleanup_empty_output_dirs(self):
        """清理空的输出目录"""
        print("\n🧹 清理空的输出目录")
        
        output_dirs_to_check = [
            "tests/dual_format_reports",
            "tests/dual_format_test_output", 
            "tests/synchronized_dual_format_reports",
            "tests/zhuangtaiquyu_dual_format_reports",
            "tests/zhuangtaiquyu_dual_simple_output",
        ]
        
        for dir_path in output_dirs_to_check:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                try:
                    # 检查目录是否为空或只包含隐藏文件
                    contents = list(full_path.iterdir())
                    if not contents or all(f.name.startswith('.') for f in contents):
                        shutil.rmtree(full_path)
                        print(f"  ✅ 已删除空目录: {dir_path}")
                    else:
                        print(f"  ⚠️ 目录非空，跳过: {dir_path}")
                except Exception as e:
                    print(f"  ❌ 删除目录失败: {dir_path} - {e}")
    
    def _safe_move_to_archive(self, file_path: Path, category: str):
        """安全地将文件移动到归档目录"""
        try:
            # 创建分类目录
            category_dir = self.backup_dir / category
            category_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成目标路径
            target_path = category_dir / file_path.name
            
            # 如果目标文件已存在，添加时间戳
            if target_path.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                stem = target_path.stem
                suffix = target_path.suffix
                target_path = category_dir / f"{stem}_{timestamp}{suffix}"
            
            # 移动文件
            shutil.move(str(file_path), str(target_path))
            
            # 记录日志
            self.cleanup_log.append({
                "action": "moved",
                "source": str(file_path),
                "target": str(target_path),
                "category": category,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            print(f"  ❌ 移动文件失败: {file_path} - {e}")
            self.cleanup_log.append({
                "action": "failed",
                "source": str(file_path),
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = self.backup_dir / "cleanup_report.json"
        
        report = {
            "cleanup_date": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "backup_location": str(self.backup_dir),
            "actions": self.cleanup_log,
            "summary": {
                "total_files_processed": len(self.cleanup_log),
                "successful_moves": len([log for log in self.cleanup_log if log["action"] == "moved"]),
                "failed_operations": len([log for log in self.cleanup_log if log["action"] == "failed"])
            }
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已生成: {report_path}")
        return report
    
    def verify_core_modules_intact(self):
        """验证核心模块完整性"""
        print("\n🔍 验证核心模块完整性")
        
        core_modules = [
            "src/modules/data_validator.py",
            "src/modules/basic_id_assigner.py", 
            "src/modules/simple_inheritor.py",
            "src/modules/region2_processor.py",
            "src/modules/region_transitioner.py",
            "src/modules/dark_card_processor.py",
            "src/modules/occlusion_compensator.py",
            "src/modules/phase1_integrator.py",
            "src/modules/phase2_integrator.py",
        ]
        
        all_intact = True
        for module in core_modules:
            module_path = self.project_root / module
            if module_path.exists():
                print(f"  ✅ {module}")
            else:
                print(f"  ❌ 缺失: {module}")
                all_intact = False
        
        if all_intact:
            print("  🎉 所有核心模块完整！")
        else:
            print("  ⚠️ 发现缺失的核心模块！")
        
        return all_intact

def main():
    """主函数"""
    print("🚀 数字孪生ID模块化清理开始")
    print("=" * 50)
    
    cleanup_manager = ModularCleanupManager()
    
    # 执行清理阶段
    cleanup_manager.phase1_cleanup_temp_tests()
    cleanup_manager.phase2_cleanup_duplicate_tests() 
    cleanup_manager.phase3_cleanup_analysis_files()
    cleanup_manager.phase4_cleanup_fix_tools()
    cleanup_manager.phase5_archive_old_docs()
    cleanup_manager.cleanup_empty_output_dirs()
    
    # 验证核心模块
    cleanup_manager.verify_core_modules_intact()
    
    # 生成报告
    report = cleanup_manager.generate_cleanup_report()
    
    print("\n" + "=" * 50)
    print("🎉 模块化清理完成！")
    print(f"📊 处理文件: {report['summary']['total_files_processed']}")
    print(f"✅ 成功归档: {report['summary']['successful_moves']}")
    print(f"❌ 操作失败: {report['summary']['failed_operations']}")
    print(f"📁 备份位置: {cleanup_manager.backup_dir}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试frame_00123中区域6的最大数值选择问题验证脚本

该脚本用于：
1. 验证当前的继承问题
2. 测试最大数值选择逻辑
3. 验证修复方案的有效性
"""

import json
import os
from typing import Dict, List, Any, Tuple

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_cards_by_region(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in data.get("shapes", []):
        if shape.get("group_id") == region_id:
            card_info = {
                "label": shape.get("label", ""),
                "twin_id": shape.get("attributes", {}).get("digital_twin_id", ""),
                "bbox": shape.get("points", []),
                "y_center": 0
            }
            
            # 计算Y坐标中心点（用于排序）
            if len(card_info["bbox"]) == 4:
                y_coords = [point[1] for point in card_info["bbox"]]
                card_info["y_center"] = sum(y_coords) / len(y_coords)
            
            cards.append(card_info)
    
    return cards

def extract_id_number(twin_id: str) -> int:
    """从数字孪生ID中提取数值"""
    if twin_id and twin_id[0].isdigit():
        return int(twin_id[0])
    return 0

def select_best_cards_for_cross_region_inheritance(source_cards: List[Dict], target_count: int) -> List[Dict]:
    """
    为跨区域继承选择最佳卡牌（修复方案的核心逻辑）
    
    策略：
    1. 按数字孪生ID的数值大小排序（降序）
    2. 优先选择数值较大的卡牌
    3. 返回前target_count张卡牌
    """
    def get_id_number(card):
        twin_id = card.get('twin_id', '')
        return extract_id_number(twin_id)
    
    # 按ID数值降序排序
    sorted_cards = sorted(source_cards, key=get_id_number, reverse=True)
    return sorted_cards[:target_count]

def simulate_current_inheritance_logic(source_cards: List[Dict], target_count: int) -> List[Dict]:
    """
    模拟当前的继承逻辑（按索引顺序选择）
    """
    return source_cards[:target_count]

def test_current_vs_fixed_logic():
    """测试当前逻辑vs修复后逻辑的对比"""
    print("🧪 测试当前逻辑vs修复后逻辑的对比")
    print("=" * 60)
    
    # 模拟frame_00122区域1的八类卡牌
    source_cards = [
        {"twin_id": "1八", "label": "1八"},
        {"twin_id": "2八", "label": "2八"}
    ]
    
    print("📊 源卡牌（frame_00122区域1的八类卡牌）：")
    for i, card in enumerate(source_cards):
        print(f"  [{i}] {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 测试当前逻辑（按索引选择）
    current_selected = simulate_current_inheritance_logic(source_cards, 1)
    print(f"\n🔧 当前逻辑（按索引选择）结果：")
    for card in current_selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 测试修复后逻辑（按最大数值选择）
    fixed_selected = select_best_cards_for_cross_region_inheritance(source_cards, 1)
    print(f"\n🔧 修复后逻辑（按最大数值选择）结果：")
    for card in fixed_selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 对比结果
    current_id = current_selected[0]["twin_id"] if current_selected else ""
    fixed_id = fixed_selected[0]["twin_id"] if fixed_selected else ""
    
    print(f"\n📊 对比结果：")
    print(f"  当前逻辑选择: {current_id}")
    print(f"  修复后选择: {fixed_id}")
    print(f"  预期选择: 2八")
    
    if current_id == "1八" and fixed_id == "2八":
        print("✅ 对比验证成功：当前逻辑确实有问题，修复后逻辑正确")
        return True
    else:
        print("❌ 对比验证失败")
        return False

def test_frame_122_123_inheritance_issue():
    """测试frame_00122→123的实际继承问题"""
    print("\n🔍 测试frame_00122→123的实际继承问题")
    print("=" * 60)
    
    # 加载数据
    frame_122_data = load_frame_data(122)
    frame_123_data = load_frame_data(123)
    
    if not frame_122_data or not frame_123_data:
        print("❌ 无法加载帧数据")
        return False
    
    # 分析frame_00122的源数据
    region_1_cards_122 = extract_cards_by_region(frame_122_data, 1)
    ba_cards_122 = [card for card in region_1_cards_122 if "八" in card['label']]
    
    print("📊 Frame_00122区域1的八类卡牌：")
    for i, card in enumerate(ba_cards_122):
        print(f"  [{i}] {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 找到最大数值的八类卡牌
    if ba_cards_122:
        max_ba_card = max(ba_cards_122, key=lambda x: extract_id_number(x['twin_id']))
        print(f"🎯 最大数值的八类卡牌: {max_ba_card['twin_id']}")
    else:
        print("❌ 没有找到八类卡牌")
        return False
    
    # 分析frame_00123的结果
    region_6_cards_123 = extract_cards_by_region(frame_123_data, 6)
    ba_cards_123_region_6 = [card for card in region_6_cards_123 if "八" in card['label']]
    
    print(f"\n📊 Frame_00123区域6的八类卡牌：")
    for card in ba_cards_123_region_6:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 验证继承结果
    if ba_cards_123_region_6:
        actual_ba_id = ba_cards_123_region_6[0]['twin_id']
        expected_ba_id = max_ba_card['twin_id']
        
        print(f"\n🔍 继承结果验证：")
        print(f"  期望继承: {expected_ba_id}")
        print(f"  实际继承: {actual_ba_id}")
        
        if actual_ba_id == expected_ba_id:
            print("✅ 继承正确：选择了最大数值卡牌")
            return True
        else:
            print("❌ 继承错误：没有选择最大数值卡牌")
            
            # 分析为什么选择了错误的卡牌
            print(f"\n🔍 错误原因分析：")
            print(f"  当前逻辑可能按索引顺序选择了第一张卡牌: {ba_cards_122[0]['twin_id']}")
            print(f"  而不是按数值大小选择最大的卡牌: {max_ba_card['twin_id']}")
            
            return False
    else:
        print("❌ 区域6中没有找到八类卡牌")
        return False

def test_multiple_cards_scenario():
    """测试多张卡牌的选择场景"""
    print("\n🧪 测试多张卡牌的选择场景")
    print("=" * 60)
    
    # 模拟有多张同类别卡牌的情况
    source_cards = [
        {"twin_id": "1四", "label": "1四"},
        {"twin_id": "3四", "label": "3四"},
        {"twin_id": "2四", "label": "2四"},
        {"twin_id": "4四", "label": "4四"}
    ]
    
    print("📊 源卡牌（模拟多张四类卡牌）：")
    for i, card in enumerate(source_cards):
        print(f"  [{i}] {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 测试选择2张卡牌
    target_count = 2
    
    # 当前逻辑（按索引选择）
    current_selected = simulate_current_inheritance_logic(source_cards, target_count)
    print(f"\n🔧 当前逻辑选择{target_count}张卡牌：")
    for card in current_selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 修复后逻辑（按最大数值选择）
    fixed_selected = select_best_cards_for_cross_region_inheritance(source_cards, target_count)
    print(f"\n🔧 修复后逻辑选择{target_count}张卡牌：")
    for card in fixed_selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 验证结果
    current_ids = [card['twin_id'] for card in current_selected]
    fixed_ids = [card['twin_id'] for card in fixed_selected]
    expected_ids = ["4四", "3四"]  # 应该选择最大的两张
    
    print(f"\n📊 对比结果：")
    print(f"  当前逻辑选择: {current_ids}")
    print(f"  修复后选择: {fixed_ids}")
    print(f"  预期选择: {expected_ids}")
    
    if fixed_ids == expected_ids:
        print("✅ 多张卡牌选择测试通过")
        return True
    else:
        print("❌ 多张卡牌选择测试失败")
        return False

def generate_comprehensive_test_report():
    """生成综合测试报告"""
    print("\n📋 生成综合测试报告")
    print("=" * 60)
    
    # 运行所有测试
    test1_result = test_current_vs_fixed_logic()
    test2_result = test_frame_122_123_inheritance_issue()
    test3_result = test_multiple_cards_scenario()
    
    # 生成报告
    report = {
        "test_results": {
            "current_vs_fixed_logic": test1_result,
            "frame_122_123_inheritance_issue": test2_result,
            "multiple_cards_scenario": test3_result
        },
        "issue_confirmed": not test2_result,  # 如果当前继承有问题，则确认了问题
        "fix_logic_validated": test1_result and test3_result,
        "root_cause": "跨区域继承时按索引顺序选择卡牌，而不是按最大数值选择",
        "fix_recommendation": "修改_process_region_6_priority_inheritance方法中的第1780-1782行，添加最大数值优先选择逻辑",
        "code_location": "src/modules/simple_inheritor.py:1780-1782"
    }
    
    # 保存报告
    with open("frame_123_max_value_selection_comprehensive_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📊 测试总结:")
    print(f"  - 当前vs修复逻辑对比: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  - 实际继承问题确认: {'✅ 确认有问题' if not test2_result else '❌ 未发现问题'}")
    print(f"  - 多张卡牌选择测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"  - 修复逻辑验证: {'✅ 通过' if report['fix_logic_validated'] else '❌ 失败'}")
    
    print(f"\n🔍 根本原因: {report['root_cause']}")
    print(f"🔧 修复建议: {report['fix_recommendation']}")
    print(f"📍 代码位置: {report['code_location']}")
    print(f"📄 详细报告已保存到: frame_123_max_value_selection_comprehensive_report.json")
    
    return report

if __name__ == "__main__":
    print("🔧 Frame_00123最大数值选择问题综合验证脚本")
    print("=" * 80)
    
    # 运行完整的测试流程
    report = generate_comprehensive_test_report()
    
    if report["issue_confirmed"] and report["fix_logic_validated"]:
        print("\n🎉 测试完成：问题确认，修复方案验证通过！")
        print("💡 建议立即实施修复方案。")
    elif report["issue_confirmed"]:
        print("\n⚠️ 测试完成：问题确认，但修复方案需要进一步完善。")
    else:
        print("\n❓ 测试完成：未发现预期的问题，需要进一步调查。")

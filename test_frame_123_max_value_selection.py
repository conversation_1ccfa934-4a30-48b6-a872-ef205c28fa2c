#!/usr/bin/env python3
"""
测试frame_00123中区域6的最大数值选择问题验证脚本

该脚本用于：
1. 验证当前的继承问题
2. 测试最大数值选择逻辑
3. 验证修复方案的有效性
"""

import json
import os
from typing import Dict, List, Any, Tuple

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_cards_by_region(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in data.get("shapes", []):
        if shape.get("group_id") == region_id:
            card_info = {
                "label": shape.get("label", ""),
                "twin_id": shape.get("attributes", {}).get("digital_twin_id", ""),
                "bbox": shape.get("points", []),
                "y_center": 0
            }
            
            # 计算Y坐标中心点（用于排序）
            if len(card_info["bbox"]) == 4:
                y_coords = [point[1] for point in card_info["bbox"]]
                card_info["y_center"] = sum(y_coords) / len(y_coords)
            
            cards.append(card_info)
    
    return cards

def extract_id_number(twin_id: str) -> int:
    """从数字孪生ID中提取数值"""
    if twin_id and twin_id[0].isdigit():
        return int(twin_id[0])
    return 0

def select_best_cards_for_cross_region_inheritance(source_cards: List[Dict], target_count: int) -> List[Dict]:
    """
    为跨区域继承选择最佳卡牌（修复方案的核心逻辑）
    
    策略：
    1. 按数字孪生ID的数值大小排序（降序）
    2. 优先选择数值较大的卡牌
    3. 返回前target_count张卡牌
    """
    def get_id_number(card):
        twin_id = card.get('twin_id', '')
        return extract_id_number(twin_id)
    
    # 按ID数值降序排序
    sorted_cards = sorted(source_cards, key=get_id_number, reverse=True)
    return sorted_cards[:target_count]

def test_max_value_selection_logic():
    """测试最大数值选择逻辑"""
    print("🧪 测试最大数值选择逻辑")
    print("=" * 50)
    
    # 模拟frame_00122区域1的八类卡牌
    source_cards = [
        {"twin_id": "1八", "label": "1八"},
        {"twin_id": "2八", "label": "2八"}
    ]
    
    print("📊 源卡牌：")
    for card in source_cards:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 测试选择1张卡牌
    selected = select_best_cards_for_cross_region_inheritance(source_cards, 1)
    
    print(f"\n🎯 选择1张卡牌的结果：")
    for card in selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 验证结果
    expected_id = "2八"
    actual_id = selected[0]["twin_id"] if selected else ""
    
    if actual_id == expected_id:
        print(f"✅ 测试通过：正确选择了最大数值卡牌 {expected_id}")
        return True
    else:
        print(f"❌ 测试失败：期望 {expected_id}，实际 {actual_id}")
        return False

def test_current_inheritance_issue():
    """测试当前的继承问题"""
    print("\n🔍 测试当前frame_00122→123的继承问题")
    print("=" * 50)
    
    # 加载数据
    frame_122_data = load_frame_data(122)
    frame_123_data = load_frame_data(123)
    
    if not frame_122_data or not frame_123_data:
        print("❌ 无法加载帧数据")
        return False
    
    # 分析frame_00122的源数据
    region_1_cards_122 = extract_cards_by_region(frame_122_data, 1)
    ba_cards_122 = [card for card in region_1_cards_122 if "八" in card['label']]
    
    print("📊 Frame_00122区域1的八类卡牌：")
    for card in ba_cards_122:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 找到最大数值的八类卡牌
    if ba_cards_122:
        max_ba_card = max(ba_cards_122, key=lambda x: extract_id_number(x['twin_id']))
        print(f"🎯 最大数值的八类卡牌: {max_ba_card['twin_id']}")
    else:
        print("❌ 没有找到八类卡牌")
        return False
    
    # 分析frame_00123的结果
    region_6_cards_123 = extract_cards_by_region(frame_123_data, 6)
    ba_cards_123_region_6 = [card for card in region_6_cards_123 if "八" in card['label']]
    
    print(f"\n📊 Frame_00123区域6的八类卡牌：")
    for card in ba_cards_123_region_6:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 验证继承结果
    if ba_cards_123_region_6:
        actual_ba_id = ba_cards_123_region_6[0]['twin_id']
        expected_ba_id = max_ba_card['twin_id']
        
        print(f"\n🔍 继承结果验证：")
        print(f"  期望继承: {expected_ba_id}")
        print(f"  实际继承: {actual_ba_id}")
        
        if actual_ba_id == expected_ba_id:
            print("✅ 继承正确：选择了最大数值卡牌")
            return True
        else:
            print("❌ 继承错误：没有选择最大数值卡牌")
            return False
    else:
        print("❌ 区域6中没有找到八类卡牌")
        return False

def simulate_fixed_inheritance():
    """模拟修复后的继承逻辑"""
    print("\n🔧 模拟修复后的继承逻辑")
    print("=" * 50)
    
    # 加载frame_00122数据
    frame_122_data = load_frame_data(122)
    if not frame_122_data:
        print("❌ 无法加载frame_00122数据")
        return False
    
    # 获取源数据
    region_1_cards = extract_cards_by_region(frame_122_data, 1)
    region_8_cards = extract_cards_by_region(frame_122_data, 8)
    
    # 模拟1→6流转（八类卡牌）
    ba_cards_region_1 = [card for card in region_1_cards if "八" in card['label']]
    if ba_cards_region_1:
        # 使用修复后的逻辑选择最大数值卡牌
        selected_ba = select_best_cards_for_cross_region_inheritance(ba_cards_region_1, 1)
        print(f"🎯 1→6流转选择的八类卡牌: {selected_ba[0]['twin_id']}")
    
    # 模拟8→6流转（七类卡牌）
    qi_cards_region_8 = [card for card in region_8_cards if "七" in card['label']]
    if qi_cards_region_8:
        selected_qi = select_best_cards_for_cross_region_inheritance(qi_cards_region_8, 1)
        print(f"🎯 8→6流转选择的七类卡牌: {selected_qi[0]['twin_id']}")
    
    # 模拟修复后的区域6结果
    print(f"\n📊 修复后的区域6预期结果（从下到上）：")
    if ba_cards_region_1 and qi_cards_region_8:
        expected_result = [
            selected_ba[0]['twin_id'],  # 2八
            "1九",  # 假设的九类卡牌
            selected_qi[0]['twin_id']   # 1七
        ]
        for i, card_id in enumerate(expected_result, 1):
            print(f"  {i}. {card_id}")
        
        return True
    
    return False

def test_multiple_cards_selection():
    """测试多张卡牌的选择逻辑"""
    print("\n🧪 测试多张卡牌的选择逻辑")
    print("=" * 50)
    
    # 模拟有多张同类别卡牌的情况
    source_cards = [
        {"twin_id": "1四", "label": "1四"},
        {"twin_id": "2四", "label": "2四"},
        {"twin_id": "3四", "label": "3四"},
        {"twin_id": "4四", "label": "4四"}
    ]
    
    print("📊 源卡牌：")
    for card in source_cards:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 测试选择2张卡牌
    selected = select_best_cards_for_cross_region_inheritance(source_cards, 2)
    
    print(f"\n🎯 选择2张卡牌的结果：")
    for card in selected:
        print(f"  - {card['label']} (ID: {card['twin_id']}, 数值: {extract_id_number(card['twin_id'])})")
    
    # 验证结果（应该选择4四和3四）
    expected_ids = ["4四", "3四"]
    actual_ids = [card['twin_id'] for card in selected]
    
    if actual_ids == expected_ids:
        print(f"✅ 测试通过：正确选择了最大数值的2张卡牌")
        return True
    else:
        print(f"❌ 测试失败：期望 {expected_ids}，实际 {actual_ids}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告")
    print("=" * 50)
    
    # 运行所有测试
    test1_result = test_max_value_selection_logic()
    test2_result = test_current_inheritance_issue()
    test3_result = simulate_fixed_inheritance()
    test4_result = test_multiple_cards_selection()
    
    # 生成报告
    report = {
        "test_results": {
            "max_value_selection_logic": test1_result,
            "current_inheritance_issue": test2_result,
            "fixed_inheritance_simulation": test3_result,
            "multiple_cards_selection": test4_result
        },
        "overall_success": all([test1_result, test3_result, test4_result]),
        "issue_confirmed": not test2_result,  # 如果当前继承有问题，则确认了问题
        "recommendations": []
    }
    
    if not test2_result:
        report["recommendations"].append("需要修复跨区域继承的最大数值选择逻辑")
    
    if test1_result and test3_result and test4_result:
        report["recommendations"].append("修复方案的逻辑验证通过，可以实施")
    
    # 保存报告
    with open("frame_123_max_value_selection_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📊 测试总结:")
    print(f"  - 最大数值选择逻辑: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  - 当前继承问题确认: {'✅ 确认有问题' if not test2_result else '❌ 未发现问题'}")
    print(f"  - 修复方案模拟: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"  - 多张卡牌选择: {'✅ 通过' if test4_result else '❌ 失败'}")
    print(f"📄 详细报告已保存到: frame_123_max_value_selection_test_report.json")
    
    return report

if __name__ == "__main__":
    print("🔧 Frame_00123最大数值选择问题验证脚本")
    print("=" * 70)
    
    # 运行完整的测试流程
    report = generate_test_report()
    
    if report["issue_confirmed"] and report["overall_success"]:
        print("\n🎉 测试完成：问题确认，修复方案验证通过！")
    elif report["issue_confirmed"]:
        print("\n⚠️ 测试完成：问题确认，但修复方案需要进一步完善。")
    else:
        print("\n❓ 测试完成：未发现预期的问题，需要进一步调查。")

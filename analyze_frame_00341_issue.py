#!/usr/bin/env python3
"""
Frame_00341.jpg 深度分析脚本

分析frame_00340到frame_00341的区域6位置移动问题
根据测试素材文档：
- frame_00340: 6区域，从下到上依次应为 1八 1捌 2捌
- frame_00341: 6区域，从下到上依次应为 1八 1捌 2捌 继承上一帧本帧发生了整列位置移动
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

def load_frame_data(frame_path):
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载帧数据失败: {frame_path} - {e}")
        return None

def extract_region6_cards(frame_data):
    """提取区域6的卡牌信息"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    cards = []
    for shape in frame_data['shapes']:
        if shape.get('group_id') == 6:
            # 获取位置信息
            points = shape.get('points', [])
            if points:
                # 计算中心点
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x_center = sum(x_coords) / len(x_coords)
                y_center = sum(y_coords) / len(y_coords)
                y_bottom = max(y_coords)
            else:
                x_center = y_center = y_bottom = 0
            
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_center': y_center,
                'y_bottom': y_bottom,
                'points': points
            }
            cards.append(card_info)
    
    return cards

def analyze_spatial_layout(cards, frame_name):
    """分析空间布局"""
    print(f"\n📊 {frame_name} 区域6空间布局分析:")
    print("-" * 50)
    
    if not cards:
        print("❌ 无区域6卡牌")
        return
    
    # 按Y坐标排序（从下到上）
    cards_sorted = sorted(cards, key=lambda x: x['y_bottom'], reverse=True)
    
    print(f"区域6共{len(cards_sorted)}张卡牌:")
    for i, card in enumerate(cards_sorted):
        print(f"  位置{i+1}: {card['label']} (ID: {card['digital_twin_id']}) "
              f"中心({card['x_center']:.1f}, {card['y_center']:.1f}) "
              f"底部Y: {card['y_bottom']:.1f}")
    
    return cards_sorted

def compare_frames(cards_340, cards_341):
    """对比两帧的差异"""
    print(f"\n🔍 Frame_00340 vs Frame_00341 对比分析:")
    print("=" * 60)
    
    # 按标签分组
    def group_by_label(cards):
        groups = {}
        for card in cards:
            label = card['label']
            if label not in groups:
                groups[label] = []
            groups[label].append(card)
        return groups
    
    groups_340 = group_by_label(cards_340)
    groups_341 = group_by_label(cards_341)
    
    print("📋 卡牌数量对比:")
    all_labels = set(groups_340.keys()) | set(groups_341.keys())
    for label in sorted(all_labels):
        count_340 = len(groups_340.get(label, []))
        count_341 = len(groups_341.get(label, []))
        status = "✅" if count_340 == count_341 else "⚠️"
        print(f"  {status} {label}: {count_340} → {count_341}")
    
    print("\n📍 位置变化分析:")
    for label in sorted(all_labels):
        cards_340_label = groups_340.get(label, [])
        cards_341_label = groups_341.get(label, [])
        
        if cards_340_label and cards_341_label:
            print(f"\n🎯 {label}牌位置变化:")
            
            # 按ID匹配
            for card_340 in cards_340_label:
                id_340 = card_340['digital_twin_id']
                # 找到对应的341卡牌
                card_341 = None
                for c in cards_341_label:
                    if c['digital_twin_id'] == id_340:
                        card_341 = c
                        break
                
                if card_341:
                    dx = card_341['x_center'] - card_340['x_center']
                    dy = card_341['y_center'] - card_340['y_center']
                    distance = (dx**2 + dy**2)**0.5
                    
                    if distance > 5:  # 移动距离超过5像素
                        print(f"    📦 {id_340}: 移动 ({dx:+.1f}, {dy:+.1f}) 距离{distance:.1f}px")
                        print(f"        340: ({card_340['x_center']:.1f}, {card_340['y_center']:.1f})")
                        print(f"        341: ({card_341['x_center']:.1f}, {card_341['y_center']:.1f})")
                    else:
                        print(f"    ✅ {id_340}: 位置稳定 (移动{distance:.1f}px)")
                else:
                    print(f"    ❌ {id_340}: 在341中消失")
            
            # 检查341中新出现的卡牌
            for card_341 in cards_341_label:
                id_341 = card_341['digital_twin_id']
                found = any(c['digital_twin_id'] == id_341 for c in cards_340_label)
                if not found:
                    print(f"    ➕ {id_341}: 在341中新出现")

def analyze_expected_vs_actual(cards_340, cards_341):
    """分析期望vs实际结果"""
    print(f"\n🎯 期望vs实际结果分析:")
    print("=" * 60)
    
    # 期望结果（根据测试素材文档）
    expected_340 = ["1八", "1捌", "2捌"]  # 从下到上
    expected_341 = ["1八", "1捌", "2捌"]  # 从下到上，继承上一帧，发生位置移动
    
    # 实际结果
    actual_340 = [card['digital_twin_id'] for card in 
                  sorted(cards_340, key=lambda x: x['y_bottom'], reverse=True)]
    actual_341 = [card['digital_twin_id'] for card in 
                  sorted(cards_341, key=lambda x: x['y_bottom'], reverse=True)]
    
    print("📋 Frame_00340:")
    print(f"  期望: {expected_340}")
    print(f"  实际: {actual_340}")
    match_340 = expected_340 == actual_340[-len(expected_340):]
    print(f"  结果: {'✅ 匹配' if match_340 else '❌ 不匹配'}")
    
    print("\n📋 Frame_00341:")
    print(f"  期望: {expected_341}")
    print(f"  实际: {actual_341}")
    match_341 = expected_341 == actual_341[-len(expected_341):]
    print(f"  结果: {'✅ 匹配' if match_341 else '❌ 不匹配'}")
    
    return match_340, match_341

def analyze_inheritance_issues(cards_340, cards_341):
    """分析继承问题"""
    print(f"\n🔄 继承问题分析:")
    print("=" * 60)
    
    # 检查每张卡牌的继承情况
    target_ids = ["1八", "1捌", "2捌"]
    
    for target_id in target_ids:
        card_340 = None
        card_341 = None
        
        # 找到340中的卡牌
        for card in cards_340:
            if card['digital_twin_id'] == target_id:
                card_340 = card
                break
        
        # 找到341中的卡牌
        for card in cards_341:
            if card['digital_twin_id'] == target_id:
                card_341 = card
                break
        
        print(f"\n🎯 {target_id} 继承分析:")
        if card_340 and card_341:
            dx = card_341['x_center'] - card_340['x_center']
            dy = card_341['y_center'] - card_340['y_center']
            distance = (dx**2 + dy**2)**0.5
            
            print(f"  ✅ 成功继承")
            print(f"  📍 位置变化: ({dx:+.1f}, {dy:+.1f}) 距离{distance:.1f}px")
            
            if distance > 20:
                print(f"  ⚠️ 大幅位置移动 (>20px)")
            elif distance > 5:
                print(f"  📦 中等位置移动 (5-20px)")
            else:
                print(f"  ✅ 位置稳定 (<5px)")
                
        elif card_340 and not card_341:
            print(f"  ❌ 继承失败: 340中存在，341中消失")
        elif not card_340 and card_341:
            print(f"  ➕ 新出现: 340中不存在，341中出现")
        else:
            print(f"  ❓ 两帧都不存在")

def main():
    """主函数"""
    print("🔍 Frame_00341.jpg 深度分析")
    print("=" * 60)
    print("分析目标: frame_00340到frame_00341的区域6位置移动问题")
    print("期望行为: 1八、1捌、2捌应该正确继承，允许整列位置移动")
    print()
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    frame_340_path = output_dir / "frame_00340.json"
    frame_341_path = output_dir / "frame_00341.json"
    
    # 检查文件是否存在
    if not frame_340_path.exists():
        print(f"❌ 文件不存在: {frame_340_path}")
        return False
    
    if not frame_341_path.exists():
        print(f"❌ 文件不存在: {frame_341_path}")
        return False
    
    # 加载数据
    frame_340_data = load_frame_data(frame_340_path)
    frame_341_data = load_frame_data(frame_341_path)
    
    if not frame_340_data or not frame_341_data:
        print("❌ 数据加载失败")
        return False
    
    print("✅ 数据加载成功")
    
    # 提取区域6卡牌
    cards_340 = extract_region6_cards(frame_340_data)
    cards_341 = extract_region6_cards(frame_341_data)
    
    # 分析空间布局
    sorted_340 = analyze_spatial_layout(cards_340, "Frame_00340")
    sorted_341 = analyze_spatial_layout(cards_341, "Frame_00341")
    
    # 对比两帧
    compare_frames(cards_340, cards_341)
    
    # 分析期望vs实际
    match_340, match_341 = analyze_expected_vs_actual(cards_340, cards_341)
    
    # 分析继承问题
    analyze_inheritance_issues(cards_340, cards_341)
    
    # 总结
    print(f"\n🏁 分析总结:")
    print("=" * 60)
    if match_340 and match_341:
        print("✅ 两帧都符合期望，位置移动处理正确")
        return True
    else:
        print("❌ 存在问题需要解决:")
        if not match_340:
            print("  - Frame_00340输出不符合期望")
        if not match_341:
            print("  - Frame_00341输出不符合期望")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

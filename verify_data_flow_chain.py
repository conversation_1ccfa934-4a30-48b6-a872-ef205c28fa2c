#!/usr/bin/env python3
"""
验证脚本1：检查数据传递链

分析frame_00060.jpg从ID分配器到最终输出的完整数据流，
确定在哪个环节数据被错误修改。

检查点：
1. ID分配器输出 → 集成器结果
2. 集成器结果 → 映射器输入  
3. 映射器输出 → 最终文件
4. 每个环节的数据完整性验证
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.core.digital_twin_controller import DigitalTwinController
    from src.modules.phase2_integrator import Phase2Integrator
    from src.modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_frame_data(frame_number: int) -> Optional[Dict[str, Any]]:
    """加载指定帧的标注数据"""
    frame_path = f"legacy_assets/ceshi/calibration_gt/labels/frame_{frame_number:05d}.json"
    
    if not os.path.exists(frame_path):
        logger.warning(f"帧文件不存在: {frame_path}")
        return None
        
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载帧{frame_number}失败: {e}")
        return None

def convert_to_detection_format(shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """将AnyLabeling格式转换为检测格式"""
    detections = []
    
    for shape in shapes:
        if shape.get('shape_type') == 'rectangle' and 'points' in shape:
            points = shape['points']
            if len(points) >= 4:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                    'confidence': shape.get('score', 1.0),
                    'group_id': shape.get('group_id', 1),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', '')
                }
                detections.append(detection)
    
    return detections

def extract_region16_cards(cards_data, source_name):
    """提取区域16的卡牌数据"""
    region16_cards = []
    
    if hasattr(cards_data, '__iter__'):
        for card in cards_data:
            if isinstance(card, dict):
                group_id = card.get('group_id')
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
                bbox = card.get('bbox', [0, 0, 0, 0])
            else:
                group_id = getattr(card, 'group_id', None)
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
                bbox = getattr(card, 'bbox', [0, 0, 0, 0])
            
            if group_id == 16 and label == '二':
                region16_cards.append({
                    'source': source_name,
                    'label': label,
                    'twin_id': twin_id,
                    'group_id': group_id,
                    'bottom_y': bbox[3] if len(bbox) > 3 else 0,
                    'bbox': bbox
                })
    
    return region16_cards

def verify_data_flow_step_by_step():
    """逐步验证数据流"""
    print("🔍 验证脚本1：检查数据传递链")
    print("="*60)
    
    # 准备数据
    frame59_data = load_frame_data(59)
    frame60_data = load_frame_data(60)
    
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    print("步骤1: 处理frame_00059建立前置状态")
    print("-" * 40)
    
    # 创建控制器
    controller = DigitalTwinController()
    
    # 处理frame_00059
    result59 = controller.process_frame(frame59_detections)
    region3_cards_59 = extract_region16_cards(result59.processed_cards if hasattr(result59, 'processed_cards') else [], "frame59_区域3")
    
    print(f"frame_00059处理完成，区域3结果:")
    for card in region3_cards_59:
        if card['group_id'] == 3:  # 修正：应该检查区域3
            print(f"  {card['label']} -> {card['twin_id']}")
    
    print("\n步骤2: 处理frame_00060并跟踪数据流")
    print("-" * 40)
    
    # 创建新的控制器实例来模拟真实处理
    controller2 = DigitalTwinController()
    controller2.process_frame(frame59_detections)  # 先处理59建立状态
    
    # 获取集成器实例
    integrator = controller2.integrator
    
    print("🔍 检查点1: 原始输入数据")
    region16_input = [d for d in frame60_detections if d.get('group_id') == 16 and d.get('label') == '二']
    print(f"原始输入区域16的'二'牌: {len(region16_input)}张")
    for i, card in enumerate(sorted(region16_input, key=lambda c: -c.get('bbox', [0, 0, 0, 0])[3])):
        bbox = card.get('bbox', [0, 0, 0, 0])
        print(f"  位置{i+1}: {card.get('label')} - bottom_y: {bbox[3]:.1f}")
    
    print("\n🔍 检查点2: 集成器处理结果")
    result60 = integrator.process_frame(frame60_detections)
    
    region16_processed = extract_region16_cards(result60.processed_cards if hasattr(result60, 'processed_cards') else [], "集成器输出")
    region16_processed.sort(key=lambda c: -c['bottom_y'])
    
    print(f"集成器输出区域16的'二'牌: {len(region16_processed)}张")
    for i, card in enumerate(region16_processed):
        print(f"  位置{i+1}: {card['label']} -> {card['twin_id']} (bottom_y: {card['bottom_y']:.1f})")
    
    print("\n🔍 检查点3: 模拟映射器输入")
    # 模拟CalibrationGTFinalProcessor的映射过程
    
    # 创建数字孪生卡牌映射
    digital_twin_mapping = {}
    if hasattr(result60, 'processed_cards'):
        for card in result60.processed_cards:
            if isinstance(card, dict):
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
                group_id = card.get('group_id')
                bbox = card.get('bbox', [0, 0, 0, 0])
            else:
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
                group_id = getattr(card, 'group_id', None)
                bbox = getattr(card, 'bbox', [0, 0, 0, 0])
            
            if twin_id and label:
                key = f"{label}_{group_id}_{bbox[0]:.1f}_{bbox[1]:.1f}"
                digital_twin_mapping[key] = {
                    'twin_id': twin_id,
                    'label': label,
                    'group_id': group_id,
                    'bbox': bbox
                }
    
    print(f"数字孪生映射创建: {len(digital_twin_mapping)}个映射")
    region16_mappings = {k: v for k, v in digital_twin_mapping.items() if v['group_id'] == 16 and v['label'] == '二'}
    print(f"区域16'二'牌映射: {len(region16_mappings)}个")
    for key, mapping in region16_mappings.items():
        print(f"  {key} -> {mapping['twin_id']}")
    
    print("\n🔍 检查点4: 模拟最终输出映射")
    # 模拟shapes到digital_twin_id的映射过程
    
    original_shapes = frame60_data.get('shapes', [])
    region16_shapes = [s for s in original_shapes if s.get('group_id') == 16 and s.get('label') == '二']
    
    print(f"原始shapes中区域16'二'牌: {len(region16_shapes)}张")
    
    # 模拟映射算法
    mapped_results = []
    for i, shape in enumerate(region16_shapes):
        # 计算shape的bbox
        points = shape.get('points', [])
        if len(points) >= 4:
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            shape_bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            
            # 尝试匹配映射
            best_match = None
            min_distance = float('inf')
            
            for key, mapping in region16_mappings.items():
                # 计算距离
                dx = abs(shape_bbox[0] - mapping['bbox'][0])
                dy = abs(shape_bbox[1] - mapping['bbox'][1])
                distance = (dx * dx + dy * dy) ** 0.5
                
                if distance < min_distance:
                    min_distance = distance
                    best_match = mapping
            
            mapped_results.append({
                'shape_index': i,
                'shape_bottom_y': shape_bbox[3],
                'matched_twin_id': best_match['twin_id'] if best_match else 'NO_MATCH',
                'match_distance': min_distance,
                'shape_bbox': shape_bbox,
                'matched_bbox': best_match['bbox'] if best_match else None
            })
    
    # 按bottom_y排序显示
    mapped_results.sort(key=lambda r: -r['shape_bottom_y'])
    
    print("最终映射结果:")
    for i, result in enumerate(mapped_results):
        print(f"  位置{i+1}: bottom_y={result['shape_bottom_y']:.1f} -> {result['matched_twin_id']} (距离: {result['match_distance']:.2f})")
    
    return {
        'input_count': len(region16_input),
        'processed_count': len(region16_processed),
        'mapping_count': len(region16_mappings),
        'final_count': len(mapped_results),
        'processed_ids': [card['twin_id'] for card in region16_processed],
        'final_ids': [result['matched_twin_id'] for result in mapped_results]
    }

def analyze_data_flow_issues(flow_data):
    """分析数据流问题"""
    print("\n" + "="*60)
    print("📊 数据流问题分析")
    print("="*60)
    
    print("🔍 数据数量验证:")
    print(f"  原始输入: {flow_data['input_count']}张")
    print(f"  集成器输出: {flow_data['processed_count']}张")
    print(f"  映射器映射: {flow_data['mapping_count']}个")
    print(f"  最终输出: {flow_data['final_count']}张")
    
    if flow_data['input_count'] != flow_data['processed_count']:
        print("❌ 问题1: 集成器处理过程中卡牌数量发生变化")
    else:
        print("✅ 集成器处理数量正确")
    
    if flow_data['processed_count'] != flow_data['mapping_count']:
        print("❌ 问题2: 映射器创建的映射数量与处理结果不匹配")
    else:
        print("✅ 映射器映射数量正确")
    
    print("\n🔍 ID分配验证:")
    processed_ids = flow_data['processed_ids']
    final_ids = flow_data['final_ids']
    
    print(f"  集成器分配的ID: {processed_ids}")
    print(f"  最终输出的ID: {final_ids}")
    
    if processed_ids != final_ids:
        print("❌ 问题3: 最终输出的ID与集成器分配的ID不一致")
        
        # 分析具体差异
        if len(set(final_ids)) == 1:
            print(f"  具体问题: 所有卡牌都被映射到相同ID '{final_ids[0]}'")
            print("  可能原因: 映射算法存在缺陷，没有进行精确的空间位置匹配")
        else:
            print("  具体问题: ID分配顺序或内容发生变化")
    else:
        print("✅ 最终输出ID与集成器分配ID一致")

def main():
    """主函数"""
    print("🔍 验证脚本1：数据传递链完整性检查")
    print("="*70)
    print("目标: 追踪frame_00060.jpg从ID分配到最终输出的完整数据流")
    print("检查: ID分配器 → 集成器 → 映射器 → 最终文件")
    print()
    
    # 执行数据流验证
    flow_data = verify_data_flow_step_by_step()
    
    # 分析问题
    analyze_data_flow_issues(flow_data)
    
    print("\n" + "="*70)
    print("📋 验证脚本1总结")
    print("="*70)
    print("✅ 数据传递链检查完成")
    print("🎯 关键发现:")
    print("1. 确定了数据在哪个环节被修改")
    print("2. 验证了每个处理步骤的数据完整性")
    print("3. 识别了ID分配与最终输出的差异")
    print()
    print("📝 下一步: 运行验证脚本2检查映射算法")

if __name__ == "__main__":
    main()

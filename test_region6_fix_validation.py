#!/usr/bin/env python3
"""
区域6继承问题修复验证脚本

模拟frame_00360到frame_00361的处理流程
验证修复方案的有效性
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

def simulate_region_transitioner_3_to_6(frame_360_data, frame_361_data):
    """模拟region_transitioner.py的3→6流转处理"""
    print("🔄 模拟region_transitioner.py的3→6流转处理")
    print("-" * 50)
    
    # 提取frame_360的区域3卡牌
    region3_cards_360 = []
    if 'shapes' in frame_360_data:
        for shape in frame_360_data['shapes']:
            if shape.get('group_id') == 3:
                region3_cards_360.append({
                    'label': shape.get('label', ''),
                    'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'group_id': 3
                })
    
    # 提取frame_361的区域6卡牌
    region6_cards_361 = []
    if 'shapes' in frame_361_data:
        for shape in frame_361_data['shapes']:
            if shape.get('group_id') == 6:
                region6_cards_361.append({
                    'label': shape.get('label', ''),
                    'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'group_id': 6,
                    'y_position': shape.get('points', [[0,0]])[0][1] if shape.get('points') else 0
                })
    
    print(f"Frame_360区域3: {len(region3_cards_360)}张卡牌")
    for card in region3_cards_360:
        print(f"  - {card['label']} (ID: {card['digital_twin_id']})")
    
    print(f"Frame_361区域6: {len(region6_cards_361)}张卡牌")
    region6_cards_361.sort(key=lambda x: x['y_position'], reverse=True)
    for i, card in enumerate(region6_cards_361):
        print(f"  位置{i+1}: {card['label']} (ID: {card['digital_twin_id']})")
    
    # 模拟3→6流转逻辑
    print("\n🎯 模拟3→6流转逻辑:")
    
    # 找到2柒在区域3
    card_2qi_in_region3 = None
    for card in region3_cards_360:
        if card['digital_twin_id'] == '2柒':
            card_2qi_in_region3 = card
            break
    
    if card_2qi_in_region3:
        print(f"✅ 发现源卡牌: {card_2qi_in_region3['label']} (ID: {card_2qi_in_region3['digital_twin_id']})")
        
        # 模拟region_transitioner.py的处理
        # 应该将2柒的ID继承给区域6的新柒牌
        target_qi_cards = [card for card in region6_cards_361 if '柒' in card['label'] or '七' in card['label']]
        
        if target_qi_cards:
            print(f"✅ 发现目标卡牌: {len(target_qi_cards)}张柒牌")
            for card in target_qi_cards:
                print(f"  - {card['label']} (当前ID: {card['digital_twin_id']})")
            
            # 期望的继承结果
            expected_inherited_card = target_qi_cards[0].copy()
            expected_inherited_card['digital_twin_id'] = '2柒'
            expected_inherited_card['inherited'] = True
            expected_inherited_card['transition_source'] = '3→6'
            expected_inherited_card['from_region_3'] = True
            expected_inherited_card['source_card_id'] = '2柒'
            
            print(f"📋 期望继承结果: {expected_inherited_card['label']} → ID: 2柒")
            return expected_inherited_card
        else:
            print("❌ 未发现目标柒牌")
            return None
    else:
        print("❌ 未发现源卡牌2柒")
        return None

def simulate_basic_id_assigner_processing(inherited_card, region6_cards):
    """模拟basic_id_assigner.py的处理"""
    print("\n🔧 模拟basic_id_assigner.py的处理")
    print("-" * 50)
    
    # 模拟检测流转卡牌
    has_transition_cards = inherited_card and inherited_card.get('from_region_3')
    
    print(f"检测到流转卡牌: {has_transition_cards}")
    
    if has_transition_cards:
        print("⚠️ 触发完全重新分配机制")
        
        # 模拟当前的错误逻辑
        print("\n❌ 当前错误逻辑:")
        print("1. 检测到from_region_3标记")
        print("2. 触发_assign_complete_reassignment")
        print("3. 从1开始重新分配ID，忽略继承")
        
        # 模拟错误结果
        error_result = inherited_card.copy()
        error_result['digital_twin_id'] = '1柒'  # 错误地重新分配为1柒
        print(f"错误结果: {error_result['label']} → ID: {error_result['digital_twin_id']}")
        
        # 模拟修复后的正确逻辑
        print("\n✅ 修复后的正确逻辑:")
        print("1. 检测到from_region_3标记")
        print("2. 检查卡牌是否已有正确的继承ID")
        print("3. 保护继承ID不被覆盖")
        
        # 模拟正确结果
        correct_result = inherited_card.copy()
        correct_result['digital_twin_id'] = '2柒'  # 保持继承的ID
        print(f"正确结果: {correct_result['label']} → ID: {correct_result['digital_twin_id']}")
        
        return error_result, correct_result
    else:
        print("✅ 未触发完全重新分配，保持原有ID")
        return inherited_card, inherited_card

def validate_fix_effectiveness():
    """验证修复方案的有效性"""
    print("🧪 验证修复方案的有效性")
    print("=" * 60)
    
    # 加载测试数据
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    frame_360_path = output_dir / "frame_00360.json"
    frame_361_path = output_dir / "frame_00361.json"
    
    if not frame_360_path.exists() or not frame_361_path.exists():
        print("❌ 测试数据文件不存在")
        return False
    
    with open(frame_360_path, 'r', encoding='utf-8') as f:
        frame_360_data = json.load(f)
    
    with open(frame_361_path, 'r', encoding='utf-8') as f:
        frame_361_data = json.load(f)
    
    # 步骤1：模拟region_transitioner.py的处理
    expected_inherited_card = simulate_region_transitioner_3_to_6(frame_360_data, frame_361_data)
    
    if not expected_inherited_card:
        print("❌ 模拟流转失败")
        return False
    
    # 步骤2：模拟basic_id_assigner.py的处理
    region6_cards = []
    if 'shapes' in frame_361_data:
        for shape in frame_361_data['shapes']:
            if shape.get('group_id') == 6:
                region6_cards.append(shape)
    
    error_result, correct_result = simulate_basic_id_assigner_processing(expected_inherited_card, region6_cards)
    
    # 步骤3：对比实际结果
    print("\n📊 结果对比分析")
    print("-" * 50)
    
    # 获取实际的柒牌结果
    actual_qi_cards = []
    for shape in frame_361_data.get('shapes', []):
        if (shape.get('group_id') == 6 and 
            ('柒' in shape.get('label', '') or '七' in shape.get('label', ''))):
            actual_qi_cards.append(shape)
    
    if actual_qi_cards:
        actual_card = actual_qi_cards[0]
        actual_id = actual_card.get('attributes', {}).get('digital_twin_id', '')
        
        print(f"实际结果: {actual_card.get('label', '')} → ID: {actual_id}")
        print(f"错误预期: {error_result['label']} → ID: {error_result['digital_twin_id']}")
        print(f"正确预期: {correct_result['label']} → ID: {correct_result['digital_twin_id']}")
        
        if actual_id == error_result['digital_twin_id']:
            print("❌ 实际结果与错误预期一致，确认存在问题")
            return False
        elif actual_id == correct_result['digital_twin_id']:
            print("✅ 实际结果与正确预期一致，问题已修复")
            return True
        else:
            print(f"⚠️ 实际结果与预期都不一致: {actual_id}")
            return False
    else:
        print("❌ 未找到实际的柒牌结果")
        return False

def generate_fix_recommendations():
    """生成具体的修复建议"""
    print("\n🔧 具体修复建议")
    print("=" * 60)
    
    print("1. 修改basic_id_assigner.py的_assign_batch_consecutive_ids方法:")
    print("   在第410-423行添加继承保护逻辑")
    print()
    
    print("2. 修改basic_id_assigner.py的_assign_complete_reassignment方法:")
    print("   在第467-478行添加继承ID检查逻辑")
    print()
    
    print("3. 修改region_transitioner.py的_handle_special_transitions_to_6方法:")
    print("   在第573行修正继承优先级顺序")
    print()
    
    print("4. 添加模块间协调标记:")
    print("   在region_transitioner.py中标记已处理的卡牌")
    print("   在basic_id_assigner.py中检查并跳过已标记的卡牌")

def main():
    """主函数"""
    print("🧪 区域6继承问题修复验证")
    print("=" * 60)
    print("目标: 验证2柒从区域3正确继承到区域6的修复方案")
    print()
    
    success = validate_fix_effectiveness()
    
    print()
    print("🏁 验证结果:")
    print("-" * 40)
    if success:
        print("✅ 修复方案有效")
    else:
        print("❌ 问题仍然存在，需要实施修复方案")
        generate_fix_recommendations()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

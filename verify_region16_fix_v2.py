#!/usr/bin/env python3
"""
验证区域16修复v2.0（暗牌排除机制）的效果
同时测试frame_00018（包含暗牌）和frame_00230（纯明牌）
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(file_path: str) -> Dict[str, Any]:
    """加载指定帧的数据"""
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def analyze_region16_comprehensive(frame_data: Dict[str, Any], frame_name: str) -> Dict[str, Any]:
    """全面分析区域16的状态"""
    print(f"\n📊 {frame_name} 区域16全面分析")
    print("-" * 50)
    
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    if not region16_cards:
        print("  ❌ 该区域无卡牌")
        return {}
    
    print(f"📋 基本信息:")
    print(f"  总卡牌数: {len(region16_cards)}")
    
    # 分离暗牌和明牌
    dark_cards = []
    normal_cards = []
    
    for card in region16_cards:
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        
        if '暗' in label or '暗' in twin_id:
            dark_cards.append(card)
        else:
            normal_cards.append(card)
    
    print(f"  暗牌数量: {len(dark_cards)}")
    print(f"  明牌数量: {len(normal_cards)}")
    
    # 分析暗牌
    print(f"\n🔒 暗牌分析:")
    if dark_cards:
        for i, card in enumerate(dark_cards):
            label = card.get('label', '')
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            processed = card.get('region16_normal_card_processed', False)
            
            status = "⚠️ 被处理" if processed else "✅ 保持原状"
            print(f"    暗牌{i+1}: {label} -> {twin_id} ({status})")
    else:
        print("    ✅ 无暗牌")
    
    # 分析明牌的列一致性
    print(f"\n🔍 明牌列一致性分析:")
    if normal_cards:
        consistency_result = analyze_column_consistency(normal_cards, "明牌")
    else:
        print("    ✅ 无明牌")
        consistency_result = {'all_consistent': True, 'consistency_rate': 1.0}
    
    # 检查处理标记
    print(f"\n🔧 处理标记检查:")
    processed_count = 0
    for card in normal_cards:
        if card.get('region16_normal_card_processed', False):
            processed_count += 1
    
    print(f"  被处理的明牌: {processed_count}/{len(normal_cards)}")
    
    if processed_count > 0:
        print(f"  ✅ 修复机制已生效")
    else:
        print(f"  ℹ️ 无需修复或修复未触发")
    
    return {
        'total_cards': len(region16_cards),
        'dark_cards': len(dark_cards),
        'normal_cards': len(normal_cards),
        'dark_cards_protected': all(not card.get('region16_normal_card_processed', False) for card in dark_cards),
        'normal_cards_consistency': consistency_result,
        'processed_normal_cards': processed_count
    }

def analyze_column_consistency(cards: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
    """分析列一致性"""
    if not cards:
        return {'all_consistent': True, 'consistency_rate': 1.0}
    
    # 提取位置信息
    for card in cards:
        points = card.get('points', [])
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            y_coords = [point[1] for point in points]
            card['_x_center'] = sum(x_coords) / len(x_coords)
            card['_y_bottom'] = max(y_coords)
    
    # 按X坐标分组
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in cards:
        x_center = card.get('_x_center', 0)
        
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"    检测到 {len(columns)} 列:")
    
    consistent_columns = 0
    total_columns = len(columns)
    inconsistent_details = []
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        # 列内按Y坐标排序（从下到上）
        column_cards.sort(key=lambda x: -x.get('_y_bottom', 0))
        
        # 分析列内的类别一致性
        base_labels = []
        for card in column_cards:
            label = card.get('label', '')
            # 提取基础标签（去掉数字前缀）
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        is_consistent = len(unique_base_labels) == 1
        
        if is_consistent:
            consistent_columns += 1
            status = "✅ 一致"
        else:
            status = "❌ 混淆"
            inconsistent_details.append({
                'column': i + 1,
                'mixed_labels': list(unique_base_labels)
            })
        
        print(f"      列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌 - {status}")
        print(f"        基础标签: {list(unique_base_labels)}")
        
        for j, card in enumerate(column_cards):
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            processed = card.get('region16_normal_card_processed', False)
            process_info = " [已处理]" if processed else ""
            print(f"          位置{j+1}: {card.get('label')} -> {twin_id}{process_info}")
    
    consistency_rate = consistent_columns / total_columns if total_columns > 0 else 1.0
    
    print(f"    📈 一致性统计:")
    print(f"      总列数: {total_columns}")
    print(f"      一致列数: {consistent_columns}")
    print(f"      一致性率: {consistency_rate:.1%}")
    
    return {
        'total_columns': total_columns,
        'consistent_columns': consistent_columns,
        'consistency_rate': consistency_rate,
        'all_consistent': consistency_rate == 1.0,
        'inconsistent_details': inconsistent_details
    }

def compare_fix_effectiveness():
    """对比修复效果"""
    print(f"\n📈 修复效果对比分析")
    print("=" * 60)
    
    # 分析frame_00018（包含暗牌的场景）
    frame18_data = load_frame_data("output/calibration_gt_final_with_digital_twin/labels/frame_00018.json")
    if frame18_data:
        frame18_analysis = analyze_region16_comprehensive(frame18_data, "Frame 18（暗牌场景）")
    else:
        frame18_analysis = None
    
    # 分析frame_00230（纯明牌场景）
    frame230_data = load_frame_data("output/calibration_gt_final_with_digital_twin/labels/frame_00230.json")
    if frame230_data:
        frame230_analysis = analyze_region16_comprehensive(frame230_data, "Frame 230（明牌场景）")
    else:
        frame230_analysis = None
    
    # 总结对比结果
    print(f"\n🎉 修复效果总结")
    print("=" * 50)
    
    if frame18_analysis:
        print(f"📊 Frame 18（暗牌场景）:")
        print(f"  暗牌保护: {'✅ 成功' if frame18_analysis['dark_cards_protected'] else '❌ 失败'}")
        print(f"  明牌一致性: {frame18_analysis['normal_cards_consistency']['consistency_rate']:.1%}")
        print(f"  处理的明牌: {frame18_analysis['processed_normal_cards']}/{frame18_analysis['normal_cards']}")
    
    if frame230_analysis:
        print(f"\n📊 Frame 230（明牌场景）:")
        print(f"  列一致性: {frame230_analysis['normal_cards_consistency']['consistency_rate']:.1%}")
        print(f"  处理的明牌: {frame230_analysis['processed_normal_cards']}/{frame230_analysis['normal_cards']}")
    
    # 评估修复成功性
    print(f"\n🏆 修复成功性评估:")
    
    success_criteria = []
    
    if frame18_analysis:
        # 暗牌保护成功
        if frame18_analysis['dark_cards_protected']:
            success_criteria.append("✅ 暗牌保护成功")
        else:
            success_criteria.append("❌ 暗牌保护失败")
    
    if frame230_analysis:
        # 明牌列混淆修复
        if frame230_analysis['normal_cards_consistency']['consistency_rate'] >= 0.75:
            success_criteria.append("✅ 明牌列混淆修复成功")
        else:
            success_criteria.append("❌ 明牌列混淆修复不充分")
    
    for criterion in success_criteria:
        print(f"  {criterion}")
    
    # 最终评估
    success_count = sum(1 for c in success_criteria if c.startswith("✅"))
    total_count = len(success_criteria)
    
    if success_count == total_count:
        print(f"\n🎉 修复完全成功！所有目标都已达成")
        return True
    elif success_count > total_count // 2:
        print(f"\n✅ 修复基本成功，达成{success_count}/{total_count}个目标")
        return True
    else:
        print(f"\n⚠️ 修复效果有限，仅达成{success_count}/{total_count}个目标")
        return False

def main():
    """主验证函数"""
    print("🧪 区域16修复v2.0（暗牌排除机制）验证")
    print("=" * 60)
    
    # 执行全面的修复效果对比
    success = compare_fix_effectiveness()
    
    print(f"\n🎯 验证结论:")
    if success:
        print(f"  🎉 修复v2.0成功！暗牌排除机制有效")
        print(f"  ✅ 可以安全部署到生产环境")
    else:
        print(f"  ⚠️ 修复v2.0需要进一步调整")
        print(f"  🔧 建议继续优化修复逻辑")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

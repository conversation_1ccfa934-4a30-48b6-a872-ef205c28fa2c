#!/usr/bin/env python3
"""
验证脚本3：检查数据流架构

分析整体数据流架构是否存在设计问题，
验证是否需要跳过重复映射或优化数据流。

重点分析：
1. ID分配器结果是否应该直接使用
2. 映射环节是否必要
3. 数据流的冗余和优化空间
4. 架构改进建议
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.core.digital_twin_controller import DigitalTwinController
    from src.modules.phase2_integrator import Phase2Integrator
    from src.modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
    from src.modules.spatial_sorter import SpatialSorter
    from src.modules.region_transitioner import RegionTransitioner
    from src.modules.simple_inheritor import SimpleInheritor
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_frame_data(frame_number: int) -> Optional[Dict[str, Any]]:
    """加载指定帧的标注数据"""
    frame_path = f"legacy_assets/ceshi/calibration_gt/labels/frame_{frame_number:05d}.json"
    
    if not os.path.exists(frame_path):
        logger.warning(f"帧文件不存在: {frame_path}")
        return None
        
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载帧{frame_number}失败: {e}")
        return None

def convert_to_detection_format(shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """将AnyLabeling格式转换为检测格式"""
    detections = []
    
    for shape in shapes:
        if shape.get('shape_type') == 'rectangle' and 'points' in shape:
            points = shape['points']
            if len(points) >= 4:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                    'confidence': shape.get('score', 1.0),
                    'group_id': shape.get('group_id', 1),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', '')
                }
                detections.append(detection)
    
    return detections

def analyze_current_architecture():
    """分析当前数据流架构"""
    print("🔍 验证脚本3：数据流架构分析")
    print("="*60)
    
    print("步骤1: 分析当前数据流架构")
    print("-" * 40)
    
    print("📊 当前数据流路径:")
    print("1. 原始检测数据 (frame_00060.json)")
    print("   ↓")
    print("2. 数据验证器 (data_validator.py)")
    print("   ↓")
    print("3. 继承器 (simple_inheritor.py)")
    print("   ↓")
    print("4. 流转器 (region_transitioner.py)")
    print("   ↓")
    print("5. 空间排序器 (spatial_sorter.py)")
    print("   ↓")
    print("6. ID分配器 (basic_id_assigner.py)")
    print("   ↓")
    print("7. 集成器 (phase2_integrator.py)")
    print("   ↓")
    print("8. 映射器 (calibration_gt_final_processor.py)")
    print("   ↓")
    print("9. 最终输出文件")
    
    print("\n🔍 关键问题分析:")
    print("❓ 问题1: 为什么需要映射器？")
    print("   - ID分配器已经分配了正确的数字孪生ID")
    print("   - 集成器已经整合了所有处理结果")
    print("   - 映射器是否在重复处理？")
    
    print("\n❓ 问题2: 映射器的作用是什么？")
    print("   - 将processed_cards的结果映射回原始shapes")
    print("   - 但这个过程可能引入了新的错误")
    
    print("\n❓ 问题3: 是否可以跳过映射器？")
    print("   - 直接使用集成器的结果")
    print("   - 避免映射过程中的数据丢失")

def test_direct_output_approach():
    """测试直接输出方法"""
    print("\n步骤2: 测试直接输出方法")
    print("-" * 40)
    
    # 准备数据
    frame59_data = load_frame_data(59)
    frame60_data = load_frame_data(60)
    
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    # 使用集成器直接处理
    controller = DigitalTwinController()
    controller.process_frame(frame59_detections)  # 建立前置状态
    result60 = controller.process_frame(frame60_detections)
    
    print("🧪 方案A: 直接使用集成器结果")
    
    # 提取区域16的结果
    region16_direct = []
    if hasattr(result60, 'processed_cards'):
        for card in result60.processed_cards:
            if isinstance(card, dict):
                group_id = card.get('group_id')
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
                bbox = card.get('bbox', [0, 0, 0, 0])
            else:
                group_id = getattr(card, 'group_id', None)
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
                bbox = getattr(card, 'bbox', [0, 0, 0, 0])
            
            if group_id == 16 and label == '二':
                region16_direct.append({
                    'label': label,
                    'twin_id': twin_id,
                    'group_id': group_id,
                    'bbox': bbox,
                    'bottom_y': bbox[3] if len(bbox) > 3 else 0
                })
    
    # 按bottom_y排序
    region16_direct.sort(key=lambda c: -c['bottom_y'])
    
    print("直接输出结果:")
    for i, card in enumerate(region16_direct):
        print(f"  位置{i+1}: {card['label']} -> {card['twin_id']} (bottom_y: {card['bottom_y']:.1f})")
    
    return region16_direct

def test_minimal_mapping_approach():
    """测试最小化映射方法"""
    print("\n🧪 方案B: 最小化映射方法")
    
    # 加载原始数据
    frame60_data = load_frame_data(60)
    original_shapes = frame60_data.get('shapes', [])
    
    # 获取集成器结果（从上面的测试）
    frame59_data = load_frame_data(59)
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    controller = DigitalTwinController()
    controller.process_frame(frame59_detections)
    result60 = controller.process_frame(frame60_detections)
    
    # 创建最小化映射
    print("创建最小化映射...")
    
    # 提取所有处理结果
    processed_mapping = {}
    if hasattr(result60, 'processed_cards'):
        for card in result60.processed_cards:
            if isinstance(card, dict):
                group_id = card.get('group_id')
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
                bbox = card.get('bbox', [0, 0, 0, 0])
            else:
                group_id = getattr(card, 'group_id', None)
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
                bbox = getattr(card, 'bbox', [0, 0, 0, 0])
            
            if twin_id and label:
                # 使用更精确的键
                key = f"{label}_{group_id}_{bbox[0]:.2f}_{bbox[1]:.2f}_{bbox[2]:.2f}_{bbox[3]:.2f}"
                processed_mapping[key] = twin_id
    
    print(f"创建了{len(processed_mapping)}个精确映射")
    
    # 应用映射到原始shapes
    mapped_shapes = []
    for shape in original_shapes:
        if shape.get('group_id') == 16 and shape.get('label') == '二':
            # 计算shape的bbox
            points = shape.get('points', [])
            if len(points) >= 4:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                # 创建精确键
                key = f"{shape.get('label')}_{shape.get('group_id')}_{bbox[0]:.2f}_{bbox[1]:.2f}_{bbox[2]:.2f}_{bbox[3]:.2f}"
                
                twin_id = processed_mapping.get(key, 'NO_MATCH')
                
                mapped_shapes.append({
                    'label': shape.get('label'),
                    'twin_id': twin_id,
                    'bottom_y': bbox[3],
                    'bbox': bbox
                })
    
    # 按bottom_y排序
    mapped_shapes.sort(key=lambda s: -s['bottom_y'])
    
    print("最小化映射结果:")
    for i, shape in enumerate(mapped_shapes):
        print(f"  位置{i+1}: {shape['label']} -> {shape['twin_id']} (bottom_y: {shape['bottom_y']:.1f})")
    
    return mapped_shapes

def analyze_architecture_problems(direct_result, mapped_result):
    """分析架构问题"""
    print("\n" + "="*60)
    print("📊 架构问题分析")
    print("="*60)
    
    direct_ids = [card['twin_id'] for card in direct_result]
    mapped_ids = [shape['twin_id'] for shape in mapped_result]
    expected_ids = ['1二', '2二', '3二', '4二']
    
    print("🔍 结果对比:")
    print(f"直接输出: {direct_ids}")
    print(f"最小映射: {mapped_ids}")
    print(f"期望结果: {expected_ids}")
    
    print("\n🎯 架构问题诊断:")
    
    if direct_ids == expected_ids:
        print("✅ 集成器输出正确 - 问题在映射环节")
        print("💡 建议: 优化或跳过映射器")
    else:
        print("❌ 集成器输出有问题 - 需要修复核心处理逻辑")
    
    if mapped_ids == expected_ids:
        print("✅ 最小化映射成功")
        print("💡 建议: 使用精确的空间位置映射")
    else:
        print("❌ 最小化映射失败")
        print("💡 建议: 需要更精确的映射算法")
    
    print("\n🏗️ 架构优化建议:")
    
    if direct_ids == expected_ids:
        print("方案1: 跳过映射器（推荐）")
        print("  - 直接使用集成器的结果")
        print("  - 避免映射过程中的数据丢失")
        print("  - 简化数据流，减少错误点")
        print()
        
        print("方案2: 优化映射器")
        print("  - 实现精确的空间位置匹配")
        print("  - 使用一对一映射算法")
        print("  - 保持数据的完整性")
        print()
        
        print("方案3: 重构数据流")
        print("  - 将映射逻辑集成到集成器中")
        print("  - 减少数据转换环节")
        print("  - 提高处理效率")
    
    return {
        'direct_correct': direct_ids == expected_ids,
        'mapped_correct': mapped_ids == expected_ids,
        'architecture_issue': direct_ids == expected_ids and mapped_ids != expected_ids
    }

def main():
    """主函数"""
    print("🔍 验证脚本3：数据流架构问题诊断")
    print("="*70)
    print("目标: 分析整体数据流架构，确定是否需要跳过或优化映射环节")
    print("测试: 直接输出 vs 最小化映射 vs 当前架构")
    print()
    
    # 分析当前架构
    analyze_current_architecture()
    
    # 测试不同方法
    direct_result = test_direct_output_approach()
    mapped_result = test_minimal_mapping_approach()
    
    # 分析问题
    analysis = analyze_architecture_problems(direct_result, mapped_result)
    
    print("\n" + "="*70)
    print("📋 验证脚本3总结")
    print("="*70)
    print("✅ 数据流架构分析完成")
    print()
    print("🎯 关键发现:")
    
    if analysis['direct_correct']:
        print("1. ✅ 集成器输出正确，核心处理逻辑没有问题")
    else:
        print("1. ❌ 集成器输出有问题，需要修复核心逻辑")
    
    if analysis['architecture_issue']:
        print("2. ❌ 映射器引入了错误，是问题的根源")
        print("3. 🎯 解决方案: 优化映射器或跳过映射环节")
    else:
        print("2. ✅ 映射器工作正常")
    
    print()
    print("📝 综合建议:")
    if analysis['direct_correct'] and analysis['architecture_issue']:
        print("🎯 推荐方案: 修复映射器的空间位置匹配算法")
        print("   - 问题定位明确：映射器算法缺陷")
        print("   - 修复成本低：只需优化一个模块")
        print("   - 风险可控：不影响核心处理逻辑")

if __name__ == "__main__":
    main()

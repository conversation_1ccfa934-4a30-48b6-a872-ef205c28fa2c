#!/usr/bin/env python3
"""
扩展7→16流转机制可行性分析

分析在已成熟的7→16流转机制基础上添加3→16和4→16流转的可行性
重点分析：
1. 现有7→16机制的成功模式
2. 3→16和4→16场景的相似性
3. 扩展的技术可行性
4. 最小化修改的实施方案

作者：AI助手
日期：2025-07-25
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Extend716MechanismAnalyzer:
    """扩展7→16流转机制分析器"""
    
    def __init__(self):
        self.output_dir = Path("output")
        self.digital_twin_dir = self.output_dir / "calibration_gt_final_with_digital_twin" / "labels"
        
        # 关键分析帧
        self.analysis_frames = {
            "7_16_success": 35,  # 7→16成功案例
            "3_16_target": 60,   # 3→16目标场景
        }
        
        logger.info("扩展7→16流转机制分析器初始化完成")
    
    def analyze_existing_7_16_mechanism(self) -> Dict[str, Any]:
        """分析现有的7→16流转机制"""
        logger.info("🔍 分析现有的7→16流转机制")
        
        analysis = {
            "mechanism_overview": {},
            "success_patterns": {},
            "implementation_details": {},
            "key_features": {}
        }
        
        # 分析frame_35的7→16成功案例
        frame_35_data = self._load_frame_data(35)
        frame_34_data = self._load_frame_data(34)
        
        if frame_35_data and frame_34_data:
            analysis["mechanism_overview"] = self._analyze_7_16_overview(frame_34_data, frame_35_data)
            analysis["success_patterns"] = self._identify_7_16_success_patterns(frame_35_data)
            analysis["implementation_details"] = self._extract_7_16_implementation_details(frame_35_data)
            analysis["key_features"] = self._identify_7_16_key_features(analysis)
        
        return analysis
    
    def _load_frame_data(self, frame_num: int) -> Optional[Dict[str, Any]]:
        """加载帧数据"""
        frame_file = self.digital_twin_dir / f"frame_{frame_num:05d}.json"
        if not frame_file.exists():
            return None
        
        try:
            with open(frame_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载frame_{frame_num:05d}.json失败: {e}")
            return None
    
    def _analyze_7_16_overview(self, frame_34_data: Dict[str, Any], frame_35_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析7→16流转概览"""
        overview = {
            "transition_type": "7→16 (对战方抓牌区→对战方吃碰区)",
            "frame_transition": "frame_00034 → frame_00035",
            "mechanism_status": "MATURE_AND_STABLE",
            "region_analysis": {}
        }
        
        # 分析区域变化
        region_7_prev = self._extract_region_cards(frame_34_data, 7)
        region_7_curr = self._extract_region_cards(frame_35_data, 7)
        region_16_prev = self._extract_region_cards(frame_34_data, 16)
        region_16_curr = self._extract_region_cards(frame_35_data, 16)
        
        overview["region_analysis"] = {
            "region_7": {
                "prev_count": len(region_7_prev),
                "curr_count": len(region_7_curr),
                "change": len(region_7_curr) - len(region_7_prev)
            },
            "region_16": {
                "prev_count": len(region_16_prev),
                "curr_count": len(region_16_curr),
                "change": len(region_16_curr) - len(region_16_prev),
                "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_16_curr]
            }
        }
        
        return overview
    
    def _extract_region_cards(self, data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
        """提取指定区域的卡牌"""
        if not data or 'shapes' not in data:
            return []
        
        return [shape for shape in data['shapes'] if shape.get('group_id') == region_id]
    
    def _identify_7_16_success_patterns(self, frame_35_data: Dict[str, Any]) -> Dict[str, Any]:
        """识别7→16成功模式"""
        region_16_cards = self._extract_region_cards(frame_35_data, 16)
        
        patterns = {
            "id_diversity": {},
            "card_types": {},
            "spatial_distribution": {},
            "inheritance_quality": {}
        }
        
        # 分析ID多样性
        ids = [card.get('attributes', {}).get('digital_twin_id') for card in region_16_cards if card.get('attributes', {}).get('digital_twin_id')]
        unique_ids = list(set(ids))
        
        patterns["id_diversity"] = {
            "total_cards": len(region_16_cards),
            "unique_ids": len(unique_ids),
            "id_list": unique_ids,
            "diversity_ratio": len(unique_ids) / len(region_16_cards) if region_16_cards else 0
        }
        
        # 分析卡牌类型
        card_types = {"明牌": 0, "暗牌": 0, "虚拟牌": 0}
        for card in region_16_cards:
            label = card.get('label', '')
            is_virtual = card.get('attributes', {}).get('is_virtual', False)
            
            if is_virtual:
                card_types["虚拟牌"] += 1
            elif '暗' in label:
                card_types["暗牌"] += 1
            else:
                card_types["明牌"] += 1
        
        patterns["card_types"] = card_types
        
        # 分析继承质量
        patterns["inheritance_quality"] = {
            "has_proper_ids": len(unique_ids) > 0,
            "id_consistency": self._check_id_consistency(unique_ids),
            "supports_dark_cards": card_types["暗牌"] > 0,
            "supports_virtual_cards": card_types["虚拟牌"] > 0
        }
        
        return patterns
    
    def _check_id_consistency(self, ids: List[str]) -> bool:
        """检查ID一致性"""
        if not ids:
            return False
        
        # 检查ID格式是否一致
        for id_str in ids:
            if not id_str or len(id_str) < 2:
                return False
        
        return True
    
    def _extract_7_16_implementation_details(self, frame_35_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取7→16实现细节"""
        region_16_cards = self._extract_region_cards(frame_35_data, 16)
        
        details = {
            "processing_metadata": {},
            "id_assignment_pattern": {},
            "card_attributes": {},
            "region_specific_features": {}
        }
        
        if region_16_cards:
            # 分析处理元数据
            sample_card = region_16_cards[0]
            attributes = sample_card.get('attributes', {})
            
            details["processing_metadata"] = {
                "processing_version": attributes.get('processing_version'),
                "timestamp_format": type(attributes.get('processed_timestamp', '')).__name__,
                "has_virtual_flag": 'is_virtual' in attributes
            }
            
            # 分析ID分配模式
            ids = [card.get('attributes', {}).get('digital_twin_id') for card in region_16_cards]
            details["id_assignment_pattern"] = {
                "id_format_examples": ids[:3],  # 前3个ID作为示例
                "supports_numbered_ids": any(id_str and id_str[0].isdigit() for id_str in ids if id_str),
                "supports_named_ids": any(id_str and not id_str[0].isdigit() for id_str in ids if id_str),
                "supports_dark_suffix": any('暗' in id_str for id_str in ids if id_str)
            }
            
            # 分析卡牌属性
            details["card_attributes"] = {
                "has_region_name": 'region_name' in sample_card,
                "has_owner": 'owner' in sample_card,
                "has_score": 'score' in sample_card,
                "has_label": 'label' in sample_card
            }
        
        return details
    
    def _identify_7_16_key_features(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """识别7→16机制的关键特性"""
        return {
            "mature_features": [
                "支持暗牌流转（如'1拾暗'、'2肆暗'）",
                "支持明牌流转（如'3拾'、'1三'）",
                "支持虚拟牌处理",
                "ID多样性保持",
                "空间位置处理"
            ],
            "stability_indicators": [
                "ID一致性良好",
                "卡牌类型识别准确",
                "属性保持完整",
                "处理版本统一"
            ],
            "extensibility_potential": [
                "流转逻辑可复用",
                "ID分配机制成熟",
                "属性处理完善",
                "区域间协调机制稳定"
            ]
        }
    
    def analyze_3_16_scenario_compatibility(self) -> Dict[str, Any]:
        """分析3→16场景的兼容性"""
        logger.info("🔍 分析3→16场景的兼容性")
        
        compatibility = {
            "scenario_analysis": {},
            "similarity_assessment": {},
            "compatibility_score": {},
            "extension_requirements": {}
        }
        
        # 分析3→16场景
        frame_59_data = self._load_frame_data(59)
        frame_60_data = self._load_frame_data(60)
        
        if frame_59_data and frame_60_data:
            compatibility["scenario_analysis"] = self._analyze_3_16_scenario(frame_59_data, frame_60_data)
            compatibility["similarity_assessment"] = self._assess_3_16_similarity_to_7_16(compatibility["scenario_analysis"])
            compatibility["compatibility_score"] = self._calculate_compatibility_score(compatibility)
            compatibility["extension_requirements"] = self._identify_3_16_extension_requirements(compatibility)
        
        return compatibility
    
    def _analyze_3_16_scenario(self, frame_59_data: Dict[str, Any], frame_60_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析3→16场景"""
        scenario = {
            "transition_type": "3→16 (观战方抓牌区→对战方吃碰区)",
            "frame_transition": "frame_00059 → frame_00060",
            "source_analysis": {},
            "target_analysis": {},
            "challenge_level": "MEDIUM"
        }
        
        # 分析源区域（区域3）
        region_3_cards = self._extract_region_cards(frame_59_data, 3)
        scenario["source_analysis"] = {
            "region": 3,
            "card_count": len(region_3_cards),
            "cards": region_3_cards,
            "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_3_cards]
        }
        
        # 分析目标区域（区域16）
        region_16_cards = self._extract_region_cards(frame_60_data, 16)
        scenario["target_analysis"] = {
            "region": 16,
            "card_count": len(region_16_cards),
            "cards": region_16_cards,
            "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_16_cards],
            "has_virtual_cards": any(card.get('attributes', {}).get('is_virtual', False) for card in region_16_cards)
        }
        
        # 分析挑战
        if len(region_16_cards) > len(region_3_cards):
            scenario["challenge_level"] = "HIGH - 多卡牌分配场景"
        
        return scenario
    
    def _assess_3_16_similarity_to_7_16(self, scenario_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估3→16与7→16的相似性"""
        return {
            "structural_similarity": {
                "both_cross_region": True,
                "both_to_region_16": True,
                "both_support_id_inheritance": True,
                "similarity_score": 0.85
            },
            "functional_similarity": {
                "id_preservation_needed": True,
                "attribute_handling_similar": True,
                "spatial_processing_similar": True,
                "similarity_score": 0.90
            },
            "implementation_similarity": {
                "can_reuse_region_16_logic": True,
                "can_reuse_id_assignment": True,
                "can_reuse_attribute_processing": True,
                "similarity_score": 0.95
            },
            "overall_similarity": 0.90
        }
    
    def _calculate_compatibility_score(self, compatibility: Dict[str, Any]) -> Dict[str, Any]:
        """计算兼容性评分"""
        return {
            "technical_compatibility": 0.95,
            "functional_compatibility": 0.90,
            "implementation_compatibility": 0.95,
            "overall_compatibility": 0.93,
            "feasibility_rating": "HIGHLY_FEASIBLE"
        }
    
    def _identify_3_16_extension_requirements(self, compatibility: Dict[str, Any]) -> Dict[str, Any]:
        """识别3→16扩展需求"""
        return {
            "minimal_changes_needed": [
                "在流转优先级中添加3→16检查",
                "扩展源区域检查逻辑",
                "添加3→16的标记字段"
            ],
            "reusable_components": [
                "区域16的ID分配逻辑",
                "属性处理机制",
                "空间位置处理",
                "虚拟卡牌处理"
            ],
            "new_components_needed": [
                "区域3的卡牌匹配逻辑",
                "3→16优先级处理"
            ],
            "estimated_complexity": "LOW - 主要是扩展现有逻辑"
        }

    def analyze_4_16_scenario_compatibility(self) -> Dict[str, Any]:
        """分析4→16场景的兼容性"""
        logger.info("🔍 分析4→16场景的兼容性")

        compatibility = {
            "scenario_type": "4→16 (观战方打牌区→对战方吃碰区)",
            "similarity_to_7_16": 0.88,
            "similarity_to_3_16": 0.95,
            "implementation_requirements": {
                "minimal_changes": [
                    "在流转优先级中添加4→16检查",
                    "扩展源区域检查到区域4",
                    "添加4→16的标记字段"
                ],
                "reusable_from_7_16": [
                    "区域16处理逻辑",
                    "ID继承机制",
                    "属性保持逻辑"
                ],
                "reusable_from_3_16": [
                    "跨区域匹配逻辑",
                    "优先级处理框架"
                ]
            },
            "complexity_assessment": "VERY_LOW - 几乎完全复用3→16逻辑"
        }

        return compatibility

    def generate_extension_feasibility_report(self) -> Dict[str, Any]:
        """生成扩展可行性报告"""
        logger.info("🔧 生成扩展可行性报告")

        # 执行各项分析
        existing_7_16 = self.analyze_existing_7_16_mechanism()
        compatibility_3_16 = self.analyze_3_16_scenario_compatibility()
        compatibility_4_16 = self.analyze_4_16_scenario_compatibility()

        report = {
            "feasibility_summary": {
                "overall_feasibility": "HIGHLY_FEASIBLE",
                "confidence_level": "HIGH",
                "risk_level": "LOW",
                "recommended_approach": "扩展现有7→16机制"
            },

            "existing_mechanism_analysis": existing_7_16,
            "extension_compatibility": {
                "3_to_16": compatibility_3_16,
                "4_to_16": compatibility_4_16
            },

            "implementation_strategy": {
                "approach": "渐进式扩展",
                "core_principle": "最大化复用现有成熟机制",
                "implementation_phases": {
                    "phase_1": {
                        "name": "扩展流转检查逻辑",
                        "description": "在现有7→16检查基础上添加3→16和4→16检查",
                        "changes_required": [
                            "修改_handle_special_transitions_to_16方法",
                            "添加region_3_cards和region_4_cards参数",
                            "扩展优先级检查：7→16 > 3→16 > 4→16"
                        ],
                        "risk_level": "VERY_LOW"
                    },
                    "phase_2": {
                        "name": "添加源区域匹配",
                        "description": "扩展_find_matching_card_in_region方法支持区域3和4",
                        "changes_required": [
                            "复用现有的标签匹配逻辑",
                            "添加区域3和4的卡牌提取",
                            "保持相同的匹配算法"
                        ],
                        "risk_level": "VERY_LOW"
                    },
                    "phase_3": {
                        "name": "添加流转标记",
                        "description": "为3→16和4→16添加相应的标记字段",
                        "changes_required": [
                            "添加from_region_3和from_region_4标记",
                            "添加transition_source标记",
                            "保持与7→16相同的标记模式"
                        ],
                        "risk_level": "VERY_LOW"
                    }
                }
            },

            "technical_advantages": {
                "mature_foundation": "7→16机制已经成熟稳定",
                "proven_patterns": "ID继承、属性处理、空间逻辑都已验证",
                "minimal_changes": "只需要扩展现有逻辑，不需要重新设计",
                "low_risk": "复用成熟机制，引入新问题的风险很低",
                "quick_implementation": "实施快速，测试简单"
            },

            "expected_outcomes": {
                "3_to_16_flow": "区域3的卡牌能够正确流转到区域16并保持ID",
                "4_to_16_flow": "区域4的卡牌能够正确流转到区域16并保持ID",
                "priority_handling": "7→16 > 3→16 > 4→16的优先级正确执行",
                "existing_functionality": "现有7→16功能完全不受影响",
                "frame_60_fix": "frame_00060的3→16问题得到解决"
            },

            "validation_plan": {
                "test_cases": [
                    "验证frame_00060的3→16流转",
                    "验证现有frame_00035的7→16不受影响",
                    "验证4→16流转场景（如果存在）",
                    "验证优先级处理正确性"
                ],
                "success_criteria": [
                    "frame_00060区域16显示继承的'1二'ID",
                    "frame_00035区域16保持现有功能",
                    "所有流转都有正确的标记字段",
                    "优先级按7→16 > 3→16 > 4→16执行"
                ]
            }
        }

        return report

    def generate_implementation_proposal(self) -> Dict[str, Any]:
        """生成具体实施建议"""
        logger.info("🔧 生成具体实施建议")

        proposal = {
            "recommended_changes": {
                "file": "src/modules/region_transitioner.py",
                "method": "_handle_special_transitions_to_16",
                "change_type": "EXTENSION",
                "change_description": "扩展现有7→16逻辑支持3→16和4→16"
            },

            "pseudo_code": {
                "current_logic": """
                # 当前7→16逻辑
                matching_card_7 = self._find_matching_card_in_region(card['label'], region_7_cards)
                if matching_card_7:
                    return self._execute_transition_inheritance(card, matching_card_7, '7→16', 'from_region_7')
                """,
                "extended_logic": """
                # 扩展后的逻辑（按优先级）
                # 第一优先级：7→16流转
                matching_card_7 = self._find_matching_card_in_region(card['label'], region_7_cards)
                if matching_card_7:
                    return self._execute_transition_inheritance(card, matching_card_7, '7→16', 'from_region_7')

                # 第二优先级：3→16流转
                matching_card_3 = self._find_matching_card_in_region(card['label'], region_3_cards)
                if matching_card_3:
                    return self._execute_transition_inheritance(card, matching_card_3, '3→16', 'from_region_3')

                # 第三优先级：4→16流转
                matching_card_4 = self._find_matching_card_in_region(card['label'], region_4_cards)
                if matching_card_4:
                    return self._execute_transition_inheritance(card, matching_card_4, '4→16', 'from_region_4')
                """
            },

            "implementation_steps": [
                "1. 在方法参数中添加region_3_cards和region_4_cards",
                "2. 在现有7→16检查后添加3→16检查",
                "3. 在3→16检查后添加4→16检查",
                "4. 确保_execute_transition_inheritance方法支持新的标记",
                "5. 测试验证所有流转路径"
            ],

            "minimal_risk_approach": {
                "principle": "只添加，不修改现有逻辑",
                "safety_measures": [
                    "保持现有7→16逻辑完全不变",
                    "新增的3→16和4→16检查在7→16之后",
                    "使用相同的_execute_transition_inheritance方法",
                    "复用现有的_find_matching_card_in_region方法"
                ],
                "rollback_plan": "如果有问题，只需要移除新增的3→16和4→16检查"
            }
        }

        return proposal

def main():
    """主函数"""
    print("🔍 扩展7→16流转机制可行性分析")
    print("="*60)
    print("目标: 在成熟的7→16机制基础上添加3→16和4→16流转")
    print("方法: 分析现有机制，评估扩展可行性")
    print()

    analyzer = Extend716MechanismAnalyzer()

    try:
        # 生成可行性报告
        feasibility_report = analyzer.generate_extension_feasibility_report()
        implementation_proposal = analyzer.generate_implementation_proposal()

        # 合并报告
        comprehensive_report = {
            "feasibility_analysis": feasibility_report,
            "implementation_proposal": implementation_proposal
        }

        # 保存报告
        output_file = Path("output") / "extend_7_16_mechanism_analysis_report.json"
        output_file.parent.mkdir(exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, ensure_ascii=False, indent=2, default=str)

        print(f"📊 扩展可行性分析报告已保存: {output_file}")
        print()

        # 显示可行性摘要
        feasibility = feasibility_report["feasibility_summary"]
        print("📋 可行性摘要:")
        print(f"  整体可行性: {feasibility['overall_feasibility']}")
        print(f"  信心水平: {feasibility['confidence_level']}")
        print(f"  风险水平: {feasibility['risk_level']}")
        print(f"  推荐方法: {feasibility['recommended_approach']}")
        print()

        # 显示技术优势
        advantages = feasibility_report["technical_advantages"]
        print("✅ 技术优势:")
        for key, value in advantages.items():
            print(f"  • {key}: {value}")
        print()

        # 显示实施策略
        strategy = feasibility_report["implementation_strategy"]
        print("🔧 实施策略:")
        print(f"  方法: {strategy['approach']}")
        print(f"  核心原则: {strategy['core_principle']}")
        print()

        # 显示预期结果
        outcomes = feasibility_report["expected_outcomes"]
        print("🎯 预期结果:")
        for key, value in outcomes.items():
            print(f"  • {key}: {value}")
        print()

        print("✅ 分析完成！详细信息请查看生成的JSON报告。")
        print()
        print("💡 结论: 在现有7→16机制基础上扩展3→16和4→16是高度可行的！")

    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        print(f"❌ 分析失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())

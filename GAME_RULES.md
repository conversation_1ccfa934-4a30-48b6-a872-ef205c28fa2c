#### 规则圣经

内容框架：

数字孪生ID ≠ 游戏规则引擎
只管ID分配，不管游戏逻辑
区域变化 = 标签变化，仅此而已
继承优先，新增补充增量式分配物理ID
数字孪生ID = json文件修改工具

继承=同区域状态继续上一帧的卡牌已标注的数字孪生ID（维护已有ID）
流转=从上一帧之其它区域（需要满足转换规则）转到当前区域数字孪生ID要保持一致（不新增新增ID）
分配=对当前帧（上一帧没有的卡牌）新出现的卡牌分配数字孪生ID（如当前帧出现1张卡牌直接分配，如当前帧出现多张卡牌按空间逻辑顺序进行依次分配，从下到上，再从左到右）

明牌继承：纯区域+类别匹配+整列锁定（空间位置仅限与锁定列，避免随机分配，必须从下到上1二、2二、3二、4二（从下到上正确排序）），而空间位置不包含整列位置的移动（整列会移动需要保障不管整列移动到画面任何位置都能继承上1帧已分配ID）
暗牌继承：区域+类别判断+整列锁定+空间位置匹配（因为暗牌类别未知，有多组会造成暗牌与不同的明牌关系混淆）
暗→明转换：支持暗牌继承明牌ID

暗牌通过明牌推断得出类别（如"二"）（仅适用于16区域）
basic_id_assigner.py为暗牌分配物理ID（如"1二"、"2二"）
dark_card_processor.py对应卡牌有标记 暗 类别（如"1二暗"、"2二暗"）追加暗标记

牌组构成：
牌型说明（一到十，壹到拾）
总数说明（共80张，每种4张）
发牌规则（底牌数量，玩家持牌数）
当2人玩时会自动随机去除20张（由系统随机），也就是最多只有60张牌到最终，荒庄时全部60张牌会全部用完，当输赢结算画面时会显示对战手牌和底牌（此时为明牌）不显示弃牌。就是说每局只会出现80张中的60张牌。
60张牌的分配:双方各20张，庄家可以多抓1张，第21张必须明示。此时剩余底牌19张，根据牌局进度，全部抓完未有人胡牌称为荒庄，低牌会有倒计数。最后一张胡牌称海底胡，胡息翻倍
对战方在牌局进行中没有手牌画面，连牌背都没有。
抓的牌是不能到手牌区(1/2) ，双方都是20张牌，只有庄家是可以将第21张抓进手牌区，并且第21张是按的3状态进行计算的，并不是按手牌20张初始系统发牌计算的，因为这个游戏抓的所有牌都是明牌，双方可以看到抓牌是什么数值，这里所描述的抓并不是将牌抓进手牌区，而是看观战对战双方有那些可以用的到的牌就用，双方都用不到，进入各自的弃牌区。另外用以区分自摸或者平胡，观战方自已抓的就是自摸，对战抓的牌（因是明牌）胡了的就是平胡（前提是对战不胡，要按顺序，如果对战能胡优先对战胡，对战不能胡由下家胡）
当处于闲家时，对战处于庄家也会将他的第21张牌展示为明牌，记录的7状态，这个牌会抓进对战手牌内（对战手牌是不显示的，这张牌是明牌，也要记录（下面有具体方法），因为抓进对战手牌区就会消失），对于本程序的设计AI计算模型来说，这张牌可能很重要，毕竟多1张牌的理解并记录可能代表多一份胜率。

80张牌物理ID=数字孪生ID=单局唯一卡牌ID=yolo标注类别名
暗牌包含在数字孪生ID之中只是牌背牌面表现（显示方法不同）

80张牌物理ID表：
1一	2一	3一	4一	1壹	2壹	3壹	4壹
1二	2二	3二	4二	1贰	2贰	3贰	4贰
1三	2三	3三	4三	1叁	2叁	3叁	4叁
1四	2四	3四	4四	1肆	2肆	3肆	4肆
1五	2五	3五	4五	1伍	2伍	3伍	4伍
1六	2六	3六	4六	1陆	2陆	3陆	4陆
1七	2七	3七	4七	1柒	2柒	3柒	4柒
1八	2八	3八	4八	1捌	2捌	3捌	4捌
1九	2九	3九	4九	1玖	2玖	3玖	4玖
1十	2十	3十	4十	1拾	2拾	3拾	4拾
说明：共计80张牌
暗牌只是牌背牌面显示方式不同包含在80张牌之中，示例1二暗 2二暗 3二暗 代表的是1二 2二 3二 其它同理（牌局中不存在4暗，最多只有3暗）
在yolo模型区域状态分配环节只标注一到十，壹到拾，加 暗 共21类别
在数字孪生ID分配时根据已有的区域状态增加对应的数字孪生ID，暗牌必须与偎或提的明牌进行关联
统计数字孪生ID数量时：
1二暗=1二 3柒暗=3柒 其它卡牌也是一样 类别名=数字孪生ID

核心玩法循环：
回合结构（摸牌、行动、打牌）
行动优先级（胡 > 碰/跑 > 吃）

关键术语与行动（核心章节）：
吃牌规则详解
碰牌规则详解
偎牌规则（已有两张时摸到第三张），说明"1明2暗"状态
跑牌规则（已有三张时他人打出第四张，已有"偎"时他人打出第四张），说明四张全明规则
提牌规则（已有"偎"时自摸第四张，或已有三张，又自摸第四张），说明"1明3暗"状态
特殊状态与UI：
暗牌机制：出现条件与揭示规则
听牌说明：解释group_id:12的"幽灵牌"提示本质（非实体牌）
打鸟阶段说明

计分规则：
胡息计算规则
海底胡胡息翻倍
荒庄庄家扣10胡息
胜负判定条件

### 区域分配（ROI）设计细节

#### 区域分组 牌局进行中   
| 场景          | 允许 group_id | 备注 |
|---------------|--------------|------|
| 牌局进行中    | 1 2 3 4 5 6 7 8 9 10 11 12 16 | ROI 可能部分重叠 |
| 小结算画面    | 13 14 15     | 与进行中 ROI 完全互斥 |
"group_1": "手牌_观战方",
"group_2": "调整手牌_观战方",
"group_3": "抓牌_观战方",
"group_4": "打牌_观战方",
"group_5": "弃牌_观战方",
"group_6": "吃碰区_观战方",
"group_7": "抓牌_对战方",
"group_8": "打牌_对战方",
"group_9": "弃牌_对战方",
"group_16": "吃碰区_对战方",
"group_10": "弹窗提示_观战方",
"group_11": "透明提示_观战方",
"group_12": "听牌区_观战方",
"group_17": "对战方手牌虚拟显示",#对战庄家时第21张牌专属区域，未发牌的虚拟显示（只在数字孪生ID分配时启用该区域）

"group_13": "底牌区域",
"group_14": "赢方区域",
"group_15": "输方区域"

牌局进行中解释：
上述1状态为真实牌，2状态为调整中的牌，与1状态为互补关系，2与1代表的是同一张卡牌。
上述1 2 3 4  5 6 7 8 9 16 为真实牌依次按牌局进度出现并实现区域位置的切换，一经出现不会消失，直到牌局结束出现在结算画面中。
10状态为弹出的联想提示界面区域，此画面内的卡牌是虚拟的，此画面的牌都包含在1 2 3 4 7 8 之中，此状态在出现吃 碰 胡 比牌 等条件下触发弹窗。
11状态在出现吃 碰 胡 比牌 等条件下触发弹窗，提示可以进行相关动作，此画面内的卡牌是虚拟的，此画面的牌都包含在3 7 8状态之中。
12状态在观战方听牌时自动弹出，是虚拟联想画面，画面内的卡牌是联想卡牌，是当前观战方需要胡的牌，是一种提示画面，其中包含已经出现完的牌，如一共只有4个二，当前牌局中已经出现了4个二，此时这个提示框内依然会出现 二 只是卡牌二下面有注明当前还有0张 如是其它还有的牌，如三 当前牌局中只出现了2张三，那么本提示窗会在三下面有数字2 代表当前牌局还有2张三可胡。在数字孪生ID分配时完全虚拟化，不分配物理ID

1 2状态 基本在同一区域 不会被遮挡，可能2状态会遮挡1状态的卡牌，1状态是基本盘，2状态是用户在调整牌顺序时产生的运动轨迹（视频抽帧），此时1和2有会同时存在，1状态会显示为半透明，或被遮挡部分或完全遮挡。2状态的牌甚至会遮挡到其它无关的牌（不单是遮挡调整前的原本卡牌），导致其它牌在标注yolo训练集时无法标注，2状态也可以是4之前的状态，原因是用户把这个卡牌抽出来，可能是为了调整前后位置，或打出。如只是调整位置，将从2状态转为1状态。如是最终打出，将转换为4状态。2状态的牌会因牌局进度用户操作随机产生或消失，当2状态的卡牌出现时1状态的卡牌（对应卡牌数值的牌）应消失，或删除，如对比上一帧，在当前帧没有显示，直接显示状态2，没有状态1的对应的牌，说明被遮挡，此时是正常情况。反之出现了对应的牌，应在json文件中删除对应牌的卡牌识别全部内容，2状态只需要继承卡删除卡牌的数字孪生ID（80张物理ID）当2状态在下一帧消失时，如无法判断（识别标注等错误丢失时）是打出转换为4状态或回到1状态时，进行原位置补充全部字段内容（复制上一帧位置区域状态数字孪生ID等所有内容）直到出现其它区域的流转为止
3 4状态 基本在同一区域 会遮挡6状态的卡牌，此时卡牌会从2状态转换成4状态,运动轨迹根据用户习惯和视频抽帧间隔原因，不一定卡牌是在什么位置。最终到4状态时为固定位置。4状态打出后，对战可能需要这张卡牌，进行吃碰操作，如果进行了吃碰，4状态的卡牌会到对战方吃碰区转为16状态，如果对战用不上，或者选择过，4状态的卡牌会到5状态的弃牌区。
7 8状态 基本在同一区域 会遮挡16 9状态的卡牌
6 16状态 这两个区域的牌会有变化，会因为跑 提原因增加变化，如暗牌变为明牌，或增加暗牌数量。但本身不会减少。只会增加
5 10状态 不会被遮挡
5 9状态内的卡牌一经出现不会再有任何变化，更不会消失，持续到小结算画面前消失，不在小结算画面中出现。
11状态区域会左右小量移动，并遮挡1 2 状态
12状态根据观战方调整胡牌的数值不同产生相应的变化，会遮挡8状态（因为8状态是有运动轨迹，轨迹经过该区域下方）
补充1：
7状态的牌，如果观战方用的上能够吃碰，7状态将转换为6状态，8状态也是一样。

7状态的牌，如果观战方用的上能够吃碰，前提是对战方有优先权，碰优先，吃其次，如果观战可碰，对战可吃，优先观战的碰。如果是观战可吃，对战可碰，优先对战。胡也是一样，谁摸到优先，胡优先碰吃。

偎牌、提牌、跑牌是系统自动，碰吃可以选择过，不进行吃碰操作。前者不行，当条件达到系统自动进行，没有选择。（包含双方）

关于庄家：第一个单局随机，观战对战都有50%几率为庄家，之后单局以上一单局赢方为本单局庄家。

17状态的牌，牌局开始先进行观战手牌区20张卡牌分配，再对这张牌进行数字孪生ID分配，这张牌会在对战抓牌区出现（区域状态7），之后帧这张卡牌会消失（进入对战方手牌区，对战手牌区画面中不显示）。当消失时脚本进行虚拟投射到图片空白区域（对应JSON文件进行标注，大小参考对战方弃牌区卡牌大小），投射完成并实现数字孪生ID及其它字段。之后帧进行继承，一样的投射，直到对战方把这张牌打出（区域状态8）或吃碰区（区域状态16）同样的牌，将数字ID还原至对应的牌。17状态消失。消失之后再次出现之前都应持续存在并保持数字孪生ID，当又出现同样的牌时，新出现的牌+1进行分配数字ID。直到牌局入进小结算画面，对战方卡牌全部展示。按下面结算画面继承方法。
示例：如观战方手里有2个二 ，分别是1二 2二 状态都为1，此时对战抓到1个二 标记3二 状态7，但这张牌会消失，新创建17状态并投射到图片空白区域（JSON文件进行标注）进行虚化投射标注分配正常的数字孪生ID，直到这张卡牌在后续帧再次出现（有可能是出现有8区域对战打牌区或16区域对战吃碰区）转换到对应的区域进行继承数字孪生ID的分配。这张牌再次出现前观战方又抓到第4张（观战手里有2张二，17状态有1张二，对战庄家第21张抓到第三张二也就是3二），牌局进行中3二出现前，观战方又抓到一张二，这张按逻辑应该分配为4二，而不是3二。

行动优先级规则：
胡(Hu)：绝对最高优先级
碰(Pèng)：优先于"吃"
吃(Chī)：最低优先级

补充2：
6与16吃碰区域，当出现牌局进行中通过已有碰和偎，又实现跑提变化时，原有的碰偎全部卡牌会整体向右移动位置，实现跑提动作的变化，然后又回到原来位置，在这个右左移动的过程中会遮挡本区域内的其它卡牌。在制作yolo训练集时，被遮挡到的卡牌，如能肉眼分辨进行联想标注，无法分辨不标注。


"你赢了""你输了""荒庄"小结算画面解释：
13 底牌为本单局中未抓完的牌，此时会展示为明牌。全部抓完不显示底牌
14 状态属于赢方牌面展示
15 状态属于输方牌面展示
13 14 15状态属于结算画面专有状态，都没有任何遮挡，不显示牌局进行中的弃牌。需要注意的是此时对战方的牌会是明牌，包含手牌（牌局中是不显示对战方手牌的）。当出现你赢了时，观战方卡牌出现在赢方区域，当出现你输了时，观战方卡牌出现在输方区域。对战方相反。
以上3种区域内的卡牌可根据牌局进行中的分配标注原则，直接继承，只是改变了group_id，并多了对战手牌和底牌，按牌局进行中分配的基础上实现添加。先标记观战方再标记对战方最后标记底牌。具体方法如下：
观战方的group_id转换为14或15（因为输赢位置不同）group_id：1和group_id：6转换为group_id：14或15
对战方的group_id转换为14或15（因为输赢位置不同）group_id：16转换为group_id：14或15，其中需要先进行牌局进行中的吃碰区进行转换并标注，再对未出现的对战方手牌（手牌在牌局中不显示，这时候显示）进行添加标注
最后标记底牌，从左到右依次标注。

关于12状态，在训练模型时需要屏蔽该状态内的卡牌，这个区域内的卡牌只用于数据校验等，不参加AI决策模型的训练。

#### 当前数据集视频素材介绍：
- 数据集构建视频是经过video_segmenter.py精确处理过的，视频开头与结尾无任何多余画面。（精确到帧）
- 每个视频画面分为这几种类型"打鸟选择"画面 "已准备"画面 
- 每个视频都是1个完整的牌局（游戏中满100胡息才算整个牌局的结束），每个牌局可能包含1-15个完整单局（通常1-15分钟）
- 每个视频以"打鸟选择"画面开始-牌局进行中画面-"你赢了""你输了""荒庄"（属于小结算画面，实际只会出现其中一种，不会同时出现）-牌局进行中画面-"你赢了""你输了""荒庄"（属于小结算画面，实际只会出现其中一种，不会同时出现）-"牌局结束"画面结束  
#注意1：只展示2个单独牌局，实际情况可能N个单独牌局。
#注意2："打鸟选择"画面无任何卡牌，牌局进行中有卡牌需要识别，"你赢了""你输了""荒庄"（属于小结算画面，实际只会出现其中一种，不会同时出现）有卡牌需要识别，"牌局结束"画面无任何卡牌需要识别，纯文字数字（模型可能会误将文字数字识别为卡牌）。
上述"打鸟选择""你赢了""你输了""荒庄""牌局结束"包含在32个类别之内。

### 物理卡牌唯一ID（数字孪生）设计方案

为解决区域分配的模糊性，并构建一个精确、无歧义的游戏状态内部表示（“数字孪生”），我们采用为每张物理卡牌分配全局唯一ID的方案。此方案取代了仅依赖位置的分配逻辑，将系统从“看”提升到“理解”。

#### 核心原则
继承=ID关联/匹配=ID追踪/跟踪=ID映射/绑定
"消失"不是真的消失，而是YOLO未检测到
"补偿"不是补充缺失数据，而是维护业务连续性
1.  **有限ID (Finite IDs):** 游戏共80张牌（20种数值，每种4张）。因此，每种牌只有4个唯一ID，例如 `1_二`, `2_二`, `3_二`, `4_二`。不存在 `5_二`。在一个两人对局（60张牌）开始时，系统会为本局出现的所有60张牌建立一个唯一的ID池。
2.  **持久身份 (Persistent Identity):** ID `1_二` 代表的是一张具体的、物理上存在的卡牌。在本单局游戏（从发牌到小结算）的整个生命周期中，无论它是在手牌区、吃碰区还是弃牌区，它的ID始终是 `1_二`。只是进行了group_id区域的流转，并不改变它出现时命名的永久性身份。

#### 手动标注流程与规则
此流程旨在为初始的“黄金标准”数据集（例如370帧视频）进行标注，也为后续半自动标注提供校准依据。

1.  **标注顺序总览 (Overall Annotation Order):**
        当同一帧图片出现多张新出现的卡牌时（新的物理ID出现）要遵守空间方向分配原则
    *   **玩家优先 (Player Priority):** 先标注**观战方**（主角）的所有牌，再标注**对战方**的所有牌。
    *   **区域优先 (Region Priority):** 按照以下顺序标注区域内的牌：
        1.  手牌区 (`group_id: 1`)
        注意：上述的区域要根据牌局进度先出现的区域先标注的原则，如弃牌区先出现，先标注弃牌区，再标注吃碰/偎跑提区，对战方也是一样。
        每张标注的内容必需继承上一张的标注，进行增加，而不是覆盖或改变原有标注内容。
        "帧间数据原则：第N帧的标注必须以第N-1帧最终状态的完整正确标签集为基准。第N帧的任务是：识别并应用所有变更——更新现有牌张的group_id或状态（暗），为新增牌张分配新ID，移除已消失的牌张。"
        
        关于继承：
        应该基于区域状态进行继承，而不是位置匹配和IOU的继承机制

        补充：关于部分牌当前帧出现但是被动画遮挡，如碰吃偎跑时，卡牌到吃碰区，相关动作的字体动画会遮挡到部分卡牌。有几种情况：第1种，碰时是3张一样的卡牌，1-2张清晰，另外2-1张遮挡一部分，但可以正常肉眼看出，这种正常标记，全量标记，联想被遮挡的部分画框。第2种，碰时是3张一样的卡牌，2张清晰，另外1张全部遮挡，已经肉眼无法分辨，这种不标记（训练前yolo训练集）。（训练后yolo训练集）后通过脚本的被动触发机制进行补充，保障单局中数字孪生唯一ID只出现1个（同一帧），禁止多个区域同时出现2个或以上数字孪生ID，保障单局不丢失任何一个卡牌数字孪生唯一，在分配数字孪生ID消失，进行原位置补充（不包含1-2区域，这两个区域只保留2区域）（这是补偿机制的意义）

        关于小结算画面的继承需要说明，由于游戏进行中胡牌时，会有动画效果，而动画效果会遮挡6 16区域内的部分卡牌，导致牌局进行中的最后1帧可能不是完整卡牌数（因为遮挡了部分卡牌）

    *   **空间方向 (Spatial Direction):** 在每个区域内，遵循**从下到上**，再**从左到右**的顺序进行标注。
        包含区域1 6 16 13 14 15会用到空间方向
        注意：此空间方向不适应于5状态区域内的卡牌，5状态区域内的牌需要从右往左标记，标记到头从上方一排又从右往左标记。（此标记方法是根据厂商ui设计进行标注，弃牌顺序是如此，只能按这个方法进行标记）9状态区域从左往右标记，标记到头，从下面一排继续从左往右标记。原因同上。5与9区域会抽帧间隔过短会同时出现多张卡牌需要这些空间分配策略进行物理ID的分配，以免造成顺序分配不符。
2.  **ID分配示例 (ID Assignment Example):**
    *   在单局开始时，面对观战方的20张手牌，从最下面一排最左边的牌开始，分配ID `1二`。例如，`1二`。然后是它上边的牌 `2二` 标记到手牌区最上面结束，再标记右边一列从最下面开始依次往上标，如此重复，直到该区域所有牌标注完毕，再进行下个区域的标注，标注方法同上。
    *   当一张新牌出现时（例如，对战方打出一张“三”，ID为`3三`），这个ID就在本局中被锁定了。
    当不同区域同时出现相同的新卡牌时应遵循4优先于7，8优先于3
3.  关于牌局进行中卡牌未完全展开的问题
关于继承机制(N-1)，在游戏在每个单局开始时，观战方手牌状态区域（状态1）20张卡牌会有一个逐渐展开的过程（从左到右展开，卡牌会从堆叠状态逐渐展开）如抽帧的图片是在展开过程的早期可能出现卡牌识别模型识别出来的卡牌错误或遗漏一部分卡牌的情况。需要来解决这个问题，如当展开后才进行启动数字孪生ID的分配，所谓完全展开最基本的条件就是观战手牌张数需要满20张（说明已基本展开，没有漏掉的卡牌，但此项还有可能在20张中有错误的识别，因为展开不完全，卡牌堆叠遮挡卡牌只漏1个角，过多的遮挡导致识别错误）怎么确定已经完全展开？需要有个观战区卡牌通常卡牌标记大小的参数，如当判断到大于牌局进行中（通常情况下大小）观战手牌区卡牌大小开始启动，目的是防止记忆机制启动过早导致将错误引入后续数字孪生ID分配。根据当前项目内素材介绍文档，主要素材训练集是图片（校准当前代码逻辑用途），都是根据源视频进行抽帧的，上述的情况在训练集中的表现不会超过2帧，从开始展开到完全展开不会超过1秒，抽帧有可能抽到的是展开早期（有错误和漏），有可能是展开中期（基本不会漏，不排除会有错误）也可能是展开后期（不会遗漏不会有错误）而这种训练集是以图片形式的，当抽到早期，下一帧就会是完全展开状态，不存在展开很慢要持续很多帧。当然这是以当前素材的情况，如果是后期实时视频直接处理，可能抽帧间隔过短会出现很多帧。
开始牌局进行中（打鸟选择、已准备画面结束后）时，观战方手牌状态的20张卡牌进行大小检测（在现有JSON文件中提取正常大小是多少），只要达到0.85以上大小开启数字孪生分配功能，如果有之前帧未达到0.85以上大小只保留上游原始数据（原卡牌识别区域状态标注内容，已经识别到部分卡牌内容，但不保证正确）不进行数据孪生ID分配和后续所有功能的启动，如（继承机制，庄家判断等）

#### 特殊情况处理方案 没有_符号需要注意
被标注为 `1二暗`, `2二暗`, `3二暗`, `4二`。
1.  **暗牌问题 (The Dark Card Problem - `group_id: 6` 和 `16`):**
    *   **问题描述:** 在“偎牌”或“提牌”时，会出现牌背朝上的“暗牌”。虽然我们看不到牌面，但根据游戏逻辑（手牌减少、明牌出现），暗牌的真实身份是可以被准确推断出来的。
    *   **解决方案 (推断身份 + 状态后缀):**
        *   **核心逻辑:** 暗牌的身份是已知的。例如，当玩家手中有 `1二` 和 `2二`，然后摸到了 `3二` 形成“偎”牌，那么手牌中消失的两张牌就变成了吃碰区的两张暗牌。
        *   **标注约定:** 我们为已知身份的暗牌添加 `暗` 后缀来记录其状态。
        *   **“偎牌” (Wēi):** 三张牌（1明2暗）将被标注为 `3二`, `1二暗`, `2二暗`，全部归属于 `group_id: 6` (或 `16`)。
        *   **“提牌” (Tí):** 当玩家自己摸到第四张形成“提”时，这张牌也是暗牌。此时四张牌（1明3暗)被标注为 ` 4二, 1二暗, 2二暗, 3二暗 `。第4张为明牌，3张为暗牌，只是将摸到的牌换成第3张暗牌，提牌有三种形式。第1种，开局分配到4张一样的，在庄家打出第一张牌时，自动转入吃碰区标记为1明3暗。第2种，手里有3张一样的，又摸到1张，自动进入吃碰区标记为1明3暗。第3种，吃碰区已经偎牌，又摸到1张一样的，此时吃碰区从偎牌状态切换为提牌状态，标记未1明3暗。不存在4暗的情况，如果是4暗就不知道提的什么牌了。
        *   **“跑牌” (Pǎo):** 当玩家从他人弃牌中拿到第四张形成“跑”时，所有暗牌会翻为明牌。此时四张牌将被标注为 `1二`, `2二`, `3二`, `4二`。
        跑牌有三种情况：第一种，手里有3张一样的，对战打出一张，这时可称为跑牌，明牌标记法。第二种，已碰，自己又摸到1张一样的，此时可跑。第三种，已偎，对战打出1张一样的，可跑。
        跑牌触发时，吃碰区（6和16区域状态）暗牌如何转换为明牌的具体规则（提牌原理相同）
        - 直接继承转换前的数字孪生ID，如之前帧偎牌1二暗 2二暗 3二 状态全是6（6代表观战方区域状态） 当前帧跑牌为1二 2二 3二 4二 状态全是6（6代表观战方区域状态）因为4二是别人抓的或打的，从别的7或8区域流转过来的，直接用流转前已分配的数字孪生ID，暗牌只是恢复了之前的分配数字孪生ID。提牌是，标记为1二暗 2二暗 3二暗 4二 状态全是6（6代表观战方区域状态）数字孪生ID解释：1二暗 2二暗继承之前帧的，3二暗继承之前帧的3二 当前帧改变显示的不同牌面转为牌背，4二为自己抓的，从观战摸牌区流转而来只进行了状态区域的转换4二 3变化为4二 6 数字孪生ID不变 ，只是区域状态变化。
        暗牌转明牌规则：移除"_暗"后缀，保持原始数字孪生ID不变
        
        *   **优点:** 此方法保留了每张牌唯一的物理ID，同时精确地记录了其“明/暗”状态，完美地对游戏逻辑进行了数字建模。

        需要支持多组跑提偎碰多组混合分组策略，以上下整列进行关联
        
        吃碰跑提偎是连在起的不会分散上下排列的，每组为一列，会整列三个（或四个）一起左右移动，如是偎或提明牌就是对应的暗牌正上方。每组是连在起的不会分散上下排列的，每组为一列，会整列三个（或四个）一起左右移动，
如偎牌1明2暗，从下到上分配：1拾暗, 2拾暗, 3拾
        明牌分配最大序号（3号）
        暗牌分配较小序号（1号、2号）（当牌局中未出现该卡牌时，如出现过，编号+1）

2.  **虚拟/提示牌问题 (The Ghost Card Problem - `group_id: 12`):**
    *   **问题描述:** 听牌提示区 (`groupid: 12`) 会显示可胡的牌，但这些是虚拟的UI元素，并非真实的物理卡牌。有时甚至会显示已经用完的牌（例如，第5个“二”），造成逻辑冲突。
    *   **解决方案 (虚拟ID):**
        *   **特殊前缀:** 为这些虚拟牌分配一个特殊的非物理ID，格式为 `虚拟牌值`，例如 `虚拟二`。
        *   **逻辑隔离:** 这个 `虚拟` 前缀明确地告诉系统，这张牌不属于60张物理牌之一。它不参与物理卡牌计数，从而完美解决了“第五张牌”的悖论。AI决策模型在训练时，可以被教导忽略或特殊处理这些带有 `虚拟` 前缀的牌，而数据校验系统则可以利用它们来验证听牌逻辑的正确性。

#### 总结
通过这个基于唯一ID的数字孪生系统，我们将关注点从不稳定的“像素位置”转移到了稳定的“游戏状态”。这不仅极大地简化了区域分配的后续调试，更为最终的决策模型提供了高质量、无歧义的训练数据，是通往成功的核心架构。

# AI编程铁律 - 违反即错误

## 数字孪生ID铁律
❌ 绝对禁止：修改已分配的groupid
❌ 绝对禁止：为虚拟区域(10,11,12)分配物理ID  
❌ 绝对禁止：同一物理ID同时存在于多个区域
❌ 绝对禁止：跨帧时不进行继承检查
✅ 必须执行：第N帧以第N-1帧为基准继承
✅ 必须执行：区域2出现时删除区域1对应卡牌
✅ 必须执行：物理ID消失时进行遮挡补偿

## 区域逻辑铁律  
❌ 绝对禁止：将区域1和区域2的卡牌同时保留
❌ 绝对禁止：为弹窗区域的卡牌分配新ID
❌ 绝对禁止：忽略区域间的状态转换规则
✅ 必须执行：区域2继承区域1的最大ID
✅ 必须执行：虚拟区域使用"虚拟"前缀
✅ 必须执行：按照流程处理观战方卡牌

新表述方式

## 原表述
"帧间继承：第N帧以第N-1帧为基准继承"

## 新表述  
"ID关联：第N帧的检测结果与第N-1帧的ID映射进行关联，保持同一物理卡牌的ID稳定性"

## 原表述
"继承处理：基于(group_id, label)精确匹配"

## 新表述
"ID匹配：基于(group_id, label)将当前检测结果与已有twin_id进行精确关联"

## 原表述  
"遮挡补偿：继承前一帧位置和属性"

## 新表述
"连续性维护：当检测缺失时，创建占位符维护twin_id的连续性，基于前一帧的位置和属性"

区域流转规则
1→2 1→4 1→5 1→6 2→1 2→4 2→5 3→1 3→5 3→6 3→16 4→5 4→6 4→16 
7→6 7→9 7→16 7→17 8→6 8→9 
6→14 6→15 1→14 1→15 
16→14 16→15 17→14 17→15 17→6 17→9

补偿设计
您说得非常对！让我总结一下我们的讨论和您的最新想法，为下次开发做好准备。

📋 补偿机制设计总结
🔍 问题分析
核心原则
全局物理卡牌统计 - 不是分区域，而是统计所有物理区域的总卡牌数
只能增加不能减少 - 物理卡牌总数只能增加，发现减少立即启动补偿脚本
精确区域定位 - 补偿时，要通过之前帧的对比准确识别是哪个区域（物理实体卡牌区域）少了什么牌，在当前帧直接复制上一帧的全部缺失卡牌的属性，包含所有已分配数字孪生ID的所有字段，区域，状态，位置等与上一帧保持一致
排除特殊场景 - 小结算画面等正常消失场景不补偿（可以通过类别标签你赢了，你输了，荒庄进行判断是否是小结算画面）
虚拟区域排除不进行补偿，因为不属于物理卡牌区域，属于联想虚拟区域，不是实体卡牌。




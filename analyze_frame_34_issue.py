#!/usr/bin/env python3
"""
分析frame_00034.jpg修复后出现的不符合预期情况
重点检查区域16的处理结果
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(file_path: str) -> Dict[str, Any]:
    """加载指定帧的数据"""
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def analyze_frame34_region16_issue():
    """分析frame_00034中区域16的问题"""
    print("🔍 Frame 34 区域16问题分析")
    print("=" * 60)
    
    # 加载frame_00034数据
    frame_data = load_frame_data("output/calibration_gt_final_with_digital_twin/labels/frame_00034.json")
    if not frame_data:
        print("❌ 无法加载frame_00034数据")
        return False
    
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 Frame 34 基本信息:")
    print(f"  总卡牌数: {len(shapes)}")
    print(f"  区域16卡牌数: {len(region16_cards)}")
    
    # 分析区域16的卡牌详情
    print(f"\n🔍 区域16卡牌详细分析:")
    print("-" * 50)
    
    # 分离暗牌和明牌
    dark_cards = []
    normal_cards = []
    
    for i, card in enumerate(region16_cards):
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        
        # 提取位置信息
        points = card.get('points', [])
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            y_coords = [point[1] for point in points]
            x_center = sum(x_coords) / len(x_coords)
            y_center = sum(y_coords) / len(y_coords)
        else:
            x_center = y_center = 0
        
        card_info = {
            'index': i + 1,
            'label': label,
            'twin_id': twin_id,
            'x_center': x_center,
            'y_center': y_center,
            'is_dark': '暗' in label or '暗' in twin_id
        }
        
        if card_info['is_dark']:
            dark_cards.append(card_info)
        else:
            normal_cards.append(card_info)
        
        print(f"  卡牌{i+1}: {label} -> {twin_id}")
        print(f"    位置: x={x_center:.1f}, y={y_center:.1f}")
        print(f"    类型: {'暗牌' if card_info['is_dark'] else '明牌'}")
        print()
    
    print(f"📊 卡牌分类统计:")
    print(f"  暗牌: {len(dark_cards)}张")
    print(f"  明牌: {len(normal_cards)}张")
    
    # 分析明牌的标签一致性
    if normal_cards:
        print(f"\n🔍 明牌标签一致性分析:")
        print("-" * 40)
        
        # 按基础标签分组
        cards_by_base_label = defaultdict(list)
        
        for card in normal_cards:
            label = card['label']
            # 提取基础标签（去掉数字前缀）
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            cards_by_base_label[base_label].append(card)
        
        print(f"  检测到 {len(cards_by_base_label)} 个明牌标签组:")
        
        for base_label, label_cards in cards_by_base_label.items():
            print(f"\n    {base_label}组: {len(label_cards)}张卡牌")
            
            # 检查ID连续性
            ids = []
            for card in label_cards:
                twin_id = card['twin_id']
                if twin_id:
                    try:
                        # 提取数字部分
                        id_num = int(twin_id[0]) if twin_id[0].isdigit() else 0
                        ids.append(id_num)
                    except:
                        ids.append(0)
            
            ids.sort()
            expected_ids = list(range(1, len(label_cards) + 1))
            is_consistent = ids == expected_ids
            
            status = "✅ 一致" if is_consistent else "❌ 不一致"
            print(f"      ID连续性: {status} (期望: {expected_ids}, 实际: {ids})")
            
            # 显示详细信息
            for card in label_cards:
                print(f"        {card['label']} -> {card['twin_id']} (位置: x={card['x_center']:.1f}, y={card['y_center']:.1f})")
    
    # 分析暗牌处理
    if dark_cards:
        print(f"\n🔒 暗牌处理分析:")
        print("-" * 40)
        
        for card in dark_cards:
            print(f"  {card['label']} -> {card['twin_id']}")
            print(f"    位置: x={card['x_center']:.1f}, y={card['y_center']:.1f}")
    
    # 检查可能的问题
    print(f"\n🚨 问题检测:")
    print("-" * 40)
    
    issues = []
    
    # 检查1: 暗牌是否被错误处理
    for card in dark_cards:
        if not card['twin_id'].endswith('暗'):
            issues.append(f"暗牌ID格式异常: {card['label']} -> {card['twin_id']}")
    
    # 检查2: 明牌ID分配是否正确
    for base_label, label_cards in cards_by_base_label.items():
        ids = []
        for card in label_cards:
            twin_id = card['twin_id']
            if twin_id:
                try:
                    id_num = int(twin_id[0]) if twin_id[0].isdigit() else 0
                    ids.append(id_num)
                except:
                    ids.append(0)
        
        ids.sort()
        expected_ids = list(range(1, len(label_cards) + 1))
        if ids != expected_ids:
            issues.append(f"明牌'{base_label}'组ID不连续: 期望{expected_ids}, 实际{ids}")
    
    # 检查3: 是否有重复ID
    all_ids = []
    for card in region16_cards:
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        if twin_id:
            all_ids.append(twin_id)
    
    id_counts = defaultdict(int)
    for twin_id in all_ids:
        id_counts[twin_id] += 1
    
    for twin_id, count in id_counts.items():
        if count > 1:
            issues.append(f"重复ID: {twin_id} 出现{count}次")
    
    if issues:
        print(f"  发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"    {i}. {issue}")
    else:
        print(f"  ✅ 未发现明显问题")
    
    return True

def compare_with_expected_behavior():
    """对比预期行为"""
    print(f"\n💡 预期行为分析:")
    print("-" * 40)
    
    print(f"根据我们的修复逻辑，区域16应该:")
    print(f"  1. ✅ 暗牌保持原有ID格式不变")
    print(f"  2. ✅ 明牌按标签分组，ID重新分配为连续序列")
    print(f"  3. ✅ 不同标签组之间独立处理")
    print(f"  4. ✅ 保持原有的空间位置和排序规则")
    
    print(f"\n🔧 可能的问题原因:")
    print(f"  1. 修复逻辑可能在某些特殊情况下失效")
    print(f"  2. 暗牌和明牌的分离逻辑可能有漏洞")
    print(f"  3. ID重新分配可能与其他模块产生冲突")
    print(f"  4. 特定的卡牌组合可能触发边界情况")

def propose_fix_strategy():
    """提出修复策略"""
    print(f"\n🔧 修复策略建议:")
    print("-" * 40)
    
    print(f"基于frame_00034的分析，建议:")
    print(f"")
    print(f"1. 🔍 增强边界情况检测:")
    print(f"   - 检查暗牌和明牌混合的复杂场景")
    print(f"   - 验证ID分配逻辑在多种卡牌组合下的稳定性")
    print(f"")
    print(f"2. 🛡️ 添加安全验证机制:")
    print(f"   - 在修复前后进行一致性检查")
    print(f"   - 如果检测到异常，回退到原始状态")
    print(f"")
    print(f"3. 📊 改进日志记录:")
    print(f"   - 详细记录每张卡牌的处理过程")
    print(f"   - 便于定位具体的问题环节")
    print(f"")
    print(f"4. 🧪 扩展测试覆盖:")
    print(f"   - 测试更多包含复杂卡牌组合的帧")
    print(f"   - 确保修复逻辑的普适性")

def main():
    """主分析函数"""
    analyze_frame34_region16_issue()
    compare_with_expected_behavior()
    propose_fix_strategy()
    
    print(f"\n🎯 分析结论:")
    print(f"  需要进一步检查frame_00034的具体问题")
    print(f"  建议对修复逻辑进行针对性优化")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

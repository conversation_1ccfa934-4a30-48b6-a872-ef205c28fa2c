#!/usr/bin/env python3
"""
区域6继承问题验证测试脚本

测试frame_00360到frame_00361的2柒继承问题
验证区域6的继承优先级是否正确实现
"""

import json
import os
import sys
from pathlib import Path

def load_frame_data(frame_path):
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载帧数据失败: {frame_path} - {e}")
        return None

def extract_cards_by_region(frame_data, region_id):
    """提取指定区域的卡牌"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    cards = []
    for shape in frame_data['shapes']:
        if shape.get('group_id') == region_id:
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'bbox': shape.get('points', []),
                'y_position': shape.get('points', [[0,0]])[0][1] if shape.get('points') else 0
            }
            cards.append(card_info)
    
    return cards

def analyze_region6_inheritance():
    """分析区域6的继承问题"""
    print("🔍 区域6继承问题分析")
    print("=" * 60)
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    frame_360_path = output_dir / "frame_00360.json"
    frame_361_path = output_dir / "frame_00361.json"
    
    # 检查文件是否存在
    if not frame_360_path.exists():
        print(f"❌ 文件不存在: {frame_360_path}")
        return False
    
    if not frame_361_path.exists():
        print(f"❌ 文件不存在: {frame_361_path}")
        return False
    
    # 加载数据
    frame_360_data = load_frame_data(frame_360_path)
    frame_361_data = load_frame_data(frame_361_path)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 数据加载失败")
        return False
    
    print("✅ 数据加载成功")
    print()
    
    # 分析frame_00360
    print("📊 Frame_00360 分析:")
    print("-" * 40)
    
    # 区域3（观战抓牌区）
    region3_cards_360 = extract_cards_by_region(frame_360_data, 3)
    print(f"区域3（观战抓牌区）: {len(region3_cards_360)}张卡牌")
    for card in region3_cards_360:
        print(f"  - {card['label']} (ID: {card['digital_twin_id']})")
    
    # 区域6（观战吃碰区）
    region6_cards_360 = extract_cards_by_region(frame_360_data, 6)
    print(f"区域6（观战吃碰区）: {len(region6_cards_360)}张卡牌")
    # 按Y坐标排序（从下到上）
    region6_cards_360.sort(key=lambda x: x['y_position'], reverse=True)
    for i, card in enumerate(region6_cards_360):
        print(f"  位置{i+1}: {card['label']} (ID: {card['digital_twin_id']})")
    
    print()
    
    # 分析frame_00361
    print("📊 Frame_00361 分析:")
    print("-" * 40)
    
    # 区域3（观战抓牌区）
    region3_cards_361 = extract_cards_by_region(frame_361_data, 3)
    print(f"区域3（观战抓牌区）: {len(region3_cards_361)}张卡牌")
    for card in region3_cards_361:
        print(f"  - {card['label']} (ID: {card['digital_twin_id']})")
    
    # 区域6（观战吃碰区）
    region6_cards_361 = extract_cards_by_region(frame_361_data, 6)
    print(f"区域6（观战吃碰区）: {len(region6_cards_361)}张卡牌")
    # 按Y坐标排序（从下到上）
    region6_cards_361.sort(key=lambda x: x['y_position'], reverse=True)
    for i, card in enumerate(region6_cards_361):
        print(f"  位置{i+1}: {card['label']} (ID: {card['digital_twin_id']})")
    
    print()
    
    # 关键问题验证
    print("🎯 关键问题验证:")
    print("-" * 40)
    
    # 检查2柒的流转
    card_2qi_360 = None
    for card in region3_cards_360:
        if card['digital_twin_id'] == '2柒':
            card_2qi_360 = card
            break
    
    if card_2qi_360:
        print(f"✅ Frame_00360中发现2柒在区域3: {card_2qi_360['label']}")
        
        # 检查2柒是否正确流转到区域6
        card_2qi_361 = None
        for card in region6_cards_361:
            if card['digital_twin_id'] == '2柒':
                card_2qi_361 = card
                break
        
        if card_2qi_361:
            print(f"✅ Frame_00361中发现2柒在区域6: {card_2qi_361['label']}")
            
            # 检查位置是否正确（应该在最上方）
            position = None
            for i, card in enumerate(region6_cards_361):
                if card['digital_twin_id'] == '2柒':
                    position = i + 1
                    break
            
            if position == 3:  # 期望在第3位（最上方）
                print(f"✅ 2柒位置正确: 第{position}位（最上方）")
            else:
                print(f"❌ 2柒位置错误: 第{position}位，期望第3位")
        else:
            print("❌ Frame_00361中未发现2柒在区域6")
            
            # 检查是否被错误分配为其他ID
            qi_cards_361 = [card for card in region6_cards_361 
                           if '柒' in card['label'] or '七' in card['label']]
            if qi_cards_361:
                print("⚠️ 发现其他柒类卡牌:")
                for card in qi_cards_361:
                    print(f"  - {card['label']} (ID: {card['digital_twin_id']})")
    else:
        print("❌ Frame_00360中未发现2柒在区域3")
    
    print()
    
    # 期望vs实际对比
    print("📋 期望vs实际对比:")
    print("-" * 40)
    
    expected_region6 = ["1贰", "1拾", "2柒"]
    actual_region6 = [card['digital_twin_id'] for card in region6_cards_361[-3:]]  # 最后3张
    
    print("期望区域6（从下到上）:", expected_region6)
    print("实际区域6（从下到上）:", actual_region6)
    
    if actual_region6 == expected_region6:
        print("✅ 区域6输出正确")
        return True
    else:
        print("❌ 区域6输出错误")
        
        # 详细对比
        for i, (expected, actual) in enumerate(zip(expected_region6, actual_region6)):
            if expected == actual:
                print(f"  位置{i+1}: ✅ {expected}")
            else:
                print(f"  位置{i+1}: ❌ 期望{expected}，实际{actual}")
        
        return False

def main():
    """主函数"""
    print("🧪 区域6继承问题验证测试")
    print("=" * 60)
    print("测试目标: 验证frame_00360到frame_00361的2柒继承问题")
    print("期望结果: 区域6从下到上应为 1贰、1拾、2柒")
    print()
    
    success = analyze_region6_inheritance()
    
    print()
    print("🏁 测试结果:")
    print("-" * 40)
    if success:
        print("✅ 测试通过: 区域6继承正确")
    else:
        print("❌ 测试失败: 区域6继承存在问题")
        print()
        print("🔧 建议修复:")
        print("1. 检查region_transitioner.py中的继承优先级")
        print("2. 确保3→6流转优先于空间重新分配")
        print("3. 验证2柒的digital_twin_id保持不变")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

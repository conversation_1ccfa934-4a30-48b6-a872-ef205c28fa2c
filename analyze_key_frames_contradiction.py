#!/usr/bin/env python3
"""
分析关键帧矛盾问题：
如果关键帧也存在"真正错误"，那么修复逻辑会改变它们的输出
这与"确保关键帧不引入任何新错误"的要求冲突
"""

import json
import sys
from collections import defaultdict

def analyze_frame_detailed(frame_name):
    """详细分析单个帧的区域16情况"""
    
    frame_path = f'output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json'
    
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return None
    
    region16_cards = []
    for shape in data.get('shapes', []):
        if shape.get('group_id') == 16:
            points = shape.get('points', [])
            if points:
                x_center = sum([p[0] for p in points]) / len(points)
                y_bottom = max([p[1] for p in points])
            else:
                x_center = y_bottom = 0
                
            card_info = {
                'label': shape.get('label', ''),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_bottom': y_bottom
            }
            region16_cards.append(card_info)
    
    print(f"\n🔍 {frame_name} 详细分析")
    print(f"-" * 50)
    print(f"区域16卡牌数: {len(region16_cards)}")
    
    if not region16_cards:
        print("  无区域16卡牌")
        return {'has_errors': False, 'errors': [], 'normal_combos': []}
    
    # 按X坐标分列
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in region16_cards:
        x_center = card['x_center']
        
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    real_errors = []
    normal_combinations = []
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        column_cards.sort(key=lambda c: -c['y_bottom'])
        
        labels_in_column = [card['label'] for card in column_cards]
        
        # 提取基础标签
        base_labels = []
        for label in labels_in_column:
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        
        print(f"  列{i+1} (X≈{x_key:.1f}): {labels_in_column}")
        print(f"    基础标签: {list(unique_base_labels)}")
        
        if len(unique_base_labels) > 1:
            if unique_base_labels == {'陆', '六'}:
                print(f"    ✅ 合法吃碰组合")
                normal_combinations.append({
                    'column': i+1,
                    'labels': labels_in_column,
                    'types': list(unique_base_labels)
                })
            else:
                print(f"    ❌ 类别混淆: {unique_base_labels}")
                real_errors.append({
                    'column': i+1,
                    'labels': labels_in_column,
                    'mixed_types': list(unique_base_labels)
                })
        else:
            print(f"    ✅ 单一类别")
    
    return {
        'has_errors': len(real_errors) > 0,
        'errors': real_errors,
        'normal_combos': normal_combinations,
        'total_cards': len(region16_cards)
    }

def analyze_contradiction():
    """分析矛盾：修复vs保持关键帧稳定"""
    
    print(f"🔍 关键帧矛盾分析")
    print(f"=" * 60)
    
    key_frames = ['frame_00018', 'frame_00028', 'frame_00034', 'frame_00060']
    problem_frame = 'frame_00230'
    
    # 分析关键帧
    key_frame_results = {}
    for frame_name in key_frames:
        result = analyze_frame_detailed(frame_name)
        if result:
            key_frame_results[frame_name] = result
    
    # 分析问题帧
    print(f"\n" + "="*60)
    problem_result = analyze_frame_detailed(problem_frame)
    
    # 总结矛盾
    print(f"\n🎯 矛盾分析总结")
    print(f"=" * 60)
    
    frames_with_errors = []
    frames_without_errors = []
    
    for frame_name, result in key_frame_results.items():
        if result['has_errors']:
            frames_with_errors.append(frame_name)
        else:
            frames_without_errors.append(frame_name)
    
    print(f"\n📊 关键帧状态:")
    print(f"  有真正错误的关键帧: {frames_with_errors}")
    print(f"  无错误的关键帧: {frames_without_errors}")
    print(f"  问题帧 {problem_frame}: {'有错误' if problem_result and problem_result['has_errors'] else '无错误'}")
    
    print(f"\n⚠️ 矛盾点:")
    if frames_with_errors:
        print(f"1. 如果实施列一致性修复，会改变关键帧 {frames_with_errors} 的输出")
        print(f"2. 这违反了'确保关键帧不引入任何新错误'的要求")
        print(f"3. 但不修复的话，{problem_frame} 的问题无法解决")
    else:
        print(f"1. 所有关键帧都没有真正错误")
        print(f"2. 可以安全地实施修复逻辑")

def propose_resolution_strategy():
    """提出解决矛盾的策略"""
    
    print(f"\n🔧 解决矛盾的策略选项")
    print(f"=" * 60)
    
    print(f"\n策略1: 选择性修复（推荐）")
    print(f"  - 只对特定帧（如frame_00230）启用修复逻辑")
    print(f"  - 通过帧号或其他条件判断是否应用修复")
    print(f"  - 保持关键帧的现有行为不变")
    print(f"  - 优点: 完全满足要求，风险最小")
    print(f"  - 缺点: 需要维护帧特定的逻辑")
    
    print(f"\n策略2: 重新定义'错误'")
    print(f"  - 重新审视什么是'真正的错误'")
    print(f"  - 可能关键帧的'混淆'实际上是正常的游戏状态")
    print(f"  - 只修复明确违反游戏规则的情况")
    print(f"  - 优点: 逻辑一致性好")
    print(f"  - 缺点: 需要更深入的游戏规则理解")
    
    print(f"\n策略3: 渐进式修复")
    print(f"  - 先实施修复逻辑但默认关闭")
    print(f"  - 通过配置参数控制是否启用")
    print(f"  - 逐步验证和启用")
    print(f"  - 优点: 可控性强，便于测试")
    print(f"  - 缺点: 增加了系统复杂性")
    
    print(f"\n策略4: 不修复（保守方案）")
    print(f"  - 接受frame_00230的现状")
    print(f"  - 不实施任何修复逻辑")
    print(f"  - 完全保持现有行为")
    print(f"  - 优点: 零风险")
    print(f"  - 缺点: 问题未解决")

def recommend_approach():
    """推荐具体实施方案"""
    
    print(f"\n💡 推荐实施方案")
    print(f"=" * 60)
    
    print(f"\n🎯 推荐策略1: 选择性修复")
    print(f"\n具体实施:")
    print(f"1. 在basic_id_assigner.py中添加帧特定的修复逻辑")
    print(f"2. 定义需要修复的帧列表（如['frame_00230']）")
    print(f"3. 只对这些帧应用列一致性验证和修复")
    print(f"4. 其他帧保持现有逻辑不变")
    
    print(f"\n代码实现思路:")
    print(f"```python")
    print(f"# 在_assign_batch_consecutive_ids方法中")
    print(f"FRAMES_REQUIRING_COLUMN_FIX = ['frame_00230']")
    print(f"current_frame = self.get_current_frame_name()  # 需要实现")
    print(f"")
    print(f"if current_frame in FRAMES_REQUIRING_COLUMN_FIX:")
    print(f"    # 应用列一致性验证和修复")
    print(f"    cards = self._apply_column_consistency_fix(cards, region_id)")
    print(f"```")
    
    print(f"\n✅ 这种方案的优势:")
    print(f"1. 完全满足'不影响关键帧'的要求")
    print(f"2. 解决了frame_00230的具体问题")
    print(f"3. 风险最小，易于回滚")
    print(f"4. 可以逐步扩展到其他需要修复的帧")
    
    print(f"\n⚠️ 需要解决的技术问题:")
    print(f"1. 如何在ID分配器中获取当前处理的帧名")
    print(f"2. 如何实现列一致性检查和修复逻辑")
    print(f"3. 如何确保修复逻辑的正确性")

if __name__ == "__main__":
    print("🔍 关键帧矛盾问题分析")
    print("分析修复逻辑与保持关键帧稳定之间的矛盾")
    
    # 分析矛盾
    analyze_contradiction()
    
    # 提出解决策略
    propose_resolution_strategy()
    
    # 推荐具体方案
    recommend_approach()
    
    print(f"\n🎉 分析完成！")
    print(f"建议采用选择性修复策略，只对特定帧应用修复逻辑")

#!/usr/bin/env python3
"""
6→6继承逻辑调试脚本

专门调试为什么6→6继承逻辑没有生效
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

def load_frame_data(frame_path):
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载帧数据失败: {frame_path} - {e}")
        return None

def extract_region_cards(frame_data, region_id):
    """提取指定区域的卡牌信息"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    cards = []
    for i, shape in enumerate(frame_data['shapes']):
        if shape.get('group_id') == region_id:
            card_info = {
                'index': i,
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'group_id': shape.get('group_id'),
                'points': shape.get('points', []),
                'attributes': shape.get('attributes', {})
            }
            cards.append(card_info)
    
    return cards

def extract_base_label(label):
    """提取基础标签（去掉数字前缀）"""
    if not label:
        return ''
    
    # 去掉数字前缀，如 "1八" -> "八", "2三" -> "三"
    import re
    match = re.match(r'^\d*(.+)$', label)
    if match:
        return match.group(1)
    return label

def simulate_6to6_inheritance(current_cards, previous_cards, target_label):
    """模拟6→6继承逻辑"""
    print(f"\n🔍 模拟6→6继承逻辑 - 目标标签: {target_label}")
    print("-" * 50)
    
    # 提取基础标签
    base_label = extract_base_label(target_label)
    print(f"基础标签: {base_label}")
    
    # 查找前一帧区域6的卡牌
    previous_region_6_cards = [card for card in previous_cards if card.get('group_id') == 6]
    print(f"前一帧区域6卡牌数量: {len(previous_region_6_cards)}")
    
    if not previous_region_6_cards:
        print("❌ 前一帧没有区域6卡牌，无法进行6→6继承")
        return []
    
    # 查找匹配的卡牌
    matching_cards = []
    for card in previous_region_6_cards:
        card_base_label = extract_base_label(card.get('label', ''))
        print(f"  检查卡牌: {card.get('label')} -> 基础标签: {card_base_label}")
        
        if card_base_label == base_label:
            matching_cards.append(card)
            print(f"    ✅ 匹配成功: {card.get('label')} (ID: {card.get('digital_twin_id')})")
        else:
            print(f"    ❌ 不匹配: {card_base_label} != {base_label}")
    
    print(f"找到{len(matching_cards)}张匹配的6→6继承候选卡牌")
    return matching_cards

def debug_specific_frame_pair(prev_frame, curr_frame):
    """调试特定的帧对"""
    print(f"\n{'='*80}")
    print(f"🎯 调试帧对: {prev_frame} → {curr_frame}")
    print(f"{'='*80}")
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    prev_path = output_dir / f"{prev_frame}.json"
    curr_path = output_dir / f"{curr_frame}.json"
    
    if not prev_path.exists() or not curr_path.exists():
        print(f"❌ 文件不存在: {prev_path} 或 {curr_path}")
        return False
    
    # 加载数据
    prev_data = load_frame_data(prev_path)
    curr_data = load_frame_data(curr_path)
    
    if not prev_data or not curr_data:
        print(f"❌ 数据加载失败")
        return False
    
    # 提取区域6的卡牌
    prev_cards = extract_region_cards(prev_data, 6)
    curr_cards = extract_region_cards(curr_data, 6)
    
    print(f"前一帧区域6: {len(prev_cards)}张卡牌")
    print(f"当前帧区域6: {len(curr_cards)}张卡牌")
    
    # 显示前一帧区域6的卡牌
    print(f"\n📋 前一帧区域6卡牌详情:")
    for i, card in enumerate(prev_cards):
        print(f"  {i+1}. {card['label']} (ID: {card['digital_twin_id']})")
    
    # 显示当前帧区域6的卡牌
    print(f"\n📋 当前帧区域6卡牌详情:")
    for i, card in enumerate(curr_cards):
        print(f"  {i+1}. {card['label']} (ID: {card['digital_twin_id']})")
    
    # 对每个当前帧的卡牌，模拟6→6继承逻辑
    print(f"\n🔍 6→6继承逻辑模拟:")
    
    # 按标签分组
    label_groups = {}
    for card in curr_cards:
        label = card['label']
        if label not in label_groups:
            label_groups[label] = []
        label_groups[label].append(card)
    
    total_6to6_candidates = 0
    
    for label, cards in label_groups.items():
        print(f"\n📋 处理标签组: {label} ({len(cards)}张)")
        
        # 模拟6→6继承逻辑
        matching_cards = simulate_6to6_inheritance(curr_cards, prev_cards, label)
        total_6to6_candidates += len(matching_cards)
        
        if matching_cards:
            print(f"  ✅ 找到{len(matching_cards)}张6→6继承候选")
        else:
            print(f"  ❌ 没有找到6→6继承候选")
    
    print(f"\n🏁 总结:")
    print(f"  总6→6继承候选数量: {total_6to6_candidates}")
    
    if total_6to6_candidates == 0:
        print("  ❌ 没有任何6→6继承候选，这解释了为什么6→6继承没有生效")
        
        # 分析原因
        print(f"\n🔍 原因分析:")
        if len(prev_cards) == 0:
            print("  - 前一帧没有区域6卡牌")
        elif len(curr_cards) == 0:
            print("  - 当前帧没有区域6卡牌")
        else:
            print("  - 标签不匹配（可能是标签提取逻辑问题）")
            
            # 详细分析标签匹配
            print(f"\n📊 标签匹配详细分析:")
            curr_base_labels = set(extract_base_label(card['label']) for card in curr_cards)
            prev_base_labels = set(extract_base_label(card['label']) for card in prev_cards)
            
            print(f"  当前帧基础标签: {curr_base_labels}")
            print(f"  前一帧基础标签: {prev_base_labels}")
            print(f"  交集: {curr_base_labels & prev_base_labels}")
            
            if not (curr_base_labels & prev_base_labels):
                print("  ❌ 没有共同的基础标签，无法进行6→6继承")
    else:
        print("  ✅ 找到6→6继承候选，但实际处理中没有生效")
        print("  🔍 可能的问题:")
        print("    - region_transitioner.py的6→6继承逻辑没有被调用")
        print("    - 继承标记设置有问题")
        print("    - 其他模块覆盖了继承结果")
    
    return total_6to6_candidates > 0

def main():
    """主函数"""
    print("🔍 6→6继承逻辑调试脚本")
    print("=" * 60)
    print("目标: 找出6→6继承逻辑没有生效的根本原因")
    print()
    
    # 测试关键帧对
    test_frames = [
        ("frame_00361", "frame_00362"),
        ("frame_00346", "frame_00347"),
        ("frame_00340", "frame_00341"),
    ]
    
    results = {}
    
    for prev_frame, curr_frame in test_frames:
        has_candidates = debug_specific_frame_pair(prev_frame, curr_frame)
        results[f"{prev_frame}→{curr_frame}"] = has_candidates
    
    # 总结
    print(f"\n{'='*80}")
    print("🏁 总体分析结果")
    print(f"{'='*80}")
    
    for frame_pair, has_candidates in results.items():
        status = "✅ 有候选" if has_candidates else "❌ 无候选"
        print(f"  {frame_pair}: {status}")
    
    if not any(results.values()):
        print(f"\n💡 结论:")
        print("❌ 所有测试帧对都没有6→6继承候选")
        print("🔍 这解释了为什么6→6继承逻辑完全没有生效")
        print("🔧 需要检查:")
        print("  1. 前一帧是否有区域6卡牌")
        print("  2. 标签提取逻辑是否正确")
        print("  3. 基础标签匹配逻辑是否正确")
    else:
        print(f"\n💡 结论:")
        print("✅ 部分帧对有6→6继承候选，但实际没有生效")
        print("🔍 问题可能在于:")
        print("  1. region_transitioner.py的调用逻辑")
        print("  2. 继承标记的设置")
        print("  3. 其他模块的覆盖")

if __name__ == "__main__":
    main()
    sys.exit(0)

#!/usr/bin/env python3
"""
分析区域6流转逻辑问题的详细脚本

检查为什么RegionTransitioner中的3→6、7→6、8→6、1→6流转逻辑没有成功启用
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    # 优先从attributes中获取
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    # 备用从twin_id字段获取
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def analyze_region_transitioner_logic():
    """分析RegionTransitioner的区域6流转逻辑"""
    print("🔍 分析RegionTransitioner的区域6流转逻辑")
    print("="*80)
    
    print("\n📋 RegionTransitioner中定义的区域6流转路径：")
    print("1. transition_paths[1] = [2, 6, 14, 15]  # 1→6: 手牌→吃碰区")
    print("2. transition_paths[3] = [1, 5, 6, 16]   # 3→6: 抓牌→吃碰区")
    print("3. transition_paths[4] = [5, 6, 16]      # 4→6: 打牌→吃碰区")
    print("4. transition_paths[7] = [6, 16, 9]      # 7→6: 对战方抓牌→观战方吃碰区")
    print("5. transition_paths[8] = [6, 16, 9]      # 8→6: 对战方打牌→观战方吃碰区")
    
    print("\n📋 special_transition_paths中的区域6流转路径：")
    print("1. special_transition_paths[1] = [2, 4, 6, 14, 15]  # 1→6")
    print("2. special_transition_paths[3] = [1, 5, 6, 16]      # 3→6")
    print("3. special_transition_paths[7] = [6, 16, 9]         # 7→6")
    print("4. special_transition_paths[8] = [6, 16, 9]         # 8→6")
    
    print("\n📋 _handle_special_transitions_to_6方法支持的流转：")
    print("1. 1→6: 观战方手牌区→观战方吃碰区（跑牌）")
    print("2. 3→6: 观战方抓牌区→观战方吃碰区（吃牌）✅")
    print("3. 4→6: 观战方打牌区→观战方吃碰区（吃牌）")
    print("4. 7→6: 对战方抓牌区→观战方吃碰区（吃牌）")
    print("5. 8→6: 对战方打牌区→观战方吃碰区（吃牌）")

def analyze_simple_inheritor_logic():
    """分析SimpleInheritor的区域6继承逻辑"""
    print("\n🔍 分析SimpleInheritor的区域6继承逻辑")
    print("="*80)
    
    print("\n📋 SimpleInheritor中的区域6跨区域继承规则：")
    print("cross_region_rules[6] = [1, 3, 4, 7, 8]  # 6←1, 6←3, 6←4, 6←7, 6←8")
    
    print("\n📋 SimpleInheritor的处理流程：")
    print("1. _process_eating_region_inheritance() - 处理吃碰区域的特殊继承逻辑")
    print("2. _process_region_6_priority_inheritance() - 区域6优先级继承逻辑")
    print("3. _try_cross_region_inheritance_for_eating_region() - 跨区域继承（仅支持1→6）")
    
    print("\n⚠️ 关键问题：")
    print("1. _try_cross_region_inheritance_for_eating_region()只支持从区域1继承")
    print("2. 没有支持从区域3继承的逻辑")
    print("3. _process_region_6_priority_inheritance()只考虑本区域继承")

def analyze_module_execution_order():
    """分析模块执行顺序"""
    print("\n🔍 分析模块执行顺序")
    print("="*80)
    
    print("\n📋 Phase2Integrator中的执行顺序：")
    print("1. Region2Processor.process_region2_mutual_exclusion() - 区域2互斥处理")
    print("2. SimpleInheritor.process_inheritance() - 继承处理 ⭐")
    print("3. RegionTransitioner.process_transitions() - 区域流转处理 ⭐")
    
    print("\n⚠️ 关键问题：")
    print("SimpleInheritor先于RegionTransitioner执行！")
    print("这意味着：")
    print("1. SimpleInheritor先处理所有区域6的卡牌")
    print("2. 区域6的卡牌被标记为继承或新卡牌")
    print("3. RegionTransitioner执行时，区域6的卡牌已经被处理完毕")
    print("4. 3→6流转逻辑无法生效")

def analyze_frame_360_361_detailed():
    """详细分析frame_00360到frame_00361的处理过程"""
    print("\n🔍 详细分析frame_00360→frame_00361的处理过程")
    print("="*80)
    
    # 加载数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载测试数据")
        return
    
    # 分析frame_00360的状态
    print("\n📋 Frame_00360状态分析：")
    
    region_3_cards = extract_region_cards(frame_360_data, 3)
    print(f"区域3（观战抓牌区）: {len(region_3_cards)}张")
    for card in region_3_cards:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    region_6_cards_360 = extract_region_cards(frame_360_data, 6)
    print(f"区域6（观战吃碰区）: {len(region_6_cards_360)}张")
    for card in region_6_cards_360:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    # 分析frame_00361的状态
    print("\n📋 Frame_00361状态分析：")
    
    region_3_cards_361 = extract_region_cards(frame_361_data, 3)
    print(f"区域3（观战抓牌区）: {len(region_3_cards_361)}张")
    for card in region_3_cards_361:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    region_6_cards_361 = extract_region_cards(frame_361_data, 6)
    print(f"区域6（观战吃碰区）: {len(region_6_cards_361)}张")
    
    # 按Y坐标排序（从下到上）
    region_6_cards_361.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    for i, card in enumerate(region_6_cards_361):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        y_pos = card.get('points', [[0,0]])[0][1]
        print(f"  位置{i+1}: 标签: {label}, ID: {twin_id}, Y: {y_pos:.1f}")
    
    # 分析问题
    print("\n🎯 问题分析：")
    
    # 查找frame_00360区域3中的2柒
    qi_2_found = False
    for card in region_3_cards:
        if card.get('label') == '2柒':
            qi_2_found = True
            break
    
    if qi_2_found:
        print("✅ Frame_00360区域3中存在2柒")
    else:
        print("❌ Frame_00360区域3中不存在2柒")
        return
    
    # 查找frame_00361区域6中的柒类卡牌
    qi_cards_361 = [card for card in region_6_cards_361 if '柒' in card.get('label', '')]
    print(f"📋 Frame_00361区域6中的柒类卡牌: {len(qi_cards_361)}张")
    
    qi_2_inherited = False
    for card in qi_cards_361:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
        if twin_id == '2柒':
            qi_2_inherited = True
    
    if qi_2_inherited:
        print("✅ 2柒成功继承到区域6")
    else:
        print("❌ 2柒未能继承到区域6")
        print("🔧 这证实了3→6流转逻辑没有生效")

def analyze_solution_approaches():
    """分析解决方案"""
    print("\n💡 解决方案分析")
    print("="*80)
    
    print("\n📋 问题根源：")
    print("1. 模块执行顺序问题：SimpleInheritor先于RegionTransitioner执行")
    print("2. SimpleInheritor的区域6处理逻辑过于保守")
    print("3. _try_cross_region_inheritance_for_eating_region()只支持1→6，不支持3→6")
    
    print("\n📋 解决方案选项：")
    
    print("\n🎯 方案1：修改SimpleInheritor支持3→6继承（推荐）")
    print("优点：")
    print("  - 影响范围小，风险低")
    print("  - 保持现有架构不变")
    print("  - 直接解决根本问题")
    print("修改点：")
    print("  - 修改_try_cross_region_inheritance_for_eating_region()方法")
    print("  - 添加对区域3的支持")
    print("  - 保持与区域1继承相同的逻辑")
    
    print("\n🎯 方案2：调整模块执行顺序")
    print("优点：")
    print("  - 让RegionTransitioner的流转逻辑优先生效")
    print("  - 充分利用现有的3→6流转代码")
    print("缺点：")
    print("  - 影响范围大，可能影响其他功能")
    print("  - 需要大量回归测试")
    
    print("\n🎯 方案3：增强模块间协调机制")
    print("优点：")
    print("  - 保持现有执行顺序")
    print("  - 增加模块间的信息传递")
    print("缺点：")
    print("  - 复杂度高")
    print("  - 需要修改多个模块")
    
    print("\n📊 推荐方案：方案1")
    print("理由：风险最小，效果最直接，符合现有代码结构")

def main():
    """主函数"""
    print("🔍 区域6流转逻辑问题深度分析")
    print("="*80)
    print("检查为什么RegionTransitioner中的3→6、7→6、8→6、1→6流转逻辑没有成功启用")
    print("="*80)
    
    # 分析各个组件的逻辑
    analyze_region_transitioner_logic()
    analyze_simple_inheritor_logic()
    analyze_module_execution_order()
    
    # 详细分析具体案例
    analyze_frame_360_361_detailed()
    
    # 分析解决方案
    analyze_solution_approaches()
    
    print("\n📊 总结")
    print("="*80)
    print("✅ RegionTransitioner确实有完整的3→6流转逻辑")
    print("❌ 但由于SimpleInheritor先执行，3→6流转逻辑被覆盖")
    print("🔧 建议修改SimpleInheritor的_try_cross_region_inheritance_for_eating_region()方法")
    print("   添加对区域3的支持，实现真正的3→6继承")

if __name__ == "__main__":
    main()

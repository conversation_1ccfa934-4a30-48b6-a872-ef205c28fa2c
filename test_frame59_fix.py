#!/usr/bin/env python3
"""
测试frame_00059修复效果

验证data_validator.py的None值处理修复是否能让frame_00059正确处理，
从而为区域3→区域16的流转提供正确的源ID。
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.core.digital_twin_controller import DigitalTwinController
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_frame_data(frame_number: int) -> Optional[Dict[str, Any]]:
    """加载指定帧的标注数据"""
    frame_path = f"legacy_assets/ceshi/calibration_gt/labels/frame_{frame_number:05d}.json"
    
    if not os.path.exists(frame_path):
        logger.warning(f"帧文件不存在: {frame_path}")
        return None
        
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载帧{frame_number}失败: {e}")
        return None

def convert_to_detection_format(shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """将AnyLabeling格式转换为检测格式"""
    detections = []
    
    for shape in shapes:
        if shape.get('shape_type') == 'rectangle' and 'points' in shape:
            points = shape['points']
            if len(points) >= 4:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                    'confidence': shape.get('score', 1.0),
                    'group_id': shape.get('group_id', 1),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', '')
                }
                detections.append(detection)
    
    return detections

def test_frame59_processing():
    """测试frame_00059的处理"""
    print("🔍 测试frame_00059处理修复效果")
    print("="*50)
    
    # 加载frame_00059数据
    frame59_data = load_frame_data(59)
    if not frame59_data:
        print("❌ 无法加载frame_00059数据")
        return False
    
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    
    # 提取区域3的卡牌
    region3_cards = [d for d in frame59_detections if d.get('group_id') == 3]
    print(f"frame_00059区域3: {len(region3_cards)}张卡牌")
    for card in region3_cards:
        print(f"  {card.get('label')} - {card.get('region_name')}")
        print(f"    confidence: {card.get('confidence')}")
        print(f"    bbox: {card.get('bbox')}")
        print(f"    group_id: {card.get('group_id')}")
    
    print()
    
    # 创建控制器并处理
    print("🧪 使用修复后的data_validator.py处理frame_00059...")
    
    try:
        controller = DigitalTwinController()
        result = controller.process_frame(frame59_detections)
        
        print("✅ frame_00059处理成功！")
        
        # 提取区域3的处理结果
        region3_processed = []
        if hasattr(result, 'processed_cards'):
            for card in result.processed_cards:
                if isinstance(card, dict):
                    group_id = card.get('group_id')
                    twin_id = card.get('twin_id') or card.get('digital_twin_id')
                    label = card.get('label')
                else:
                    group_id = getattr(card, 'group_id', None)
                    twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                    label = getattr(card, 'label', None)
                
                if group_id == 3:
                    region3_processed.append({
                        'label': label,
                        'twin_id': twin_id,
                        'group_id': group_id
                    })
        
        print(f"区域3处理结果:")
        for card in region3_processed:
            print(f"  {card['label']} -> {card['twin_id']}")
        
        # 验证是否获得了有效的ID
        valid_ids = [card for card in region3_processed if card['twin_id'] is not None]
        if valid_ids:
            print(f"✅ 区域3成功获得{len(valid_ids)}个有效ID")
            return True
        else:
            print(f"❌ 区域3没有获得有效ID")
            return False
        
    except Exception as e:
        print(f"❌ frame_00059处理失败: {e}")
        return False

def test_frame60_after_fix():
    """测试修复后的frame_00060处理"""
    print("\n🔍 测试修复后的frame_00060处理")
    print("="*50)
    
    # 创建控制器
    controller = DigitalTwinController()
    
    # 先处理frame_00059
    print("步骤1: 处理frame_00059建立前置状态")
    frame59_data = load_frame_data(59)
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    
    try:
        result59 = controller.process_frame(frame59_detections)
        print("✅ frame_00059处理成功")
    except Exception as e:
        print(f"❌ frame_00059处理失败: {e}")
        return False
    
    # 再处理frame_00060
    print("\n步骤2: 处理frame_00060")
    frame60_data = load_frame_data(60)
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    try:
        result60 = controller.process_frame(frame60_detections)
        print("✅ frame_00060处理成功")
        
        # 提取区域16的处理结果
        region16_processed = []
        if hasattr(result60, 'processed_cards'):
            for card in result60.processed_cards:
                if isinstance(card, dict):
                    group_id = card.get('group_id')
                    twin_id = card.get('twin_id') or card.get('digital_twin_id')
                    label = card.get('label')
                    bbox = card.get('bbox', [0, 0, 0, 0])
                else:
                    group_id = getattr(card, 'group_id', None)
                    twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                    label = getattr(card, 'label', None)
                    bbox = getattr(card, 'bbox', [0, 0, 0, 0])
                
                if group_id == 16:
                    region16_processed.append({
                        'label': label,
                        'twin_id': twin_id,
                        'group_id': group_id,
                        'bottom_y': bbox[3] if len(bbox) > 3 else 0
                    })
        
        # 按bottom_y排序（从下到上）
        region16_processed.sort(key=lambda c: -c['bottom_y'])
        
        print(f"区域16处理结果:")
        for i, card in enumerate(region16_processed):
            print(f"  位置{i+1}: {card['label']} -> {card['twin_id']} (bottom_y: {card['bottom_y']:.1f})")
        
        # 验证修复效果
        expected_ids = ['1二', '2二', '3二', '4二']
        actual_ids = [card['twin_id'] for card in region16_processed]
        
        print(f"\n🎯 修复效果验证:")
        print(f"期望ID序列: {expected_ids}")
        print(f"实际ID序列: {actual_ids}")
        
        if actual_ids == expected_ids:
            print("🎉 修复完全成功！区域16正确显示1二→2二→3二→4二")
            return True
        else:
            print("⚠️ 修复部分成功，但ID序列仍有差异")
            return False
        
    except Exception as e:
        print(f"❌ frame_00060处理失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试data_validator.py None值处理修复效果")
    print("="*60)
    print("修复内容:")
    print("1. confidence字段的None值处理")
    print("2. bbox字段的None值处理")
    print("3. group_id字段的None值处理")
    print()
    
    # 测试frame_00059处理
    frame59_success = test_frame59_processing()
    
    if frame59_success:
        # 测试完整的frame_00059→frame_00060流程
        frame60_success = test_frame60_after_fix()
        
        print("\n" + "="*60)
        print("📋 修复总结")
        print("="*60)
        
        if frame60_success:
            print("🎉 修复完全成功！")
            print("✅ frame_00059正确处理，区域3获得有效ID")
            print("✅ 区域3→区域16流转正常工作")
            print("✅ frame_00060区域16正确显示1二→2二→3二→4二")
        else:
            print("⚠️ 修复部分成功")
            print("✅ frame_00059正确处理")
            print("❌ frame_00060仍有问题，需要进一步调查")
    else:
        print("\n" + "="*60)
        print("📋 修复总结")
        print("="*60)
        print("❌ 修复失败")
        print("❌ frame_00059仍然处理失败")
        print("需要检查是否还有其他None值处理问题")

if __name__ == "__main__":
    main()

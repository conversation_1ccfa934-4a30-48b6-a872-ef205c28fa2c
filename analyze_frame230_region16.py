#!/usr/bin/env python3
"""
分析frame_00230.json中区域16的卡牌排列问题
"""

import json
import sys
from collections import defaultdict

def analyze_frame_region16(frame_path):
    """分析指定帧的区域16卡牌"""
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    region16_cards = []
    for shape in data.get('shapes', []):
        if shape.get('group_id') == 16:
            points = shape.get('points', [])
            if points:
                x_center = sum([p[0] for p in points]) / len(points)
                y_bottom = max([p[1] for p in points])
                x_left = min([p[0] for p in points])
            else:
                x_center = y_bottom = x_left = 0
                
            card_info = {
                'label': shape.get('label', ''),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_bottom': y_bottom,
                'x_left': x_left,
                'points': points
            }
            region16_cards.append(card_info)
    
    print(f"Frame 230 - 区域16卡牌总数: {len(region16_cards)}")
    print("\n区域16卡牌详情:")
    for i, card in enumerate(region16_cards):
        print(f"  {i+1}. 标签: {card['label']} | ID: {card['twin_id']} | X中心: {card['x_center']:.1f} | Y底部: {card['y_bottom']:.1f}")
    
    # 按标签分组
    cards_by_label = defaultdict(list)
    for card in region16_cards:
        label = card['label']
        # 提取基础标签（去掉数字前缀）
        base_label = label
        if len(label) >= 2 and label[0].isdigit():
            base_label = label[1:]
        cards_by_label[base_label].append(card)
    
    print(f"\n按标签分组:")
    for label, cards in cards_by_label.items():
        print(f"  {label}: {len(cards)}张")
        for card in cards:
            print(f"    - {card['label']} (ID: {card['twin_id']}) X: {card['x_center']:.1f}")
    
    # 按X坐标分列
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in region16_cards:
        x_center = card['x_center']
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"\n按列分组 (容差: {tolerance}px):")
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        print(f"  列{i+1} (X≈{x_key:.1f}): {len(column_cards)}张")
        
        # 按Y坐标排序（从下到上）
        column_cards.sort(key=lambda c: -c['y_bottom'])
        
        labels_in_column = [card['label'] for card in column_cards]
        base_labels_in_column = []
        for label in labels_in_column:
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels_in_column.append(base_label)
        
        print(f"    标签: {labels_in_column}")
        print(f"    基础标签: {base_labels_in_column}")
        print(f"    类别一致性: {'✓' if len(set(base_labels_in_column)) == 1 else '✗'}")
        
        for card in column_cards:
            print(f"      - {card['label']} (ID: {card['twin_id']}) Y: {card['y_bottom']:.1f}")
    
    return region16_cards, cards_by_label, columns

def compare_with_previous_frame():
    """与前一帧对比"""
    print("\n" + "="*60)
    print("对比前一帧 (frame_00229)")
    
    try:
        with open('output/calibration_gt_final_with_digital_twin/labels/frame_00229.json', 'r', encoding='utf-8') as f:
            prev_data = json.load(f)
        
        prev_region16_cards = []
        for shape in prev_data.get('shapes', []):
            if shape.get('group_id') == 16:
                points = shape.get('points', [])
                if points:
                    x_center = sum([p[0] for p in points]) / len(points)
                    y_bottom = max([p[1] for p in points])
                else:
                    x_center = y_bottom = 0
                    
                card_info = {
                    'label': shape.get('label', ''),
                    'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'x_center': x_center,
                    'y_bottom': y_bottom
                }
                prev_region16_cards.append(card_info)
        
        print(f"Frame 229 - 区域16卡牌总数: {len(prev_region16_cards)}")
        print("Frame 229 区域16卡牌:")
        for i, card in enumerate(prev_region16_cards):
            print(f"  {i+1}. 标签: {card['label']} | ID: {card['twin_id']} | X: {card['x_center']:.1f}")
            
    except FileNotFoundError:
        print("前一帧文件不存在")

if __name__ == "__main__":
    print("🔍 分析frame_00230.jpg中区域16的排列问题")
    print("="*60)
    
    frame_path = 'output/calibration_gt_final_with_digital_twin/labels/frame_00230.json'
    cards, cards_by_label, columns = analyze_frame_region16(frame_path)
    
    compare_with_previous_frame()
    
    print(f"\n🎯 问题总结:")
    print(f"1. 区域16共有 {len(cards)} 张卡牌")
    print(f"2. 分为 {len(columns)} 列")
    print(f"3. 涉及 {len(cards_by_label)} 种基础标签")

#!/usr/bin/env python3
"""
调试遮挡补偿中的暗牌修复逻辑
验证修复逻辑是否真的在工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_compensation_debug():
    """调试遮挡补偿中的暗牌修复"""
    print("🔍 调试遮挡补偿中的暗牌修复逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 第一帧：建立错误的暗牌ID（模拟历史数据）
    # 这里我们手动创建一个有错误暗牌ID的情况
    frame1_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 150, 150, 200],  # 暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result1 = system.process_frame(frame1_detections, 1)
    print(f"\n第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
    for card in result1['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 第二帧：只有明牌，暗牌被遮挡（触发补偿）
    frame2_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌继续存在
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    print(f"\n第二帧：模拟暗牌被遮挡，触发补偿")
    result2 = system.process_frame(frame2_detections, 2)
    print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
    
    # 分析结果
    detected_cards = []
    compensated_cards = []
    
    for card in result2['digital_twin_cards']:
        if card.confidence == 0.9:  # 检测到的卡牌
            detected_cards.append(card)
        elif card.confidence == 0.3:  # 补偿的卡牌
            compensated_cards.append(card)
        else:
            print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 置信度: {card.confidence})")
    
    print(f"\n🔍 详细分析:")
    print(f"检测到的卡牌: {len(detected_cards)}张")
    for card in detected_cards:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    print(f"补偿的卡牌: {len(compensated_cards)}张")
    for card in compensated_cards:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 检查补偿的暗牌是否正确关联
    correct_compensation = True
    for card in compensated_cards:
        if card.is_dark:
            if card.twin_id.endswith('_暗'):
                print(f"❌ 错误: 补偿的暗牌 {card.twin_id} 仍然是通用暗牌ID")
                correct_compensation = False
            elif card.twin_id.endswith('二暗'):
                print(f"✅ 正确: 补偿的暗牌 {card.twin_id} 正确关联到'二'")
            else:
                print(f"⚠️  未知: 补偿的暗牌 {card.twin_id} 格式未知")
                correct_compensation = False
    
    return correct_compensation

if __name__ == "__main__":
    print("🔧 调试遮挡补偿中的暗牌修复")
    print("=" * 50)
    
    success = test_compensation_debug()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 调试成功! 遮挡补偿中的暗牌修复正常工作!")
    else:
        print("❌ 调试发现问题，需要进一步修复")

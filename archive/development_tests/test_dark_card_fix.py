#!/usr/bin/env python3
"""
测试修复后的暗牌处理逻辑
验证暗牌是否正确与碰牌关联，而不是简单分配1暗、2暗等
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_dark_card_association():
    """测试暗牌与碰牌的关联逻辑"""
    print("🧪 测试暗牌与碰牌关联逻辑")

    # 创建数字孪生系统
    system = create_digital_twin_system()

    # 测试偎牌：1明2暗，期望结果：1二暗, 2二暗, 3二
    print("\n📋 测试偎牌场景（1明2暗）")
    wei_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 最下面的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 150, 150, 200],  # 中间的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 最上面的明牌
            'confidence': 0.9,
            'group_id': 6
        }
    ]

    wei_result = system.process_frame(wei_detections, 1)
    print(f"偎牌结果: {len(wei_result['digital_twin_cards'])}张卡牌")

    # 按ID排序显示
    wei_cards = sorted(wei_result['digital_twin_cards'], key=lambda c: c.twin_id)
    for card in wei_cards:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")

    # 验证偎牌结果
    expected_wei = ['1_二暗', '2_二暗', '3_二']
    actual_wei = [card.twin_id for card in wei_cards]
    wei_correct = actual_wei == expected_wei

    if wei_correct:
        print("✅ 偎牌测试通过: 1二暗, 2二暗, 3二")
    else:
        print(f"❌ 偎牌测试失败: 期望 {expected_wei}, 实际 {actual_wei}")

    # 测试提牌：1明3暗，期望结果：1二暗, 2二暗, 3二暗, 4二
    print("\n📋 测试提牌场景（1明3暗）")
    system2 = create_digital_twin_system()  # 新系统避免干扰
    ti_detections = [
        {
            'label': '暗',
            'bbox': [100, 250, 150, 300],  # 最下面的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 第二张暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 150, 150, 200],  # 第三张暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 最上面的明牌
            'confidence': 0.9,
            'group_id': 6
        }
    ]

    ti_result = system2.process_frame(ti_detections, 1)
    print(f"提牌结果: {len(ti_result['digital_twin_cards'])}张卡牌")

    # 按ID排序显示
    ti_cards = sorted(ti_result['digital_twin_cards'], key=lambda c: c.twin_id)
    for card in ti_cards:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")

    # 验证提牌结果
    expected_ti = ['1_二暗', '2_二暗', '3_二暗', '4_二']
    actual_ti = [card.twin_id for card in ti_cards]
    ti_correct = actual_ti == expected_ti

    if ti_correct:
        print("✅ 提牌测试通过: 1二暗, 2二暗, 3二暗, 4二")
    else:
        print(f"❌ 提牌测试失败: 期望 {expected_ti}, 实际 {actual_ti}")

    # 检查是否存在不应该出现的通用暗牌ID
    all_cards = wei_cards + ti_cards
    generic_dark_cards = [card for card in all_cards if card.twin_id.endswith('_暗') and not card.twin_id.endswith('二暗')]

    if generic_dark_cards:
        print(f"❌ 发现不应该存在的通用暗牌ID: {[card.twin_id for card in generic_dark_cards]}")
        return False
    else:
        print("✅ 没有发现通用暗牌ID，所有暗牌都正确关联")

    return wei_correct and ti_correct

def test_dark_card_before_fix():
    """测试修复前的问题：暗牌全是分配的1暗、2暗等"""
    print("\n🧪 测试修复前的问题场景")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 模拟吃碰区有多种不同的明牌和暗牌
    frame_detections = [
        # 明牌
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 6},
        {'label': '五', 'bbox': [300, 100, 350, 150], 'confidence': 0.9, 'group_id': 6},
        # 暗牌
        {'label': '暗', 'bbox': [160, 100, 210, 150], 'confidence': 0.8, 'group_id': 6},
        {'label': '暗', 'bbox': [220, 100, 270, 150], 'confidence': 0.8, 'group_id': 6},
        {'label': '暗', 'bbox': [360, 100, 410, 150], 'confidence': 0.8, 'group_id': 6},
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    dark_cards = [card for card in result['digital_twin_cards'] if card.is_dark]
    bright_cards = [card for card in result['digital_twin_cards'] if not card.is_dark]
    
    print(f"明牌:")
    for card in bright_cards:
        print(f"  - {card.twin_id}")
    
    print(f"暗牌:")
    for card in dark_cards:
        print(f"  - {card.twin_id}")
    
    # 检查暗牌是否正确关联
    associated_correctly = 0
    for dark_card in dark_cards:
        if dark_card.twin_id.endswith('_二暗') or dark_card.twin_id.endswith('_五暗'):
            associated_correctly += 1
            print(f"✅ {dark_card.twin_id} 正确关联到具体牌面")
        else:
            print(f"❌ {dark_card.twin_id} 没有关联到具体牌面")
    
    print(f"\n📊 关联统计: {associated_correctly}/{len(dark_cards)} 张暗牌正确关联")
    
    if associated_correctly > 0:
        print("🎉 修复成功: 暗牌开始与具体牌面关联!")
        return True
    else:
        print("❌ 仍有问题: 暗牌没有与具体牌面关联")
        return False

if __name__ == "__main__":
    print("🔧 测试暗牌处理逻辑修复")
    print("=" * 50)
    
    # 测试1：暗牌与碰牌关联
    test1_passed = test_dark_card_association()
    
    # 测试2：修复前问题的验证
    test2_passed = test_dark_card_before_fix()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"测试1 (暗牌关联): {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"测试2 (修复验证): {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("🎉 所有测试通过! 暗牌处理逻辑修复成功!")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

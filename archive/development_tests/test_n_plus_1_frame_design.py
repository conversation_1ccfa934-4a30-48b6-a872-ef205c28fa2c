"""
测试修复后的n+1帧设计
验证继承机制是否正确实现，确保物理ID不会被重复分配
"""

import sys
sys.path.append('src')

from modules.phase2_integrator import Phase2Integrator
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_inheritance_mechanism():
    """测试继承机制"""
    print("🧪 测试修复后的n+1帧设计")
    
    # 创建第二阶段系统
    system = Phase2Integrator()
    
    # 模拟第1帧数据
    frame1_data = [
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},
        {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.9, 'group_id': 1},
    ]
    
    print(f"\n📋 第1帧：{len(frame1_data)}张卡牌")
    result1 = system.process_frame(frame1_data)
    
    if result1.success:
        print("✅ 第1帧处理成功")
        for card in result1.processed_cards:
            print(f"   分配ID: {card['twin_id']} (标签: {card['label']})")
        
        # 检查全局ID状态
        global_ids = list(system.global_id_manager.global_id_registry.keys())
        print(f"   全局ID注册表: {global_ids}")
    else:
        print("❌ 第1帧处理失败")
        return
    
    # 模拟第2帧数据（相同位置的卡牌应该继承ID）
    frame2_data = [
        {'label': '二', 'bbox': [105, 105, 155, 155], 'confidence': 0.9, 'group_id': 1},  # 位置略有变化
        {'label': '三', 'bbox': [205, 105, 255, 155], 'confidence': 0.8, 'group_id': 1},  # 位置略有变化
        {'label': '五', 'bbox': [400, 100, 450, 150], 'confidence': 0.9, 'group_id': 1},  # 新卡牌
    ]
    
    print(f"\n📋 第2帧：{len(frame2_data)}张卡牌")
    result2 = system.process_frame(frame2_data)
    
    if result2.success:
        print("✅ 第2帧处理成功")
        for card in result2.processed_cards:
            inherited = card.get('inherited', False)
            status = "继承" if inherited else "新分配"
            print(f"   {status}ID: {card['twin_id']} (标签: {card['label']})")
        
        # 检查全局ID状态
        global_ids = list(system.global_id_manager.global_id_registry.keys())
        print(f"   全局ID注册表: {global_ids}")
        
        # 验证继承率
        stats = result2.statistics.get('inheritance', {})
        inheritance_rate = stats.get('inheritance_rate', 0)
        print(f"   继承率: {inheritance_rate:.1%}")
    else:
        print("❌ 第2帧处理失败")
        return
    
    # 验证物理ID管理
    print(f"\n🎯 物理ID管理验证:")
    id_counters = system.global_id_manager.id_counters
    total_physical_ids = sum(id_counters.values())
    print(f"   已分配物理ID总数: {total_physical_ids}")
    print(f"   各类型ID计数: {dict(id_counters)}")
    
    # 验证是否符合80张物理牌设计
    if total_physical_ids <= 80:
        print(f"✅ 物理ID管理正确：{total_physical_ids}/80张")
    else:
        print(f"❌ 物理ID超限：{total_physical_ids}/80张")
    
    print("\n🎉 n+1帧设计测试完成！")

def test_massive_frames():
    """测试大量帧的处理，验证物理ID不会耗尽"""
    print("\n🧪 测试大量帧处理")
    
    system = Phase2Integrator()
    
    # 模拟20帧，每帧3张相同的卡牌
    for frame_num in range(1, 21):
        frame_data = [
            {'label': '二', 'bbox': [100 + frame_num, 100, 150 + frame_num, 150], 'confidence': 0.9, 'group_id': 1},
            {'label': '三', 'bbox': [200 + frame_num, 100, 250 + frame_num, 150], 'confidence': 0.8, 'group_id': 1},
            {'label': '四', 'bbox': [300 + frame_num, 100, 350 + frame_num, 150], 'confidence': 0.9, 'group_id': 1},
        ]
        
        result = system.process_frame(frame_data)
        
        if result.success:
            total_physical_ids = sum(system.global_id_manager.id_counters.values())
            inheritance_rate = result.statistics.get('inheritance', {}).get('inheritance_rate', 0)
            print(f"   第{frame_num:2d}帧: 物理ID总数={total_physical_ids:2d}, 继承率={inheritance_rate:.1%}")
        else:
            print(f"   第{frame_num:2d}帧: 处理失败")
    
    # 最终统计
    final_physical_ids = sum(system.global_id_manager.id_counters.values())
    print(f"\n🎯 最终结果:")
    print(f"   物理ID总数: {final_physical_ids}/80张")
    print(f"   ID分配详情: {dict(system.global_id_manager.id_counters)}")
    
    if final_physical_ids <= 6:  # 理论上只需要3种牌，每种最多2个ID
        print("✅ 物理ID管理优秀：继承机制有效防止了ID重复分配")
    else:
        print("⚠️ 物理ID使用较多：可能存在继承失效的情况")

if __name__ == "__main__":
    test_inheritance_mechanism()
    test_massive_frames()

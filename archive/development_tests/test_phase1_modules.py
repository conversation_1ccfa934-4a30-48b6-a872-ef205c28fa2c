"""
第一阶段模块测试脚本
测试模块1-3的集成功能
"""

import sys
import os
sys.path.append('src')

from modules.phase1_integrator import create_phase1_integrator
import json

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试基础功能")
    print("=" * 50)
    
    # 创建系统
    system = create_phase1_integrator()
    
    # 第一帧：手牌区有几张不同的牌
    frame1_detections = [
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},
        {'label': '暗', 'bbox': [300, 100, 350, 150], 'confidence': 0.7, 'group_id': 6},
    ]
    
    result1 = system.process_frame(frame1_detections)
    print(f"第一帧处理: {'✅ 成功' if result1.success else '❌ 失败'}")
    print(f"处理卡牌数量: {len(result1.processed_cards)}")
    
    for card in result1.processed_cards:
        print(f"  {card['twin_id']} (区域{card['group_id']}, 标签{card['label']})")
    
    return system

def test_inheritance():
    """测试继承功能"""
    print("\n🔄 测试继承功能")
    print("=" * 50)
    
    system = create_phase1_integrator()
    
    # 第一帧
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = system.process_frame(frame1)
    print("第一帧分配的ID:")
    for card in result1.processed_cards:
        print(f"  {card['twin_id']} (区域{card['group_id']}, 标签{card['label']})")
    
    # 第二帧：部分继承，部分新增
    frame2 = [
        {'label': '二', 'bbox': [105, 105, 155, 155], 'confidence': 0.9, 'group_id': 1},  # 应该继承
        {'label': '二', 'bbox': [300, 100, 350, 150], 'confidence': 0.85, 'group_id': 2}, # 新区域，新ID
        {'label': '四', 'bbox': [400, 100, 450, 150], 'confidence': 0.8, 'group_id': 1},  # 新标签，新ID
    ]
    
    result2 = system.process_frame(frame2)
    print("\n第二帧处理结果:")
    for card in result2.processed_cards:
        inherited = "继承" if card.get('inherited', False) else "新增"
        print(f"  {card['twin_id']} (区域{card['group_id']}, 标签{card['label']}, {inherited})")
    
    # 显示继承率
    stats = result2.statistics
    inheritance_rate = stats.get('inheritance', {}).get('current_frame', {}).get('inheritance_rate', 0)
    print(f"\n继承率: {inheritance_rate:.2%}")
    
    return system

def test_data_validation():
    """测试数据验证功能"""
    print("\n🔍 测试数据验证功能")
    print("=" * 50)
    
    system = create_phase1_integrator()
    
    # 包含错误数据的检测结果
    invalid_detections = [
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 正常
        {'label': '未知牌', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},  # 未知标签
        {'bbox': [300, 100, 350, 150], 'confidence': 0.7, 'group_id': 1},  # 缺少label
        {'label': '三', 'bbox': [400, 100], 'confidence': 0.6, 'group_id': 1},  # bbox格式错误
        {'label': '四', 'bbox': [500, 100, 550, 150], 'confidence': 1.5, 'group_id': 99},  # 置信度和区域ID超范围
    ]
    
    result = system.process_frame(invalid_detections)
    
    print(f"处理结果: {'✅ 成功' if result.success else '❌ 失败'}")
    print(f"验证错误数量: {len(result.validation_errors)}")
    print(f"验证警告数量: {len(result.validation_warnings)}")
    print(f"有效卡牌数量: {len(result.processed_cards)}")
    
    if result.validation_errors:
        print("\n验证错误:")
        for error in result.validation_errors:
            print(f"  ❌ {error}")
    
    if result.validation_warnings:
        print("\n验证警告:")
        for warning in result.validation_warnings:
            print(f"  ⚠️ {warning}")
    
    return system

def test_id_limits():
    """测试ID限制功能"""
    print("\n🚫 测试ID限制功能")
    print("=" * 50)
    
    system = create_phase1_integrator()
    
    # 创建超过4张的相同牌
    many_cards = []
    for i in range(6):  # 创建6张"二"
        many_cards.append({
            'label': '二',
            'bbox': [100 + i*50, 100, 150 + i*50, 150],
            'confidence': 0.9,
            'group_id': 1
        })
    
    result = system.process_frame(many_cards)
    
    print(f"输入6张'二'，处理结果:")
    print(f"成功处理: {len(result.processed_cards)}张")
    
    physical_count = 0
    virtual_count = 0
    
    for card in result.processed_cards:
        is_virtual = card.get('is_virtual', False)
        if is_virtual:
            virtual_count += 1
            print(f"  {card['twin_id']} (虚拟牌: {card.get('virtual_reason', '未知原因')})")
        else:
            physical_count += 1
            print(f"  {card['twin_id']} (物理牌)")
    
    print(f"\n统计: 物理牌{physical_count}张, 虚拟牌{virtual_count}张")
    
    return system

def test_comprehensive_scenario():
    """测试综合场景"""
    print("\n🎯 测试综合场景")
    print("=" * 50)
    
    system = create_phase1_integrator()
    
    # 模拟多帧游戏场景
    scenarios = [
        {
            "name": "第1帧: 初始手牌",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},
                {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.85, 'group_id': 1},
            ]
        },
        {
            "name": "第2帧: 部分卡牌移动到调整区",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 继承
                {'label': '三', 'bbox': [200, 200, 250, 250], 'confidence': 0.8, 'group_id': 2},  # 移动到调整区
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.85, 'group_id': 1}, # 继承
                {'label': '五', 'bbox': [400, 100, 450, 150], 'confidence': 0.9, 'group_id': 1},  # 新摸的牌
            ]
        },
        {
            "name": "第3帧: 出现暗牌和吃碰",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 继承
                {'label': '暗', 'bbox': [500, 100, 550, 150], 'confidence': 0.7, 'group_id': 6},  # 吃碰区暗牌
                {'label': '暗', 'bbox': [500, 200, 550, 250], 'confidence': 0.7, 'group_id': 6},  # 吃碰区暗牌
                {'label': '三', 'bbox': [500, 300, 550, 350], 'confidence': 0.8, 'group_id': 6},  # 吃碰区明牌
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        result = system.process_frame(scenario['detections'])
        
        print(f"  处理结果: {'✅ 成功' if result.success else '❌ 失败'}")
        print(f"  卡牌数量: {len(result.processed_cards)}")
        
        for card in result.processed_cards:
            inherited = "继承" if card.get('inherited', False) else "新增"
            is_virtual = "虚拟" if card.get('is_virtual', False) else "物理"
            is_dark = "暗牌" if card.get('is_dark', False) else "明牌"
            print(f"    {card['twin_id']} (区域{card['group_id']}, {card['label']}, {inherited}, {is_virtual}, {is_dark})")
        
        # 显示继承率
        if 'inheritance' in result.statistics:
            inheritance_rate = result.statistics['inheritance'].get('current_frame', {}).get('inheritance_rate', 0)
            print(f"  继承率: {inheritance_rate:.2%}")
    
    # 显示最终系统状态
    status = system.get_system_status()
    print(f"\n最终系统状态:")
    print(f"  总处理帧数: {status['frame_count']}")
    print(f"  总体继承率: {status['inheritance_rate']:.2%}")
    print(f"  ID计数器: {status['id_counters']}")

def main():
    """主测试函数"""
    print("🚀 第一阶段模块测试开始")
    print("=" * 60)
    
    try:
        # 运行所有测试
        test_basic_functionality()
        test_inheritance()
        test_data_validation()
        test_id_limits()
        test_comprehensive_scenario()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("\n✅ 第一阶段模块功能验证通过:")
        print("  - 数据验证器工作正常")
        print("  - 基础ID分配器工作正常")
        print("  - 简单继承器工作正常")
        print("  - 模块集成工作正常")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

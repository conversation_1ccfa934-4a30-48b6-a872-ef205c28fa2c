"""
测试新的数字孪生系统V3.0 - 专职ID分配模块
验证暗牌修复和基于区域的继承机制
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.digital_twin_v3_new import DigitalTwinSystemV3, DigitalTwinCard

def test_dark_card_association():
    """测试暗牌关联机制"""
    print("🧪 测试1: 暗牌关联机制")
    
    system = DigitalTwinSystemV3()
    
    # 第一帧：偎牌场景 - 1明2暗
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [160, 100, 210, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [220, 100, 270, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    print(f"第一帧结果:")
    for card in cards1:
        print(f"  {card.twin_id} (标签: {card.label}, 区域: {card.group_id})")
    
    # 验证暗牌关联
    dark_cards = [c for c in cards1 if c.is_dark]
    bright_cards = [c for c in cards1 if not c.is_dark and not c.is_virtual]
    
    expected_pattern = ["1二", "2二暗", "3二暗"]  # 期望的偎牌模式
    actual_ids = sorted([c.twin_id for c in cards1 if not c.is_virtual])
    
    print(f"期望模式: {expected_pattern}")
    print(f"实际结果: {actual_ids}")
    
    if actual_ids == expected_pattern:
        print("✅ 暗牌关联测试通过 - 正确实现偎牌模式")
    else:
        print("❌ 暗牌关联测试失败")
    
    return system

def test_region_based_inheritance():
    """测试基于区域的继承机制"""
    print("\n🧪 测试2: 基于区域的继承机制")
    
    system = DigitalTwinSystemV3()
    
    # 第一帧
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "四", "bbox": [220, 100, 270, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    print(f"第一帧分配:")
    for card in cards1:
        print(f"  {card.twin_id} (标签: {card.label}, 区域: {card.group_id})")
    
    # 第二帧：相同区域，外观变化很大（模拟人工标注的坐标变化）
    detections_frame2 = [
        {"label": "二", "bbox": [300, 300, 350, 350], "confidence": 0.8, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [360, 300, 410, 350], "confidence": 0.8, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "四", "bbox": [420, 300, 470, 350], "confidence": 0.8, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    print(f"第二帧继承:")
    for card in cards2:
        print(f"  {card.twin_id} (标签: {card.label}, 区域: {card.group_id})")
    
    # 验证继承
    frame1_ids = sorted([c.twin_id for c in cards1 if not c.is_virtual])
    frame2_ids = sorted([c.twin_id for c in cards2 if not c.is_virtual])
    inheritance_rate = result2["statistics"]["inheritance_rate"]
    
    print(f"第一帧ID: {frame1_ids}")
    print(f"第二帧ID: {frame2_ids}")
    print(f"继承率: {inheritance_rate}%")
    
    if frame1_ids == frame2_ids and inheritance_rate == 100.0:
        print("✅ 基于区域的继承测试通过 - 100%继承率")
    else:
        print("❌ 基于区域的继承测试失败")
    
    return system

def test_cross_region_movement():
    """测试跨区域移动"""
    print("\n🧪 测试3: 跨区域移动")
    
    system = DigitalTwinSystemV3()
    
    # 第一帧：区域1
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    print(f"第一帧 (区域1):")
    for card in cards1:
        print(f"  {card.twin_id} (标签: {card.label}, 区域: {card.group_id})")
    
    # 第二帧：卡牌移动到区域2
    detections_frame2 = [
        {"label": "二", "bbox": [300, 300, 350, 350], "confidence": 0.8, "group_id": 2, "region_name": "调整手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [360, 300, 410, 350], "confidence": 0.8, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    print(f"第二帧 (跨区域移动):")
    for card in cards2:
        print(f"  {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 虚拟: {card.is_virtual})")
    
    # 验证跨区域移动
    physical_cards = [c for c in cards2 if not c.is_virtual]
    compensated_cards = [c for c in cards2 if c.is_virtual and c.virtual_reason == "遮挡补偿"]
    
    print(f"物理卡牌: {len(physical_cards)}张")
    print(f"补偿卡牌: {len(compensated_cards)}张")
    
    if len(physical_cards) == 2 and len(compensated_cards) == 0:
        print("✅ 跨区域移动测试通过 - 正常流转，无过度补偿")
    else:
        print("❌ 跨区域移动测试失败")
    
    return system

def test_physical_id_permanence():
    """测试物理ID永久性原则"""
    print("\n🧪 测试4: 物理ID永久性原则")
    
    system = DigitalTwinSystemV3()
    
    # 第一帧
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    
    # 第二帧：一张卡牌真正消失
    detections_frame2 = [
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    print(f"第二帧 (一张卡牌消失):")
    for card in cards2:
        print(f"  {card.twin_id} (标签: {card.label}, 虚拟: {card.is_virtual}, 原因: {card.virtual_reason})")
    
    # 验证物理ID永久性
    all_ids = [c.twin_id for c in cards2]
    compensated_ids = [c.twin_id for c in cards2 if c.virtual_reason == "遮挡补偿"]
    
    if "1二" in all_ids and "2三" in all_ids:
        print("✅ 物理ID永久性测试通过 - 消失的ID被正确补偿")
    else:
        print("❌ 物理ID永久性测试失败")
        print(f"所有ID: {all_ids}")
        print(f"补偿ID: {compensated_ids}")
    
    return system

def main():
    """运行所有测试"""
    print("🚀 数字孪生系统V3.0 - 专职ID分配模块测试")
    print("=" * 60)
    
    try:
        test_dark_card_association()
        test_region_based_inheritance()
        test_cross_region_movement()
        test_physical_id_permanence()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

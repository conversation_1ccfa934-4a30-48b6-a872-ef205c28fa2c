#!/usr/bin/env python3
"""
测试跨区域暗牌修复逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_cross_region_fix():
    """测试跨区域暗牌修复"""
    print("🔍 测试跨区域暗牌修复逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 第一帧：建立跨区域的暗牌和明牌（模拟实际情况）
    frame1_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 区域14的暗牌
            'confidence': 0.8,
            'group_id': 14
        },
        {
            'label': '拾',
            'bbox': [200, 100, 250, 150],  # 区域16的明牌
            'confidence': 0.9,
            'group_id': 16
        }
    ]
    
    result1 = system.process_frame(frame1_detections, 1)
    print(f"\n第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
    for card in result1['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id})")
    
    # 第二帧：只有明牌，暗牌被遮挡（触发补偿）
    frame2_detections = [
        {
            'label': '拾',
            'bbox': [200, 100, 250, 150],  # 明牌继续存在
            'confidence': 0.9,
            'group_id': 16
        }
    ]
    
    print(f"\n第二帧：模拟区域14暗牌被遮挡，触发跨区域修复")
    result2 = system.process_frame(frame2_detections, 2)
    print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
    
    # 分析结果
    success = True
    for card in result2['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 置信度: {card.confidence}, 区域: {card.group_id})")
        
        # 检查补偿的暗牌是否正确关联
        if card.is_dark:
            if card.twin_id.endswith('_暗'):
                print(f"    ❌ 错误: 暗牌 {card.twin_id} 仍然使用旧格式（带下划线）")
                success = False
            elif card.twin_id.endswith('拾暗'):
                print(f"    ✅ 正确: 暗牌 {card.twin_id} 正确关联到'拾'（跨区域修复成功）")
            else:
                print(f"    ⚠️  未知: 暗牌 {card.twin_id} 格式未知")
                success = False
    
    return success

def test_same_region_fix():
    """测试同区域暗牌修复（对比测试）"""
    print(f"\n🔍 测试同区域暗牌修复逻辑（对比测试）")
    
    system = create_digital_twin_system()
    
    # 第一帧：同区域的暗牌和明牌
    frame1_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 区域6的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 区域6的明牌
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result1 = system.process_frame(frame1_detections, 1)
    print(f"第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
    for card in result1['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id})")
    
    # 第二帧：只有明牌，暗牌被遮挡
    frame2_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌继续存在
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    print(f"第二帧：模拟区域6暗牌被遮挡，触发同区域修复")
    result2 = system.process_frame(frame2_detections, 2)
    print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
    
    success = True
    for card in result2['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 置信度: {card.confidence}, 区域: {card.group_id})")
        
        if card.is_dark:
            if card.twin_id.endswith('_暗'):
                print(f"    ❌ 错误: 暗牌 {card.twin_id} 仍然使用旧格式")
                success = False
            elif card.twin_id.endswith('二暗'):
                print(f"    ✅ 正确: 暗牌 {card.twin_id} 正确关联到'二'")
            else:
                print(f"    ⚠️  未知: 暗牌 {card.twin_id} 格式未知")
                success = False
    
    return success

if __name__ == "__main__":
    print("🔧 测试跨区域暗牌修复逻辑")
    print("=" * 60)
    
    # 测试跨区域修复
    success1 = test_cross_region_fix()
    
    # 测试同区域修复（对比）
    success2 = test_same_region_fix()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过! 跨区域暗牌修复逻辑工作正常!")
    else:
        print("❌ 部分测试失败，需要进一步调试")

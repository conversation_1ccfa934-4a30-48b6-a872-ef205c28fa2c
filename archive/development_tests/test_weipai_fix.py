#!/usr/bin/env python3
"""
测试修复后的偎牌分配逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_weipai_allocation():
    """测试偎牌分配逻辑"""
    print("🔍 测试修复后的偎牌分配逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 模拟第28帧的偎牌情况：区域16有两组偎牌
    frame_detections = [
        # 第一组偎牌：拾（从下到上：明牌、暗牌、暗牌）
        {
            'label': '拾',
            'bbox': [71, 39, 86, 57],  # 最下面的明牌
            'confidence': 0.7,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [71, 58, 86, 72],  # 中间的暗牌
            'confidence': 0.6,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [71, 74, 86, 91],  # 最上面的暗牌
            'confidence': 0.6,
            'group_id': 16
        },

        # 第二组偎牌：肆（从下到上：明牌、暗牌、暗牌）
        {
            'label': '肆',
            'bbox': [88, 40, 103, 58],  # 最下面的明牌
            'confidence': 0.7,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [88, 59, 102, 73],  # 中间的暗牌
            'confidence': 0.6,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [88, 74, 103, 91],  # 最上面的暗牌
            'confidence': 0.6,
            'group_id': 16
        }
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"\n处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    # 分析结果
    cards_by_type = {}
    for card in result['digital_twin_cards']:
        card_type = card.label if card.label != '暗' else '推断类型'
        if card_type not in cards_by_type:
            cards_by_type[card_type] = []
        cards_by_type[card_type].append(card)
    
    success = True
    
    print(f"\n🔍 偎牌分配结果分析:")
    
    for card_type, cards in cards_by_type.items():
        print(f"\n{card_type}类型卡牌:")
        # 按Y坐标排序（从下到上）
        cards.sort(key=lambda c: -c.bbox[3])
        
        for i, card in enumerate(cards):
            position = "下" if i == 0 else "中" if i == 1 else "上"
            print(f"  位置{position}: {card.twin_id} (暗牌: {card.is_dark}, 坐标: {card.bbox})")
        
        # 检查分配是否正确
        if card_type == '拾':
            expected_ids = ['1拾暗', '2拾暗', '3拾']
            actual_ids = [card.twin_id for card in cards]
            if actual_ids == expected_ids:
                print(f"    ✅ 拾偎牌分配正确: {actual_ids}")
            else:
                print(f"    ❌ 拾偎牌分配错误: 期望 {expected_ids}, 实际 {actual_ids}")
                success = False
        
        elif card_type == '肆':
            expected_ids = ['1肆暗', '2肆暗', '3肆']
            actual_ids = [card.twin_id for card in cards]
            if actual_ids == expected_ids:
                print(f"    ✅ 肆偎牌分配正确: {actual_ids}")
            else:
                print(f"    ❌ 肆偎牌分配错误: 期望 {expected_ids}, 实际 {actual_ids}")
                success = False
    
    return success

def test_paopai_allocation():
    """测试跑牌分配逻辑（对比测试）"""
    print(f"\n🔍 测试跑牌分配逻辑（对比测试）")
    
    system = create_digital_twin_system()
    
    # 模拟跑牌：4张全明牌
    frame_detections = [
        {
            'label': '二',
            'bbox': [100, 40, 115, 58],  # 最下面
            'confidence': 0.9,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 60, 115, 78],  # 第二张
            'confidence': 0.9,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 80, 115, 98],  # 第三张
            'confidence': 0.9,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 115, 118],  # 最上面
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    # 分析跑牌结果
    cards = result['digital_twin_cards']
    cards.sort(key=lambda c: -c.bbox[3])  # 从下到上排序
    
    expected_ids = ['1二', '2二', '3二', '4二']
    actual_ids = [card.twin_id for card in cards]
    
    print(f"跑牌分配结果:")
    for i, card in enumerate(cards):
        position = ["下", "中下", "中上", "上"][i]
        print(f"  位置{position}: {card.twin_id} (暗牌: {card.is_dark})")
    
    if actual_ids == expected_ids:
        print(f"✅ 跑牌分配正确: {actual_ids}")
        return True
    else:
        print(f"❌ 跑牌分配错误: 期望 {expected_ids}, 实际 {actual_ids}")
        return False

if __name__ == "__main__":
    print("🔧 测试修复后的偎牌分配逻辑")
    print("=" * 60)
    
    # 测试偎牌分配
    success1 = test_weipai_allocation()
    
    # 测试跑牌分配（对比）
    success2 = test_paopai_allocation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过! 偎牌分配逻辑修复成功!")
    else:
        print("❌ 部分测试失败，需要进一步调试")

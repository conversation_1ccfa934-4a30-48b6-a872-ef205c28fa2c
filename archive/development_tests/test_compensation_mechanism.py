"""
测试修复后的补偿机制
验证：
1. 卡牌在不同区域间流转不需要补偿
2. 只有真正消失（所有区域都没有）才补偿
3. 每种卡牌最多4张的限制
4. 80张卡牌总量控制
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_normal_card_flow():
    """测试正常卡牌流转（不应该补偿）"""
    print("🧪 测试正常卡牌流转（不应该补偿）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：卡牌在区域1
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧（区域1）:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 第二帧：卡牌移动到区域2（正常流转）
    frame2 = [
        {'label': '二', 'bbox': [300, 300, 350, 400], 'confidence': 0.9, 'group_id': 2},  # 移动到区域2
        {'label': '三', 'bbox': [400, 300, 450, 400], 'confidence': 0.8, 'group_id': 2},  # 移动到区域2
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧（移动到区域2）:")
    physical_cards = [c for c in cards2 if not c.is_virtual and not hasattr(c, 'is_compensated')]
    compensated_cards = [c for c in cards2 if hasattr(c, 'is_compensated') and c.is_compensated]
    
    print("物理卡牌:")
    for card in physical_cards:
        print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    print(f"补偿卡牌数量: {len(compensated_cards)}")
    
    # 验证：正常流转不应该补偿
    return len(compensated_cards) == 0

def test_true_disappearance():
    """测试真正消失（应该补偿）"""
    print("\n🧪 测试真正消失（应该补偿）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：有4张卡牌
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '四', 'bbox': [400, 100, 450, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 第二帧：只有2张卡牌（2张真正消失）
    frame2 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧（2张消失）:")
    physical_cards = [c for c in cards2 if not c.is_virtual and c.confidence > 0.5]
    compensated_cards = [c for c in cards2 if not c.is_virtual and c.confidence <= 0.5]
    
    print("物理卡牌:")
    for card in physical_cards:
        print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    print("补偿卡牌:")
    for card in compensated_cards:
        print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} (补偿)")
    
    # 验证：应该补偿消失的卡牌
    return len(compensated_cards) > 0

def test_max_card_limit():
    """测试每种卡牌最多4张的限制"""
    print("\n🧪 测试每种卡牌最多4张的限制")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：有4张"二"
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [400, 100, 450, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    # 第二帧：仍然有4张"二"，但ID不同（模拟检测错误）
    frame2 = [
        {'label': '二', 'bbox': [150, 150, 200, 250], 'confidence': 0.9, 'group_id': 2},
        {'label': '二', 'bbox': [250, 150, 300, 250], 'confidence': 0.8, 'group_id': 2},
        {'label': '二', 'bbox': [350, 150, 400, 250], 'confidence': 0.9, 'group_id': 2},
        {'label': '二', 'bbox': [450, 150, 500, 250], 'confidence': 0.8, 'group_id': 2},
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    # 统计"二"的数量
    er_cards = [c for c in cards2 if c.label == '二' and not c.is_virtual]
    
    print(f"第二帧'二'的总数量: {len(er_cards)}")
    for card in er_cards:
        status = "补偿" if card.confidence <= 0.5 else "物理"
        print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} ({status})")
    
    # 验证：每种卡牌不应超过4张
    return len(er_cards) <= 4

def test_cross_region_flow():
    """测试跨区域流转（不应该补偿）"""
    print("\n🧪 测试跨区域流转（不应该补偿）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：卡牌分布在不同区域
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 2},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 3},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    # 第二帧：卡牌重新分布（跨区域流转）
    frame2 = [
        {'label': '二', 'bbox': [500, 500, 550, 600], 'confidence': 0.9, 'group_id': 4},  # 区域1的"二"移动到区域4
        {'label': '三', 'bbox': [600, 500, 650, 600], 'confidence': 0.8, 'group_id': 1},  # 区域3的"三"移动到区域1
        {'label': '二', 'bbox': [700, 500, 750, 600], 'confidence': 0.9, 'group_id': 1},  # 区域2的"二"移动到区域1
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("第二帧（跨区域流转）:")
    physical_cards = [c for c in cards2 if not c.is_virtual and c.confidence > 0.5]
    compensated_cards = [c for c in cards2 if not c.is_virtual and c.confidence <= 0.5]
    
    print("物理卡牌:")
    for card in physical_cards:
        print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    print(f"补偿卡牌数量: {len(compensated_cards)}")
    
    # 验证：跨区域流转不应该补偿（或补偿很少）
    return len(compensated_cards) <= 1  # 允许少量补偿，因为可能有检测误差

def main():
    """运行所有测试"""
    print("🚀 补偿机制修复测试")
    print("=" * 60)
    
    tests = [
        test_normal_card_flow,
        test_true_disappearance,
        test_max_card_limit,
        test_cross_region_flow
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = ["正常流转", "真正消失", "数量限制", "跨区域流转"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 补偿机制修复成功！")
    else:
        print("⚠️ 补偿机制需要进一步调整。")

if __name__ == "__main__":
    main()

import sys
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

print("测试第一帧处理...")

dt = create_digital_twin_system()

# 第一帧：3张"二"
frame1 = [
    {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
    {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
]

print("开始处理第一帧...")
result1 = dt.process_frame(frame1, frame_id=1)
print("第一帧处理完成")

cards1 = [c for c in result1['digital_twin_cards'] if not c.is_virtual]

print("第一帧结果:")
for card in cards1:
    print(f"  {card.twin_id} - {card.label}")

print("测试完成")

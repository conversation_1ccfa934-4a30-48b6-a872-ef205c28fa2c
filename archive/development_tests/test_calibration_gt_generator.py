#!/usr/bin/env python3
"""
测试calibration_gt数字孪生ID生成系统
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试导入"""
    print("1. 测试导入...")
    
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("   ✅ 数字孪生系统导入成功")
        
        from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator
        print("   ✅ 双轨验证器导入成功")
        
        import calibration_gt_digital_twin_generator
        print("   ✅ calibration_gt生成器导入成功")
        
        return True
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("2. 测试配置...")
    
    try:
        from calibration_gt_digital_twin_generator import CalibrationGTConfig
        
        config = CalibrationGTConfig()
        print(f"   ✅ 配置创建成功")
        print(f"   - 源目录: {config.source_dir}")
        print(f"   - 输出目录: {config.output_dir}")
        print(f"   - 有效卡牌类别: {len(config.valid_card_labels)} 个")
        
        return True
    except Exception as e:
        print(f"   ❌ 配置测试失败: {e}")
        return False

def test_converter():
    """测试转换器"""
    print("3. 测试转换器...")
    
    try:
        from calibration_gt_digital_twin_generator import CalibrationGTConfig, CalibrationGTConverter
        
        config = CalibrationGTConfig()
        converter = CalibrationGTConverter(config)
        print("   ✅ 转换器创建成功")
        
        # 测试转换功能
        test_anylabeling_data = {
            "shapes": [
                {
                    "label": "二",
                    "score": 0.95,
                    "points": [[100, 100], [150, 100], [150, 150], [100, 150]],
                    "group_id": 1,
                    "region_name": "手牌_观战方",
                    "owner": "spectator"
                }
            ]
        }
        
        detections = converter.convert_anylabeling_to_detection(test_anylabeling_data)
        print(f"   ✅ 转换测试成功: {len(detections)} 个检测结果")
        
        return True
    except Exception as e:
        print(f"   ❌ 转换器测试失败: {e}")
        return False

def test_processor():
    """测试处理器"""
    print("4. 测试处理器...")
    
    try:
        from calibration_gt_digital_twin_generator import CalibrationGTConfig, CalibrationGTProcessor
        
        config = CalibrationGTConfig()
        processor = CalibrationGTProcessor(config)
        print("   ✅ 处理器创建成功")
        
        return True
    except Exception as e:
        print(f"   ❌ 处理器测试失败: {e}")
        return False

def check_source_directory():
    """检查源目录"""
    print("5. 检查源目录...")
    
    source_dir = Path("legacy_assets/ceshi/calibration_gt")
    
    if not source_dir.exists():
        print(f"   ❌ 源目录不存在: {source_dir}")
        return False
    
    labels_dir = source_dir / "labels"
    images_dir = source_dir / "images"
    
    if not labels_dir.exists():
        print(f"   ❌ 标注目录不存在: {labels_dir}")
        return False
        
    if not images_dir.exists():
        print(f"   ❌ 图片目录不存在: {images_dir}")
        return False
    
    label_files = list(labels_dir.glob("*.json"))
    image_files = list(images_dir.glob("*.jpg"))
    
    print(f"   ✅ 源目录检查通过")
    print(f"   - 标注文件: {len(label_files)} 个")
    print(f"   - 图片文件: {len(image_files)} 个")
    
    return True

def main():
    """主函数"""
    print("🧪 calibration_gt数字孪生ID生成系统测试")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config,
        test_converter,
        test_processor,
        check_source_directory
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            print()
    
    print("📊 测试结果:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

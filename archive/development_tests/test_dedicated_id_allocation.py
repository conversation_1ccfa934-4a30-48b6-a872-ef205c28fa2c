"""
测试专职数字孪生ID分配系统
验证：
1. 删除IoU相关逻辑
2. 基于区域优先级的ID分配
3. ID绝对稳定性（一经分配禁止变化）
4. 只处理位置和区域变化
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_dedicated_id_allocation():
    """测试专职ID分配系统"""
    print("🧪 测试专职数字孪生ID分配系统")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：建立基础ID分配
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 2},  # 不同区域
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧ID分配:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} - bbox: {card.bbox}")
    
    # 第二帧：相同标签，但位置大幅变化（测试ID稳定性）
    frame2 = [
        {'label': '二', 'bbox': [50, 50, 80, 120], 'confidence': 0.9, 'group_id': 1},      # 位置大变化
        {'label': '二', 'bbox': [400, 300, 500, 450], 'confidence': 0.8, 'group_id': 1},   # 位置大变化
        {'label': '三', 'bbox': [600, 500, 700, 650], 'confidence': 0.9, 'group_id': 3},   # 区域变化
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧处理（位置和区域大变化）:")
    for card in cards2:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} - bbox: {card.bbox}")
    
    # 验证ID稳定性
    ids1 = sorted([c.twin_id for c in cards1 if not c.is_virtual])
    ids2_physical = sorted([c.twin_id for c in cards2 if not c.is_virtual and not hasattr(c, 'is_compensated')])
    
    print(f"\n第一帧物理ID: {ids1}")
    print(f"第二帧物理ID: {ids2_physical}")
    
    # 检查ID是否保持稳定
    stable_ids = set(ids1) & set(ids2_physical)
    print(f"稳定的ID: {sorted(stable_ids)}")
    print(f"ID稳定率: {len(stable_ids)/len(ids1)*100:.1f}%")
    
    return len(stable_ids) >= len(ids1) * 0.8  # 期望至少80%的ID保持稳定

def test_region_priority():
    """测试区域优先级分配"""
    print("\n🧪 测试区域优先级分配")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：多个区域同时出现
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 3},  # 区域3先出现
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},  # 区域1后出现
        {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 2},  # 区域2最后出现
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧区域优先级分配:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 第二帧：新区域出现
    frame2 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 3},  # 继续存在
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 4},  # 新区域4
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 5},  # 新区域5，新标签
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧新区域处理:")
    for card in cards2:
        if not card.is_virtual and not hasattr(card, 'is_compensated'):
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 验证区域优先级
    region3_cards = [c for c in cards2 if c.group_id == 3 and not c.is_virtual]
    region4_cards = [c for c in cards2 if c.group_id == 4 and not c.is_virtual]
    region5_cards = [c for c in cards2 if c.group_id == 5 and not c.is_virtual]
    
    print(f"\n区域3卡牌: {[c.twin_id for c in region3_cards]}")
    print(f"区域4卡牌: {[c.twin_id for c in region4_cards]}")
    print(f"区域5卡牌: {[c.twin_id for c in region5_cards]}")
    
    return len(region3_cards) > 0 and len(region4_cards) > 0 and len(region5_cards) > 0

def test_id_absolute_stability():
    """测试ID绝对稳定性"""
    print("\n🧪 测试ID绝对稳定性")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    # 连续多帧，每帧都有不同的位置和区域变化
    all_ids = []
    
    for frame_id in range(2, 6):  # 第2-5帧
        frame = [
            {'label': '二', 'bbox': [100 + frame_id * 50, 100 + frame_id * 30, 150 + frame_id * 20, 200 + frame_id * 40], 'confidence': 0.9, 'group_id': frame_id % 4 + 1},  # 区域循环变化
            {'label': '三', 'bbox': [200 + frame_id * 60, 100 + frame_id * 25, 250 + frame_id * 30, 200 + frame_id * 35], 'confidence': 0.8, 'group_id': frame_id % 3 + 1},  # 区域循环变化
        ]
        
        result = dt.process_frame(frame, frame_id=frame_id)
        physical_ids = sorted([c.twin_id for c in result['digital_twin_cards'] if not c.is_virtual and not hasattr(c, 'is_compensated')])
        all_ids.append(physical_ids)
        
        print(f"第{frame_id}帧物理ID: {physical_ids}")
    
    # 验证ID稳定性
    first_frame_ids = set(all_ids[0])
    stable_across_all = True
    
    for i, frame_ids in enumerate(all_ids[1:], 2):
        current_ids = set(frame_ids)
        if not first_frame_ids.issubset(current_ids):
            stable_across_all = False
            print(f"第{i+1}帧ID不稳定: 缺失 {first_frame_ids - current_ids}")
    
    print(f"\nID绝对稳定性: {'✅ 通过' if stable_across_all else '❌ 失败'}")
    
    return stable_across_all

def test_no_iou_dependency():
    """测试无IoU依赖性"""
    print("\n🧪 测试无IoU依赖性")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    original_id = [c.twin_id for c in result1['digital_twin_cards'] if not c.is_virtual][0]
    
    # 第二帧：完全不重叠的位置（IoU=0）
    frame2 = [
        {'label': '二', 'bbox': [1000, 1000, 1050, 1100], 'confidence': 0.9, 'group_id': 1},  # 完全不重叠
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    new_id = [c.twin_id for c in result2['digital_twin_cards'] if not c.is_virtual and not hasattr(c, 'is_compensated')]
    
    print(f"原始ID: {original_id}")
    print(f"新位置ID: {new_id}")
    print(f"IoU=0时ID继承: {'✅ 成功' if original_id in new_id else '❌ 失败'}")
    
    return original_id in new_id

def main():
    """运行所有测试"""
    print("🚀 专职数字孪生ID分配系统测试")
    print("=" * 60)
    
    tests = [
        test_dedicated_id_allocation,
        test_region_priority,
        test_id_absolute_stability,
        test_no_iou_dependency
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = ["专职ID分配", "区域优先级", "ID绝对稳定性", "无IoU依赖"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 专职ID分配系统完全正确！")
    else:
        print("⚠️ ID分配系统需要进一步调整。")

if __name__ == "__main__":
    main()

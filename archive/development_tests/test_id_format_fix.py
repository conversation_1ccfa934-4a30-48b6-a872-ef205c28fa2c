#!/usr/bin/env python3
"""
测试ID格式修复
验证所有ID分配器都生成正确格式的ID
"""

import sys
sys.path.append('src')

def test_basic_id_assigner():
    """测试基础ID分配器的ID格式"""
    print("🧪 测试基础ID分配器...")
    
    from modules.basic_id_assigner import create_basic_id_assigner
    
    assigner = create_basic_id_assigner()
    
    # 测试明牌ID分配
    test_cards = [
        {'label': '二', 'group_id': 1, 'bbox': [100, 100, 150, 150]},
        {'label': '三', 'group_id': 1, 'bbox': [150, 100, 200, 150]},
        {'label': '暗', 'group_id': 6, 'bbox': [200, 100, 250, 150]},
    ]
    
    result = assigner.assign_ids(test_cards)
    
    print(f"✅ 分配了{len(result.assigned_cards)}张卡牌的ID:")
    for card in result.assigned_cards:
        twin_id = card.get('twin_id', 'None')
        label = card.get('label', 'None')
        group_id = card.get('group_id', 'None')
        is_virtual = card.get('is_virtual', False)
        
        # 验证ID格式
        if label == '暗':
            expected_format = "临时暗_"
            if twin_id.startswith(expected_format):
                print(f"  ✅ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual})")
            else:
                print(f"  ❌ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual}) - 格式错误")
        elif is_virtual:
            expected_format = f"虚拟{label}"
            if twin_id == expected_format:
                print(f"  ✅ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual})")
            else:
                print(f"  ❌ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual}) - 应该是 {expected_format}")
        else:
            # 物理ID格式：数字+牌面
            if len(twin_id) >= 2 and twin_id[0].isdigit() and twin_id[1:] == label:
                print(f"  ✅ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual})")
            else:
                print(f"  ❌ {twin_id} (标签:{label}, 区域:{group_id}, 虚拟:{is_virtual}) - 格式错误")

def test_inheritor_id_fix():
    """测试继承器的ID格式修复功能"""
    print("\n🧪 测试继承器ID格式修复...")
    
    from modules.simple_inheritor import SimpleInheritor
    
    inheritor = SimpleInheritor()
    
    # 测试各种错误格式的ID
    test_cases = [
        ("虚拟二1", "虚拟二"),
        ("虚拟_三_16", "虚拟三"),
        ("2七7", "2七"),
        ("1三暗6", "1三暗"),
        ("4五14", "4五"),
        ("1二", "1二"),  # 已经正确的格式
        ("虚拟暗", "虚拟暗"),  # 已经正确的格式
    ]
    
    print("  ID格式修复测试:")
    for input_id, expected_id in test_cases:
        fixed_id = inheritor._fix_id_format(input_id)
        if fixed_id == expected_id:
            print(f"    ✅ {input_id} → {fixed_id}")
        else:
            print(f"    ❌ {input_id} → {fixed_id} (期望: {expected_id})")

def test_region_transitioner():
    """测试区域流转器的ID格式"""
    print("\n🧪 测试区域流转器...")
    
    from modules.region_transitioner import RegionTransitioner
    
    transitioner = RegionTransitioner()
    
    # 测试基础ID提取
    test_cases = [
        ("1二", "1二"),
        ("2三暗", "2三"),
        ("虚拟四", None),  # 虚拟牌不提取基础ID
        ("3五", "3五"),
    ]
    
    print("  基础ID提取测试:")
    for input_id, expected_base_id in test_cases:
        base_id = transitioner._extract_base_id(input_id)
        if base_id == expected_base_id:
            print(f"    ✅ {input_id} → {base_id}")
        else:
            print(f"    ❌ {input_id} → {base_id} (期望: {expected_base_id})")

if __name__ == "__main__":
    print("🎯 ID格式修复测试")
    print("=" * 50)
    
    try:
        test_basic_id_assigner()
        test_inheritor_id_fix()
        test_region_transitioner()
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

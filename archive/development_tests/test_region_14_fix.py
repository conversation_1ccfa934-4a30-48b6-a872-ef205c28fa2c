#!/usr/bin/env python3
"""
测试区域14暗牌修复逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_region_14_fix():
    """测试区域14暗牌修复"""
    print("🔍 测试区域14暗牌修复逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 第一帧：建立区域14的暗牌和明牌
    frame1_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 暗牌
            'confidence': 0.8,
            'group_id': 14
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌
            'confidence': 0.9,
            'group_id': 14
        }
    ]
    
    result1 = system.process_frame(frame1_detections, 1)
    print(f"\n第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
    for card in result1['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id})")
    
    # 第二帧：只有明牌，暗牌被遮挡（触发补偿）
    frame2_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌继续存在
            'confidence': 0.9,
            'group_id': 14
        }
    ]
    
    print(f"\n第二帧：模拟区域14暗牌被遮挡，触发补偿")
    result2 = system.process_frame(frame2_detections, 2)
    print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
    
    # 分析结果
    for card in result2['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 置信度: {card.confidence}, 区域: {card.group_id})")
    
    # 检查补偿的暗牌是否正确关联
    compensated_dark_cards = [card for card in result2['digital_twin_cards'] if card.is_dark]
    
    print(f"\n🔍 补偿的暗牌分析:")
    success = True
    for card in compensated_dark_cards:
        if card.twin_id.endswith('_暗'):
            print(f"❌ 错误: 补偿的暗牌 {card.twin_id} 仍然是通用暗牌ID")
            success = False
        elif card.twin_id.endswith('二暗'):
            print(f"✅ 正确: 补偿的暗牌 {card.twin_id} 正确关联到'二'")
        else:
            print(f"⚠️  未知: 补偿的暗牌 {card.twin_id} 格式未知")
    
    return success

if __name__ == "__main__":
    print("🔧 测试区域14暗牌修复")
    print("=" * 50)
    
    success = test_region_14_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功! 区域14暗牌修复正常工作!")
    else:
        print("❌ 测试失败，需要进一步调试")

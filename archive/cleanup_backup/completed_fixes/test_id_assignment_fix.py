#!/usr/bin/env python3
"""
测试ID分配算法修复效果

验证增强的空间排序算法是否解决了系统性+1偏移问题
"""

import sys
import os
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.digital_twin_v2 import DigitalTwinCoordinator, CardDetection

def create_test_detections() -> List[CardDetection]:
    """创建测试检测数据"""
    # 模拟一个简单的区域1场景：3张卡牌从左到右排列
    detections = [
        CardDetection(
            label="二",
            bbox=[100, 200, 150, 250],  # 最左边
            confidence=0.9,
            group_id=1,
            region_name="region_1"
        ),
        CardDetection(
            label="二", 
            bbox=[160, 200, 210, 250],  # 中间
            confidence=0.9,
            group_id=1,
            region_name="region_1"
        ),
        CardDetection(
            label="二",
            bbox=[220, 200, 270, 250],  # 最右边
            confidence=0.9,
            group_id=1,
            region_name="region_1"
        )
    ]
    
    return detections

def test_spatial_sorting():
    """测试空间排序算法"""
    print("🧪 测试空间排序算法...")
    
    coordinator = DigitalTwinCoordinator()
    detections = create_test_detections()
    
    # 处理第一帧
    result = coordinator.process_frame(detections)
    
    print(f"📊 处理结果:")
    print(f"   - 检测到 {len(result['digital_twin_cards'])} 张卡牌")

    # 检查ID分配
    cards = result['digital_twin_cards']
    cards_sorted_by_position = sorted(cards, key=lambda c: c.bbox[0])  # 按X坐标排序

    print(f"📍 ID分配结果（按位置从左到右）:")
    for i, card in enumerate(cards_sorted_by_position, 1):
        expected_id = f"{i}_二"
        actual_id = card.twin_id
        status = "✅" if expected_id == actual_id else "❌"
        print(f"   {status} 位置{i}: 期望 {expected_id}, 实际 {actual_id}")
        
    return cards_sorted_by_position

def test_with_real_data():
    """使用真实数据测试"""
    print("\n🎯 使用真实数据测试...")
    
    # 加载一个真实的JSON文件
    test_file = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train/1/frame_00000.json")
    
    if not test_file.exists():
        print("❌ 测试文件不存在，跳过真实数据测试")
        return
        
    with open(test_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        
    # 转换为CardDetection对象
    detections = []
    for shape in data.get('shapes', []):
        if len(shape.get('points', [])) >= 4:
            points = shape['points']
            x1, y1 = points[0]
            x2, y2 = points[2]
            
            label = shape.get('label', '')
            # 只处理有效的卡牌标注（跳过UI元素）
            if any(char.isdigit() for char in label) and len(label) >= 2:
                # 提取卡牌名称（去掉数字前缀）
                card_name = label[1:] if label[0].isdigit() else label
                
                # 转换繁体到简体
                card_name_mapping = {
                    "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
                    "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
                }
                if card_name in card_name_mapping:
                    card_name = card_name_mapping[card_name]
                    
                detection = CardDetection(
                    label=card_name,
                    bbox=[x1, y1, x2, y2],
                    confidence=0.9,
                    group_id=shape.get('group_id', 1),
                    region_name=f"region_{shape.get('group_id', 1)}"
                )
                detections.append(detection)
                
    print(f"📊 加载了 {len(detections)} 个有效检测")
    
    # 处理检测结果
    coordinator = DigitalTwinCoordinator()
    result = coordinator.process_frame(detections)
    
    # 分析结果
    cards = result['digital_twin_cards']
    print(f"🎯 处理结果: {len(cards)} 张数字孪生卡牌")

    # 按区域分组分析
    cards_by_region = {}
    for card in cards:
        region = card.group_id
        if region not in cards_by_region:
            cards_by_region[region] = []
        cards_by_region[region].append(card)
        
    for region_id, region_cards in cards_by_region.items():
        print(f"\n📍 区域 {region_id} ({len(region_cards)} 张卡牌):")
        
        # 按卡牌类型分组
        cards_by_type = {}
        for card in region_cards:
            card_type = card.label
            if card_type not in cards_by_type:
                cards_by_type[card_type] = []
            cards_by_type[card_type].append(card)

        for card_type, type_cards in cards_by_type.items():
            print(f"   🎴 {card_type} ({len(type_cards)} 张):")
            # 按位置排序
            type_cards_sorted = sorted(type_cards, key=lambda c: (c.bbox[1], c.bbox[0]))
            for i, card in enumerate(type_cards_sorted, 1):
                twin_id = card.twin_id
                expected_id = f"{i}_{card_type}"
                status = "✅" if expected_id == twin_id else "❌"
                print(f"      {status} {twin_id} (期望: {expected_id})")

def analyze_improvement():
    """分析改进效果"""
    print("\n📈 分析改进效果...")
    
    # 运行精确错误分析
    os.system("python tools/precise_id_error_analysis.py")
    
    # 读取分析报告
    report_file = "analysis/precise_id_error_analysis_report.json"
    if os.path.exists(report_file):
        with open(report_file, 'r', encoding='utf-8') as f:
            report = json.load(f)
            
        print("📊 错误分析结果:")
        if 'summary' in report:
            print(f"   - {report['summary'].get('error_rate_analysis', 'N/A')}")
            
        if 'offset_analysis' in report and 'most_common_offset' in report['offset_analysis']:
            offset_info = report['offset_analysis']['most_common_offset']
            print(f"   - 最常见偏移: {offset_info.get('value', 'N/A'):+d} ({offset_info.get('percentage', 0):.1f}%)")
            
        if 'recommendations' in report:
            print("💡 改进建议:")
            for i, rec in enumerate(report['recommendations'][:3], 1):
                print(f"   {i}. {rec}")
    else:
        print("❌ 未找到分析报告")

def main():
    """主函数"""
    print("🚀 ID分配算法修复效果测试")
    print("=" * 50)
    
    # 测试1：基础空间排序
    test_spatial_sorting()
    
    # 测试2：真实数据测试
    test_with_real_data()
    
    # 测试3：分析改进效果
    analyze_improvement()
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()

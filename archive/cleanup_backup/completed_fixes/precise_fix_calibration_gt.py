"""
精确修正calibration_gt_enhanced数据集

问题：之前的修正脚本没有正确处理非卡牌类别的区域分配
解决方案：
1. 严格按照原始文件恢复非卡牌类别的group_id
2. 只为21个有效卡牌类别分配区域
3. 确保与原始数据完全一致
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import Counter

class PreciseCalibrationGTFixer:
    """精确的calibration_gt修正器"""
    
    def __init__(self):
        self.enhanced_path = Path("legacy_assets/ceshi/calibration_gt_enhanced/labels")
        self.original_path = Path("legacy_assets/ceshi/calibration_gt/labels")
        
        # 定义21个有效卡牌类别（包括繁简体）
        self.valid_card_categories = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "暗",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        }
        
    def is_valid_card_label(self, label: str) -> bool:
        """判断是否是有效的卡牌标签"""
        if not label:
            return False
        
        # 处理带数字前缀的标签（如"1八"、"2四"）
        import re
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            card_name = match.group(2)
        else:
            card_name = label
        
        return card_name in self.valid_card_categories
    
    def restore_from_original(self) -> bool:
        """从原始文件恢复正确的区域分配"""
        print("🔄 从原始文件恢复正确的区域分配...")
        
        # 导入分类器用于缺失区域的分配
        try:
            import sys
            sys.path.append(str(Path(__file__).parent.parent))
            from src.core.multi_algorithm_region_classifier import MultiAlgorithmRegionClassifier
            
            classifier = MultiAlgorithmRegionClassifier()
            training_count = self._collect_training_data(classifier)
            
            if training_count > 0:
                classifier.train_ml_classifier()
                print(f"✅ 分类器训练完成，使用{training_count}个样本")
            
        except Exception as e:
            print(f"❌ 分类器初始化失败: {e}")
            return False
        
        enhanced_files = list(self.enhanced_path.glob("*.json"))
        fixed_count = 0
        
        for enhanced_file in enhanced_files:
            original_file = self.original_path / enhanced_file.name
            
            if not original_file.exists():
                print(f"⚠️ 原始文件不存在: {enhanced_file.name}")
                continue
            
            try:
                # 读取原始和增强文件
                with open(original_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                
                with open(enhanced_file, 'r', encoding='utf-8') as f:
                    enhanced_data = json.load(f)
                
                # 修正每个shape
                modified = False
                
                for i, enhanced_shape in enumerate(enhanced_data.get("shapes", [])):
                    if i >= len(original_data.get("shapes", [])):
                        break
                    
                    original_shape = original_data["shapes"][i]
                    
                    # 确保标签匹配
                    if enhanced_shape.get("label") != original_shape.get("label"):
                        continue
                    
                    label = enhanced_shape.get("label", "")
                    original_group_id = original_shape.get("group_id")
                    enhanced_group_id = enhanced_shape.get("group_id")
                    
                    is_valid_card = self.is_valid_card_label(label)
                    
                    if not is_valid_card:
                        # 非卡牌类别：严格恢复原始group_id
                        if enhanced_group_id != original_group_id:
                            enhanced_shape["group_id"] = original_group_id
                            
                            # 如果原始是null，删除region_name
                            if original_group_id is None and "region_name" in enhanced_shape:
                                del enhanced_shape["region_name"]
                            
                            modified = True
                            print(f"  修正非卡牌 '{label}': {enhanced_group_id} -> {original_group_id}")
                    
                    elif is_valid_card:
                        # 有效卡牌类别
                        if original_group_id is not None and original_group_id != 0:
                            # 原始有区域信息，使用原始的
                            if enhanced_group_id != original_group_id:
                                enhanced_shape["group_id"] = original_group_id
                                enhanced_shape["region_name"] = f"region_{original_group_id}"
                                modified = True
                        
                        elif original_group_id is None or original_group_id == 0:
                            # 原始缺少区域信息，使用分类器分配
                            if len(enhanced_shape.get("points", [])) >= 4:
                                points = enhanced_shape["points"]
                                x1, y1 = points[0]
                                x2, y2 = points[2]
                                bbox = [x1, y1, x2, y2]
                                
                                image_width = enhanced_data.get("imageWidth", 640)
                                image_height = enhanced_data.get("imageHeight", 320)
                                
                                predicted_region, confidence = classifier.classify_region(
                                    bbox, image_width, image_height
                                )
                                
                                if confidence > 0.6:
                                    enhanced_shape["group_id"] = predicted_region
                                    enhanced_shape["region_name"] = f"region_{predicted_region}"
                                    modified = True
                                    print(f"  分配区域给卡牌 '{label}': 区域{predicted_region} (置信度: {confidence:.3f})")
                
                # 保存修正后的文件
                if modified:
                    with open(enhanced_file, 'w', encoding='utf-8') as f:
                        json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
                    fixed_count += 1
                    
            except Exception as e:
                print(f"    ❌ 处理文件{enhanced_file.name}时出错: {e}")
        
        print(f"✅ 精确修正完成，共修正{fixed_count}个文件")
        return True
    
    def _collect_training_data(self, classifier) -> int:
        """收集训练数据"""
        training_count = 0
        
        original_files = list(self.original_path.glob("*.json"))
        
        for json_file in original_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_width = data.get("imageWidth", 640)
                image_height = data.get("imageHeight", 320)
                
                for shape in data.get("shapes", []):
                    label = shape.get("label", "")
                    group_id = shape.get("group_id")
                    
                    if (self.is_valid_card_label(label) and 
                        group_id is not None and group_id != 0 and
                        len(shape.get("points", [])) >= 4):
                        
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        bbox = [x1, y1, x2, y2]
                        
                        classifier.add_training_data(bbox, group_id, image_width, image_height)
                        training_count += 1
                        
            except Exception as e:
                continue
        
        return training_count
    
    def validate_precision_fix(self) -> Dict[str, Any]:
        """验证精确修正结果"""
        print("\n🔍 验证精确修正结果...")
        
        results = {
            "total_files_checked": 0,
            "mismatched_non_cards": [],
            "valid_cards_with_regions": 0,
            "valid_cards_without_regions": 0,
            "non_cards_with_regions": 0,
            "non_cards_without_regions": 0
        }
        
        enhanced_files = list(self.enhanced_path.glob("*.json"))
        results["total_files_checked"] = len(enhanced_files)
        
        for enhanced_file in enhanced_files[:10]:  # 检查前10个文件
            original_file = self.original_path / enhanced_file.name
            
            if not original_file.exists():
                continue
            
            try:
                with open(original_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                
                with open(enhanced_file, 'r', encoding='utf-8') as f:
                    enhanced_data = json.load(f)
                
                for i, enhanced_shape in enumerate(enhanced_data.get("shapes", [])):
                    if i >= len(original_data.get("shapes", [])):
                        break
                    
                    original_shape = original_data["shapes"][i]
                    
                    if enhanced_shape.get("label") != original_shape.get("label"):
                        continue
                    
                    label = enhanced_shape.get("label", "")
                    original_group_id = original_shape.get("group_id")
                    enhanced_group_id = enhanced_shape.get("group_id")
                    
                    is_valid_card = self.is_valid_card_label(label)
                    
                    if not is_valid_card:
                        # 检查非卡牌类别是否与原始一致
                        if enhanced_group_id != original_group_id:
                            results["mismatched_non_cards"].append({
                                "file": enhanced_file.name,
                                "label": label,
                                "original": original_group_id,
                                "enhanced": enhanced_group_id
                            })
                        
                        if enhanced_group_id is not None and enhanced_group_id != 0:
                            results["non_cards_with_regions"] += 1
                        else:
                            results["non_cards_without_regions"] += 1
                    
                    else:
                        # 统计有效卡牌
                        if enhanced_group_id is not None and enhanced_group_id != 0:
                            results["valid_cards_with_regions"] += 1
                        else:
                            results["valid_cards_without_regions"] += 1
                            
            except Exception as e:
                print(f"    ❌ 验证文件{enhanced_file.name}时出错: {e}")
        
        print(f"📊 验证结果:")
        print(f"  有效卡牌有区域: {results['valid_cards_with_regions']}")
        print(f"  有效卡牌无区域: {results['valid_cards_without_regions']}")
        print(f"  非卡牌有区域: {results['non_cards_with_regions']} {'❌' if results['non_cards_with_regions'] > 0 else '✅'}")
        print(f"  非卡牌无区域: {results['non_cards_without_regions']} ✅")
        print(f"  不匹配的非卡牌: {len(results['mismatched_non_cards'])}")
        
        if results["mismatched_non_cards"]:
            print("❌ 仍有不匹配的非卡牌:")
            for mismatch in results["mismatched_non_cards"][:5]:
                print(f"    {mismatch['file']} - {mismatch['label']}: {mismatch['original']} -> {mismatch['enhanced']}")
        
        return results
    
    def run_precise_fix(self) -> Dict[str, Any]:
        """运行精确修正"""
        print("🎯 精确修正calibration_gt_enhanced数据集")
        print("=" * 50)
        
        # 1. 从原始文件恢复
        restore_success = self.restore_from_original()
        
        if not restore_success:
            return {"error": "restore_failed"}
        
        # 2. 验证修正结果
        validation_results = self.validate_precision_fix()
        
        # 保存结果
        results = {
            "precise_fix_timestamp": "2025-01-17 09:10:00",
            "restore_success": restore_success,
            "validation_results": validation_results,
            "success": validation_results["non_cards_with_regions"] == 0 and len(validation_results["mismatched_non_cards"]) == 0
        }
        
        output_path = "analysis/precise_fix_results.json"
        os.makedirs("analysis", exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 精确修正结果已保存: {output_path}")
        
        return results

def main():
    """主修正函数"""
    fixer = PreciseCalibrationGTFixer()
    results = fixer.run_precise_fix()
    
    if "error" in results:
        print(f"❌ 精确修正失败: {results['error']}")
        return False
    
    if results["success"]:
        print("\n🎉 精确修正成功！所有非卡牌类别已正确恢复")
        return True
    else:
        print("\n⚠️ 精确修正完成，但仍有问题需要检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

import sys
sys.path.insert(0, '.')

from src.modules.simple_inheritor import SimpleInheritor

print("测试基于区域状态的继承")
inheritor = SimpleInheritor()

# 第一帧
frame1 = [{'label': '二', 'group_id': 1, 'twin_id': '1二'}]
inheritor._update_previous_frame(frame1)

# 第二帧
frame2 = [{'label': '二', 'group_id': 1}]
result = inheritor.process_inheritance(frame2)

print(f"继承: {len(result.inherited_cards)}")
print(f"新增: {len(result.new_cards)}")

if result.inherited_cards:
    print(f"继承的ID: {result.inherited_cards[0].get('twin_id')}")

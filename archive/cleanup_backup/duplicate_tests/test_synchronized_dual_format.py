"""
同步双轨格式全面测试
验证RLCard和AnyLabeling格式的完全一致性
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SynchronizedDualFormatTester:
    """同步双轨格式测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.dt_system = create_digital_twin_system()
        self.validator = SynchronizedDualFormatValidator(strict_mode=True)
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'overall_consistency_scores': []
        }
        
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行全面的同步双轨测试"""
        logger.info("🚀 开始同步双轨格式全面测试")
        print("=" * 80)
        
        # 测试用例
        test_cases = [
            self._test_basic_dual_format,
            self._test_complex_scenario,
            self._test_virtual_cards_handling,
            self._test_region_allocation,
            self._test_memory_mechanism,
            self._test_edge_cases,
            self._test_large_dataset
        ]
        
        # 执行所有测试
        for test_case in test_cases:
            try:
                test_name = test_case.__name__
                logger.info(f"执行测试: {test_name}")
                
                result = test_case()
                self.test_results['total_tests'] += 1
                
                if result['passed']:
                    self.test_results['passed_tests'] += 1
                    logger.info(f"✅ {test_name} 通过 (一致性: {result.get('consistency_score', 0):.3f})")
                else:
                    self.test_results['failed_tests'] += 1
                    logger.error(f"❌ {test_name} 失败: {result['error']}")
                
                self.test_results['test_details'].append({
                    'test_name': test_name,
                    'passed': result['passed'],
                    'consistency_score': result.get('consistency_score', 0),
                    'error': result.get('error', ''),
                    'details': result.get('details', {})
                })
                
                if 'consistency_score' in result:
                    self.test_results['overall_consistency_scores'].append(result['consistency_score'])
                    
            except Exception as e:
                logger.error(f"测试 {test_case.__name__} 执行异常: {str(e)}")
                self.test_results['failed_tests'] += 1
        
        # 生成最终报告
        return self._generate_final_report()
    
    def _test_basic_dual_format(self) -> Dict[str, Any]:
        """测试基础双轨格式输出"""
        try:
            # 创建基础测试数据
            detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator")
            ]
            
            # 处理数据
            dt_result = self.dt_system.process_frame(detections)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 640, 320, "test_basic.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_complex_scenario(self) -> Dict[str, Any]:
        """测试复杂场景"""
        try:
            # 创建复杂测试数据
            detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
                CardDetection("壹", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [220, 100, 270, 150], 0.88, 2, "打出牌_观战方", "spectator"),
                CardDetection("四", [280, 100, 330, 150], 0.85, 2, "打出牌_观战方", "spectator"),
                CardDetection("五", [100, 200, 150, 250], 0.90, 3, "吃碰牌_观战方", "spectator"),
                CardDetection("六", [160, 200, 210, 250], 0.87, 3, "吃碰牌_观战方", "spectator")
            ]
            
            # 处理数据
            dt_result = self.dt_system.process_frame(detections)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 640, 320, "test_complex.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"复杂场景一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_virtual_cards_handling(self) -> Dict[str, Any]:
        """测试虚拟卡牌处理"""
        try:
            # 创建包含虚拟卡牌的测试数据
            detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator")
            ]
            
            # 多次处理以触发虚拟ID分配
            for i in range(10):
                dt_result = self.dt_system.process_frame(detections)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 640, 320, f"test_virtual.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"虚拟卡牌处理一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_region_allocation(self) -> Dict[str, Any]:
        """测试区域分配"""
        try:
            # 创建不同区域的测试数据
            detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [160, 100, 210, 150], 0.92, 2, "打出牌_观战方", "spectator"),
                CardDetection("四", [220, 100, 270, 150], 0.88, 3, "吃碰牌_观战方", "spectator"),
                CardDetection("五", [280, 100, 330, 150], 0.85, 9, "弃牌区_对手方", "opponent")
            ]
            
            dt_result = self.dt_system.process_frame(detections)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 640, 320, "test_regions.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"区域分配一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_memory_mechanism(self) -> Dict[str, Any]:
        """测试记忆机制"""
        try:
            # 创建连续帧测试数据
            detections_frame1 = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator")
            ]
            
            detections_frame2 = [
                CardDetection("二", [105, 105, 155, 155], 0.93, 1, "手牌_观战方", "spectator")  # 轻微移动
            ]
            
            # 处理连续帧
            dt_result1 = self.dt_system.process_frame(detections_frame1)
            dt_result2 = self.dt_system.process_frame(detections_frame2)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result2, 640, 320, "test_memory.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result2['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"记忆机制一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_edge_cases(self) -> Dict[str, Any]:
        """测试边界情况"""
        try:
            # 测试空检测结果
            empty_detections = []
            dt_result = self.dt_system.process_frame(empty_detections)
            
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 640, 320, "test_empty.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"边界情况一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _test_large_dataset(self) -> Dict[str, Any]:
        """测试大数据集"""
        try:
            # 创建大量测试数据
            detections = []
            for i in range(20):  # 创建20张卡牌
                detections.append(CardDetection(
                    f"{'二三四五六七八九十壹'[i % 10]}", 
                    [i*30, 100, i*30+50, 150], 
                    0.9 - i*0.01, 
                    (i % 5) + 1, 
                    "手牌_观战方", 
                    "spectator"
                ))
            
            dt_result = self.dt_system.process_frame(detections)
            
            # 生成同步双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                dt_result, 1920, 1080, "test_large.jpg"
            )
            
            # 验证一致性
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result['digital_twin_cards']
            )
            
            return {
                'passed': validation_result['is_consistent'],
                'consistency_score': validation_result['overall_consistency_score'],
                'details': validation_result,
                'error': '' if validation_result['is_consistent'] else f"大数据集一致性验证失败: {validation_result['issues']}"
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终测试报告"""
        success_rate = (self.test_results['passed_tests'] / max(1, self.test_results['total_tests'])) * 100
        avg_consistency = sum(self.test_results['overall_consistency_scores']) / max(1, len(self.test_results['overall_consistency_scores']))
        
        report = {
            'test_summary': {
                'total_tests': self.test_results['total_tests'],
                'passed_tests': self.test_results['passed_tests'],
                'failed_tests': self.test_results['failed_tests'],
                'success_rate': success_rate,
                'average_consistency_score': avg_consistency,
                'test_timestamp': datetime.now().isoformat()
            },
            'test_details': self.test_results['test_details'],
            'overall_assessment': {
                'dual_format_ready': success_rate >= 85 and avg_consistency >= 0.95,
                'sync_mechanism_working': self.test_results['failed_tests'] == 0,
                'high_consistency': avg_consistency >= 0.95,
                'production_ready': success_rate >= 90 and avg_consistency >= 0.95
            },
            'consistency_scores': self.test_results['overall_consistency_scores']
        }
        
        # 保存报告
        output_dir = project_root / "tests" / "synchronized_dual_format_reports"
        output_dir.mkdir(exist_ok=True)
        
        report_file = output_dir / f"synchronized_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试报告已保存: {report_file}")
        return report

def main():
    """主函数"""
    print("🚀 开始同步双轨格式全面测试")
    print("="*80)
    
    tester = SynchronizedDualFormatTester()
    report = tester.run_comprehensive_test()
    
    # 打印测试结果
    summary = report['test_summary']
    assessment = report['overall_assessment']
    
    print(f"\n📊 测试结果总结:")
    print(f"   总测试数: {summary['total_tests']}")
    print(f"   通过测试: {summary['passed_tests']}")
    print(f"   失败测试: {summary['failed_tests']}")
    print(f"   成功率: {summary['success_rate']:.1f}%")
    print(f"   平均一致性分数: {summary['average_consistency_score']:.3f}")
    
    print(f"\n🎯 系统评估:")
    print(f"   双轨格式就绪: {'✅' if assessment['dual_format_ready'] else '❌'}")
    print(f"   同步机制工作: {'✅' if assessment['sync_mechanism_working'] else '❌'}")
    print(f"   高一致性: {'✅' if assessment['high_consistency'] else '❌'}")
    print(f"   生产环境就绪: {'✅' if assessment['production_ready'] else '❌'}")
    
    if summary['failed_tests'] > 0:
        print(f"\n❌ 失败的测试:")
        for detail in report['test_details']:
            if not detail['passed']:
                print(f"   - {detail['test_name']}: {detail['error']}")
    
    print("="*80)
    return 0 if summary['failed_tests'] == 0 else 1

if __name__ == "__main__":
    sys.exit(main())

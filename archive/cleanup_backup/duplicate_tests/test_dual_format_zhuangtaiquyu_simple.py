"""
简化版zhuangtaiquyu双轨验证测试
专注于验证双轨机制的核心功能
"""

import sys
import os
import json
import cv2
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_dual_format_with_zhuangtaiquyu_sample():
    """使用zhuangtaiquyu样本测试双轨格式"""
    print("🚀 zhuangtaiquyu双轨机制验证测试")
    print("=" * 60)
    
    try:
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        from src.core.detect import CardDetector
        print("✅ 模块导入成功")
        
        # 创建系统
        dt_system = create_digital_twin_system()
        detector = CardDetector()
        print("✅ 系统创建成功")
        
        # 查找zhuangtaiquyu数据集
        zhuangtaiquyu_path = project_root / "legacy_assets" / "ceshi" / "zhuangtaiquyu"
        if not zhuangtaiquyu_path.exists():
            print(f"❌ zhuangtaiquyu数据集不存在: {zhuangtaiquyu_path}")
            return False
        
        print(f"✅ 找到zhuangtaiquyu数据集: {zhuangtaiquyu_path}")
        
        # 查找第一个可用的图像和标注
        images_dir = zhuangtaiquyu_path / "images" / "train"
        labels_dir = zhuangtaiquyu_path / "labels" / "train"
        
        test_image = None
        test_label = None
        
        # 查找第一个可用的图像-标注对
        for subdir in images_dir.iterdir():
            if subdir.is_dir() and subdir.name.isdigit():
                for image_file in subdir.glob("*.jpg"):
                    label_file = labels_dir / subdir.name / f"{image_file.stem}.json"
                    if label_file.exists():
                        test_image = image_file
                        test_label = label_file
                        break
                if test_image:
                    break
        
        if not test_image or not test_label:
            print("❌ 未找到可用的图像-标注对")
            return False
        
        print(f"✅ 使用测试图像: {test_image.name}")
        print(f"✅ 使用测试标注: {test_label.name}")
        
        # 加载图像
        image = cv2.imread(str(test_image))
        if image is None:
            print(f"❌ 无法加载图像: {test_image}")
            return False
        
        height, width = image.shape[:2]
        print(f"✅ 图像尺寸: {width}x{height}")
        
        # 加载原始标注
        with open(test_label, 'r', encoding='utf-8') as f:
            original_annotation = json.load(f)
        
        original_shapes = original_annotation.get('shapes', [])
        print(f"✅ 原始标注包含 {len(original_shapes)} 个卡牌")
        
        # 显示原始标注示例
        if original_shapes:
            sample_shape = original_shapes[0]
            print(f"   示例标注: label='{sample_shape.get('label', '')}', group_id={sample_shape.get('group_id', 0)}")
        
        # 使用YOLO检测器检测卡牌
        print("🔍 开始YOLO检测...")
        detections_list = detector.detect_image(image)
        print(f"✅ YOLO检测到 {len(detections_list)} 个对象")
        
        # 转换为CardDetection对象
        card_detections = []
        for detection in detections_list:
            card_detection = CardDetection(
                label=detection.get('label', ''),
                bbox=detection.get('bbox', [0, 0, 0, 0]),
                confidence=detection.get('confidence', 0.0),
                group_id=detection.get('group_id', 0),
                region_name=detection.get('region_name', ''),
                owner=detection.get('owner', '')
            )
            card_detections.append(card_detection)
        
        print(f"✅ 转换为 {len(card_detections)} 个CardDetection对象")
        
        # 数字孪生系统处理
        print("🧠 数字孪生系统处理...")
        dt_result = dt_system.process_frame(card_detections)
        digital_twin_cards = dt_result['digital_twin_cards']
        print(f"✅ 数字孪生处理完成: {len(digital_twin_cards)} 张卡牌")
        
        # 显示数字孪生卡牌示例
        if digital_twin_cards:
            sample_card = digital_twin_cards[0]
            print(f"   示例数字孪生卡牌: twin_id='{sample_card.twin_id}', region='{sample_card.region_name}'")
        
        # 生成同步双轨输出
        print("🔄 生成同步双轨输出...")
        dual_result = dt_system.export_synchronized_dual_format(
            dt_result, width, height, test_image.name
        )
        print("✅ 同步双轨输出生成成功")
        
        # 检查双轨输出结构
        required_keys = ['rlcard_format', 'anylabeling_format', 'consistency_validation', 'metadata']
        for key in required_keys:
            if key not in dual_result:
                print(f"❌ 双轨输出缺少键: {key}")
                return False
        
        print("✅ 双轨输出结构完整")
        
        # 检查一致性验证
        consistency = dual_result['consistency_validation']
        print(f"📊 双轨一致性分数: {consistency['consistency_score']:.3f}")
        print(f"📊 双轨是否一致: {'✅' if consistency['is_consistent'] else '❌'}")
        
        if consistency['issues']:
            print(f"⚠️ 一致性问题: {consistency['issues']}")
        
        # 检查RLCard格式
        rlcard_format = dual_result['rlcard_format']
        rlcard_total = sum(len(cards) for cards in [
            rlcard_format.get('hand', []),
            rlcard_format.get('discard_pile', []),
            rlcard_format.get('opponent_discard_pile', []),
            rlcard_format.get('combo_cards', []),
            rlcard_format.get('opponent_combo_cards', [])
        ])
        print(f"📊 RLCard格式卡牌总数: {rlcard_total}")
        
        # 检查AnyLabeling格式
        anylabeling_format = dual_result['anylabeling_format']
        anylabeling_total = len(anylabeling_format.get('shapes', []))
        print(f"📊 AnyLabeling格式标注总数: {anylabeling_total}")
        
        # 与原始zhuangtaiquyu标注对比
        print("\n🔍 与原始zhuangtaiquyu标注对比:")
        print(f"   原始标注卡牌数: {len(original_shapes)}")
        print(f"   生成标注卡牌数: {anylabeling_total}")
        print(f"   数字孪生卡牌数: {len(digital_twin_cards)}")
        
        # 简单的标签对比
        original_labels = set(shape.get('label', '') for shape in original_shapes)
        generated_labels = set()
        for shape in anylabeling_format.get('shapes', []):
            # 从数字孪生ID构建zhuangtaiquyu格式标签
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            if twin_id:
                # 转换格式：1_二 -> 1二
                if '_' in twin_id and not twin_id.startswith('虚拟'):
                    parts = twin_id.split('_')
                    if len(parts) >= 2:
                        generated_labels.add(f"{parts[0]}{parts[1]}")
                elif twin_id.startswith('虚拟_'):
                    generated_labels.add(twin_id.replace('虚拟_', '虚拟'))
                else:
                    generated_labels.add(twin_id)
        
        print(f"   原始标签示例: {list(original_labels)[:5]}")
        print(f"   生成标签示例: {list(generated_labels)[:5]}")
        
        # 计算标签匹配度
        if original_labels and generated_labels:
            matched_labels = original_labels.intersection(generated_labels)
            label_match_rate = len(matched_labels) / len(original_labels)
            print(f"   标签匹配率: {label_match_rate:.3f} ({len(matched_labels)}/{len(original_labels)})")
        
        # 保存测试结果
        output_dir = project_root / "tests" / "zhuangtaiquyu_dual_simple_output"
        output_dir.mkdir(exist_ok=True)
        
        # 保存生成的AnyLabeling格式
        with open(output_dir / "generated_anylabeling.json", 'w', encoding='utf-8') as f:
            json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
        
        # 保存生成的RLCard格式
        with open(output_dir / "generated_rlcard.json", 'w', encoding='utf-8') as f:
            json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
        
        # 保存原始标注（用于对比）
        with open(output_dir / "original_zhuangtaiquyu.json", 'w', encoding='utf-8') as f:
            json.dump(original_annotation, f, ensure_ascii=False, indent=2)
        
        # 保存一致性验证结果
        with open(output_dir / "consistency_validation.json", 'w', encoding='utf-8') as f:
            json.dump(consistency, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {output_dir}")
        
        # 评估测试结果
        success_criteria = [
            consistency['is_consistent'],  # 双轨一致性
            rlcard_total > 0,             # RLCard有卡牌
            anylabeling_total > 0,        # AnyLabeling有标注
            rlcard_total == anylabeling_total == len(digital_twin_cards)  # 数量一致
        ]
        
        success = all(success_criteria)
        
        print(f"\n🎯 测试结果评估:")
        print(f"   双轨一致性: {'✅' if consistency['is_consistent'] else '❌'}")
        print(f"   RLCard有效: {'✅' if rlcard_total > 0 else '❌'}")
        print(f"   AnyLabeling有效: {'✅' if anylabeling_total > 0 else '❌'}")
        print(f"   数量完全一致: {'✅' if rlcard_total == anylabeling_total == len(digital_twin_cards) else '❌'}")
        print(f"   总体成功: {'✅' if success else '❌'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始zhuangtaiquyu双轨机制简化验证")
    print("=" * 60)
    
    success = test_dual_format_with_zhuangtaiquyu_sample()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 zhuangtaiquyu双轨机制验证成功！")
        print("   - 双轨输出功能正常")
        print("   - 一致性验证通过")
        print("   - 与zhuangtaiquyu格式兼容")
        print("   - 可以进行大规模验证")
    else:
        print("⚠️ zhuangtaiquyu双轨机制验证失败")
        print("   - 请检查错误信息")
        print("   - 修复问题后重新测试")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

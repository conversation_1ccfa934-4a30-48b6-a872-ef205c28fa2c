#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
zhuangtaiquyu数据集双轨机制验证测试
使用真实的zhuangtaiquyu数据集验证同步双轨输出系统
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
import traceback

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator

class ZhuangtaiquyuDualFormatTester:
    """zhuangtaiquyu数据集双轨机制验证器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        self.validator = SynchronizedDualFormatValidator()
        self.zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
        self.output_path = Path("tests/zhuangtaiquyu_dual_format_reports")
        self.output_path.mkdir(exist_ok=True)
        
        # 测试统计
        self.test_results = {
            "total_files": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "consistency_scores": [],
            "format_compatibility": [],
            "detailed_results": []
        }
    
    def load_zhuangtaiquyu_sample(self, train_dir: str, frame_name: str) -> Dict[str, Any]:
        """加载zhuangtaiquyu样本数据"""
        json_path = self.zhuangtaiquyu_path / "labels" / "train" / train_dir / f"{frame_name}.json"
        if not json_path.exists():
            raise FileNotFoundError(f"找不到文件: {json_path}")
        
        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def convert_zhuangtaiquyu_to_detections(self, zhuangtaiquyu_data: Dict[str, Any]) -> List[CardDetection]:
        """将zhuangtaiquyu格式转换为CardDetection列表"""
        detections = []
        
        for shape in zhuangtaiquyu_data.get("shapes", []):
            label = shape.get("label", "")
            points = shape.get("points", [])
            group_id = shape.get("group_id", 1)
            
            if not points or len(points) != 4:
                continue
            
            # 转换坐标格式：points -> bbox
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            
            # 解析标签：如"1壹" -> 卡牌名称"壹"，区域信息从group_id获取
            card_name = label[1:] if len(label) > 1 and label[0].isdigit() else label
            
            # 创建CardDetection对象
            detection = CardDetection(
                label=card_name,
                bbox=bbox,
                confidence=0.95,  # 使用默认置信度
                group_id=group_id,
                region_name=f"区域_{group_id}",
                owner="player"
            )
            detections.append(detection)
        
        return detections
    
    def test_single_file(self, train_dir: str, frame_name: str) -> Dict[str, Any]:
        """测试单个文件的双轨输出"""
        try:
            print(f"🔍 测试文件: {train_dir}/{frame_name}")
            
            # 1. 加载zhuangtaiquyu原始数据
            original_data = self.load_zhuangtaiquyu_sample(train_dir, frame_name)
            
            # 2. 转换为检测数据
            detections = self.convert_zhuangtaiquyu_to_detections(original_data)
            
            if not detections:
                print(f"⚠️  文件 {frame_name} 没有有效检测数据")
                return {"status": "skipped", "reason": "no_valid_detections"}
            
            # 3. 数字孪生系统处理
            result = self.dt_system.process_frame(detections)
            
            # 4. 双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                result, 640, 320, f"{frame_name}.jpg"
            )
            
            # 5. 一致性验证
            consistency = dual_result["consistency_validation"]
            
            # 6. 格式兼容性验证
            anylabeling_format = dual_result["anylabeling_format"]
            rlcard_format = dual_result["rlcard_format"]
            
            # 验证与原始zhuangtaiquyu格式的兼容性
            format_compatibility = self.validate_format_compatibility(
                original_data, anylabeling_format
            )
            
            test_result = {
                "file": f"{train_dir}/{frame_name}",
                "status": "success",
                "original_cards": len(original_data.get("shapes", [])),
                "detected_cards": len(detections),
                "digital_twin_cards": len(result["digital_twin_cards"]),
                "consistency_score": consistency["consistency_score"],
                "is_consistent": consistency["is_consistent"],
                "format_compatibility": format_compatibility,
                "rlcard_hand_count": len(rlcard_format.get("hand", [])),
                "anylabeling_shapes_count": len(anylabeling_format.get("shapes", [])),
                "errors": consistency.get("errors", [])
            }
            
            print(f"✅ 测试成功: 一致性={consistency['consistency_score']:.3f}")
            return test_result
            
        except Exception as e:
            error_msg = f"测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                "file": f"{train_dir}/{frame_name}",
                "status": "failed",
                "error": error_msg,
                "traceback": traceback.format_exc()
            }
    
    def validate_format_compatibility(self, original: Dict[str, Any], generated: Dict[str, Any]) -> Dict[str, Any]:
        """验证生成的AnyLabeling格式与原始zhuangtaiquyu格式的兼容性"""
        original_shapes = original.get("shapes", [])
        generated_shapes = generated.get("shapes", [])
        
        # 基本结构兼容性
        structure_compatible = (
            "shapes" in generated and
            "imageHeight" in generated and
            "imageWidth" in generated
        )
        
        # 标签格式兼容性
        label_format_compatible = True
        for shape in generated_shapes:
            label = shape.get("label", "")
            if not label or not isinstance(label, str):
                label_format_compatible = False
                break
        
        # 坐标格式兼容性
        coordinate_format_compatible = True
        for shape in generated_shapes:
            points = shape.get("points", [])
            if not points or len(points) != 4:
                coordinate_format_compatible = False
                break
        
        return {
            "structure_compatible": structure_compatible,
            "label_format_compatible": label_format_compatible,
            "coordinate_format_compatible": coordinate_format_compatible,
            "overall_compatible": structure_compatible and label_format_compatible and coordinate_format_compatible
        }
    
    def run_comprehensive_test(self):
        """运行全面的zhuangtaiquyu数据集验证测试"""
        print("🚀 开始zhuangtaiquyu数据集双轨机制全面验证")
        print("=" * 60)
        
        # 获取所有训练目录
        train_dirs = [d for d in (self.zhuangtaiquyu_path / "labels" / "train").iterdir() 
                     if d.is_dir()]
        
        print(f"📁 发现 {len(train_dirs)} 个训练目录")
        
        for train_dir in train_dirs:
            dir_name = train_dir.name
            print(f"\n📂 处理目录: {dir_name}")
            
            # 获取目录中的所有JSON文件
            json_files = list(train_dir.glob("*.json"))
            print(f"   发现 {len(json_files)} 个JSON文件")
            
            for json_file in json_files[:5]:  # 每个目录测试前5个文件
                frame_name = json_file.stem
                
                # 运行单个文件测试
                result = self.test_single_file(dir_name, frame_name)
                
                # 更新统计
                self.test_results["total_files"] += 1
                if result["status"] == "success":
                    self.test_results["successful_tests"] += 1
                    self.test_results["consistency_scores"].append(result["consistency_score"])
                    self.test_results["format_compatibility"].append(result["format_compatibility"]["overall_compatible"])
                else:
                    self.test_results["failed_tests"] += 1
                
                self.test_results["detailed_results"].append(result)
        
        # 生成测试报告
        self.generate_test_report()
        
        print("\n🎉 zhuangtaiquyu数据集双轨机制验证完成！")
        print("=" * 60)
    
    def generate_test_report(self):
        """生成详细的测试报告"""
        report = {
            "test_summary": {
                "total_files": self.test_results["total_files"],
                "successful_tests": self.test_results["successful_tests"],
                "failed_tests": self.test_results["failed_tests"],
                "success_rate": self.test_results["successful_tests"] / max(self.test_results["total_files"], 1)
            },
            "consistency_analysis": {
                "average_consistency_score": sum(self.test_results["consistency_scores"]) / max(len(self.test_results["consistency_scores"]), 1),
                "min_consistency_score": min(self.test_results["consistency_scores"]) if self.test_results["consistency_scores"] else 0,
                "max_consistency_score": max(self.test_results["consistency_scores"]) if self.test_results["consistency_scores"] else 0,
                "consistency_scores_above_95": sum(1 for score in self.test_results["consistency_scores"] if score >= 0.95)
            },
            "format_compatibility_analysis": {
                "compatible_files": sum(self.test_results["format_compatibility"]),
                "compatibility_rate": sum(self.test_results["format_compatibility"]) / max(len(self.test_results["format_compatibility"]), 1)
            },
            "detailed_results": self.test_results["detailed_results"]
        }
        
        # 保存报告
        report_path = self.output_path / f"zhuangtaiquyu_dual_format_report_{len(self.test_results['detailed_results'])}_files.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print(f"\n📊 测试摘要:")
        print(f"   总文件数: {report['test_summary']['total_files']}")
        print(f"   成功测试: {report['test_summary']['successful_tests']}")
        print(f"   失败测试: {report['test_summary']['failed_tests']}")
        print(f"   成功率: {report['test_summary']['success_rate']:.2%}")
        print(f"   平均一致性分数: {report['consistency_analysis']['average_consistency_score']:.3f}")
        print(f"   格式兼容率: {report['format_compatibility_analysis']['compatibility_rate']:.2%}")
        print(f"   详细报告已保存到: {report_path}")

def main():
    """主函数"""
    try:
        tester = ZhuangtaiquyuDualFormatTester()
        tester.run_comprehensive_test()
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        traceback.print_exc()
        return 1
    return 0

if __name__ == "__main__":
    exit(main())

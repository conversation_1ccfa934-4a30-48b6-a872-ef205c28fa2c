"""
数字孪生系统V2.0 集成验证测试

本测试脚本对数字孪生系统进行全面验证，包括：
1. 基础功能测试
2. 物理约束验证
3. 帧间继承测试
4. 多帧共识验证
5. 大数据集验证（calibration_gt和zhuangtaiquyu）
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator, 
    CardDetection, 
    create_digital_twin_system,
    convert_detections_to_cards
)

class DigitalTwinV2Tester:
    """数字孪生V2.0测试器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        self.test_results = {
            "basic_tests": {},
            "constraint_tests": {},
            "inheritance_tests": {},
            "consensus_tests": {},
            "dataset_tests": {}
        }
        
    def run_basic_functionality_tests(self):
        """基础功能测试"""
        print("🧪 开始基础功能测试...")
        
        # 测试1：单帧处理
        detections = [
            CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
        ]
        
        result = self.dt_system.process_frame(detections)
        
        self.test_results["basic_tests"]["single_frame"] = {
            "passed": len(result["digital_twin_cards"]) == 2,
            "cards_count": len(result["digital_twin_cards"]),
            "consensus_score": result["consensus_score"]
        }
        
        # 测试2：AnyLabeling格式导出
        anylabeling_data = self.dt_system.export_to_anylabeling_format(result, 640, 320)
        
        self.test_results["basic_tests"]["anylabeling_export"] = {
            "passed": "shapes" in anylabeling_data and len(anylabeling_data["shapes"]) == 2,
            "shapes_count": len(anylabeling_data.get("shapes", [])),
            "has_metadata": "digital_twin_metadata" in anylabeling_data
        }
        
        print(f"✅ 基础功能测试完成")
        
    def run_physical_constraint_tests(self):
        """物理约束测试"""
        print("🔒 开始物理约束测试...")
        
        # 重置系统
        self.dt_system.reset_session()
        
        # 测试：尝试分配超过4张相同卡牌
        detections = []
        for i in range(6):  # 尝试分配6张"二"
            detections.append(
                CardDetection("二", [100+i*10, 100, 150+i*10, 150], 0.9, 1, "手牌_观战方", "spectator")
            )
        
        result = self.dt_system.process_frame(detections)
        
        # 检查是否有虚拟牌
        virtual_cards = [card for card in result["digital_twin_cards"] if card.is_virtual]
        physical_cards = [card for card in result["digital_twin_cards"] if not card.is_virtual]
        
        self.test_results["constraint_tests"]["virtual_card_creation"] = {
            "passed": len(virtual_cards) == 2 and len(physical_cards) == 4,
            "virtual_count": len(virtual_cards),
            "physical_count": len(physical_cards),
            "constraint_valid": self.dt_system.card_manager.validate_constraints()
        }
        
        print(f"✅ 物理约束测试完成")
        
    def run_inheritance_tests(self):
        """帧间继承测试"""
        print("🔄 开始帧间继承测试...")
        
        # 重置系统
        self.dt_system.reset_session()
        
        # 第一帧
        frame1_detections = [
            CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
        ]
        result1 = self.dt_system.process_frame(frame1_detections)
        
        # 第二帧（卡牌位置略有变化）
        frame2_detections = [
            CardDetection("二", [105, 105, 155, 155], 0.93, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [165, 105, 215, 155], 0.90, 1, "手牌_观战方", "spectator"),
        ]
        result2 = self.dt_system.process_frame(frame2_detections)
        
        # 检查ID是否保持一致
        frame1_ids = {card.twin_id for card in result1["digital_twin_cards"]}
        frame2_ids = {card.twin_id for card in result2["digital_twin_cards"]}
        
        self.test_results["inheritance_tests"]["id_consistency"] = {
            "passed": frame1_ids == frame2_ids,
            "frame1_ids": list(frame1_ids),
            "frame2_ids": list(frame2_ids),
            "consistency_ratio": len(frame1_ids & frame2_ids) / len(frame1_ids | frame2_ids)
        }
        
        # 测试区域流转
        frame3_detections = [
            CardDetection("二", [105, 105, 155, 155], 0.93, 6, "吃碰区_观战方", "spectator"),  # 区域变化
            CardDetection("三", [165, 105, 215, 155], 0.90, 1, "手牌_观战方", "spectator"),
        ]
        result3 = self.dt_system.process_frame(frame3_detections)
        
        self.test_results["inheritance_tests"]["region_transition"] = {
            "passed": len(result3["region_transitions"]) > 0,
            "transitions_count": len(result3["region_transitions"]),
            "transitions": result3["region_transitions"]
        }
        
        print(f"✅ 帧间继承测试完成")
        
    def run_consensus_tests(self):
        """多帧共识测试"""
        print("🤝 开始多帧共识测试...")
        
        # 重置系统
        self.dt_system.reset_session()
        
        # 处理多帧以建立历史
        for i in range(6):
            detections = [
                CardDetection("二", [100+i, 100+i, 150+i, 150+i], 0.9, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [160+i, 100+i, 210+i, 150+i], 0.9, 1, "手牌_观战方", "spectator"),
            ]
            result = self.dt_system.process_frame(detections)
        
        # 最后一帧的共识分数应该比较高
        final_consensus = result["consensus_score"]
        
        self.test_results["consensus_tests"]["consensus_score"] = {
            "passed": final_consensus > 0.5,
            "score": final_consensus,
            "frames_processed": self.dt_system.total_frames_processed
        }
        
        # 测试异常检测（突然出现新卡牌）
        anomaly_detections = [
            CardDetection("二", [100, 100, 150, 150], 0.9, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160, 100, 210, 150], 0.9, 1, "手牌_观战方", "spectator"),
            CardDetection("四", [220, 100, 270, 150], 0.9, 1, "手牌_观战方", "spectator"),  # 新卡牌
        ]
        anomaly_result = self.dt_system.process_frame(anomaly_detections)
        
        self.test_results["consensus_tests"]["anomaly_detection"] = {
            "passed": len(anomaly_result["anomalies"]) > 0,
            "anomalies_count": len(anomaly_result["anomalies"]),
            "anomalies": anomaly_result["anomalies"]
        }
        
        print(f"✅ 多帧共识测试完成")
        
    def run_dataset_validation(self):
        """大数据集验证"""
        print("📊 开始大数据集验证...")
        
        # 验证calibration_gt数据集
        calibration_path = Path("legacy_assets/ceshi/calibration_gt")
        if calibration_path.exists():
            self._validate_calibration_gt(calibration_path)
        else:
            print("⚠️ calibration_gt数据集未找到")
            
        # 验证zhuangtaiquyu数据集
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
        if zhuangtaiquyu_path.exists():
            self._validate_zhuangtaiquyu(zhuangtaiquyu_path)
        else:
            print("⚠️ zhuangtaiquyu数据集未找到")
            
        print(f"✅ 大数据集验证完成")
        
    def _validate_calibration_gt(self, dataset_path: Path):
        """验证calibration_gt数据集"""
        print("  📸 验证calibration_gt数据集...")
        
        labels_path = dataset_path / "labels"
        if not labels_path.exists():
            return
            
        # 重置系统
        self.dt_system.reset_session()
        
        processed_frames = 0
        total_cards = 0
        
        # 处理前50帧进行快速验证
        for json_file in sorted(labels_path.glob("*.json"))[:50]:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 转换为检测结果
                detections = []
                for shape in data.get("shapes", []):
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        
                        detection = CardDetection(
                            label=shape.get("label", ""),
                            bbox=[x1, y1, x2, y2],
                            confidence=shape.get("score", 1.0),
                            group_id=shape.get("group_id", 0),
                            region_name=shape.get("region_name", ""),
                            owner=shape.get("owner", "")
                        )
                        detections.append(detection)
                
                # 处理帧
                result = self.dt_system.process_frame(detections)
                processed_frames += 1
                total_cards += len(result["digital_twin_cards"])
                
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        stats = self.dt_system.get_session_statistics()
        
        self.test_results["dataset_tests"]["calibration_gt"] = {
            "processed_frames": processed_frames,
            "total_cards": total_cards,
            "average_cards_per_frame": total_cards / processed_frames if processed_frames > 0 else 0,
            "final_consensus_score": stats.get("consensus_score", 0),
            "virtual_cards_created": stats.get("virtual_cards_created", 0),
            "physical_constraints_valid": stats.get("physical_constraints_valid", False)
        }
        
        print(f"    ✅ calibration_gt验证完成: {processed_frames}帧, {total_cards}张卡牌")
        
    def _validate_zhuangtaiquyu(self, dataset_path: Path):
        """验证zhuangtaiquyu数据集"""
        print("  🎯 验证zhuangtaiquyu数据集...")
        
        # 这里可以添加zhuangtaiquyu数据集的验证逻辑
        # 由于时间限制，暂时跳过详细实现
        
        self.test_results["dataset_tests"]["zhuangtaiquyu"] = {
            "status": "skipped",
            "reason": "detailed_implementation_pending"
        }
        
    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        report = {
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "system_info": {
                "session_id": self.dt_system.session_id,
                "total_frames_processed": self.dt_system.total_frames_processed
            },
            "test_results": self.test_results,
            "summary": self._calculate_summary()
        }
        
        return report
        
    def _calculate_summary(self) -> Dict[str, Any]:
        """计算测试总结"""
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            for test_name, result in tests.items():
                if isinstance(result, dict) and "passed" in result:
                    total_tests += 1
                    if result["passed"]:
                        passed_tests += 1
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "overall_status": "PASSED" if passed_tests == total_tests else "FAILED"
        }

def main():
    """主测试函数"""
    print("🚀 数字孪生系统V2.0 集成验证测试")
    print("=" * 50)
    
    tester = DigitalTwinV2Tester()
    
    # 运行所有测试
    tester.run_basic_functionality_tests()
    tester.run_physical_constraint_tests()
    tester.run_inheritance_tests()
    tester.run_consensus_tests()
    tester.run_dataset_validation()
    
    # 生成报告
    report = tester.generate_report()
    
    # 保存报告
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    report_file = f"tests/validation_report_v2_{timestamp}.json"
    
    os.makedirs("tests", exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    summary = report["summary"]
    print("\n" + "=" * 50)
    print("📋 测试总结")
    print(f"总测试数: {summary['total_tests']}")
    print(f"通过测试: {summary['passed_tests']}")
    print(f"成功率: {summary['success_rate']:.1%}")
    print(f"整体状态: {summary['overall_status']}")
    print(f"报告已保存: {report_file}")
    
    return summary["overall_status"] == "PASSED"

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

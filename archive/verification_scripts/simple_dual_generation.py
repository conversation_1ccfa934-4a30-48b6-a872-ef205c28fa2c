import sys
import os
import json
sys.path.insert(0, '.')

from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection

print("开始简单双轨生成测试...")

# 创建系统
dt_system = create_digital_twin_system()
print('系统创建成功')

# 创建测试数据
detection = CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')

# 处理数据
result = dt_system.process_frame([detection])
print(f'处理结果: {len(result.get("digital_twin_cards", []))} 张卡牌')

# 双轨输出
dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')

# 保存AnyLabeling格式
anylabeling_format = dual_result['anylabeling_format']
with open('test_anylabeling_output.json', 'w', encoding='utf-8') as f:
    json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)

print('AnyLabeling格式文件已保存: test_anylabeling_output.json')
print('文件内容预览:')
print(json.dumps(anylabeling_format, ensure_ascii=False, indent=2)[:500] + '...')

print('简单双轨生成测试完成')

#!/usr/bin/env python3
"""
真实数据验证脚本
基于calibration_gt和zhuangtaiquyu的真实标注数据进行双轨验证
"""

import sys
import os
import json
from datetime import datetime

sys.path.insert(0, '.')

def load_real_annotation(file_path):
    """加载真实标注文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return None

def convert_annotation_to_detections(annotation_data, image_name):
    """将标注数据转换为检测结果"""
    from src.core.digital_twin_v2 import CardDetection
    
    detections = []
    if not annotation_data or 'shapes' not in annotation_data:
        return detections
    
    for shape in annotation_data['shapes']:
        try:
            label = shape.get('label', 'unknown')
            points = shape.get('points', [])
            group_id = shape.get('group_id', 1)
            
            if len(points) >= 2:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                detection = CardDetection(
                    label=label,
                    bbox=[x1, y1, x2, y2],
                    confidence=0.95,  # 真实标注的高置信度
                    region_id=group_id,
                    region_name=f'region_{group_id}',
                    player_perspective='spectator'
                )
                detections.append(detection)
        except:
            continue
    
    return detections

def main():
    print("🚀 真实数据双轨验证")
    print("基于calibration_gt和zhuangtaiquyu的真实标注数据")
    print("=" * 60)
    
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统创建成功")
        
        if not hasattr(dt_system, 'export_synchronized_dual_format'):
            print("❌ 双轨输出方法不存在")
            return False
        print("✅ 双轨输出方法存在")
        
        # 验证calibration_gt数据
        print("\n📋 验证calibration_gt真实数据...")
        calibration_results = []
        
        # 检查前10个标注文件
        for i in range(10):
            label_file = f"legacy_assets/ceshi/calibration_gt/labels/frame_{i:05d}.json"
            if os.path.exists(label_file):
                annotation = load_real_annotation(label_file)
                if annotation:
                    detections = convert_annotation_to_detections(annotation, f"frame_{i:05d}.jpg")
                    if detections:
                        try:
                            result = dt_system.process_frame(detections)
                            dual_result = dt_system.export_synchronized_dual_format(
                                result, 640, 480, f"frame_{i:05d}.jpg"
                            )
                            
                            consistency = dual_result['consistency_validation']
                            score = consistency.get('consistency_score', 0)
                            calibration_results.append({
                                'frame': f"frame_{i:05d}",
                                'detections_count': len(detections),
                                'consistency_score': score,
                                'rlcard_cards': len(dual_result['rlcard_format'].get('hand', [])),
                                'anylabeling_shapes': len(dual_result['anylabeling_format'].get('shapes', []))
                            })
                            print(f"   frame_{i:05d}: {len(detections)}张卡牌, 一致性{score:.3f}")
                        except Exception as e:
                            print(f"   ⚠️ frame_{i:05d}处理失败: {e}")
        
        # 验证zhuangtaiquyu数据
        print("\n📋 验证zhuangtaiquyu真实数据...")
        zhuangtaiquyu_results = []
        
        # 检查第1个子目录的前5个文件
        subdir = "1"
        for i in range(5):
            label_file = f"legacy_assets/ceshi/zhuangtaiquyu/labels/train/{subdir}/frame_{i:05d}.json"
            if os.path.exists(label_file):
                annotation = load_real_annotation(label_file)
                if annotation:
                    detections = convert_annotation_to_detections(annotation, f"frame_{i:05d}.jpg")
                    if detections:
                        try:
                            result = dt_system.process_frame(detections)
                            dual_result = dt_system.export_synchronized_dual_format(
                                result, 640, 480, f"zhuangtaiquyu_{subdir}_frame_{i:05d}.jpg"
                            )
                            
                            consistency = dual_result['consistency_validation']
                            score = consistency.get('consistency_score', 0)
                            zhuangtaiquyu_results.append({
                                'frame': f"subdir_{subdir}_frame_{i:05d}",
                                'detections_count': len(detections),
                                'consistency_score': score,
                                'rlcard_cards': len(dual_result['rlcard_format'].get('hand', [])),
                                'anylabeling_shapes': len(dual_result['anylabeling_format'].get('shapes', []))
                            })
                            print(f"   subdir_{subdir}_frame_{i:05d}: {len(detections)}张卡牌, 一致性{score:.3f}")
                        except Exception as e:
                            print(f"   ⚠️ subdir_{subdir}_frame_{i:05d}处理失败: {e}")
        
        # 统计分析
        all_results = calibration_results + zhuangtaiquyu_results
        if all_results:
            scores = [r['consistency_score'] for r in all_results]
            avg_score = sum(scores) / len(scores)
            min_score = min(scores)
            max_score = max(scores)
            high_quality = len([s for s in scores if s >= 0.95])
            
            print(f"\n📊 真实数据验证结果:")
            print(f"   验证文件数: {len(all_results)}")
            print(f"   calibration_gt: {len(calibration_results)} 个文件")
            print(f"   zhuangtaiquyu: {len(zhuangtaiquyu_results)} 个文件")
            print(f"   平均一致性: {avg_score:.3f}")
            print(f"   最低一致性: {min_score:.3f}")
            print(f"   最高一致性: {max_score:.3f}")
            print(f"   高质量率(≥0.95): {high_quality}/{len(scores)} ({(high_quality/len(scores))*100:.1f}%)")
            
            # 详细分析
            print(f"\n🔍 详细分析:")
            for result in all_results:
                frame = result['frame']
                detections = result['detections_count']
                score = result['consistency_score']
                rlcard = result['rlcard_cards']
                anylabeling = result['anylabeling_shapes']
                status = "✅" if score >= 0.95 else "⚠️"
                print(f"   {status} {frame}: {detections}检测→{rlcard}RLCard+{anylabeling}AnyLabeling, 一致性{score:.3f}")
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"real_data_verification_{timestamp}.json"
            
            verification_data = {
                "timestamp": timestamp,
                "summary": {
                    "total_files": len(all_results),
                    "calibration_gt_files": len(calibration_results),
                    "zhuangtaiquyu_files": len(zhuangtaiquyu_results),
                    "average_consistency": avg_score,
                    "min_consistency": min_score,
                    "max_consistency": max_score,
                    "high_quality_count": high_quality,
                    "high_quality_rate": (high_quality/len(scores))*100
                },
                "detailed_results": all_results
            }
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(verification_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 验证结果已保存: {result_file}")
            
            # 最终判断
            if avg_score >= 0.95:
                print(f"\n🎉 真实数据验证完全成功！")
                print(f"   ✅ 平均一致性: {avg_score:.3f} (≥0.95)")
                print(f"   ✅ 基于真实标注数据验证")
                print(f"   ✅ 覆盖calibration_gt和zhuangtaiquyu数据集")
                
                # 与开发过程14对比
                improvement = ((avg_score - 0.3) / 0.3) * 100
                print(f"\n📈 与开发过程14对比:")
                print(f"   改进前: 一致性分数0.3 (StateBuilder黑盒问题)")
                print(f"   改进后: 一致性分数{avg_score:.3f} (统一数据源)")
                print(f"   提升幅度: {improvement:.1f}%")
                print(f"   技术突破: ✅ 彻底解决双轨同步问题")
                
                return True
            else:
                print(f"\n⚠️ 真实数据验证需要改进")
                print(f"   当前一致性: {avg_score:.3f} (目标: ≥0.95)")
                return False
        else:
            print("❌ 没有找到有效的真实数据")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    print(f"真实数据验证结果: {'🎉 成功' if success else '❌ 需要改进'}")
    sys.exit(0 if success else 1)

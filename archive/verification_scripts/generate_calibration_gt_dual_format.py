#!/usr/bin/env python3
"""
生成calibration_gt数据集的同步双轨输出
为372张图像生成最新的AnyLabeling格式JSON文件，用于人工审核
"""

import sys
import os
import json
import shutil
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

def load_original_annotation(file_path):
    """加载原始标注文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"   ⚠️ 无法加载标注文件 {file_path}: {e}")
        return None

def convert_to_detections(annotation_data, image_name):
    """将原始标注转换为检测结果"""
    from src.core.digital_twin_v2 import CardDetection
    
    detections = []
    if not annotation_data or 'shapes' not in annotation_data:
        return detections
    
    for i, shape in enumerate(annotation_data['shapes']):
        try:
            label = shape.get('label', 'unknown')
            points = shape.get('points', [])
            group_id = shape.get('group_id', 1)
            score = shape.get('score', 0.9)
            
            if len(points) >= 2:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                detection = CardDetection(
                    label=label,
                    bbox=[x1, y1, x2, y2],
                    confidence=score,
                    region_id=group_id,
                    region_name=f'region_{group_id}',
                    player_perspective='spectator'
                )
                detections.append(detection)
        except Exception as e:
            print(f"   ⚠️ 处理标注时出错: {e}")
            continue
    
    return detections

def main():
    print("🚀 生成calibration_gt数据集的同步双轨输出")
    print("=" * 70)
    
    try:
        # 导入模块
        print("1. 导入核心模块...")
        from src.core.digital_twin_v2 import create_digital_twin_system
        print("   ✅ 模块导入成功")
        
        # 创建数字孪生系统
        print("2. 创建数字孪生系统V2.0...")
        dt_system = create_digital_twin_system()
        print("   ✅ 数字孪生系统创建成功")
        
        # 检查双轨输出方法
        if not hasattr(dt_system, 'export_synchronized_dual_format'):
            print("   ❌ 双轨输出方法不存在")
            return False
        print("   ✅ 双轨输出方法存在")
        
        # 设置路径
        source_images_dir = "legacy_assets/ceshi/calibration_gt/images"
        source_labels_dir = "legacy_assets/ceshi/calibration_gt/labels"
        
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_base_dir = f"calibration_gt_synchronized_dual_format_{timestamp}"
        
        # 创建目录结构
        output_images_dir = os.path.join(output_base_dir, "images")
        output_anylabeling_dir = os.path.join(output_base_dir, "anylabeling_labels")
        output_rlcard_dir = os.path.join(output_base_dir, "rlcard_outputs")
        
        os.makedirs(output_images_dir, exist_ok=True)
        os.makedirs(output_anylabeling_dir, exist_ok=True)
        os.makedirs(output_rlcard_dir, exist_ok=True)
        
        print(f"3. 输出目录创建: {output_base_dir}")
        
        # 获取所有图像文件
        image_files = [f for f in os.listdir(source_images_dir) if f.endswith('.jpg')]
        image_files.sort()
        
        print(f"4. 发现 {len(image_files)} 张图像")
        
        # 处理统计
        processed_count = 0
        success_count = 0
        consistency_scores = []
        
        print("5. 开始批量处理...")
        
        for i, image_file in enumerate(image_files):
            try:
                # 构建文件路径
                image_name = os.path.splitext(image_file)[0]
                label_file = f"{image_name}.json"
                
                source_image_path = os.path.join(source_images_dir, image_file)
                source_label_path = os.path.join(source_labels_dir, label_file)
                
                if not os.path.exists(source_label_path):
                    print(f"   ⚠️ 跳过 {image_file}: 缺少标注文件")
                    continue
                
                processed_count += 1
                
                # 加载原始标注
                annotation_data = load_original_annotation(source_label_path)
                if not annotation_data:
                    continue
                
                # 转换为检测结果
                detections = convert_to_detections(annotation_data, image_file)
                if not detections:
                    print(f"   ⚠️ 跳过 {image_file}: 无有效检测")
                    continue
                
                # 数字孪生处理
                result = dt_system.process_frame(detections)
                
                # 双轨输出
                dual_result = dt_system.export_synchronized_dual_format(
                    result, 640, 320, image_file
                )
                
                # 保存AnyLabeling格式
                anylabeling_output_path = os.path.join(output_anylabeling_dir, label_file)
                with open(anylabeling_output_path, 'w', encoding='utf-8') as f:
                    json.dump(dual_result['anylabeling_format'], f, ensure_ascii=False, indent=2)
                
                # 保存RLCard格式
                rlcard_output_path = os.path.join(output_rlcard_dir, label_file)
                with open(rlcard_output_path, 'w', encoding='utf-8') as f:
                    json.dump(dual_result['rlcard_format'], f, ensure_ascii=False, indent=2)
                
                # 复制图像文件
                output_image_path = os.path.join(output_images_dir, image_file)
                shutil.copy2(source_image_path, output_image_path)
                
                # 记录一致性分数
                consistency = dual_result['consistency_validation']
                score = consistency.get('consistency_score', 0)
                consistency_scores.append(score)
                
                success_count += 1
                
                # 进度显示
                if (i + 1) % 50 == 0:
                    avg_score = sum(consistency_scores[-50:]) / min(50, len(consistency_scores))
                    print(f"   进度: {i+1}/{len(image_files)}, 最近50次平均一致性: {avg_score:.3f}")
                
            except Exception as e:
                print(f"   ❌ 处理 {image_file} 时出错: {e}")
                continue
        
        # 生成统计报告
        if consistency_scores:
            avg_consistency = sum(consistency_scores) / len(consistency_scores)
            min_consistency = min(consistency_scores)
            max_consistency = max(consistency_scores)
            high_quality_count = len([s for s in consistency_scores if s >= 0.95])
            
            print(f"\n📊 处理结果统计:")
            print(f"   总图像数: {len(image_files)}")
            print(f"   处理成功: {success_count}")
            print(f"   成功率: {(success_count/len(image_files))*100:.1f}%")
            print(f"   平均一致性: {avg_consistency:.3f}")
            print(f"   最低一致性: {min_consistency:.3f}")
            print(f"   最高一致性: {max_consistency:.3f}")
            print(f"   高质量率(≥0.95): {high_quality_count}/{len(consistency_scores)} ({(high_quality_count/len(consistency_scores))*100:.1f}%)")
        
        # 保存处理报告
        report = {
            "generation_info": {
                "timestamp": timestamp,
                "generator_version": "2.1.0_synchronized_dual_format",
                "source_dataset": "calibration_gt",
                "total_images": len(image_files),
                "processed_images": processed_count,
                "successful_images": success_count
            },
            "consistency_analysis": {
                "average_score": avg_consistency if consistency_scores else 0,
                "min_score": min_consistency if consistency_scores else 0,
                "max_score": max_consistency if consistency_scores else 0,
                "high_quality_count": high_quality_count if consistency_scores else 0,
                "high_quality_rate": (high_quality_count/len(consistency_scores))*100 if consistency_scores else 0
            },
            "output_structure": {
                "images_dir": "images/",
                "anylabeling_labels_dir": "anylabeling_labels/",
                "rlcard_outputs_dir": "rlcard_outputs/",
                "description": "AnyLabeling格式文件可直接导入AnyLabeling进行人工审核"
            }
        }
        
        report_file = os.path.join(output_base_dir, "generation_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 创建使用指南
        usage_guide = f"""# calibration_gt同步双轨输出使用指南

## 生成信息
- 生成时间: {timestamp}
- 源数据集: calibration_gt (372张图像)
- 处理成功: {success_count} 张图像
- 平均一致性分数: {avg_consistency:.3f}

## 目录结构
```
{output_base_dir}/
├── images/                    # 图像文件 (372张)
├── anylabeling_labels/        # AnyLabeling格式JSON文件 (用于人工审核)
├── rlcard_outputs/           # RLCard格式JSON文件 (用于AI决策)
├── generation_report.json    # 详细生成报告
└── 使用指南.md               # 本文件
```

## AnyLabeling导入步骤

### 1. 打开AnyLabeling
启动AnyLabeling软件

### 2. 导入数据集
1. 点击 "File" → "Open Dir"
2. 选择 `{output_base_dir}/images` 目录
3. AnyLabeling会自动加载所有图像

### 3. 加载标注
1. 确保 `{output_base_dir}/anylabeling_labels` 目录中的JSON文件与图像文件名对应
2. AnyLabeling会自动加载对应的标注文件
3. 如果没有自动加载，可以手动指定标注目录

### 4. 人工审核
1. 逐张检查标注的准确性
2. 重点关注：
   - 数字孪生ID分配是否正确
   - 区域分配是否合理
   - 卡牌识别是否准确
3. 对错误的标注进行修正

### 5. 保存修正结果
1. 修正后保存标注文件
2. 可以导出修正后的数据集用于进一步训练

## 特殊说明

### 数字孪生信息
每个标注包含完整的数字孪生信息：
- `digital_twin_id`: 数字孪生唯一ID
- `region_name`: 区域名称
- `confidence_original`: 原始检测置信度
- `is_virtual`: 是否为虚拟卡牌
- `is_dark`: 是否为暗牌

### 一致性验证
- 平均一致性分数: {avg_consistency:.3f}
- 高质量标注率: {(high_quality_count/len(consistency_scores))*100:.1f}%
- 建议重点审核一致性分数较低的图像

## 技术支持
如有问题，请参考项目文档或联系开发团队。
"""
        
        usage_guide_file = os.path.join(output_base_dir, "使用指南.md")
        with open(usage_guide_file, 'w', encoding='utf-8') as f:
            f.write(usage_guide)
        
        print(f"\n💾 输出完成:")
        print(f"   📁 输出目录: {output_base_dir}")
        print(f"   🖼️ 图像文件: {output_images_dir}")
        print(f"   📋 AnyLabeling标注: {output_anylabeling_dir}")
        print(f"   🤖 RLCard输出: {output_rlcard_dir}")
        print(f"   📊 生成报告: {report_file}")
        print(f"   📖 使用指南: {usage_guide_file}")
        
        print(f"\n🎯 AnyLabeling导入路径:")
        print(f"   图像目录: {os.path.abspath(output_images_dir)}")
        print(f"   标注目录: {os.path.abspath(output_anylabeling_dir)}")
        
        if success_count > 0:
            print(f"\n🎉 同步双轨输出生成成功!")
            print(f"   ✅ 成功处理 {success_count} 张图像")
            print(f"   ✅ 平均一致性分数: {avg_consistency:.3f}")
            print(f"   ✅ 可直接导入AnyLabeling进行人工审核")
            return True
        else:
            print(f"\n❌ 没有成功处理任何图像")
            return False
            
    except Exception as e:
        print(f"\n❌ 生成过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*70}")
    print(f"最终结果: {'🎉 生成成功' if success else '❌ 生成失败'}")
    sys.exit(0 if success else 1)

"""
简单的继承机制测试
"""

import sys
sys.path.append('src')

def test_simple_inheritance():
    """简单测试继承机制"""
    print("🧪 简单继承测试")
    
    try:
        from modules.simple_inheritor import SimpleInheritor
        
        # 创建继承器
        inheritor = SimpleInheritor(iou_threshold=0.3)
        print("✅ 继承器创建成功")
        
        # 第1帧数据 - 模拟已分配ID的卡牌
        frame1_data = [
            {
                'label': '二', 
                'bbox': [100.0, 100.0, 150.0, 150.0], 
                'confidence': 0.9, 
                'group_id': 1,
                'twin_id': '1二',
                'is_virtual': False
            }
        ]
        
        print(f"📋 第1帧: {len(frame1_data)}张卡牌")
        result1 = inheritor.process_inheritance(frame1_data)
        print(f"   继承: {len(result1.inherited_cards)}张, 新增: {len(result1.new_cards)}张")
        print(f"   前一帧记录: {len(inheritor.previous_frame_cards)}张")
        
        # 检查前一帧记录
        if inheritor.previous_frame_cards:
            prev_card = inheritor.previous_frame_cards[0]
            print(f"   前一帧卡牌: {prev_card['label']} (ID: {prev_card['twin_id']})")
        
        # 第2帧数据 - 相同卡牌，位置略有变化
        frame2_data = [
            {
                'label': '二', 
                'bbox': [105.0, 105.0, 155.0, 155.0],  # 位置略有变化
                'confidence': 0.9, 
                'group_id': 1
                # 注意：没有twin_id，需要继承
            }
        ]
        
        print(f"\n📋 第2帧: {len(frame2_data)}张卡牌")
        
        # 手动测试匹配
        current_card = frame2_data[0]
        best_idx, best_score = inheritor._find_best_match(current_card, set())
        print(f"   匹配测试: idx={best_idx}, score={best_score:.3f}")
        
        if best_idx is not None:
            prev_card = inheritor.previous_frame_cards[best_idx]
            
            # 详细分析匹配分数
            label_match = current_card.get('label') == prev_card.get('label')
            group_match = current_card.get('group_id') == prev_card.get('group_id')
            
            current_bbox = current_card.get('bbox', [])
            prev_bbox = prev_card.get('bbox', [])
            iou = inheritor._calculate_iou(current_bbox, prev_bbox)
            
            print(f"   详细分析:")
            print(f"     标签匹配: {label_match} ({current_card.get('label')} vs {prev_card.get('label')})")
            print(f"     区域匹配: {group_match} ({current_card.get('group_id')} vs {prev_card.get('group_id')})")
            print(f"     IoU: {iou:.3f} (阈值: {inheritor.iou_threshold})")
            print(f"     总分: {best_score:.3f}")
        
        # 执行继承处理
        result2 = inheritor.process_inheritance(frame2_data)
        print(f"\n✅ 第2帧处理完成")
        print(f"   继承: {len(result2.inherited_cards)}张, 新增: {len(result2.new_cards)}张")
        
        # 显示结果
        if result2.inherited_cards:
            print("   🎯 继承成功:")
            for card in result2.inherited_cards:
                print(f"     ID: {card['twin_id']} (标签: {card['label']})")
        else:
            print("   ❌ 没有继承任何卡牌")
        
        if result2.new_cards:
            print("   🆕 新卡牌:")
            for card in result2.new_cards:
                print(f"     标签: {card['label']} (需要分配ID)")
        
        # 统计
        stats = result2.statistics
        inheritance_rate = stats.get('inheritance_rate', 0)
        print(f"\n📊 继承率: {inheritance_rate:.1%}")
        
        if inheritance_rate > 0:
            print("🎉 继承机制工作正常！")
        else:
            print("❌ 继承机制有问题！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_inheritance()

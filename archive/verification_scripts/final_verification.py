#!/usr/bin/env python3
"""
开发过程14最终验证测试
"""

import sys
import json
from datetime import datetime

sys.path.insert(0, '.')

def main():
    print("🚀 开发过程14最终验证测试")
    print("=" * 60)
    
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统V2.0创建成功")
        
        # 测试1: 基础双轨输出
        print("\n📋 测试1: 基础双轨输出功能")
        test_detections = [
            CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator'),
            CardDetection('三', [200, 100, 250, 150], 0.90, 1, '手牌_观战方', 'spectator'),
        ]
        
        result = dt_system.process_frame(test_detections)
        dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        
        consistency = dual_result['consistency_validation']
        score1 = consistency.get('consistency_score', 0)
        
        print(f"   一致性分数: {score1:.3f}")
        print(f"   状态: {'✅ 通过' if score1 >= 0.95 else '❌ 失败'}")
        
        # 测试2: zhuangtaiquyu格式兼容性
        print("\n📋 测试2: zhuangtaiquyu格式兼容性")
        zhuangtaiquyu_detections = [
            CardDetection('壹', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator'),
            CardDetection('贰', [200, 100, 250, 150], 0.90, 1, '手牌_观战方', 'spectator'),
            CardDetection('叁', [300, 100, 350, 150], 0.88, 6, '出牌区_观战方', 'spectator'),
        ]
        
        result2 = dt_system.process_frame(zhuangtaiquyu_detections)
        dual_result2 = dt_system.export_synchronized_dual_format(result2, 640, 480, 'zhuangtaiquyu.jpg')
        
        consistency2 = dual_result2['consistency_validation']
        score2 = consistency2.get('consistency_score', 0)
        
        print(f"   一致性分数: {score2:.3f}")
        print(f"   状态: {'✅ 通过' if score2 >= 0.95 else '❌ 失败'}")
        
        # 检查AnyLabeling格式
        anylabeling_format = dual_result2['anylabeling_format']
        shapes = anylabeling_format.get('shapes', [])
        
        print(f"   AnyLabeling标注数: {len(shapes)}")
        for i, shape in enumerate(shapes):
            label = shape.get('label', 'Unknown')
            group_id = shape.get('group_id', 'N/A')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
            print(f"     标注{i+1}: {label} (区域:{group_id}, ID:{twin_id})")
        
        # 测试3: 虚拟卡牌处理
        print("\n📋 测试3: 虚拟卡牌处理")
        # 创建大量相同卡牌来触发虚拟卡牌机制
        virtual_test_detections = []
        for i in range(6):  # 超过4张相同卡牌
            virtual_test_detections.append(
                CardDetection('二', [100+i*50, 100, 150+i*50, 150], 0.9, 1, '手牌_观战方', 'spectator')
            )
        
        result3 = dt_system.process_frame(virtual_test_detections)
        dual_result3 = dt_system.export_synchronized_dual_format(result3, 640, 320, 'virtual_test.jpg')
        
        consistency3 = dual_result3['consistency_validation']
        score3 = consistency3.get('consistency_score', 0)
        
        virtual_cards = len([card for card in result3['digital_twin_cards'] if card.is_virtual])
        
        print(f"   一致性分数: {score3:.3f}")
        print(f"   虚拟卡牌数: {virtual_cards}")
        print(f"   状态: {'✅ 通过' if score3 >= 0.95 else '❌ 失败'}")
        
        # 总结
        print("\n📊 验证结果总结")
        print("=" * 60)
        
        avg_score = (score1 + score2 + score3) / 3
        all_passed = all(score >= 0.95 for score in [score1, score2, score3])
        
        print(f"测试1 (基础功能): {score1:.3f} {'✅' if score1 >= 0.95 else '❌'}")
        print(f"测试2 (zhuangtaiquyu兼容): {score2:.3f} {'✅' if score2 >= 0.95 else '❌'}")
        print(f"测试3 (虚拟卡牌): {score3:.3f} {'✅' if score3 >= 0.95 else '❌'}")
        print(f"平均一致性分数: {avg_score:.3f}")
        
        print(f"\n🎯 开发过程14验证结果:")
        if all_passed and avg_score >= 0.95:
            print("🎉 验证完全成功！")
            print("   ✅ 双轨机制工作正常")
            print("   ✅ 一致性分数达标")
            print("   ✅ 格式兼容性良好")
            print("   ✅ 虚拟卡牌处理正确")
            print(f"   📊 平均一致性: {avg_score:.3f} (目标: ≥0.95)")
            
            # 与开发过程14文档对比
            print(f"\n📋 与开发过程14对比:")
            print(f"   问题: 之前一致性分数仅0.3")
            print(f"   解决: 现在一致性分数{avg_score:.3f}")
            print(f"   改进: 提升{((avg_score - 0.3) / 0.3 * 100):.1f}%")
            print(f"   状态: ✅ 彻底解决StateBuilder黑盒问题")
            
            return True
        else:
            print("❌ 验证存在问题")
            print(f"   平均一致性: {avg_score:.3f} (目标: ≥0.95)")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    print(f"最终结果: {'🎉 开发过程14验证成功' if success else '❌ 验证失败'}")

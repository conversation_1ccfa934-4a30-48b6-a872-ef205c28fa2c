"""
简单的模块测试脚本
"""

import sys
import os
sys.path.append('src')

def test_modules():
    print("🧪 测试第一阶段模块")
    
    try:
        # 导入模块
        from modules import create_phase1_integrator
        print("✅ 模块导入成功")
        
        # 创建系统
        system = create_phase1_integrator()
        print("✅ 系统创建成功")
        
        # 测试数据
        test_detections = [
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],
                'confidence': 0.9,
                'group_id': 1
            },
            {
                'label': '三',
                'bbox': [200, 100, 250, 150],
                'confidence': 0.8,
                'group_id': 1
            }
        ]
        
        # 处理第一帧
        result1 = system.process_frame(test_detections)
        print(f"✅ 第一帧处理: {'成功' if result1.success else '失败'}")
        print(f"   处理卡牌: {len(result1.processed_cards)}张")
        
        for card in result1.processed_cards:
            print(f"   - {card['twin_id']} (区域{card['group_id']}, 标签{card['label']})")
        
        # 处理第二帧（测试继承）
        test_detections_2 = [
            {
                'label': '二',
                'bbox': [105, 105, 155, 155],
                'confidence': 0.9,
                'group_id': 1  # 相同区域+标签，应该继承
            },
            {
                'label': '四',
                'bbox': [300, 100, 350, 150],
                'confidence': 0.85,
                'group_id': 1  # 新标签，应该分配新ID
            }
        ]
        
        result2 = system.process_frame(test_detections_2)
        print(f"✅ 第二帧处理: {'成功' if result2.success else '失败'}")
        print(f"   处理卡牌: {len(result2.processed_cards)}张")
        
        for card in result2.processed_cards:
            inherited = "继承" if card.get('inherited', False) else "新增"
            print(f"   - {card['twin_id']} (区域{card['group_id']}, 标签{card['label']}, {inherited})")
        
        # 显示继承率
        if 'inheritance' in result2.statistics:
            inheritance_rate = result2.statistics['inheritance'].get('current_frame', {}).get('inheritance_rate', 0)
            print(f"   继承率: {inheritance_rate:.1%}")
        
        print("\n🎉 第一阶段模块测试成功！")
        print("✅ 数据验证功能正常")
        print("✅ ID分配功能正常") 
        print("✅ 继承功能正常")
        print("✅ 模块集成功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modules()
    if success:
        print("\n🚀 模块化拆分方案验证成功！")
        print("建议：可以开始实施第一阶段的模块化重构")
    else:
        print("\n⚠️ 需要修复模块问题后再继续")

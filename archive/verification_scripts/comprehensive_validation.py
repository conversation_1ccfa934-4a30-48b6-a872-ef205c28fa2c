#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
记忆机制改进全面验证测试
"""

import sys
sys.path.append('src')

import json
import time
from pathlib import Path
from core.memory_manager import MemoryManager, MemoryConfig
from core.digital_twin_v2 import DigitalTwinCoordinator, CardDetection

def load_full_dataset():
    """加载完整数据集"""
    dataset_path = Path('legacy_assets/ceshi/zhuangtaiquyu')
    json_files = list(dataset_path.rglob('*.json'))
    
    print(f'📁 找到 {len(json_files)} 个标注文件')
    
    test_frames = []
    for json_file in sorted(json_files):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            detections = []
            ground_truth = []
            
            for shape in data.get('shapes', []):
                if shape.get('shape_type') == 'rectangle':
                    points = shape.get('points', [])
                    if len(points) == 4:
                        x_coords = [p[0] for p in points]
                        y_coords = [p[1] for p in points]
                        x1, x2 = min(x_coords), max(x_coords)
                        y1, y2 = min(y_coords), max(y_coords)
                        bbox = [x1, y1, x2, y2]
                        
                        detection = {
                            'label': shape.get('label', ''),
                            'bbox': bbox,
                            'confidence': 0.9,
                            'group_id': shape.get('group_id', 0)
                        }
                        detections.append(detection)
                        
                        # 保存真实标注
                        ground_truth.append({
                            'label': shape.get('label', ''),
                            'bbox': bbox,
                            'group_id': shape.get('group_id', 0)
                        })
            
            if detections:  # 只保留有检测结果的帧
                test_frames.append({
                    'frame_id': json_file.stem,
                    'detections': detections,
                    'ground_truth': ground_truth
                })
        
        except Exception as e:
            print(f'⚠️ 加载文件失败 {json_file.name}: {e}')
            continue
    
    return test_frames

def calculate_iou(bbox1, bbox2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1[:4]
    x1_2, y1_2, x2_2, y2_2 = bbox2[:4]
    
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0

def calculate_accuracy_metrics(predictions, ground_truth):
    """计算准确率指标"""
    if not predictions or not ground_truth:
        return 0.0, 0.0, 0
    
    region_correct = 0
    id_correct = 0
    total_matches = 0
    
    for pred in predictions:
        pred_bbox = pred.get('bbox', [])
        pred_label = pred.get('label', '')
        pred_region = pred.get('region_id', pred.get('group_id', 0))
        pred_twin_id = pred.get('twin_id', '')
        
        # 寻找最匹配的ground truth
        best_match = None
        best_iou = 0
        
        for gt in ground_truth:
            gt_bbox = gt.get('bbox', [])
            gt_label = gt.get('label', '')
            
            # 处理标签格式（去掉区域前缀）
            gt_clean_label = gt_label[1:] if gt_label and len(gt_label) > 1 and gt_label[0].isdigit() else gt_label
            pred_clean_label = pred_label[1:] if pred_label and len(pred_label) > 1 and pred_label[0].isdigit() else pred_label
            
            if len(pred_bbox) >= 4 and len(gt_bbox) >= 4:
                iou = calculate_iou(pred_bbox, gt_bbox)
                if iou > best_iou and iou > 0.5 and pred_clean_label == gt_clean_label:
                    best_iou = iou
                    best_match = gt
        
        if best_match:
            total_matches += 1
            
            # 区域准确率
            gt_region = best_match.get('group_id', 0)
            if pred_region == gt_region:
                region_correct += 1
            
            # ID准确率（如果有有效的twin_id）
            if pred_twin_id and not pred_twin_id.startswith('虚拟_') and not pred_twin_id.startswith('未知_'):
                id_correct += 1
    
    region_accuracy = region_correct / total_matches if total_matches > 0 else 0.0
    id_accuracy = id_correct / total_matches if total_matches > 0 else 0.0
    
    return region_accuracy, id_accuracy, total_matches

def test_improved_memory(test_frames):
    """测试改进后的记忆机制"""
    print('\n📊 测试改进后的记忆机制（启用展开状态检测）')
    print('-' * 60)
    
    memory_config_improved = MemoryConfig(enable_expansion_detection=True)
    memory_manager_improved = MemoryManager(memory_config_improved)
    digital_twin_improved = DigitalTwinCoordinator()
    
    improved_results = {
        'region_accuracies': [],
        'id_accuracies': [],
        'total_matches': [],
        'processing_times': [],
        'expansion_states': {},
        'memory_modes': {},
        'memory_activations': 0
    }
    
    print('开始测试改进版本...')
    for i, frame_data in enumerate(test_frames):
        frame_id = frame_data['frame_id']
        detections = frame_data['detections']
        ground_truth = frame_data['ground_truth']
        
        start_time = time.time()
        
        # 记忆机制处理
        memory_result = memory_manager_improved.process_frame(frame_id, detections)
        enhanced_detections = memory_result['detections']
        
        # 转换为数字孪生格式
        twin_detections = []
        for det in enhanced_detections:
            twin_det = CardDetection(
                label=det['label'],
                bbox=det['bbox'],
                confidence=det['confidence'],
                group_id=det.get('group_id', 0)
            )
            twin_detections.append(twin_det)
        
        # 数字孪生处理
        twin_result = digital_twin_improved.process_frame(twin_detections)
        
        processing_time = time.time() - start_time
        improved_results['processing_times'].append(processing_time)
        
        # 计算准确率
        predictions = twin_result.get('cards', [])
        region_acc, id_acc, matches = calculate_accuracy_metrics(predictions, ground_truth)
        
        improved_results['region_accuracies'].append(region_acc)
        improved_results['id_accuracies'].append(id_acc)
        improved_results['total_matches'].append(matches)
        
        # 记忆机制统计
        expansion_state = memory_result.get('expansion_state', 'UNKNOWN')
        memory_mode = memory_result.get('memory_mode', 'UNKNOWN')
        
        improved_results['expansion_states'][expansion_state] = improved_results['expansion_states'].get(expansion_state, 0) + 1
        improved_results['memory_modes'][memory_mode] = improved_results['memory_modes'].get(memory_mode, 0) + 1
        
        if memory_result['statistics']['memory_activated']:
            improved_results['memory_activations'] += 1
        
        if (i + 1) % 20 == 0:
            print(f'  改进版本处理进度: {i + 1}/{len(test_frames)} 帧')
    
    print('✅ 改进版本测试完成')
    return improved_results

def test_traditional_memory(test_frames):
    """测试传统记忆机制"""
    print('\n📊 测试传统记忆机制（禁用展开状态检测）')
    print('-' * 60)
    
    memory_config_traditional = MemoryConfig(enable_expansion_detection=False)
    memory_manager_traditional = MemoryManager(memory_config_traditional)
    digital_twin_traditional = DigitalTwinCoordinator()
    
    traditional_results = {
        'region_accuracies': [],
        'id_accuracies': [],
        'total_matches': [],
        'processing_times': [],
        'memory_activations': 0
    }
    
    print('开始测试传统版本...')
    for i, frame_data in enumerate(test_frames):
        frame_id = frame_data['frame_id']
        detections = frame_data['detections']
        ground_truth = frame_data['ground_truth']
        
        start_time = time.time()
        
        # 记忆机制处理
        memory_result = memory_manager_traditional.process_frame(frame_id, detections)
        enhanced_detections = memory_result['detections']
        
        # 转换为数字孪生格式
        twin_detections = []
        for det in enhanced_detections:
            twin_det = CardDetection(
                label=det['label'],
                bbox=det['bbox'],
                confidence=det['confidence'],
                group_id=det.get('group_id', 0)
            )
            twin_detections.append(twin_det)
        
        # 数字孪生处理
        twin_result = digital_twin_traditional.process_frame(twin_detections)
        
        processing_time = time.time() - start_time
        traditional_results['processing_times'].append(processing_time)
        
        # 计算准确率
        predictions = twin_result.get('cards', [])
        region_acc, id_acc, matches = calculate_accuracy_metrics(predictions, ground_truth)
        
        traditional_results['region_accuracies'].append(region_acc)
        traditional_results['id_accuracies'].append(id_acc)
        traditional_results['total_matches'].append(matches)
        
        if memory_result['statistics']['memory_activated']:
            traditional_results['memory_activations'] += 1
        
        if (i + 1) % 20 == 0:
            print(f'  传统版本处理进度: {i + 1}/{len(test_frames)} 帧')
    
    print('✅ 传统版本测试完成')
    return traditional_results

def analyze_results(improved_results, traditional_results, total_frames):
    """分析验证结果"""
    print('\n📊 全面验证结果分析')
    print('=' * 80)

    # 计算平均准确率
    avg_region_improved = sum(improved_results['region_accuracies']) / len(improved_results['region_accuracies']) * 100
    avg_id_improved = sum(improved_results['id_accuracies']) / len(improved_results['id_accuracies']) * 100
    avg_time_improved = sum(improved_results['processing_times']) / len(improved_results['processing_times']) * 1000

    avg_region_traditional = sum(traditional_results['region_accuracies']) / len(traditional_results['region_accuracies']) * 100
    avg_id_traditional = sum(traditional_results['id_accuracies']) / len(traditional_results['id_accuracies']) * 100
    avg_time_traditional = sum(traditional_results['processing_times']) / len(traditional_results['processing_times']) * 1000

    # 计算总匹配数
    total_matches_improved = sum(improved_results['total_matches'])
    total_matches_traditional = sum(traditional_results['total_matches'])

    print(f'\n🎯 全面验证结果对比（基于{total_frames}帧测试）:')
    print('验证方法                区域准确率    ID准确率     处理时间     匹配数')
    print('-' * 75)
    print('改进后记忆机制          {:.1f}%        {:.1f}%       {:.2f}ms     {}'.format(
        avg_region_improved, avg_id_improved, avg_time_improved, total_matches_improved))
    print('传统记忆机制            {:.1f}%        {:.1f}%       {:.2f}ms     {}'.format(
        avg_region_traditional, avg_id_traditional, avg_time_traditional, total_matches_traditional))

    # 计算改进幅度
    region_improvement = avg_region_improved - avg_region_traditional
    id_improvement = avg_id_improved - avg_id_traditional
    time_overhead = avg_time_improved - avg_time_traditional
    matches_change = total_matches_improved - total_matches_traditional

    print('\n📈 改进效果分析:')
    print('区域准确率变化: {:+.1f}% ({:.1f}% → {:.1f}%)'.format(
        region_improvement, avg_region_traditional, avg_region_improved))
    print('ID准确率变化: {:+.1f}% ({:.1f}% → {:.1f}%)'.format(
        id_improvement, avg_id_traditional, avg_id_improved))
    print('处理时间开销: {:+.2f}ms ({:.2f}ms → {:.2f}ms)'.format(
        time_overhead, avg_time_traditional, avg_time_improved))
    print('匹配数变化: {:+d} ({} → {})'.format(
        matches_change, total_matches_traditional, total_matches_improved))

    # 与基准数据对比
    print('\n📊 与开发文档10基准数据对比:')
    print('验证方法                区域准确率    ID准确率     说明')
    print('-' * 70)
    print('增强系统验证（基准）    91.4%        56.9%       小规模验证（685张）')
    print('全量真实验证（基准）    78.1%        46.0%       大规模验证（5,810张）')
    print('改进后记忆机制          {:.1f}%        {:.1f}%       全面验证（{}帧）'.format(
        avg_region_improved, avg_id_improved, total_frames))
    print('传统记忆机制            {:.1f}%        {:.1f}%       全面验证（{}帧）'.format(
        avg_region_traditional, avg_id_traditional, total_frames))

    print('\n🎯 改进版本独有功能统计:')
    print('展开状态分布:')
    for state, count in improved_results['expansion_states'].items():
        percentage = count / total_frames * 100
        print('  {}: {} 帧 ({:.1f}%)'.format(state, count, percentage))

    print('\n记忆模式分布:')
    for mode, count in improved_results['memory_modes'].items():
        percentage = count / total_frames * 100
        print('  {}: {} 帧 ({:.1f}%)'.format(mode, count, percentage))

    memory_activation_rate_improved = improved_results['memory_activations'] / total_frames * 100
    memory_activation_rate_traditional = traditional_results['memory_activations'] / total_frames * 100

    print('\n记忆激活统计:')
    print('改进版本记忆激活率: {:.1f}% ({}/{})'.format(
        memory_activation_rate_improved, improved_results['memory_activations'], total_frames))
    print('传统版本记忆激活率: {:.1f}% ({}/{})'.format(
        memory_activation_rate_traditional, traditional_results['memory_activations'], total_frames))

    print('\n✅ 全面验证结论:')
    if region_improvement > 0:
        print('✅ 区域准确率有显著提升: +{:.1f}%'.format(region_improvement))
    elif region_improvement > -1:
        print('✅ 区域准确率基本保持稳定: {:.1f}%'.format(region_improvement))
    else:
        print('⚠️ 区域准确率有所下降: {:.1f}%'.format(region_improvement))

    if id_improvement > 0:
        print('✅ ID准确率有显著提升: +{:.1f}%'.format(id_improvement))
    elif id_improvement > -1:
        print('✅ ID准确率基本保持稳定: {:.1f}%'.format(id_improvement))
    else:
        print('⚠️ ID准确率有所下降: {:.1f}%'.format(id_improvement))

    if time_overhead < 2.0:
        print('✅ 处理时间开销可接受: +{:.2f}ms'.format(time_overhead))
    else:
        print('⚠️ 处理时间开销较大: +{:.2f}ms'.format(time_overhead))

    print('\n🎉 记忆机制改进全面验证完成！')

    # 保存结果
    save_results(improved_results, traditional_results, total_frames,
                 avg_region_improved, avg_id_improved, avg_region_traditional, avg_id_traditional)

def save_results(improved_results, traditional_results, total_frames,
                 avg_region_improved, avg_id_improved, avg_region_traditional, avg_id_traditional):
    """保存验证结果"""
    comprehensive_results = {
        'test_date': str(__import__('datetime').datetime.now()),
        'test_frames': total_frames,
        'dataset': 'zhuangtaiquyu',
        'improved_version': {
            'avg_region_accuracy': avg_region_improved,
            'avg_id_accuracy': avg_id_improved,
            'total_matches': sum(improved_results['total_matches']),
            'memory_activation_rate': improved_results['memory_activations'] / total_frames * 100,
            'expansion_states': improved_results['expansion_states'],
            'memory_modes': improved_results['memory_modes']
        },
        'traditional_version': {
            'avg_region_accuracy': avg_region_traditional,
            'avg_id_accuracy': avg_id_traditional,
            'total_matches': sum(traditional_results['total_matches']),
            'memory_activation_rate': traditional_results['memory_activations'] / total_frames * 100
        },
        'improvements': {
            'region_accuracy_change': avg_region_improved - avg_region_traditional,
            'id_accuracy_change': avg_id_improved - avg_id_traditional
        }
    }

    with open('comprehensive_memory_validation_results.json', 'w', encoding='utf-8') as f:
        json.dump(comprehensive_results, f, indent=2, ensure_ascii=False)

    print('\n💾 详细验证结果已保存到: comprehensive_memory_validation_results.json')

def main():
    print('🚀 记忆机制改进全面验证测试')
    print('=' * 80)

    # 加载完整数据集
    test_frames = load_full_dataset()
    print(f'✅ 成功加载 {len(test_frames)} 个有效测试帧')

    # 测试改进版本和传统版本
    improved_results = test_improved_memory(test_frames)
    traditional_results = test_traditional_memory(test_frames)

    # 分析结果
    analyze_results(improved_results, traditional_results, len(test_frames))

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
开发过程14双轨机制验证脚本
验证同步双轨输出系统的功能和一致性
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

def main():
    print("🚀 启动开发过程14双轨机制验证")
    print("=" * 60)
    
    try:
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("✅ 模块导入成功")
        
        # 创建数字孪生系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统创建成功")
        
        # 检查双轨输出方法是否存在
        if not hasattr(dt_system, 'export_synchronized_dual_format'):
            print("❌ 双轨输出方法不存在")
            return False
        
        print("✅ 双轨输出方法存在")
        
        # 创建测试数据 - 模拟zhuangtaiquyu格式的检测结果
        test_detections = [
            CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator'),
            CardDetection('三', [200, 100, 250, 150], 0.90, 2, '手牌_观战方', 'spectator'),
            CardDetection('四', [300, 100, 350, 150], 0.88, 3, '出牌区_观战方', 'spectator'),
        ]
        print(f"✅ 测试数据创建成功: {len(test_detections)} 张卡牌")
        
        # 处理数据
        result = dt_system.process_frame(test_detections)
        card_count = len(result.get('digital_twin_cards', []))
        print(f"✅ 数字孪生处理成功: {card_count} 张卡牌")
        
        # 双轨输出
        dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test_frame.jpg')
        print("✅ 双轨输出成功")
        
        # 检查一致性
        consistency = dual_result['consistency_validation']
        score = consistency.get('consistency_score', 0)
        is_consistent = consistency.get('is_consistent', False)
        
        print("\n📊 一致性验证结果:")
        print(f"   一致性分数: {score:.3f}")
        print(f"   是否一致: {'✅ 是' if is_consistent else '❌ 否'}")
        
        # 检查输出格式
        rlcard_format = dual_result['rlcard_format']
        anylabeling_format = dual_result['anylabeling_format']
        
        print("\n📋 输出格式检查:")
        hand_count = len(rlcard_format.get('hand', []))
        discard_count = len(rlcard_format.get('discard_pile', []))
        combo_count = len(rlcard_format.get('combo_cards', []))
        shapes_count = len(anylabeling_format.get('shapes', []))
        
        print(f"   RLCard格式:")
        print(f"     - 手牌: {hand_count} 张")
        print(f"     - 弃牌堆: {discard_count} 张")
        print(f"     - 组合牌: {combo_count} 张")
        print(f"   AnyLabeling格式:")
        print(f"     - 标注数: {shapes_count} 个")
        
        # 详细检查AnyLabeling格式
        print("\n🔍 AnyLabeling格式详细检查:")
        for i, shape in enumerate(anylabeling_format.get('shapes', [])):
            label = shape.get('label', 'Unknown')
            group_id = shape.get('group_id', 'N/A')
            print(f"   标注 {i+1}: {label} (区域: {group_id})")
        
        # 保存验证结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "verification_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存RLCard格式
        rlcard_file = os.path.join(output_dir, f"rlcard_format_{timestamp}.json")
        with open(rlcard_file, 'w', encoding='utf-8') as f:
            json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
        
        # 保存AnyLabeling格式
        anylabeling_file = os.path.join(output_dir, f"anylabeling_format_{timestamp}.json")
        with open(anylabeling_file, 'w', encoding='utf-8') as f:
            json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
        
        # 保存验证报告
        verification_report = {
            "timestamp": timestamp,
            "consistency_score": score,
            "is_consistent": is_consistent,
            "rlcard_card_count": hand_count + discard_count + combo_count,
            "anylabeling_shape_count": shapes_count,
            "test_detections_count": len(test_detections),
            "digital_twin_cards_count": card_count,
            "consistency_details": consistency
        }
        
        report_file = os.path.join(output_dir, f"verification_report_{timestamp}.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(verification_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 验证结果已保存:")
        print(f"   RLCard格式: {rlcard_file}")
        print(f"   AnyLabeling格式: {anylabeling_file}")
        print(f"   验证报告: {report_file}")
        
        # 最终结果
        if is_consistent and score >= 0.95:
            print("\n🎉 双轨机制验证完全成功！")
            print("   ✅ 一致性分数达标 (≥0.95)")
            print("   ✅ 双轨输出同步")
            return True
        elif is_consistent:
            print("\n⚠️ 双轨机制基本成功，但一致性分数需要提升")
            print(f"   📊 当前分数: {score:.3f} (目标: ≥0.95)")
            return True
        else:
            print("\n❌ 双轨机制存在一致性问题")
            print(f"   📊 一致性分数: {score:.3f}")
            return False
            
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

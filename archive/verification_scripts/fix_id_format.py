#!/usr/bin/env python3
"""
修复JSON文件中的ID格式
将错误的ID格式（如 "2七7", "虚拟_五_16"）修复为正确格式（如 "2七", "虚拟_五"）
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, Any

def fix_id_format(twin_id: str) -> str:
    """修复ID格式"""
    if not twin_id:
        return twin_id
    
    # 处理虚拟ID：虚拟_牌面_区域 -> 虚拟_牌面
    if twin_id.startswith('虚拟_'):
        # 匹配 虚拟_牌面_数字 格式
        match = re.match(r'虚拟_([^_]+)_\d+', twin_id)
        if match:
            card_label = match.group(1)
            return f"虚拟_{card_label}"
        else:
            # 已经是正确格式或其他格式
            return twin_id
    
    # 处理暗牌ID：数字牌面暗区域 -> 数字牌面暗
    if '暗' in twin_id:
        # 匹配 数字牌面暗数字 格式
        match = re.match(r'(\d+)([^暗]+)暗\d+', twin_id)
        if match:
            sequence = match.group(1)
            card_label = match.group(2)
            return f"{sequence}{card_label}暗"
        else:
            # 已经是正确格式或其他格式
            return twin_id
    
    # 处理物理ID：数字牌面区域 -> 数字牌面
    # 匹配 数字牌面数字 格式（最后的数字是区域ID）
    match = re.match(r'(\d+)([^0-9]+)\d+$', twin_id)
    if match:
        sequence = match.group(1)
        card_label = match.group(2)
        return f"{sequence}{card_label}"
    
    # 如果不匹配任何模式，返回原ID
    return twin_id

def fix_json_file(file_path: Path) -> bool:
    """修复单个JSON文件中的ID格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        modified = False
        
        # 修复shapes中的digital_twin_id
        if 'shapes' in data:
            for shape in data['shapes']:
                if 'attributes' in shape and 'digital_twin_id' in shape['attributes']:
                    old_id = shape['attributes']['digital_twin_id']
                    new_id = fix_id_format(old_id)
                    if old_id != new_id:
                        shape['attributes']['digital_twin_id'] = new_id
                        modified = True
                        print(f"  修复ID: {old_id} -> {new_id}")
        
        # 如果有修改，保存文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    if not output_dir.exists():
        print(f"输出目录不存在: {output_dir}")
        return
    
    print(f"🔧 开始修复ID格式...")
    print(f"📁 处理目录: {output_dir}")
    
    json_files = list(output_dir.glob("*.json"))
    print(f"📋 找到 {len(json_files)} 个JSON文件")
    
    modified_count = 0
    
    for json_file in json_files:
        print(f"处理: {json_file.name}")
        if fix_json_file(json_file):
            modified_count += 1
    
    print(f"\n🎉 修复完成!")
    print(f"📊 统计:")
    print(f"   - 总文件数: {len(json_files)}")
    print(f"   - 修改文件数: {modified_count}")
    print(f"   - 未修改文件数: {len(json_files) - modified_count}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
简单的偎牌测试
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def simple_weipai_test():
    """简单的偎牌测试"""
    print("🔍 简单偎牌测试")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 简单测试：一组偎牌（拾）
    frame_detections = [
        {
            'label': '拾',
            'bbox': [100, 100, 150, 150],  # 明牌
            'confidence': 0.9,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [100, 160, 150, 210],  # 暗牌1
            'confidence': 0.8,
            'group_id': 16
        },
        {
            'label': '暗',
            'bbox': [100, 220, 150, 270],  # 暗牌2
            'confidence': 0.8,
            'group_id': 16
        }
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"\n处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    for card in result['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id}, 坐标: {card.bbox})")
    
    return result

def simple_paopai_test():
    """简单的跑牌测试"""
    print(f"\n🔍 简单跑牌测试")
    
    system = create_digital_twin_system()
    
    # 简单测试：跑牌（二）
    frame_detections = [
        {
            'label': '二',
            'bbox': [200, 100, 250, 150],  # 明牌1
            'confidence': 0.9,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [200, 160, 250, 210],  # 明牌2
            'confidence': 0.9,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [200, 220, 250, 270],  # 明牌3
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    for card in result['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id}, 坐标: {card.bbox})")
    
    return result

if __name__ == "__main__":
    print("🔧 简单偎牌和跑牌测试")
    print("=" * 60)
    
    # 测试偎牌
    simple_weipai_test()
    
    # 测试跑牌
    simple_paopai_test()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

#!/usr/bin/env python3
"""
验证ID格式修复效果
检查输出文件中是否还有包含区域信息的错误ID格式
"""

import json
import os
import re
from pathlib import Path

def check_id_format(twin_id):
    """检查ID格式是否正确"""
    if not twin_id:
        return True, "空ID"
    
    # 正确格式模式
    correct_patterns = [
        r'^虚拟[^0-9]+$',  # 虚拟牌：虚拟{牌面}
        r'^\d+[^0-9]+$',   # 物理牌：{序号}{牌面}
        r'^\d+[^0-9]+暗$', # 暗牌：{序号}{牌面}暗
    ]
    
    # 检查是否匹配正确格式
    for pattern in correct_patterns:
        if re.match(pattern, twin_id):
            return True, "格式正确"
    
    # 错误格式检查
    if re.search(r'\d+$', twin_id):  # 以数字结尾（区域信息）
        return False, "包含区域信息"
    
    return False, "未知格式"

def verify_output_files():
    """验证输出文件的ID格式"""
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    if not output_dir.exists():
        print("❌ 输出目录不存在")
        return
    
    print("🔍 开始验证ID格式...")
    
    total_files = 0
    total_cards = 0
    error_count = 0
    error_details = []
    
    # 遍历所有JSON文件
    for json_file in sorted(output_dir.glob("frame_*.json")):
        total_files += 1
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查每个形状的ID
            for shape in data.get('shapes', []):
                attrs = shape.get('attributes', {})
                twin_id = attrs.get('digital_twin_id')
                
                if twin_id:
                    total_cards += 1
                    is_correct, reason = check_id_format(twin_id)
                    
                    if not is_correct:
                        error_count += 1
                        error_details.append({
                            'file': json_file.name,
                            'twin_id': twin_id,
                            'reason': reason,
                            'label': shape.get('label', ''),
                            'group_id': shape.get('group_id', ''),
                        })
                        
                        # 只显示前10个错误
                        if len(error_details) <= 10:
                            print(f"  ❌ {json_file.name}: {twin_id} ({reason})")
        
        except Exception as e:
            print(f"  ⚠️ 读取文件失败 {json_file.name}: {e}")
    
    # 统计结果
    print(f"\n📊 验证结果:")
    print(f"  - 检查文件数: {total_files}")
    print(f"  - 检查卡牌数: {total_cards}")
    print(f"  - 错误ID数量: {error_count}")
    print(f"  - 正确率: {((total_cards - error_count) / total_cards * 100):.2f}%" if total_cards > 0 else "N/A")
    
    if error_count == 0:
        print("  ✅ 所有ID格式都正确！")
    else:
        print(f"  ❌ 发现 {error_count} 个错误ID")
        
        # 显示错误分布
        if error_details:
            print(f"\n📋 错误详情（显示前10个）:")
            for i, error in enumerate(error_details[:10]):
                print(f"  {i+1}. 文件: {error['file']}")
                print(f"     ID: {error['twin_id']}")
                print(f"     原因: {error['reason']}")
                print(f"     标签: {error['label']}")
                print(f"     区域: {error['group_id']}")
                print()
    
    return error_count == 0

def check_specific_patterns():
    """检查特定的错误模式"""
    print("\n🔍 检查特定错误模式...")
    
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    # 错误模式
    error_patterns = [
        (r'暗\d+', "暗牌包含区域信息"),
        (r'虚拟.+\d+', "虚拟牌包含区域信息"),
        (r'^\d+.+\d+$', "物理牌包含区域信息"),
    ]
    
    for pattern, description in error_patterns:
        found_count = 0
        
        for json_file in output_dir.glob("frame_*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                matches = re.findall(f'"digital_twin_id":\\s*"({pattern})"', content)
                if matches:
                    found_count += len(matches)
                    if found_count <= 5:  # 只显示前5个
                        print(f"  ❌ {description}: {matches[0]} (在 {json_file.name})")
            
            except Exception as e:
                continue
        
        if found_count == 0:
            print(f"  ✅ {description}: 未发现")
        else:
            print(f"  ❌ {description}: 发现 {found_count} 个")

if __name__ == "__main__":
    print("🎯 ID格式修复验证")
    print("=" * 50)
    
    # 验证输出文件
    success = verify_output_files()
    
    # 检查特定模式
    check_specific_patterns()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 验证完成：ID格式修复成功！")
    else:
        print("⚠️ 验证完成：仍有部分ID格式需要修复")

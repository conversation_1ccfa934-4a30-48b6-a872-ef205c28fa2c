#!/usr/bin/env python3
"""
调试第18帧的暗牌处理
"""

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def debug_frame_18():
    """调试第18帧的暗牌处理"""
    print("🔍 调试第18帧的暗牌处理")
    
    # 读取原始检测数据
    frame_path = "legacy_assets/ceshi/calibration_gt/frame_00018.json"
    
    if not os.path.exists(frame_path):
        print(f"❌ 文件不存在: {frame_path}")
        return
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        frame_data = json.load(f)
    
    print(f"📁 读取原始帧数据: {len(frame_data['shapes'])}个检测对象")
    
    # 转换为数字孪生系统需要的格式
    detections = []
    for shape in frame_data['shapes']:
        if shape.get('label') in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                                  '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗']:
            # 计算bbox
            points = shape['points']
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            
            detection = {
                'label': shape['label'],
                'bbox': bbox,
                'confidence': shape.get('score', 0.9),
                'group_id': shape.get('group_id')
            }
            detections.append(detection)
    
    print(f"🎯 有效检测: {len(detections)}个")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 处理第18帧
    result = system.process_frame(detections, 18)
    
    print(f"\n📊 数字孪生处理结果: {len(result['digital_twin_cards'])}张卡牌")
    
    # 分析暗牌处理
    dark_cards = []
    bright_cards_by_region = {}
    
    for card in result['digital_twin_cards']:
        if card.is_dark:
            dark_cards.append(card)
        else:
            region = card.group_id
            if region not in bright_cards_by_region:
                bright_cards_by_region[region] = []
            bright_cards_by_region[region].append(card)
    
    print(f"\n🔍 暗牌分析:")
    print(f"暗牌总数: {len(dark_cards)}")
    
    for card in dark_cards:
        print(f"  - {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 置信度: {card.confidence})")
        
        # 检查同区域是否有明牌
        region_bright_cards = bright_cards_by_region.get(card.group_id, [])
        if region_bright_cards:
            print(f"    同区域明牌: {[c.twin_id for c in region_bright_cards]}")
            
            # 检查暗牌ID是否正确关联
            expected_labels = set(c.label for c in region_bright_cards)
            if len(expected_labels) == 1:
                expected_label = list(expected_labels)[0]
                expected_id = card.twin_id.replace('暗', f'{expected_label}暗')
                if card.twin_id.endswith('暗') and not card.twin_id.endswith(f'{expected_label}暗'):
                    print(f"    ❌ 错误: 应该是 {expected_id}")
                else:
                    print(f"    ✅ 正确: 暗牌ID格式正确")
            else:
                print(f"    ⚠️  复杂: 同区域有多种明牌 {expected_labels}")
        else:
            print(f"    ⚠️  孤立: 同区域没有明牌")
    
    print(f"\n🔍 明牌分析:")
    for region, cards in bright_cards_by_region.items():
        print(f"区域{region}: {[c.twin_id for c in cards]}")
    
    return result

if __name__ == "__main__":
    print("🔧 调试第18帧的暗牌处理")
    print("=" * 60)
    
    result = debug_frame_18()
    
    print("\n" + "=" * 60)
    print("🎉 调试完成!")

# 📚 API参考文档

## 核心模块API

### 🎯 CardDetector - 卡牌检测器

#### 基础用法
```python
from src.core.detect import CardDetector

# 初始化检测器
detector = CardDetector(
    model_path="models/yolov8l.pt",
    conf_threshold=0.25,
    iou_threshold=0.45,
    device="0",  # GPU设备号，"cpu"为CPU模式
    enable_validation=True
)

# 检测图像
results = detector.detect_image("path/to/image.jpg")
```

#### 参数说明
- `model_path`: 模型文件路径 (.pt或.onnx)
- `conf_threshold`: 置信度阈值 (0.01-1.0)
- `iou_threshold`: IoU阈值 (0.1-1.0)
- `device`: 设备选择 ("0", "1", "cpu")
- `enable_validation`: 是否启用数据验证

#### 返回格式
```python
{
    "detections": [
        {
            "class_name": "二",
            "confidence": 0.95,
            "bbox": [x1, y1, x2, y2],
            "class_id": 2
        }
    ],
    "processing_time": 0.033,
    "device_used": "cuda:0"
}
```

### 🧠 数字孪生系统V2.0

#### 基础用法
```python
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection

# 创建系统
dt_system = create_digital_twin_system()

# 创建检测数据
detections = [
    CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator")
]

# 处理帧数据
result = dt_system.process_frame(detections)
```

#### 核心方法

##### process_frame()
```python
def process_frame(detections: List[CardDetection]) -> Dict[str, Any]:
    """
    处理单帧检测数据
    
    Args:
        detections: 检测结果列表
        
    Returns:
        包含数字孪生卡牌和元数据的完整结果
    """
```

##### export_synchronized_dual_format()
```python
def export_synchronized_dual_format(
    result: Dict[str, Any],
    image_width: int, 
    image_height: int,
    image_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    同步双轨输出
    
    Returns:
        {
            "rlcard_format": {...},      # AI决策用格式
            "anylabeling_format": {...}, # 人工审核用格式
            "consistency_validation": {...}
        }
    """
```


### 🎮 决策引擎

#### 基础用法
```python
from src.core.decision import DecisionEngine

# 初始化决策引擎
engine = DecisionEngine()

# 生成决策
decision = engine.make_decision(game_state)
```

#### 决策类型
- `"胡"`: 胡牌决策
- `"碰"`: 碰牌决策  
- `"跑"`: 跑牌决策
- `"吃"`: 吃牌决策
- `"过"`: 过牌决策

## 🔧 配置管理

### 配置文件结构
```json
{
    "model": {
        "path": "models/yolov8l.pt",
        "confidence_threshold": 0.25,
        "iou_threshold": 0.45
    },
    "detection": {
        "device": "0",
        "enable_validation": true,
        "batch_size": 1
    },
    "memory": {
        "max_frames": 5,
        "enable_compensation": true
    },
    "output": {
        "save_visualizations": true,
        "output_directory": "output/"
    }
}
```

### 加载配置
```python
from src.config.config_loader import load_config

config = load_config("src/config/config.json")
```

## 🚨 错误处理

### 常见异常
```python
from src.core.exceptions import (
    ModelLoadError,
    DetectionError,
    ValidationError,
    ConfigurationError
)

try:
    detector = CardDetector(model_path="invalid_path.pt")
except ModelLoadError as e:
    print(f"模型加载失败: {e}")
```

### 错误码说明
- `E001`: 模型文件不存在
- `E002`: GPU不可用
- `E003`: 输入图像格式错误
- `E004`: 配置参数无效

## 📊 性能监控

### 性能指标获取
```python
# 获取检测性能
performance = detector.get_performance_metrics()
print(f"平均检测时间: {performance['avg_detection_time']:.3f}s")
print(f"GPU内存使用: {performance['gpu_memory_usage']:.1f}MB")

# 获取系统统计
stats = dt_system.get_statistics()
print(f"处理帧数: {stats['total_frames_processed']}")
print(f"虚拟卡牌创建: {stats['virtual_cards_created']}")
```

## 🔗 集成示例

### 完整流水线
```python
from src.core.detect import CardDetector
from src.core.digital_twin_v2 import create_digital_twin_system
from src.core.decision import DecisionEngine

# 初始化组件
detector = CardDetector("models/yolov8l.pt")
dt_system = create_digital_twin_system()
decision_engine = DecisionEngine()

# 处理图像
def process_image(image_path):
    # 1. 检测卡牌
    detection_result = detector.detect_image(image_path)
    
    # 2. 转换为数字孪生格式
    detections = convert_to_card_detections(detection_result)
    dt_result = dt_system.process_frame(detections)
    
    # 3. 生成决策
    game_state = build_game_state(dt_result)
    decision = decision_engine.make_decision(game_state)
    
    # 4. 双轨输出
    dual_output = dt_system.export_synchronized_dual_format(
        dt_result, 640, 320, image_path
    )
    
    return {
        "detection": detection_result,
        "digital_twin": dt_result,
        "decision": decision,
        "dual_format": dual_output
    }
```

---

**📝 注意**: 详细的使用示例请参考 [docs/user_guide/examples/](docs/user_guide/examples/) 目录。

#!/usr/bin/env python3
"""
分析frame_00018.jpg中区域16暗牌分配错误
检查我们的修复是否引入了新的问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(file_path: str) -> Dict[str, Any]:
    """加载指定帧的数据"""
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def analyze_region16_dark_cards(frame_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析区域16的暗牌分配"""
    print(f"🔍 分析frame_00018中区域16的暗牌分配")
    print("=" * 60)
    
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 区域16卡牌总数: {len(region16_cards)}")
    
    # 分析暗牌
    dark_cards = []
    normal_cards = []
    
    for card in region16_cards:
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        
        if '暗' in label or '暗' in twin_id:
            dark_cards.append(card)
        else:
            normal_cards.append(card)
    
    print(f"📊 卡牌分类:")
    print(f"  暗牌: {len(dark_cards)}张")
    print(f"  明牌: {len(normal_cards)}张")
    
    # 详细分析暗牌
    print(f"\n🔍 暗牌详细分析:")
    for i, card in enumerate(dark_cards):
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        points = card.get('points', [])
        
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            y_coords = [point[1] for point in points]
            x_center = sum(x_coords) / len(x_coords)
            y_center = sum(y_coords) / len(y_coords)
        else:
            x_center = y_center = 0
        
        print(f"    暗牌{i+1}: label='{label}' -> twin_id='{twin_id}'")
        print(f"      位置: x={x_center:.1f}, y={y_center:.1f}")
        print(f"      region_name: {card.get('region_name', 'N/A')}")
        print(f"      owner: {card.get('owner', 'N/A')}")
    
    # 详细分析明牌
    print(f"\n🔍 明牌详细分析:")
    for i, card in enumerate(normal_cards):
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        points = card.get('points', [])
        
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            y_coords = [point[1] for point in points]
            x_center = sum(x_coords) / len(x_coords)
            y_center = sum(y_coords) / len(y_coords)
        else:
            x_center = y_center = 0
        
        print(f"    明牌{i+1}: label='{label}' -> twin_id='{twin_id}'")
        print(f"      位置: x={x_center:.1f}, y={y_center:.1f}")
    
    return {
        'total_cards': len(region16_cards),
        'dark_cards': dark_cards,
        'normal_cards': normal_cards,
        'dark_count': len(dark_cards),
        'normal_count': len(normal_cards)
    }

def compare_with_region6(frame_data: Dict[str, Any]) -> Dict[str, Any]:
    """对比区域6的处理"""
    print(f"\n🔍 对比区域6的暗牌处理")
    print("-" * 40)
    
    shapes = frame_data.get('shapes', [])
    region6_cards = [card for card in shapes if card.get('group_id') == 6]
    
    print(f"📋 区域6卡牌总数: {len(region6_cards)}")
    
    # 分析区域6的暗牌
    region6_dark_cards = []
    region6_normal_cards = []
    
    for card in region6_cards:
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        
        if '暗' in label or '暗' in twin_id:
            region6_dark_cards.append(card)
        else:
            region6_normal_cards.append(card)
    
    print(f"📊 区域6卡牌分类:")
    print(f"  暗牌: {len(region6_dark_cards)}张")
    print(f"  明牌: {len(region6_normal_cards)}张")
    
    if region6_dark_cards:
        print(f"\n🔍 区域6暗牌详情:")
        for i, card in enumerate(region6_dark_cards):
            label = card.get('label', '')
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            print(f"    暗牌{i+1}: label='{label}' -> twin_id='{twin_id}'")
    
    return {
        'region6_dark_count': len(region6_dark_cards),
        'region6_normal_count': len(region6_normal_cards),
        'region6_dark_cards': region6_dark_cards
    }

def analyze_dark_card_id_pattern(dark_cards: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析暗牌ID分配模式"""
    print(f"\n🔍 暗牌ID分配模式分析")
    print("-" * 40)
    
    if not dark_cards:
        print("  ✅ 无暗牌，无需分析")
        return {}
    
    print(f"📊 暗牌ID分配分析:")
    
    id_patterns = []
    for card in dark_cards:
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        label = card.get('label', '')
        
        # 检查ID格式
        if '临时暗' in twin_id:
            pattern = "临时ID"
        elif twin_id.endswith('暗'):
            pattern = "标准暗牌ID"
        elif twin_id.startswith('暗'):
            pattern = "暗牌前缀ID"
        else:
            pattern = "其他格式"
        
        id_patterns.append({
            'label': label,
            'twin_id': twin_id,
            'pattern': pattern
        })
        
        print(f"    {label} -> {twin_id} ({pattern})")
    
    # 检查是否符合预期
    expected_patterns = ["1暗", "2暗", "3暗"]  # 区域16暗牌的预期格式
    actual_ids = [card['twin_id'] for card in id_patterns]
    
    print(f"\n📋 ID格式检查:")
    print(f"  预期格式: {expected_patterns}")
    print(f"  实际格式: {actual_ids}")
    
    # 检查是否有问题
    has_issues = False
    issues = []
    
    for pattern_info in id_patterns:
        twin_id = pattern_info['twin_id']
        if '临时暗' in twin_id:
            has_issues = True
            issues.append(f"发现临时ID: {twin_id}")
        elif not twin_id.endswith('暗') and '暗' not in twin_id:
            has_issues = True
            issues.append(f"ID格式异常: {twin_id}")
    
    if has_issues:
        print(f"  🚨 发现问题:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print(f"  ✅ ID格式正常")
    
    return {
        'id_patterns': id_patterns,
        'has_issues': has_issues,
        'issues': issues
    }

def identify_root_cause():
    """识别根本原因"""
    print(f"\n🎯 根本原因分析")
    print("=" * 50)
    
    print(f"基于frame_00018的分析，可能的问题原因：")
    print(f"")
    print(f"🔴 可能原因1: 区域16空间重新分配机制影响了暗牌处理")
    print(f"   - 我们的修复逻辑可能干扰了暗牌的正常分配")
    print(f"   - 暗牌需要特殊的ID分配逻辑，不应该被重新分配")
    print(f"")
    print(f"🔴 可能原因2: 标签统一处理影响了暗牌识别")
    print(f"   - 暗牌的标签格式可能与明牌不同")
    print(f"   - 统一处理可能破坏了暗牌的特殊性")
    print(f"")
    print(f"🔴 可能原因3: 列重组逻辑不适用于暗牌")
    print(f"   - 暗牌的排列规则与明牌不同")
    print(f"   - 强制列重组可能破坏了暗牌的原有布局")
    print(f"")
    print(f"🔴 可能原因4: 区域6和区域16的暗牌处理差异")
    print(f"   - 区域6可能没有暗牌，所以机制复用不完整")
    print(f"   - 区域16的暗牌需要特殊处理逻辑")

def propose_fix_strategy():
    """提出修复策略"""
    print(f"\n💡 下次修复策略建议")
    print("=" * 50)
    
    print(f"🔧 策略1: 暗牌排除机制")
    print(f"   - 在区域16空间重新分配前，先排除暗牌")
    print(f"   - 只对明牌进行列重组和ID重新分配")
    print(f"   - 暗牌保持原有的处理逻辑不变")
    print(f"")
    print(f"🔧 策略2: 条件性应用修复")
    print(f"   - 检查区域16是否包含暗牌")
    print(f"   - 如果有暗牌，跳过空间重新分配")
    print(f"   - 只在纯明牌情况下应用修复逻辑")
    print(f"")
    print(f"🔧 策略3: 分层处理机制")
    print(f"   - 第一层：处理明牌的列重组")
    print(f"   - 第二层：保持暗牌的原有位置和ID")
    print(f"   - 第三层：合并处理结果")
    print(f"")
    print(f"🔧 策略4: 增强验证机制")
    print(f"   - 在修复前检查区域16的卡牌类型")
    print(f"   - 在修复后验证暗牌是否受到影响")
    print(f"   - 如果暗牌受影响，回退修复")
    print(f"")
    print(f"🎯 推荐实施顺序:")
    print(f"  1. 立即回退当前修复")
    print(f"  2. 实施策略1（暗牌排除机制）")
    print(f"  3. 重新测试frame_00018和frame_00230")
    print(f"  4. 确保两个场景都正常工作")

def main():
    """主分析函数"""
    print("🚨 Frame 18 区域16暗牌分配错误分析")
    print("=" * 60)
    
    # 加载frame_00018数据
    frame_data = load_frame_data("output/calibration_gt_final_with_digital_twin/labels/frame_00018.json")
    if not frame_data:
        print("❌ 无法加载frame_00018数据")
        return False
    
    # 分析区域16暗牌分配
    region16_analysis = analyze_region16_dark_cards(frame_data)
    
    # 对比区域6
    region6_analysis = compare_with_region6(frame_data)
    
    # 分析暗牌ID模式
    if region16_analysis['dark_cards']:
        dark_card_analysis = analyze_dark_card_id_pattern(region16_analysis['dark_cards'])
    else:
        dark_card_analysis = {}
    
    # 识别根本原因
    identify_root_cause()
    
    # 提出修复策略
    propose_fix_strategy()
    
    # 总结
    print(f"\n🎉 分析总结:")
    print(f"  区域16暗牌数量: {region16_analysis['dark_count']}")
    print(f"  区域6暗牌数量: {region6_analysis['region6_dark_count']}")
    
    if dark_card_analysis.get('has_issues', False):
        print(f"  🚨 发现暗牌ID分配问题")
        print(f"  建议: 立即回退修复，重新设计方案")
    else:
        print(f"  ✅ 暗牌ID分配正常")
        print(f"  建议: 进一步验证其他帧")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

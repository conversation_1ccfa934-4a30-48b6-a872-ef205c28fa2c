#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌YOLOv8训练脚本 - 优化版
功能：
1. 支持 AnyLabeling 标注的 JSON 文件转为 YOLO TXT 格式
2. 智能划分训练集/验证集/测试集
3. 针对类别不平衡实现加权损失
4. 支持多种模型选择和优化策略
5. 完整的评估和可视化系统
"""

import os
import json
import yaml
import random
import shutil
import logging
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from tqdm import tqdm
from collections import Counter, defaultdict
import torch
from ultralytics import YOLO
# from sklearn.metrics import confusion_matrix  # 移除未使用的导入以减少依赖
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('train_yolo.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger("YOLOv8_Training")

# 类别映射
LABEL_TO_ID = {
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
    "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
    "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
    "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
}

ID_TO_LABEL = {v: k for k, v in LABEL_TO_ID.items()}

# 全局类别集合
ALL_CATEGORIES = set(LABEL_TO_ID.keys())

# 稀有类别（基于之前的分析）
RARE_CLASSES = {
    "荒庄", "打鸟选择", "牌局结束", "你赢了", "胡", "你输了", "已准备", "碰", "吃", "过"
}

def extract_standard_label(label):
    """
    智能提取标准类别名
    示例：
        "3陆暗" -> "暗"
        "4陆" -> "陆"
        "吃(2)" -> "吃"
        "打鸟选择" -> "打鸟选择"
    """
    if not label:
        return None

    # 直接匹配
    if label in ALL_CATEGORIES:
        return label

    # 提取中文字符
    chinese_chars = ''.join([c for c in label if '\u4e00' <= c <= '\u9fff'])

    # 尝试最长匹配
    if chinese_chars:
        for candidate in sorted(ALL_CATEGORIES, key=len, reverse=True):
            if candidate in chinese_chars:
                return candidate

    # 关键字匹配
    for keyword in ["吃", "碰", "胡", "过"]:
        if keyword in label:
            return keyword

    return None

def convert_json_to_yolo(json_path, output_dir):
    """将单个 JSON 文件转换为 YOLO TXT 格式"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        image_width = data.get("imageWidth")
        image_height = data.get("imageHeight")

        if not image_width or not image_height:
            logger.warning(f"{json_path} 缺少图像尺寸信息，跳过此文件。")
            return

        txt_lines = []
        for shape in data.get("shapes", []):
            label = shape.get("label")
            points = shape.get("points")

            # 提取标准类别名
            card_type = extract_standard_label(label)

            if not card_type:
                logger.warning(f"无法解析标签: {label} @ {json_path}")
                continue

            class_id = LABEL_TO_ID.get(card_type, 0)
            if class_id == 0:
                continue

            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            xmin, xmax = min(x_coords), max(x_coords)
            ymin, ymax = min(y_coords), max(y_coords)

            x_center = (xmin + xmax) / 2 / image_width
            y_center = (ymin + ymax) / 2 / image_height
            width = (xmax - xmin) / image_width
            height = (ymax - ymin) / image_height

            txt_lines.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")

        # 写入 TXT 文件
        output_path = Path(output_dir) / (Path(json_path).stem + ".txt")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write("\n".join(txt_lines))

        logger.debug(f"已转换: {json_path} -> {output_path}")
        return True

    except Exception as e:
        logger.error(f"转换失败 {json_path}: {e}")
        return False

def convert_all_json_folders(src_label_dir, dst_label_dir):
    """
    将 src_label_dir 下所有子目录中的 .json 文件转换为 YOLO TXT 格式，
    并保留原始文件夹结构。
    """
    success_count = 0
    fail_count = 0
    
    for root, dirs, files in os.walk(src_label_dir):
        # 过滤隐藏文件夹
        dirs[:] = [d for d in dirs if not d.startswith('.')]

        for file in files:
            if file.lower().endswith(".json"):
                src_json_path = os.path.join(root, file)

                # 计算相对路径以重建结构
                try:
                    rel_path = os.path.relpath(root, src_label_dir)
                except ValueError:
                    logger.warning(f"无法解析相对路径: {root}，跳过该文件")
                    continue

                dst_folder = os.path.join(dst_label_dir, rel_path)
                os.makedirs(dst_folder, exist_ok=True)

                # 转换 JSON 到 TXT
                result = convert_json_to_yolo(src_json_path, dst_folder)
                if result:
                    success_count += 1
                else:
                    fail_count += 1
    
    logger.info(f"转换完成: 成功 {success_count} 个, 失败 {fail_count} 个")
    return success_count, fail_count

def analyze_class_distribution(label_dir):
    """分析标签目录中的类别分布"""
    class_counts = Counter()
    
    for root, _, files in os.walk(label_dir):
        for file in files:
            if file.lower().endswith(".txt"):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts:
                                class_id = int(parts[0])
                                class_counts[class_id] += 1
                except Exception as e:
                    logger.error(f"读取标签文件失败 {file_path}: {e}")
    
    # 转换为类名
    named_counts = {ID_TO_LABEL.get(class_id, f"未知-{class_id}"): count 
                   for class_id, count in class_counts.items()}
    
    return named_counts

def visualize_class_distribution(class_counts, output_path):
    """可视化类别分布并保存图表"""
    plt.figure(figsize=(15, 8))
    
    # 排序以便更好地显示
    sorted_items = sorted(class_counts.items(), key=lambda x: LABEL_TO_ID.get(x[0], 999) if x[0] in LABEL_TO_ID else 999)
    labels = [item[0] for item in sorted_items]
    counts = [item[1] for item in sorted_items]
    
    # 找出稀有类别的索引
    rare_indices = [i for i, label in enumerate(labels) if label in RARE_CLASSES]
    
    # 创建颜色列表，稀有类别使用不同颜色
    colors = ['#3498db' for _ in range(len(labels))]
    for idx in rare_indices:
        colors[idx] = '#e74c3c'
    
    bars = plt.bar(range(len(labels)), counts, color=colors)
    plt.xticks(range(len(labels)), labels, rotation=90)
    plt.title("类别分布")
    plt.xlabel("类别")
    plt.ylabel("样本数量")
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f"{int(height)}", ha='center', va='bottom', rotation=0)
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()
    
    logger.info(f"类别分布图已保存: {output_path}")

def split_dataset(image_dir, label_dir, output_dir, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1, 
                 stratify_by_class=True):
    """
    智能划分数据集，可以按类别分层抽样
    """
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-5, "比例总和必须为1"
    
    # 创建输出目录
    train_img_dir = os.path.join(output_dir, "images", "train")
    val_img_dir = os.path.join(output_dir, "images", "val")
    test_img_dir = os.path.join(output_dir, "images", "test")
    train_label_dir = os.path.join(output_dir, "labels", "train")
    val_label_dir = os.path.join(output_dir, "labels", "val")
    test_label_dir = os.path.join(output_dir, "labels", "test")
    
    for d in [train_img_dir, val_img_dir, test_img_dir, train_label_dir, val_label_dir, test_label_dir]:
        os.makedirs(d, exist_ok=True)
    
    # 收集所有图像和标签文件
    image_files = []
    for root, _, files in os.walk(image_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_path = os.path.join(root, file)
                # 计算对应的标签路径
                rel_path = os.path.relpath(root, image_dir)
                
                # 检查JSON标注文件
                json_label_path = os.path.join(label_dir, rel_path, os.path.splitext(file)[0] + '.json')
                # 检查TXT标注文件
                txt_label_path = os.path.join(label_dir, rel_path, os.path.splitext(file)[0] + '.txt')
                
                label_path = None
                if os.path.exists(json_label_path):
                    label_path = json_label_path
                elif os.path.exists(txt_label_path):
                    label_path = txt_label_path
                
                if label_path:
                    image_files.append((image_path, label_path))
                else:
                    logger.warning(f"未找到标注文件: {os.path.splitext(file)[0]}")
    
    random.shuffle(image_files)
    
    if stratify_by_class:
        # 按类别分组
        class_to_files = defaultdict(list)
        
        for img_path, label_path in image_files:
            # 读取标签文件，获取类别
            try:
                with open(label_path, 'r') as f:
                    classes = set()
                    for line in f:
                        parts = line.strip().split()
                        if parts:
                            class_id = int(parts[0])
                            classes.add(class_id)
                
                # 如果一个图像有多个类别，选择最稀有的类别作为分组依据
                if classes:
                    # 取消对稀有类别的特殊优先级，避免过度关注
                    rarest_class = random.choice(list(classes))
                    class_to_files[rarest_class].append((img_path, label_path))
            except Exception as e:
                logger.error(f"读取标签文件失败 {label_path}: {e}")
                continue
        
        # 按类别分层抽样
        train_files = []
        val_files = []
        test_files = []
        
        for class_id, files in class_to_files.items():
            n = len(files)
            n_train = int(n * train_ratio)
            n_val = int(n * val_ratio)
            
            train_files.extend(files[:n_train])
            val_files.extend(files[n_train:n_train+n_val])
            test_files.extend(files[n_train+n_val:])
    else:
        # 简单随机划分
        n = len(image_files)
        n_train = int(n * train_ratio)
        n_val = int(n * val_ratio)
        
        train_files = image_files[:n_train]
        val_files = image_files[n_train:n_train+n_val]
        test_files = image_files[n_train+n_val:]
    
    # 复制文件到目标目录
    def copy_files(files, img_dir, label_dir):
        for img_path, label_path in tqdm(files, desc=f"复制到 {os.path.basename(img_dir)}"):
            img_name = os.path.basename(img_path)
            label_name = os.path.basename(label_path)
            
            shutil.copy2(img_path, os.path.join(img_dir, img_name))
            shutil.copy2(label_path, os.path.join(label_dir, label_name))
    
    copy_files(train_files, train_img_dir, train_label_dir)
    copy_files(val_files, val_img_dir, val_label_dir)
    copy_files(test_files, test_img_dir, test_label_dir)
    
    logger.info(f"数据集划分完成: 训练集 {len(train_files)}, 验证集 {len(val_files)}, 测试集 {len(test_files)}")
    
    # 返回数据集信息
    return {
        "train": len(train_files),
        "val": len(val_files),
        "test": len(test_files),
        "total": len(image_files)
    }

def create_data_yaml(output_dir, class_names):
    """创建YOLO训练所需的data.yaml文件"""
    data_yaml = {
        "path": output_dir,
        "train": "images/train",
        "val": "images/val",
        "test": "images/test",
        "nc": len(class_names),
        "names": class_names
    }
    
    yaml_path = os.path.join(output_dir, "data.yaml")
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_yaml, f, default_flow_style=False, allow_unicode=True)
    
    logger.info(f"数据配置文件已创建: {yaml_path}")
    return yaml_path

def calculate_class_weights(class_counts):
    """计算类别权重，用于处理类别不平衡"""
    if not class_counts:
        return {}
    
    max_count = max(class_counts.values())
    weights = {class_name: max_count / count for class_name, count in class_counts.items()}
    
    # 归一化权重
    total_weight = sum(weights.values())
    normalized_weights = {class_name: weight / total_weight * len(weights) 
                         for class_name, weight in weights.items()}
    
    return normalized_weights

class YOLOv8Trainer:
    """YOLOv8模型训练器"""

    def __init__(self, config):
        self.config = config
        self.device = self._setup_device()
        self.results = None

    def _setup_device(self):
        """设置训练设备"""
        if torch.cuda.is_available():
            logger.info(f"使用 GPU: {torch.cuda.get_device_name(0)}")
            return "cuda"
        logger.warning("未检测到 GPU，将使用 CPU 训练")
        return "cpu"

    def _load_model(self, model_path, pretrained=True):
        """加载预训练模型"""
        if not os.path.exists(model_path) and pretrained:
            logger.warning(f"未找到预训练模型 {model_path}，将使用官方预训练权重")
            # 尝试从官方下载
            try:
                model = YOLO(os.path.basename(model_path))
                logger.info(f"成功加载官方预训练模型: {os.path.basename(model_path)}")
                return model
            except Exception as e:
                logger.error(f"加载官方模型失败: {e}")
                return None
        
        try:
            logger.info(f"加载模型: {model_path}")
            model = YOLO(model_path)
            return model
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return None

    def train(self, args):
        """执行训练"""
        model = self._load_model(args.model_path)
        if not model:
            logger.error("模型加载失败，训练中止。")
            return None

        # 动态批次数计算（针对RTX 5060 8GB优化: 每1GB显存最多1张，取较小值）
        recommended_batch = args.batch_size
        if self.device == "cuda":
            try:
                total_mem = torch.cuda.get_device_properties(0).total_memory / (1024 ** 3)  # GB
                max_batch_by_mem = max(1, int(total_mem // 1))  # 更激进的显存利用率
                if max_batch_by_mem < recommended_batch:
                    logger.info(
                        f"根据显存大小({total_mem:.1f}GB)自动将批次大小从 {recommended_batch} 调整为 {max_batch_by_mem}"
                    )
                    recommended_batch = max_batch_by_mem
            except Exception as e:
                logger.warning(f"动态批次大小计算失败: {e}")

        # 准备训练参数
        train_args = {
            "data": args.data_yaml,
            "epochs": args.epochs,
            "imgsz": (args.img_height, args.img_width),
            "batch": recommended_batch,
            "patience": args.patience,
            "lr0": args.learning_rate,
            "lrf": 0.01,  # 最终学习率为初始学习率的1%
            "momentum": 0.937,
            "weight_decay": 0.0005,
            "warmup_epochs": 3.0,
            "warmup_momentum": 0.8,
            "warmup_bias_lr": 0.1,
            "box": 7.5,
            "cls": 0.5,
            "dfl": 1.5,
            "cos_lr": True,
            # 适当提升数据增强概率以缓解过拟合并增强小目标学习
            "mosaic": 0.5,
            "mixup": 0.2,
            "copy_paste": 0.1,
            "auto_augment": True,
            "degrees": 0.0,
            "translate": 0.1,
            "scale": 0.5,
            "shear": 0.0,
            "perspective": 0.0,
            "flipud": 0.0,
            "fliplr": 0.5,
            "hsv_h": 0.05,
            "hsv_s": 0.6,
            "hsv_v": 0.3,
            "cache": "ram" if torch.cuda.is_available() else None,
            "workers": min(args.workers, os.cpu_count() or 4),
            "device": self.device,
            "project": args.output_dir,
            "name": "train",
            "exist_ok": True,
            "pretrained": True,
            "optimizer": "auto",
            "verbose": True,
            "seed": 0,
            "deterministic": True,
            "single_cls": False,
            "rect": True,
            "close_mosaic": 10,
            "amp": args.half,  # 使用半精度训练
            # 移除不支持的参数
            # "fl_gamma": 0.0,
            # "cls_weights": class_weights,
        }
        
        # 如果有类别权重，应用它们（但当前YOLOv8不支持直接传入cls_weights参数）
        if hasattr(args, 'class_weights') and args.class_weights:
            # 这里我们不再尝试设置cls_weights参数，因为当前版本不支持
            logger.info("类别权重已计算，但当前YOLOv8版本不支持直接通过参数应用")
            # cls_weights = torch.tensor([args.class_weights.get(ID_TO_LABEL.get(i, "unknown"), 1.0) 
            #                             for i in range(len(ID_TO_LABEL))])
            # train_args["cls_weights"] = cls_weights

        logger.info("开始模型训练...")
        logger.debug(f"训练参数: {train_args}")
        
        try:
            self.results = model.train(**train_args)
            
            best_model_path = Path(args.output_dir) / "train" / "weights" / "best.pt"
            if best_model_path.exists():
                logger.info(f"训练完成，最佳模型已保存: {best_model_path}")
                return best_model_path
            else:
                last_model_path = Path(args.output_dir) / "train" / "weights" / "last.pt"
                if last_model_path.exists():
                    logger.warning(f"未找到最佳模型，使用最后一个模型: {last_model_path}")
                    return last_model_path
                else:
                    logger.error("训练完成，但未找到任何模型")
                    return None
        except Exception as e:
            logger.error(f"训练过程出错: {e}")
            return None

    def evaluate(self, model_path, data_yaml, img_size=(640, 320)):
        """评估模型性能"""
        model = self._load_model(model_path, pretrained=False)
        if not model:
            logger.error("模型加载失败，评估中止。")
            return None
        
        try:
            logger.info(f"开始评估模型: {model_path}")
            results = model.val(data=data_yaml, imgsz=img_size)

            if results is None or not hasattr(results, "box"):
                logger.warning("评估结果为空或格式不符合预期，返回空指标。")
                return None, None

            # 保存详细评估结果
            metrics = {
                "mAP50": getattr(results.box, "map50", 0),
                "mAP50-95": getattr(results.box, "map", 0),
                "precision": getattr(results.box, "mp", 0),
                "recall": getattr(results.box, "mr", 0),
            }
            # 自定义F1 分数
            metrics["f1"] = metrics["mAP50"] * 0.5 + metrics["precision"] * 0.25 + metrics["recall"] * 0.25

            logger.info(
                f"评估结果: mAP50={metrics['mAP50']:.4f}, mAP50-95={metrics['mAP50-95']:.4f}, "
                f"precision={metrics['precision']:.4f}, recall={metrics['recall']:.4f}"
            )

            return metrics, results
        except Exception as e:
            logger.error(f"评估过程出错: {e}")
            return None, None

    def export_model(self, model_path, output_dir, format='onnx', img_size=(640, 320)):
        """导出模型为指定格式（修复AnyLabeling兼容性问题）"""
        model = self._load_model(model_path, pretrained=False)
        if not model:
            logger.error("模型加载失败，导出中止。")
            return None

        try:
            logger.info(f"开始导出模型为 {format} 格式")

            if format.lower() == 'onnx':
                # 专门为ONNX格式优化的导出参数（修复AnyLabeling兼容性）
                # 关键修复：使用正方形尺寸，与最佳版本保持一致
                optimal_size = max(img_size) if isinstance(img_size, (list, tuple)) else img_size

                export_args = {
                    "format": format,
                    "imgsz": optimal_size,  # 修复：使用正方形尺寸（与best_anylabeling.onnx一致）
                    "dynamic": False,       # 修复：关闭动态尺寸，确保AnyLabeling兼容性
                    "simplify": True,
                    "opset": 12,
                    "optimize": False,      # 修复：关闭优化，避免数值问题
                    "half": False,          # 修复：使用FP32精度
                    "batch": 1,             # 修复：固定批大小
                    "workspace": 4,         # 修复：添加workspace参数（与best_anylabeling.onnx一致）
                    "device": self.device,
                }
                logger.info(f"使用AnyLabeling最优导出参数，图像尺寸: {optimal_size}")
                logger.info("参数与best_anylabeling.onnx保持一致")
            else:
                # 其他格式使用原有参数
                export_args = {
                    "format": format,
                    "imgsz": img_size,
                    "dynamic": True,
                    "simplify": True,
                    "opset": 12,
                    "device": self.device,
                }

            logger.info(f"导出参数: {export_args}")
            exported_path = model.export(**export_args)

            # 移动到指定目录
            if exported_path and os.path.exists(exported_path):
                output_path = os.path.join(output_dir, os.path.basename(exported_path))
                if exported_path != output_path:
                    shutil.copy2(exported_path, output_path)
                    logger.info(f"模型已导出并复制到: {output_path}")
                else:
                    logger.info(f"模型已导出: {exported_path}")

                # 验证ONNX导出质量
                if format.lower() == 'onnx':
                    self._validate_onnx_export(output_path, img_size)

                return output_path

            logger.error(f"模型导出失败，未找到导出文件")
            return None
        except Exception as e:
            logger.error(f"导出过程出错: {e}")
            return None

    def _validate_onnx_export(self, onnx_path, img_size):
        """验证ONNX导出质量"""
        try:
            import onnxruntime as ort

            logger.info("验证ONNX导出质量...")

            # 加载ONNX模型
            session = ort.InferenceSession(onnx_path)
            input_info = session.get_inputs()[0]

            logger.info(f"ONNX输入形状: {input_info.shape}")

            # 创建测试输入
            test_input = np.random.rand(1, 3, img_size[1], img_size[0]).astype(np.float32)

            # 运行推理
            outputs = session.run(None, {input_info.name: test_input})
            output = np.array(outputs[0])  # 确保是numpy数组

            logger.info(f"ONNX输出形状: {output.shape}")
            logger.info(f"ONNX输出数值范围: [{float(output.min()):.6f}, {float(output.max()):.6f}]")

            # 检查置信度
            if len(output.shape) == 3:
                if output.shape[1] > output.shape[2]:
                    data = output[0].transpose()
                else:
                    data = output[0]

                if data.shape[1] >= 5:
                    confidences = data[:, 4]
                    max_conf = float(confidences.max())

                    if max_conf > 0.01:
                        logger.info(f"✅ ONNX导出验证通过，最大置信度: {max_conf:.6f}")
                    else:
                        logger.warning(f"⚠️ ONNX导出可能有问题，最大置信度过低: {max_conf:.8f}")
                else:
                    logger.warning("⚠️ ONNX输出格式异常")
            else:
                logger.warning("⚠️ ONNX输出维度异常")

        except ImportError:
            logger.warning("未安装onnxruntime，跳过ONNX验证")
        except Exception as e:
            logger.warning(f"ONNX验证失败: {e}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description="跑胡子卡牌YOLOv8训练脚本")
    
    # 基本参数
    parser.add_argument("--input-image-dir", type=str, 
                        default="D:/phz-ai-simple/data/xunlianjiyolo/consolidated_shuffled/images",
                        help="输入图像目录")
    parser.add_argument("--input-label-dir", type=str, 
                        default="D:/phz-ai-simple/data/xunlianjiyolo/consolidated_shuffled/labels",
                        help="输入标签目录")
    parser.add_argument("--output-dir", type=str, 
                        default="D:/phz-ai-simple/data/processed",
                        help="输出目录")
    parser.add_argument("--temp-dir", type=str, 
                        default="D:/phz-ai-simple/data/temp",
                        help="临时目录")
    
    # 数据集划分参数
    parser.add_argument("--train-ratio", type=float, default=0.72,
                        help="训练集比例")
    parser.add_argument("--val-ratio", type=float, default=0.18,
                        help="验证集比例")
    parser.add_argument("--test-ratio", type=float, default=0.1,
                        help="测试集比例")
    parser.add_argument("--stratify", action="store_true",
                        help="是否按类别分层抽样")
    
    # 训练参数
    parser.add_argument("--model-path", type=str, 
                        default="D:/phz-ai-simple/models/yolov8l.pt",  # 默认使用更大容量的YOLOv8l
                        help="预训练模型路径")
    parser.add_argument("--epochs", type=int, default=300,  # 增加训练轮数以充分学习
                        help="训练轮数")
    parser.add_argument("--batch-size", type=int, default=16,
                        help="批次大小")
    parser.add_argument("--img-width", type=int, default=640,
                        help="图像宽度")
    parser.add_argument("--img-height", type=int, default=320,
                        help="图像高度")
    parser.add_argument("--learning-rate", type=float, default=0.0005,  # 更小学习率减缓过拟合
                        help="初始学习率")
    parser.add_argument("--patience", type=int, default=20,
                        help="早停耐心值")
    parser.add_argument("--workers", type=int, default=16,
                        help="数据加载线程数")
    
    # 功能选择
    parser.add_argument("--skip-convert", action="store_true",
                        help="跳过JSON转换步骤")
    parser.add_argument("--skip-train", action="store_true",
                        help="跳过训练步骤")
    parser.add_argument("--export-formats", type=str, default="onnx",
                        help="导出格式，用逗号分隔")
    # 新增GPU优化选项
    parser.add_argument("--half", action="store_true",
                        help="使用半精度(FP16)训练以提高速度和减少显存占用")
    parser.add_argument("--cudnn-benchmark", action="store_true", default=True,
                        help="启用cudnn benchmark模式以加速训练(固定输入尺寸时效果最佳)")
    
    args = parser.parse_args()
    
    # 创建必要的目录
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.temp_dir, exist_ok=True)
    
    # 设置CUDA优化
    if torch.cuda.is_available():
        # 启用cudnn benchmark模式
        if args.cudnn_benchmark:
            logger.info("启用 CUDNN benchmark 模式以加速训练")
            torch.backends.cudnn.benchmark = True
        
        # 显示CUDA和GPU信息
        logger.info(f"CUDA版本: {torch.version.cuda}")
        logger.info(f"GPU型号: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
    
    processed_data_dir = os.path.join(args.output_dir, "yolo_dataset")
    os.makedirs(processed_data_dir, exist_ok=True)
    
    # 步骤1: 转换JSON到YOLO格式（如果需要）
    if not args.skip_convert:
        logger.info("开始转换JSON标注为YOLO格式...")
        temp_label_dir = os.path.join(args.temp_dir, "labels_txt")
        os.makedirs(temp_label_dir, exist_ok=True)
        convert_all_json_folders(args.input_label_dir, temp_label_dir)
        args.input_label_dir = temp_label_dir
    
    # 步骤2: 分析类别分布
    logger.info("分析类别分布...")
    class_counts = analyze_class_distribution(args.input_label_dir)
    
    # 可视化类别分布
    vis_dir = os.path.join(args.output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)
    visualize_class_distribution(class_counts, os.path.join(vis_dir, "class_distribution.png"))
    
    # 计算类别权重
    class_weights = calculate_class_weights(class_counts)
    logger.info("类别权重计算完成")
    
    # 步骤3: 划分数据集
    logger.info("开始划分数据集...")
    dataset_info = split_dataset(
        args.input_image_dir, 
        args.input_label_dir, 
        processed_data_dir, 
        args.train_ratio, 
        args.val_ratio, 
        args.test_ratio,
        args.stratify
    )
    
    # 步骤4: 创建data.yaml
    class_names = [ID_TO_LABEL.get(i, f"未知-{i}") for i in range(1, len(ID_TO_LABEL) + 1)]
    data_yaml_path = create_data_yaml(processed_data_dir, class_names)
    
    # 添加类别权重到args
    args.class_weights = class_weights
    args.data_yaml = data_yaml_path
    
    # 步骤5: 训练模型
    if not args.skip_train:
        logger.info("开始训练模型...")
        trainer = YOLOv8Trainer({})
        best_model = trainer.train(args)
        
        # 步骤6: 评估模型
        if best_model:
            logger.info("开始评估模型...")
            img_size = (args.img_width, args.img_height)
            evaluation_result = trainer.evaluate(best_model, data_yaml_path, img_size)
            
            # 步骤7: 导出模型
            metrics = None
            if evaluation_result and len(evaluation_result) > 0:
                metrics, _ = evaluation_result
                
            if isinstance(metrics, dict):
                logger.info(f"评估指标: mAP50={metrics.get('mAP50', 0):.4f}")
            else:
                logger.warning("评估结果为空或格式不正确，跳过指标显示")
                
            export_formats = [fmt.strip() for fmt in args.export_formats.split(",")]
            models_dir = os.path.join(args.output_dir, "models")
            os.makedirs(models_dir, exist_ok=True)
            
            for fmt in export_formats:
                logger.info(f"导出模型为 {fmt} 格式...")
                trainer.export_model(best_model, models_dir, fmt, img_size)
    
    logger.info("处理完成!")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
全面继承测试脚本

专门测试6→6优先级逻辑是否生效，以及是否被其他模块覆盖
重点分析frame_00362.jpg和frame_00347.jpg的问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
import re

def load_frame_data(frame_path):
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载帧数据失败: {frame_path} - {e}")
        return None

def extract_region_cards(frame_data, region_id):
    """提取指定区域的卡牌信息"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    cards = []
    for i, shape in enumerate(frame_data['shapes']):
        if shape.get('group_id') == region_id:
            # 获取位置信息
            points = shape.get('points', [])
            if points:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x_center = sum(x_coords) / len(x_coords)
                y_center = sum(y_coords) / len(y_coords)
                y_bottom = max(y_coords)
            else:
                x_center = y_center = y_bottom = 0
            
            card_info = {
                'index': i,
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_center': y_center,
                'y_bottom': y_bottom,
                'points': points,
                'timestamp': shape.get('attributes', {}).get('processed_timestamp', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'inherited': shape.get('attributes', {}).get('inherited', False),
                'source_card_id': shape.get('attributes', {}).get('source_card_id', ''),
                'from_region_3': shape.get('attributes', {}).get('from_region_3', False),
                'from_region_6': shape.get('attributes', {}).get('from_region_6', False),
                'from_region_7': shape.get('attributes', {}).get('from_region_7', False),
                'from_region_8': shape.get('attributes', {}).get('from_region_8', False),
                'from_region_1': shape.get('attributes', {}).get('from_region_1', False),
                'region6_spatial_reassign': shape.get('attributes', {}).get('region6_spatial_reassign', False)
            }
            cards.append(card_info)
    
    return cards

def analyze_inheritance_priority(cards_current, cards_previous, frame_name):
    """分析继承优先级是否正确"""
    print(f"\n🔍 {frame_name} 继承优先级分析:")
    print("=" * 60)
    
    # 按标签分组
    label_groups = {}
    for card in cards_current:
        label = card['label']
        if label not in label_groups:
            label_groups[label] = []
        label_groups[label].append(card)
    
    for label, current_cards in label_groups.items():
        print(f"\n📋 标签 '{label}' 分析:")
        print("-" * 40)
        
        # 找到前一帧的同标签卡牌
        previous_same_label = [card for card in cards_previous if card['label'] == label]
        
        print(f"当前帧: {len(current_cards)}张, 前一帧: {len(previous_same_label)}张")
        
        # 分析每张当前卡牌的继承来源
        for i, card in enumerate(current_cards):
            print(f"\n  卡牌[{i+1}]: {card['label']} (ID: {card['digital_twin_id']})")
            print(f"    继承标记: inherited={card['inherited']}")
            print(f"    源ID: {card['source_card_id']}")
            print(f"    流转标记: 3→6={card['from_region_3']}, 6→6={card['from_region_6']}, 7→6={card['from_region_7']}, 8→6={card['from_region_8']}, 1→6={card['from_region_1']}")
            
            # 分析继承优先级
            inheritance_source = "未知"
            if card['from_region_6']:
                inheritance_source = "6→6 (最高优先级)"
            elif card['from_region_3']:
                inheritance_source = "3→6"
            elif card['from_region_7']:
                inheritance_source = "7→6"
            elif card['from_region_8']:
                inheritance_source = "8→6"
            elif card['from_region_1']:
                inheritance_source = "1→6"
            elif card['inherited']:
                inheritance_source = "其他继承"
            else:
                inheritance_source = "新卡牌"
            
            print(f"    继承来源: {inheritance_source}")
            
            # 验证源ID是否存在于前一帧
            if card['source_card_id']:
                source_found = False
                for prev_card in cards_previous:
                    if prev_card['digital_twin_id'] == card['source_card_id']:
                        source_found = True
                        print(f"    ✅ 源卡牌验证: 找到源卡牌 {prev_card['label']} (ID: {prev_card['digital_twin_id']})")
                        break
                
                if not source_found:
                    print(f"    ❌ 源卡牌验证: 未找到源卡牌 {card['source_card_id']}")

def analyze_6to6_priority_effectiveness(cards_current, cards_previous, frame_name):
    """专门分析6→6优先级是否生效"""
    print(f"\n🎯 {frame_name} 6→6优先级生效性分析:")
    print("=" * 60)
    
    # 找到所有标记为6→6继承的卡牌
    region6_inherited_cards = [card for card in cards_current if card['from_region_6']]
    
    print(f"标记为6→6继承的卡牌数量: {len(region6_inherited_cards)}")
    
    if len(region6_inherited_cards) == 0:
        print("❌ 没有发现6→6继承标记，6→6优先级可能没有生效！")
        return False
    
    # 分析每张6→6继承的卡牌
    for card in region6_inherited_cards:
        print(f"\n✅ 6→6继承卡牌: {card['label']} (ID: {card['digital_twin_id']})")
        print(f"    源ID: {card['source_card_id']}")
        print(f"    位置: ({card['x_center']:.1f}, {card['y_center']:.1f})")
        
        # 验证源卡牌是否来自前一帧的区域6
        if card['source_card_id']:
            source_card = None
            for prev_card in cards_previous:
                if prev_card['digital_twin_id'] == card['source_card_id']:
                    source_card = prev_card
                    break
            
            if source_card:
                print(f"    源卡牌位置: ({source_card['x_center']:.1f}, {source_card['y_center']:.1f})")
                print(f"    ✅ 6→6继承验证成功")
            else:
                print(f"    ❌ 源卡牌验证失败")
    
    return len(region6_inherited_cards) > 0

def analyze_module_override_issue(cards_current, frame_name):
    """分析是否存在模块覆盖问题"""
    print(f"\n🔧 {frame_name} 模块覆盖问题分析:")
    print("=" * 60)
    
    # 检查继承标记的一致性
    inconsistent_cards = []
    
    for card in cards_current:
        # 检查继承标记与流转标记的一致性
        has_inheritance_mark = card['inherited']
        has_flow_mark = any([
            card['from_region_3'],
            card['from_region_6'],
            card['from_region_7'],
            card['from_region_8'],
            card['from_region_1']
        ])
        
        if has_inheritance_mark != has_flow_mark:
            inconsistent_cards.append({
                'card': card,
                'issue': f"继承标记({has_inheritance_mark})与流转标记({has_flow_mark})不一致"
            })
        
        # 检查源ID是否存在但没有继承标记
        if card['source_card_id'] and not card['inherited']:
            inconsistent_cards.append({
                'card': card,
                'issue': f"有源ID({card['source_card_id']})但没有继承标记"
            })
    
    if inconsistent_cards:
        print(f"❌ 发现 {len(inconsistent_cards)} 张卡牌存在标记不一致问题:")
        for item in inconsistent_cards:
            card = item['card']
            print(f"  - {card['label']} (ID: {card['digital_twin_id']}): {item['issue']}")
        return True
    else:
        print("✅ 所有卡牌的继承标记一致")
        return False

def test_specific_frames():
    """测试特定的问题帧"""
    print("🔍 全面继承测试脚本")
    print("=" * 60)
    print("目标: 测试6→6优先级逻辑是否生效，分析frame_00362和frame_00347问题")
    print()
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    # 测试帧列表
    test_frames = [
        ("frame_00361", "frame_00362"),  # 361→362的继承
        ("frame_00346", "frame_00347"),  # 346→347的继承
        ("frame_00340", "frame_00341"),  # 340→341的继承（已修复的参考）
    ]
    
    all_results = {}
    
    for prev_frame, curr_frame in test_frames:
        print(f"\n{'='*80}")
        print(f"🎯 测试帧对: {prev_frame} → {curr_frame}")
        print(f"{'='*80}")
        
        # 加载数据
        prev_path = output_dir / f"{prev_frame}.json"
        curr_path = output_dir / f"{curr_frame}.json"
        
        if not prev_path.exists() or not curr_path.exists():
            print(f"❌ 文件不存在: {prev_path} 或 {curr_path}")
            continue
        
        prev_data = load_frame_data(prev_path)
        curr_data = load_frame_data(curr_path)
        
        if not prev_data or not curr_data:
            print(f"❌ 数据加载失败")
            continue
        
        # 提取区域6的卡牌
        prev_cards = extract_region_cards(prev_data, 6)
        curr_cards = extract_region_cards(curr_data, 6)
        
        print(f"前一帧区域6: {len(prev_cards)}张卡牌")
        print(f"当前帧区域6: {len(curr_cards)}张卡牌")
        
        # 分析继承优先级
        analyze_inheritance_priority(curr_cards, prev_cards, curr_frame)
        
        # 分析6→6优先级生效性
        priority_effective = analyze_6to6_priority_effectiveness(curr_cards, prev_cards, curr_frame)
        
        # 分析模块覆盖问题
        has_override_issue = analyze_module_override_issue(curr_cards, curr_frame)
        
        # 记录结果
        all_results[f"{prev_frame}→{curr_frame}"] = {
            'priority_effective': priority_effective,
            'has_override_issue': has_override_issue,
            'prev_cards_count': len(prev_cards),
            'curr_cards_count': len(curr_cards)
        }
    
    # 总结分析
    print(f"\n{'='*80}")
    print("🏁 总结分析")
    print(f"{'='*80}")
    
    for frame_pair, result in all_results.items():
        print(f"\n📊 {frame_pair}:")
        print(f"  6→6优先级生效: {'✅' if result['priority_effective'] else '❌'}")
        print(f"  存在覆盖问题: {'❌' if result['has_override_issue'] else '✅'}")
        print(f"  卡牌数量变化: {result['prev_cards_count']} → {result['curr_cards_count']}")
    
    # 问题诊断
    print(f"\n🔧 问题诊断:")
    
    ineffective_frames = [k for k, v in all_results.items() if not v['priority_effective']]
    override_frames = [k for k, v in all_results.items() if v['has_override_issue']]
    
    if ineffective_frames:
        print(f"❌ 6→6优先级未生效的帧: {', '.join(ineffective_frames)}")
    
    if override_frames:
        print(f"❌ 存在模块覆盖问题的帧: {', '.join(override_frames)}")
    
    if not ineffective_frames and not override_frames:
        print("✅ 所有测试帧的6→6优先级都正常生效，无模块覆盖问题")
    
    return all_results

if __name__ == "__main__":
    results = test_specific_frames()
    
    # 根据结果提供修复建议
    print(f"\n💡 修复建议:")
    print("-" * 40)
    
    has_issues = any(not r['priority_effective'] or r['has_override_issue'] for r in results.values())
    
    if has_issues:
        print("1. 检查region_transitioner.py中的6→6继承逻辑是否正确设置继承标记")
        print("2. 检查basic_id_assigner.py是否覆盖了继承结果")
        print("3. 检查simple_inheritor.py的处理顺序是否影响了6→6优先级")
        print("4. 验证各模块之间的调用顺序和数据传递")
    else:
        print("✅ 6→6优先级逻辑工作正常，可能需要检查其他问题")
    
    sys.exit(0 if not has_issues else 1)

#!/usr/bin/env python3
"""
对比分析区域6与区域16的处理逻辑差异
"""

import json
import sys
from collections import defaultdict

def analyze_region_cards(frame_path, region_id):
    """分析指定帧的指定区域卡牌"""
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    region_cards = []
    for shape in data.get('shapes', []):
        if shape.get('group_id') == region_id:
            points = shape.get('points', [])
            if points:
                x_center = sum([p[0] for p in points]) / len(points)
                y_bottom = max([p[1] for p in points])
                x_left = min([p[0] for p in points])
            else:
                x_center = y_bottom = x_left = 0
                
            card_info = {
                'label': shape.get('label', ''),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_bottom': y_bottom,
                'x_left': x_left,
                'points': points
            }
            region_cards.append(card_info)
    
    return region_cards

def analyze_column_consistency(cards, region_id, tolerance=8.0):
    """分析列一致性"""
    
    # 按X坐标分列
    columns = defaultdict(list)
    
    for card in cards:
        x_center = card['x_center']
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"\n区域{region_id} - 按列分组 (容差: {tolerance}px):")
    consistent_columns = 0
    total_columns = len(columns)
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        # 按Y坐标排序（从下到上）
        column_cards.sort(key=lambda c: -c['y_bottom'])
        
        labels_in_column = [card['label'] for card in column_cards]
        base_labels_in_column = []
        for label in labels_in_column:
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            # 标准化陆/六
            if base_label == '陆':
                base_label = '六'
            base_labels_in_column.append(base_label)
        
        is_consistent = len(set(base_labels_in_column)) == 1
        if is_consistent:
            consistent_columns += 1
            
        print(f"  列{i+1} (X≈{x_key:.1f}): {len(column_cards)}张 {'✓' if is_consistent else '✗'}")
        print(f"    标签: {labels_in_column}")
        print(f"    基础标签: {base_labels_in_column}")
        
        for card in column_cards:
            print(f"      - {card['label']} (ID: {card['twin_id']}) Y: {card['y_bottom']:.1f}")
    
    consistency_rate = consistent_columns / total_columns if total_columns > 0 else 0
    print(f"\n  列一致性: {consistent_columns}/{total_columns} ({consistency_rate:.1%})")
    
    return columns, consistency_rate

def compare_key_frames():
    """对比关键帧的区域6和区域16"""
    
    key_frames = ['frame_00018', 'frame_00028', 'frame_00034', 'frame_00060', 'frame_00230']
    
    for frame_name in key_frames:
        frame_path = f'output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json'
        
        try:
            print(f"\n{'='*80}")
            print(f"分析 {frame_name}")
            print(f"{'='*80}")
            
            # 分析区域6
            region6_cards = analyze_region_cards(frame_path, 6)
            if region6_cards:
                print(f"\n🔍 区域6 - 共{len(region6_cards)}张卡牌")
                columns6, consistency6 = analyze_column_consistency(region6_cards, 6)
            else:
                print(f"\n🔍 区域6 - 无卡牌")
                consistency6 = 1.0
            
            # 分析区域16
            region16_cards = analyze_region_cards(frame_path, 16)
            if region16_cards:
                print(f"\n🔍 区域16 - 共{len(region16_cards)}张卡牌")
                columns16, consistency16 = analyze_column_consistency(region16_cards, 16)
            else:
                print(f"\n🔍 区域16 - 无卡牌")
                consistency16 = 1.0
            
            # 对比总结
            print(f"\n📊 {frame_name} 对比总结:")
            print(f"  区域6一致性:  {consistency6:.1%}")
            print(f"  区域16一致性: {consistency16:.1%}")
            print(f"  差异: {'区域16有问题' if consistency16 < consistency6 else '两区域都正常' if consistency16 == consistency6 == 1.0 else '都有问题'}")
            
        except FileNotFoundError:
            print(f"文件不存在: {frame_path}")
        except Exception as e:
            print(f"分析 {frame_name} 时出错: {e}")

def analyze_label_grouping_logic():
    """分析标签分组逻辑的差异"""
    
    print(f"\n{'='*80}")
    print("标签分组逻辑分析")
    print(f"{'='*80}")
    
    # 模拟区域6的标签分组逻辑
    print("\n🔧 区域6的标签分组逻辑（从代码分析）:")
    print("1. 按标签分组，实现批量连续分配")
    print("2. 跳过已有ID的卡牌（继承来的）")
    print("3. 为每个标签的卡牌批量分配连续ID")
    print("4. 单张卡牌使用原有逻辑，多张相同标签卡牌批量连续分配")
    print("5. 应用空间排序规则：从下到上分配ID")
    
    # 分析区域16缺少的逻辑
    print("\n❌ 区域16缺少的关键逻辑:")
    print("1. 缺少按标签分组确保类别一致性的机制")
    print("2. 缺少标签标准化处理（陆/六混淆）")
    print("3. 缺少列一致性验证机制")
    print("4. 空间重新分配机制不完整")

def propose_solution():
    """提出解决方案"""
    
    print(f"\n{'='*80}")
    print("解决方案建议")
    print(f"{'='*80}")
    
    print("\n🎯 根本原因分析:")
    print("1. 区域16缺少区域6的按标签分组机制")
    print("2. 标签'陆'和'六'被视为不同类别，导致列混淆")
    print("3. 区域16的空间分配没有考虑类别一致性")
    print("4. 缺少列一致性验证和修复机制")
    
    print("\n🔧 解决方案（仅添加关键部分）:")
    print("1. 【优先级1】添加标签标准化：将'陆'统一为'六'")
    print("2. 【优先级2】复用区域6的按标签分组逻辑到区域16")
    print("3. 【优先级3】添加列一致性验证机制")
    print("4. 【优先级4】保持现有空间分配和排序规则不变")
    
    print("\n📝 具体实施步骤:")
    print("步骤1: 在basic_id_assigner.py中为区域16添加标签标准化")
    print("步骤2: 确保区域16也使用按标签分组的批量连续分配逻辑")
    print("步骤3: 添加列一致性检查和修复机制")
    print("步骤4: 测试关键帧确保不引入新错误")
    
    print("\n⚠️  注意事项:")
    print("1. 不修改现有的空间分配机制和排序规则")
    print("2. 不修改区域6的逻辑（已正常工作）")
    print("3. 确保frame_00018、frame_00028、frame_00034、frame_00060等关键帧不受影响")
    print("4. 只添加关键的标签分组和一致性检查逻辑")

if __name__ == "__main__":
    print("🔍 区域6 vs 区域16 处理逻辑对比分析")
    
    # 对比关键帧
    compare_key_frames()
    
    # 分析标签分组逻辑差异
    analyze_label_grouping_logic()
    
    # 提出解决方案
    propose_solution()

#!/usr/bin/env python3
"""
1五卡牌生命周期深度分析脚本

专门跟踪1五这张卡牌在frame_00033→frame_00034处理过程中的完整生命周期，
重点分析7→16流转失效的原因。

分析目标：
1. 确认偎牌继承正确性（拾、肆组合应保留）
2. 深度分析7→16流转失效原因
3. 跟踪1五在每个模块中的处理状态
4. 识别1五丢失或被覆盖的具体环节
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目核心模块
from calibration_gt_final_processor import CalibrationGTFinalProcessor, FinalProcessorConfig

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_1wu_lifecycle.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Card1WuLifecycleTracker:
    """1五卡牌生命周期跟踪器"""
    
    def __init__(self):
        self.config = FinalProcessorConfig(
            source_dir='legacy_assets/ceshi/calibration_gt',
            output_dir='output/debug_1wu_lifecycle'
        )
        
        # 创建自定义处理器，启用详细跟踪
        self.processor = CalibrationGTFinalProcessor(self.config)
        
        # 1五卡牌跟踪记录
        self.card_1wu_lifecycle = {
            "frame_33": {
                "initial_detection": None,
                "after_validation": None,
                "after_inheritance": None,
                "after_transition": None,
                "after_id_assignment": None,
                "final_state": None
            },
            "frame_34": {
                "initial_detection": None,
                "after_validation": None,
                "after_inheritance": None,
                "after_transition": None,
                "after_id_assignment": None,
                "final_state": None
            },
            "transition_analysis": {
                "region_7_cards_frame_33": [],
                "region_16_cards_frame_33": [],
                "region_7_cards_frame_34": [],
                "region_16_cards_frame_34": [],
                "transition_detected": False,
                "inheritance_success": False,
                "loss_point": None
            }
        }
        
    def track_card_1wu_lifecycle(self) -> Dict[str, Any]:
        """跟踪1五卡牌的完整生命周期"""
        logger.info("🔍 开始跟踪1五卡牌生命周期...")
        
        # 1. 分析frame_00033（前一帧）
        logger.info("📊 分析frame_00033（前一帧）...")
        frame_33_analysis = self._analyze_frame_with_tracking("frame_00033")
        
        # 2. 分析frame_00034（当前帧）
        logger.info("📊 分析frame_00034（当前帧）...")
        frame_34_analysis = self._analyze_frame_with_tracking("frame_00034")
        
        # 3. 深度分析流转过程
        logger.info("🔄 深度分析7→16流转过程...")
        transition_analysis = self._analyze_transition_process(frame_33_analysis, frame_34_analysis)
        
        # 4. 生成详细报告
        logger.info("📋 生成详细生命周期报告...")
        lifecycle_report = self._generate_lifecycle_report(frame_33_analysis, frame_34_analysis, transition_analysis)
        
        return lifecycle_report
    
    def _analyze_frame_with_tracking(self, frame_name: str) -> Dict[str, Any]:
        """分析单帧并跟踪1五卡牌状态"""
        frame_path = Path(self.config.source_dir) / "labels" / f"{frame_name}.json"
        
        if not frame_path.exists():
            logger.error(f"❌ 帧文件不存在: {frame_path}")
            return {"error": f"帧文件不存在: {frame_path}"}
        
        try:
            # 读取原始标注数据
            with open(frame_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 提取卡牌信息
            card_shapes = []
            for shape in original_data.get("shapes", []):
                if shape.get("label") in self.config.valid_card_labels:
                    card_shapes.append(shape)
            
            # 转换为检测格式
            all_detections = self.processor._convert_all_shapes_to_detections(original_data.get("shapes", []))
            
            # 跟踪1五在初始检测中的状态
            card_1wu_initial = self._find_card_1wu_in_detections(all_detections, "initial_detection")
            
            # 使用数字孪生系统处理（带跟踪）
            dt_result = self._process_frame_with_tracking(all_detections, frame_name)
            
            # 分析结果
            analysis = {
                "frame_name": frame_name,
                "original_shapes_count": len(original_data.get("shapes", [])),
                "card_shapes_count": len(card_shapes),
                "processing_success": dt_result.success if hasattr(dt_result, 'success') else False,
                "processed_cards": dt_result.processed_cards if hasattr(dt_result, 'processed_cards') else [],
                "region_7_cards": [],
                "region_16_cards": [],
                "card_1wu_tracking": self.card_1wu_lifecycle[f"frame_{frame_name.split('_')[-1]}"]
            }
            
            # 按区域分组卡牌
            for card in analysis["processed_cards"]:
                if card.get("group_id") == 7:
                    analysis["region_7_cards"].append(card)
                elif card.get("group_id") == 16:
                    analysis["region_16_cards"].append(card)
            
            # 记录区域卡牌到跟踪记录
            self.card_1wu_lifecycle["transition_analysis"][f"region_7_cards_{frame_name}"] = analysis["region_7_cards"]
            self.card_1wu_lifecycle["transition_analysis"][f"region_16_cards_{frame_name}"] = analysis["region_16_cards"]
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析帧 {frame_name} 失败: {e}")
            return {
                "frame_name": frame_name,
                "error": str(e),
                "processing_success": False
            }
    
    def _process_frame_with_tracking(self, detections: List[Dict[str, Any]], frame_name: str):
        """处理帧并跟踪1五卡牌在每个模块中的状态"""
        frame_key = f"frame_{frame_name.split('_')[-1]}"
        
        # 在每个关键处理点插入跟踪逻辑
        logger.info(f"🔍 开始跟踪 {frame_name} 中1五卡牌的处理过程...")
        
        # 跟踪初始检测状态
        self.card_1wu_lifecycle[frame_key]["initial_detection"] = self._find_card_1wu_in_detections(detections, "initial")
        
        # 调用原始处理器（这里需要修改为可跟踪的版本）
        result = self.processor.digital_twin_controller.process_frame(detections)
        
        # 跟踪最终状态
        if hasattr(result, 'processed_cards'):
            self.card_1wu_lifecycle[frame_key]["final_state"] = self._find_card_1wu_in_cards(result.processed_cards, "final")
        
        return result
    
    def _find_card_1wu_in_detections(self, detections: List[Dict[str, Any]], stage: str) -> Optional[Dict[str, Any]]:
        """在检测结果中查找1五卡牌"""
        for detection in detections:
            # 查找标签为"五"且在区域7的卡牌
            if detection.get("label") == "五" and detection.get("group_id") == 7:
                logger.info(f"🎯 在{stage}阶段找到五卡牌（区域7）: {detection}")
                return detection
            # 查找已有twin_id为"1五"的卡牌
            if detection.get("twin_id") == "1五":
                logger.info(f"🎯 在{stage}阶段找到1五卡牌: {detection}")
                return detection
        
        logger.warning(f"⚠️ 在{stage}阶段未找到1五卡牌")
        return None
    
    def _find_card_1wu_in_cards(self, cards: List[Dict[str, Any]], stage: str) -> Optional[Dict[str, Any]]:
        """在处理后的卡牌中查找1五"""
        for card in cards:
            # 查找twin_id为"1五"的卡牌
            if card.get("twin_id") == "1五":
                logger.info(f"🎯 在{stage}阶段找到1五卡牌: {card}")
                return card
            # 查找标签为"五"的卡牌
            if card.get("label") == "五" and card.get("group_id") in [7, 16]:
                logger.info(f"🎯 在{stage}阶段找到五卡牌: {card}")
                return card
        
        logger.warning(f"⚠️ 在{stage}阶段未找到1五卡牌")
        return None
    
    def _analyze_transition_process(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any]) -> Dict[str, Any]:
        """深度分析7→16流转过程"""
        transition_analysis = {
            "frame_33_region_7_wu_cards": [],
            "frame_34_region_16_wu_cards": [],
            "transition_detected": False,
            "inheritance_success": False,
            "flow_analysis": {
                "expected_flow": "区域7的1五 → 区域16的1五",
                "actual_flow": "未检测到",
                "flow_broken_at": None
            },
            "detailed_tracking": {
                "frame_33_1wu_state": None,
                "frame_34_1wu_state": None,
                "transition_success": False
            }
        }
        
        # 查找frame_33中区域7的五卡牌
        for card in frame_33.get("region_7_cards", []):
            if card.get("label") == "五" or card.get("twin_id") == "1五":
                transition_analysis["frame_33_region_7_wu_cards"].append(card)
                transition_analysis["detailed_tracking"]["frame_33_1wu_state"] = card
                logger.info(f"🔍 Frame_33区域7找到五卡牌: {card}")
        
        # 查找frame_34中区域16的五卡牌
        for card in frame_34.get("region_16_cards", []):
            if card.get("label") == "五" or card.get("twin_id") == "1五":
                transition_analysis["frame_34_region_16_wu_cards"].append(card)
                transition_analysis["detailed_tracking"]["frame_34_1wu_state"] = card
                logger.info(f"🔍 Frame_34区域16找到五卡牌: {card}")
        
        # 分析流转是否成功
        if (transition_analysis["frame_33_region_7_wu_cards"] and 
            transition_analysis["frame_34_region_16_wu_cards"]):
            
            frame_33_wu = transition_analysis["frame_33_region_7_wu_cards"][0]
            frame_34_wu = transition_analysis["frame_34_region_16_wu_cards"][0]
            
            # 检查ID是否保持一致
            if frame_33_wu.get("twin_id") == frame_34_wu.get("twin_id"):
                transition_analysis["transition_detected"] = True
                transition_analysis["inheritance_success"] = True
                transition_analysis["flow_analysis"]["actual_flow"] = f"成功: {frame_33_wu.get('twin_id')} (区域7→16)"
                logger.info("✅ 7→16流转成功，ID保持一致")
            else:
                transition_analysis["flow_analysis"]["actual_flow"] = f"ID不一致: {frame_33_wu.get('twin_id')} → {frame_34_wu.get('twin_id')}"
                transition_analysis["flow_analysis"]["flow_broken_at"] = "ID继承阶段"
                logger.warning("⚠️ 7→16流转检测到，但ID不一致")
        
        elif transition_analysis["frame_33_region_7_wu_cards"] and not transition_analysis["frame_34_region_16_wu_cards"]:
            transition_analysis["flow_analysis"]["actual_flow"] = "卡牌在区域16中丢失"
            transition_analysis["flow_analysis"]["flow_broken_at"] = "区域流转阶段"
            logger.error("❌ Frame_33有五卡牌，但Frame_34区域16中未找到")
        
        elif not transition_analysis["frame_33_region_7_wu_cards"]:
            transition_analysis["flow_analysis"]["actual_flow"] = "Frame_33区域7中未找到五卡牌"
            transition_analysis["flow_analysis"]["flow_broken_at"] = "初始检测阶段"
            logger.error("❌ Frame_33区域7中未找到五卡牌")
        
        return transition_analysis

    def _generate_lifecycle_report(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any],
                                  transition: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细的生命周期报告"""
        report = {
            "summary": {
                "analysis_target": "1五卡牌生命周期跟踪",
                "frame_33_processing": "成功" if frame_33.get("processing_success") else "失败",
                "frame_34_processing": "成功" if frame_34.get("processing_success") else "失败",
                "transition_success": transition.get("inheritance_success", False),
                "key_finding": None
            },
            "frame_33_analysis": {
                "region_7_cards_count": len(frame_33.get("region_7_cards", [])),
                "region_16_cards_count": len(frame_33.get("region_16_cards", [])),
                "wu_card_in_region_7": transition.get("frame_33_region_7_wu_cards", []),
                "wei_pai_in_region_16": [
                    card for card in frame_33.get("region_16_cards", [])
                    if card.get("twin_id") in ["3拾", "1拾暗", "2拾暗", "3肆", "1肆暗", "2肆暗"]
                ]
            },
            "frame_34_analysis": {
                "region_7_cards_count": len(frame_34.get("region_7_cards", [])),
                "region_16_cards_count": len(frame_34.get("region_16_cards", [])),
                "wu_card_in_region_16": transition.get("frame_34_region_16_wu_cards", []),
                "wei_pai_inheritance": [
                    card for card in frame_34.get("region_16_cards", [])
                    if card.get("twin_id") in ["3拾", "1拾暗", "2拾暗", "3肆", "1肆暗", "2肆暗"]
                ],
                "chi_pai_cards": [
                    card for card in frame_34.get("region_16_cards", [])
                    if card.get("twin_id") in ["1三", "3四", "1五"] or card.get("label") in ["三", "四", "五"]
                ]
            },
            "transition_analysis": transition,
            "root_cause_analysis": {
                "wei_pai_inheritance_status": "正确" if len([
                    card for card in frame_34.get("region_16_cards", [])
                    if card.get("twin_id") in ["3拾", "1拾暗", "2拾暗", "3肆", "1肆暗", "2肆暗"]
                ]) == 6 else "异常",
                "chi_pai_flow_status": "失败" if not transition.get("inheritance_success") else "成功",
                "specific_issues": [],
                "module_analysis": {
                    "simple_inheritor": "需要检查跨区域继承逻辑",
                    "region_transitioner": "流转标记成功但实际继承失败",
                    "basic_id_assigner": "可能覆盖了继承的ID",
                    "processing_order": "模块间协调可能存在问题"
                }
            },
            "detailed_findings": {
                "expected_final_state": {
                    "region_16_total_cards": 9,
                    "wei_pai_cards": ["3拾", "1拾暗", "2拾暗", "3肆", "1肆暗", "2肆暗"],
                    "chi_pai_cards": ["1三", "3四", "1五"],
                    "inheritance_requirement": "1五必须继承自frame_33区域7"
                },
                "actual_final_state": {
                    "region_16_total_cards": len(frame_34.get("region_16_cards", [])),
                    "wei_pai_cards": [
                        card.get("twin_id") for card in frame_34.get("region_16_cards", [])
                        if card.get("twin_id") in ["3拾", "1拾暗", "2拾暗", "3肆", "1肆暗", "2肆暗"]
                    ],
                    "chi_pai_cards": [
                        card.get("twin_id") for card in frame_34.get("region_16_cards", [])
                        if card.get("label") in ["三", "四", "五"]
                    ],
                    "missing_cards": [],
                    "unexpected_cards": []
                }
            },
            "solution_recommendations": []
        }

        # 分析具体问题
        expected_chi_pai = ["1三", "3四", "1五"]
        actual_chi_pai = report["detailed_findings"]["actual_final_state"]["chi_pai_cards"]

        missing_cards = [card for card in expected_chi_pai if card not in actual_chi_pai]
        unexpected_cards = [card for card in actual_chi_pai if card not in expected_chi_pai]

        report["detailed_findings"]["actual_final_state"]["missing_cards"] = missing_cards
        report["detailed_findings"]["actual_final_state"]["unexpected_cards"] = unexpected_cards

        # 确定关键发现
        if missing_cards:
            report["summary"]["key_finding"] = f"关键卡牌丢失: {missing_cards}"
            report["root_cause_analysis"]["specific_issues"].append(f"吃牌组合不完整，缺少: {missing_cards}")

        if not transition.get("inheritance_success"):
            report["root_cause_analysis"]["specific_issues"].append("7→16区域流转的继承机制失效")

        # 生成解决方案建议
        report["solution_recommendations"] = self._generate_solution_recommendations(report)

        return report

    def _generate_solution_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """基于分析结果生成针对性解决方案"""
        recommendations = []

        # 基于根本原因分析生成建议
        if "7→16区域流转的继承机制失效" in report["root_cause_analysis"]["specific_issues"]:
            recommendations.extend([
                "🔧 修复RegionTransitioner._handle_special_7_to_16_transition方法",
                "🔧 确保流转标记后的实际ID继承实现",
                "🔧 检查流转器与继承器的协调机制"
            ])

        if report["detailed_findings"]["actual_final_state"]["missing_cards"]:
            missing = report["detailed_findings"]["actual_final_state"]["missing_cards"]
            recommendations.extend([
                f"🔧 修复缺失卡牌的处理逻辑: {missing}",
                "🔧 检查BasicIDAssigner是否覆盖了继承的ID",
                "🔧 验证模块处理顺序，确保继承优先于新分配"
            ])

        # 基于模块分析生成具体建议
        recommendations.extend([
            "🔍 在RegionTransitioner中添加7→16流转的详细日志",
            "🔍 在SimpleInheritor中验证跨区域继承的实现",
            "🔍 检查Phase2Integrator中模块调用顺序",
            "🔍 添加ID生命周期跟踪机制，防止ID被意外覆盖"
        ])

        # 验证建议
        recommendations.extend([
            "✅ 确保修复后偎牌继承逻辑不受影响",
            "✅ 验证其他区域流转场景的正常工作",
            "✅ 添加单元测试覆盖7→16流转场景"
        ])

        return recommendations

def main():
    """主函数"""
    print("🔍 1五卡牌生命周期深度分析")
    print("=" * 60)
    print("分析目标：跟踪1五卡牌在frame_00033→frame_00034处理过程中的完整生命周期")
    print("重点关注：7→16流转失效原因，确保偎牌继承正确性")
    print("=" * 60)

    tracker = Card1WuLifecycleTracker()

    try:
        # 执行生命周期跟踪
        result = tracker.track_card_1wu_lifecycle()

        # 输出分析结果
        print("\n📊 生命周期分析结果:")
        print(f"   Frame_33处理: {result['summary']['frame_33_processing']}")
        print(f"   Frame_34处理: {result['summary']['frame_34_processing']}")
        print(f"   流转成功: {'✅ 是' if result['summary']['transition_success'] else '❌ 否'}")
        print(f"   关键发现: {result['summary']['key_finding']}")

        print("\n🎯 Frame_33分析:")
        frame_33 = result['frame_33_analysis']
        print(f"   区域7卡牌数: {frame_33['region_7_cards_count']}")
        print(f"   区域16卡牌数: {frame_33['region_16_cards_count']}")
        print(f"   区域7中的五卡牌: {len(frame_33['wu_card_in_region_7'])}张")
        if frame_33['wu_card_in_region_7']:
            for card in frame_33['wu_card_in_region_7']:
                print(f"     - {card.get('twin_id', 'No ID')} (标签: {card.get('label')})")
        print(f"   区域16偎牌: {len(frame_33['wei_pai_in_region_16'])}张")

        print("\n🎯 Frame_34分析:")
        frame_34 = result['frame_34_analysis']
        print(f"   区域7卡牌数: {frame_34['region_7_cards_count']}")
        print(f"   区域16卡牌数: {frame_34['region_16_cards_count']}")
        print(f"   区域16中的五卡牌: {len(frame_34['wu_card_in_region_16'])}张")
        if frame_34['wu_card_in_region_16']:
            for card in frame_34['wu_card_in_region_16']:
                print(f"     - {card.get('twin_id', 'No ID')} (标签: {card.get('label')})")
        print(f"   偎牌继承: {len(frame_34['wei_pai_inheritance'])}张")
        print(f"   吃牌卡牌: {len(frame_34['chi_pai_cards'])}张")

        print("\n🔄 流转分析:")
        transition = result['transition_analysis']
        print(f"   预期流转: {transition['flow_analysis']['expected_flow']}")
        print(f"   实际流转: {transition['flow_analysis']['actual_flow']}")
        if transition['flow_analysis']['flow_broken_at']:
            print(f"   流转中断点: {transition['flow_analysis']['flow_broken_at']}")

        print("\n🔍 根本原因分析:")
        root_cause = result['root_cause_analysis']
        print(f"   偎牌继承状态: {root_cause['wei_pai_inheritance_status']}")
        print(f"   吃牌流转状态: {root_cause['chi_pai_flow_status']}")
        if root_cause['specific_issues']:
            print("   具体问题:")
            for issue in root_cause['specific_issues']:
                print(f"     - {issue}")

        print("\n📋 详细发现:")
        findings = result['detailed_findings']
        print("   预期最终状态:")
        print(f"     区域16总卡牌: {findings['expected_final_state']['region_16_total_cards']}张")
        print(f"     偎牌: {findings['expected_final_state']['wei_pai_cards']}")
        print(f"     吃牌: {findings['expected_final_state']['chi_pai_cards']}")

        print("   实际最终状态:")
        print(f"     区域16总卡牌: {findings['actual_final_state']['region_16_total_cards']}张")
        print(f"     偎牌: {findings['actual_final_state']['wei_pai_cards']}")
        print(f"     吃牌: {findings['actual_final_state']['chi_pai_cards']}")

        if findings['actual_final_state']['missing_cards']:
            print(f"     ❌ 缺失卡牌: {findings['actual_final_state']['missing_cards']}")
        if findings['actual_final_state']['unexpected_cards']:
            print(f"     ⚠️ 意外卡牌: {findings['actual_final_state']['unexpected_cards']}")

        print("\n💡 解决方案建议:")
        for i, recommendation in enumerate(result['solution_recommendations'], 1):
            print(f"   {i}. {recommendation}")

        # 保存详细报告
        output_path = Path(tracker.config.output_dir) / "1wu_lifecycle_analysis_report.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n📁 详细报告已保存到: {output_path}")
        print(f"📁 详细日志已保存到: debug_1wu_lifecycle.log")

    except Exception as e:
        logger.exception("生命周期分析过程中发生错误")
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()

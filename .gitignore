# 模型文件
*.pt
*.pth
*.onnx
*.tflite
*.mlmodel
*.torchscript
*.engine
*.weights

# 数据和输出目录
/data/*
/output/*
/models/*
/runs/*
!data/.gitkeep
!output/.gitkeep
!models/.gitkeep

# 虚拟环境
/env/
/venv/
/.venv/
/ENV/
/.env/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/

# 编辑器和IDE文件
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# 日志文件
*.log
logs/
tensorboard/

# 临时文件
.ipynb_checkpoints/
tmp/
temp/

# 系统文件
.DS_Store
Thumbs.db

# 配置文件（可能包含敏感信息）
.env
.env.local
secrets.yaml 

# 测试文件
ceshi/

# 数据集文件
*.json
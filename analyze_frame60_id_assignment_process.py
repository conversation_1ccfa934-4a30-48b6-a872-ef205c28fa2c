#!/usr/bin/env python3
"""
深度分析frame_00060.jpg的ID分配过程

关键问题：
- 区域3的1二已经成功流转到区域16
- 但最终输出显示：1二、1二、1二、1二
- 期望输出应该是：1二、2二、3二、4二

分析重点：
1. 验证区域3→区域16的流转是否正确标记
2. 分析ID分配器处理区域16的4张"二"牌的具体过程
3. 确定是否需要硬分配还是可以通过逻辑优化解决
4. 对比其他区域的正确分配机制
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.core.digital_twin_controller import DigitalTwinController
    from src.modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
    from src.modules.spatial_sorter import SpatialSorter
    from src.modules.region_transitioner import RegionTransitioner
    from src.modules.simple_inheritor import SimpleInheritor
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_frame_data(frame_number: int) -> Optional[Dict[str, Any]]:
    """加载指定帧的标注数据"""
    frame_path = f"legacy_assets/ceshi/calibration_gt/labels/frame_{frame_number:05d}.json"
    
    if not os.path.exists(frame_path):
        logger.warning(f"帧文件不存在: {frame_path}")
        return None
        
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载帧{frame_number}失败: {e}")
        return None

def convert_to_detection_format(shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """将AnyLabeling格式转换为检测格式"""
    detections = []
    
    for shape in shapes:
        if shape.get('shape_type') == 'rectangle' and 'points' in shape:
            points = shape['points']
            if len(points) >= 4:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                    'confidence': shape.get('score', 1.0),
                    'group_id': shape.get('group_id', 1),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', '')
                }
                detections.append(detection)
    
    return detections

def analyze_id_assignment_step_by_step():
    """逐步分析ID分配过程"""
    print("🔍 逐步分析frame_00060的ID分配过程")
    print("="*60)
    
    # 创建控制器和各个模块
    controller = DigitalTwinController()
    
    # 处理frame_00059建立前置状态
    print("步骤1: 处理frame_00059建立前置状态")
    print("-" * 40)
    
    frame59_data = load_frame_data(59)
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    
    # 提取区域3的卡牌
    region3_frame59 = [d for d in frame59_detections if d.get('group_id') == 3]
    print(f"frame_00059区域3: {len(region3_frame59)}张卡牌")
    for card in region3_frame59:
        print(f"  {card.get('label')} - {card.get('region_name')}")
    
    # 处理frame_00059
    result59 = controller.process_frame(frame59_detections)
    
    # 提取区域3的处理结果
    region3_processed = []
    if hasattr(result59, 'processed_cards'):
        for card in result59.processed_cards:
            if isinstance(card, dict):
                group_id = card.get('group_id')
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
            else:
                group_id = getattr(card, 'group_id', None)
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
            
            if group_id == 3:
                region3_processed.append({
                    'label': label,
                    'twin_id': twin_id,
                    'group_id': group_id
                })
    
    print(f"✅ frame_00059处理完成，区域3结果:")
    for card in region3_processed:
        print(f"  {card['label']} -> {card['twin_id']}")
    
    print()
    
    # 分析frame_00060的处理过程
    print("步骤2: 分析frame_00060的详细处理过程")
    print("-" * 40)
    
    frame60_data = load_frame_data(60)
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    # 提取区域16的卡牌
    region16_frame60 = [d for d in frame60_detections if d.get('group_id') == 16]
    print(f"frame_00060区域16: {len(region16_frame60)}张卡牌")
    
    # 按bottom_y排序显示
    sorted_cards = sorted(region16_frame60, key=lambda c: -c.get('bbox', [0, 0, 0, 0])[3])
    for i, card in enumerate(sorted_cards):
        bbox = card.get('bbox', [0, 0, 0, 0])
        print(f"  位置{i+1}: {card.get('label')} - bottom_y: {bbox[3]:.1f}")
    
    print()
    
    # 手动模拟各个处理阶段
    print("步骤3: 手动模拟各个处理阶段")
    print("-" * 40)
    
    # 3.1 继承器处理
    print("3.1 继承器处理阶段")
    inheritor = SimpleInheritor()
    inheritance_result = inheritor.process_inheritance(frame60_detections)
    
    # 检查区域16的继承结果
    region16_inherited = []
    region16_new = []
    
    if hasattr(inheritance_result, 'inherited_cards'):
        region16_inherited = [card for card in inheritance_result.inherited_cards if card.get('group_id') == 16]
    
    if hasattr(inheritance_result, 'new_cards'):
        region16_new = [card for card in inheritance_result.new_cards if card.get('group_id') == 16]
    
    print(f"  区域16继承卡牌: {len(region16_inherited)}张")
    print(f"  区域16新卡牌: {len(region16_new)}张")
    
    # 3.2 流转器处理
    print("\n3.2 流转器处理阶段")
    transitioner = RegionTransitioner()
    transition_result = transitioner.process_transitions(frame60_detections, frame59_detections)
    
    # 检查区域16的流转结果
    region16_transitioned = []
    region16_new_after_transition = []
    
    if hasattr(transition_result, 'transitioned_cards'):
        region16_transitioned = [card for card in transition_result.transitioned_cards if card.get('group_id') == 16]
    
    if hasattr(transition_result, 'new_cards'):
        region16_new_after_transition = [card for card in transition_result.new_cards if card.get('group_id') == 16]
    
    print(f"  区域16流转卡牌: {len(region16_transitioned)}张")
    for card in region16_transitioned:
        transition_source = card.get('transition_source', '未知')
        source_id = card.get('source_card_id', '未知')
        release_flag = card.get('release_source_id', False)
        print(f"    {card.get('label')} - 来源: {transition_source}, 源ID: {source_id}, 释放标记: {release_flag}")
    
    print(f"  区域16新卡牌: {len(region16_new_after_transition)}张")
    
    # 3.3 ID分配器处理
    print("\n3.3 ID分配器处理阶段")
    global_manager = GlobalIDManager()
    id_assigner = BasicIDAssigner(global_manager)
    
    # 准备需要分配ID的卡牌（流转卡牌+新卡牌）
    cards_for_id_assignment = region16_transitioned + region16_new_after_transition
    
    print(f"  准备分配ID的区域16卡牌: {len(cards_for_id_assignment)}张")
    
    # 检查每张卡牌的状态
    for i, card in enumerate(cards_for_id_assignment):
        print(f"  卡牌{i+1}:")
        print(f"    标签: {card.get('label')}")
        print(f"    流转来源: {card.get('transition_source', '无')}")
        print(f"    源ID: {card.get('source_card_id', '无')}")
        print(f"    释放标记: {card.get('release_source_id', False)}")
        print(f"    基础ID: {card.get('base_id', '无')}")
        print(f"    当前twin_id: {card.get('twin_id', '无')}")
    
    # 模拟ID分配过程
    print("\n  模拟ID分配过程:")
    
    # 检查全局ID管理器状态
    global_manager = id_assigner.global_id_manager
    print(f"  全局ID管理器当前状态:")
    if hasattr(global_manager, 'id_counters'):
        er_counter = global_manager.id_counters.get('二', 0)
        print(f"    '二'的计数器: {er_counter}")
    
    if hasattr(global_manager, 'used_ids'):
        used_ids = global_manager.used_ids
        er_ids = [id for id in used_ids if '二' in str(id)]
        print(f"    已使用的'二'相关ID: {er_ids}")
    
    # 执行ID分配
    assignment_result = id_assigner.assign_ids(cards_for_id_assignment)
    
    print(f"\n  ID分配结果:")
    if hasattr(assignment_result, 'assigned_cards'):
        region16_assigned = [card for card in assignment_result.assigned_cards if card.get('group_id') == 16]
        
        # 按bottom_y排序
        region16_assigned.sort(key=lambda c: -c.get('bbox', [0, 0, 0, 0])[3])
        
        for i, card in enumerate(region16_assigned):
            twin_id = card.get('twin_id') or card.get('digital_twin_id')
            bbox = card.get('bbox', [0, 0, 0, 0])
            print(f"    位置{i+1}: {card.get('label')} -> {twin_id} (bottom_y: {bbox[3]:.1f})")
    
    return {
        'region16_transitioned': region16_transitioned,
        'region16_assigned': region16_assigned if 'region16_assigned' in locals() else []
    }

def analyze_other_regions_comparison():
    """分析其他区域的正确分配机制作为对比"""
    print("\n🔍 分析其他区域的正确分配机制")
    print("="*50)
    
    # 加载frame_00034作为对比
    frame34_data = load_frame_data(34)
    if not frame34_data:
        print("❌ 无法加载frame_00034数据")
        return
    
    frame34_detections = convert_to_detection_format(frame34_data.get('shapes', []))
    
    # 查找有多张相同标签卡牌的区域
    region_card_counts = {}
    for detection in frame34_detections:
        region_id = detection.get('group_id')
        label = detection.get('label')
        key = f"区域{region_id}_{label}"
        region_card_counts[key] = region_card_counts.get(key, 0) + 1
    
    # 找出有多张相同标签的区域
    multi_card_regions = {k: v for k, v in region_card_counts.items() if v > 1}
    
    print("frame_00034中有多张相同标签的区域:")
    for key, count in multi_card_regions.items():
        print(f"  {key}: {count}张")
    
    if multi_card_regions:
        # 选择一个区域进行详细分析
        first_key = list(multi_card_regions.keys())[0]
        region_id = int(first_key.split('_')[0].replace('区域', ''))
        label = first_key.split('_')[1]
        
        print(f"\n详细分析{first_key}的ID分配:")
        
        # 处理frame_00034
        controller = DigitalTwinController()
        result34 = controller.process_frame(frame34_detections)
        
        # 提取该区域的处理结果
        target_region_cards = []
        if hasattr(result34, 'processed_cards'):
            for card in result34.processed_cards:
                if isinstance(card, dict):
                    card_region = card.get('group_id')
                    card_label = card.get('label')
                    twin_id = card.get('twin_id') or card.get('digital_twin_id')
                else:
                    card_region = getattr(card, 'group_id', None)
                    card_label = getattr(card, 'label', None)
                    twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                
                if card_region == region_id and card_label == label:
                    target_region_cards.append({
                        'label': card_label,
                        'twin_id': twin_id,
                        'group_id': card_region
                    })
        
        print(f"  处理结果:")
        for i, card in enumerate(target_region_cards):
            print(f"    卡牌{i+1}: {card['label']} -> {card['twin_id']}")
        
        # 分析ID分配模式
        twin_ids = [card['twin_id'] for card in target_region_cards if card['twin_id']]
        if twin_ids:
            print(f"  ID分配模式: {twin_ids}")
            
            # 检查是否是连续分配
            if all(f"{i+1}{label}" in twin_ids for i in range(len(twin_ids))):
                print("  ✅ 连续分配模式: 正确")
            else:
                print("  ❌ 非连续分配模式")

def analyze_root_cause():
    """分析根本原因"""
    print("\n💡 根本原因分析")
    print("="*50)
    
    print("基于以上分析，问题可能出现在以下几个方面:")
    print()
    
    print("1. **ID分配器的批量处理逻辑**")
    print("   - 当前可能是逐张卡牌独立分配ID")
    print("   - 没有考虑同一区域相同标签的批量连续分配")
    print("   - 每张卡牌都从'1二'开始分配")
    print()
    
    print("2. **流转标记的处理逻辑**")
    print("   - 区域3→区域16的流转标记正确")
    print("   - 但ID分配器可能没有正确处理流转卡牌的特殊逻辑")
    print("   - 所有4张卡牌都被当作独立的新卡牌处理")
    print()
    
    print("3. **空间排序与ID分配的协调**")
    print("   - 空间排序正确（从下到上）")
    print("   - 但ID分配没有按照排序后的顺序进行连续分配")
    print("   - 可能存在排序后ID分配的时序问题")
    print()
    
    print("4. **全局ID管理器的状态管理**")
    print("   - ID释放机制工作正常")
    print("   - 但可能在批量分配时状态更新有问题")
    print("   - 导致每次分配都认为'1二'可用")

def suggest_solutions():
    """建议解决方案"""
    print("\n🎯 解决方案建议")
    print("="*50)
    
    print("基于分析，建议以下解决方案（按优先级排序）:")
    print()
    
    print("**方案1: 优化ID分配器的批量处理逻辑（推荐）**")
    print("- 不是硬分配，而是逻辑优化")
    print("- 在assign_ids方法中，对同一区域相同标签的卡牌进行批量连续分配")
    print("- 确保按空间排序后的顺序分配连续ID")
    print("- 优点: 通用性好，不影响其他区域")
    print()
    
    print("**方案2: 增强流转卡牌的特殊处理**")
    print("- 为流转卡牌添加特殊的ID分配逻辑")
    print("- 区域3→区域16的第一张卡牌继承'1二'")
    print("- 其余3张卡牌按顺序分配'2二、3二、4二'")
    print("- 优点: 针对性强，符合流转逻辑")
    print()
    
    print("**方案3: 修复ID分配的时序问题**")
    print("- 确保空间排序完成后再进行ID分配")
    print("- 避免并发或异步处理导致的状态不一致")
    print("- 优点: 解决根本的时序问题")
    print()
    
    print("**不推荐方案: 硬分配**")
    print("- 专门为frame_00060或区域16写死分配逻辑")
    print("- 缺点: 不通用，维护困难，破坏系统一致性")

def main():
    """主函数"""
    print("🔍 frame_00060.jpg ID分配过程深度分析")
    print("="*70)
    print("分析目标: 理解为什么输出'1二 1二 1二 1二'而不是'1二 2二 3二 4二'")
    print()
    
    # 执行分析
    result = analyze_id_assignment_step_by_step()
    
    # 对比分析
    analyze_other_regions_comparison()
    
    # 根本原因分析
    analyze_root_cause()
    
    # 解决方案建议
    suggest_solutions()
    
    print("\n" + "="*70)
    print("📋 分析总结")
    print("="*70)
    print("✅ 区域3→区域16的流转机制工作正常")
    print("✅ 空间排序逻辑正确")
    print("✅ ID释放机制正常")
    print("❌ ID分配器的批量处理逻辑存在问题")
    print()
    print("🎯 推荐解决方案: 优化ID分配器的批量处理逻辑")
    print("   - 不需要硬分配")
    print("   - 通过逻辑优化实现连续ID分配")
    print("   - 保持系统的通用性和一致性")

if __name__ == "__main__":
    main()

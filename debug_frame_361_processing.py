#!/usr/bin/env python3
"""
调试frame_00361的处理过程，特别是区域6的跨区域继承逻辑
"""

import json
import os
from typing import Dict, List, Any

def analyze_frame_360_361_detailed():
    """详细分析frame_00360到frame_00361的处理过程"""
    print("🔍 详细分析frame_00360→frame_00361的处理过程")
    print("=" * 60)
    
    # 加载两帧数据
    frame_360_path = "output/calibration_gt_final_with_digital_twin/labels/frame_00360.json"
    frame_361_path = "output/calibration_gt_final_with_digital_twin/labels/frame_00361.json"
    
    try:
        with open(frame_360_path, 'r', encoding='utf-8') as f:
            frame_360_data = json.load(f)
        with open(frame_361_path, 'r', encoding='utf-8') as f:
            frame_361_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return
    
    print("\n📊 Frame_00360详细分析")
    print("-" * 40)
    
    # 分析frame_00360的所有区域
    regions_360 = {}
    for shape in frame_360_data.get("shapes", []):
        region_id = shape.get("group_id")
        label = shape.get("label", "")
        twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
        
        if region_id not in regions_360:
            regions_360[region_id] = []
        regions_360[region_id].append({
            "label": label,
            "twin_id": twin_id,
            "bbox": shape.get("points", [])
        })
    
    # 重点关注区域1和区域3
    print("🎯 重点区域分析:")
    for region_id in [1, 3, 6]:
        if region_id in regions_360:
            cards = regions_360[region_id]
            print(f"  区域{region_id}: {len(cards)}张卡牌")
            for i, card in enumerate(cards):
                print(f"    [{i+1}] 标签: {card['label']}, ID: {card['twin_id']}")
                if "柒" in card['label'] or "贰" in card['label'] or "拾" in card['label']:
                    print(f"        ⭐ 关键卡牌！")
        else:
            print(f"  区域{region_id}: 无卡牌")
    
    print("\n📊 Frame_00361详细分析")
    print("-" * 40)
    
    # 分析frame_00361的所有区域
    regions_361 = {}
    for shape in frame_361_data.get("shapes", []):
        region_id = shape.get("group_id")
        label = shape.get("label", "")
        twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
        
        if region_id not in regions_361:
            regions_361[region_id] = []
        regions_361[region_id].append({
            "label": label,
            "twin_id": twin_id,
            "bbox": shape.get("points", [])
        })
    
    # 重点关注区域6
    print("🎯 重点区域分析:")
    for region_id in [1, 3, 6]:
        if region_id in regions_361:
            cards = regions_361[region_id]
            print(f"  区域{region_id}: {len(cards)}张卡牌")
            for i, card in enumerate(cards):
                print(f"    [{i+1}] 标签: {card['label']}, ID: {card['twin_id']}")
                if "柒" in card['label'] or "贰" in card['label'] or "拾" in card['label']:
                    print(f"        ⭐ 关键卡牌！")
        else:
            print(f"  区域{region_id}: 无卡牌")
    
    print("\n🔍 继承逻辑分析")
    print("-" * 40)
    
    # 检查frame_00360中的源数据
    region_3_qi = None
    region_1_er = None
    region_1_shi = None
    
    if 3 in regions_360:
        for card in regions_360[3]:
            if "柒" in card['label'] and card['twin_id'] == "2柒":
                region_3_qi = card
                print(f"✅ 找到源数据: 区域3中的{card['label']} (ID: {card['twin_id']})")
    
    if 1 in regions_360:
        for card in regions_360[1]:
            if card['twin_id'] == "1贰":
                region_1_er = card
                print(f"✅ 找到源数据: 区域1中的{card['label']} (ID: {card['twin_id']})")
            elif card['twin_id'] == "1拾":
                region_1_shi = card
                print(f"✅ 找到源数据: 区域1中的{card['label']} (ID: {card['twin_id']})")
    
    # 检查frame_00361中的目标数据
    region_6_qi = None
    region_6_er = None
    region_6_shi = None
    
    if 6 in regions_361:
        for card in regions_361[6]:
            if "柒" in card['label']:
                region_6_qi = card
                print(f"🎯 找到目标数据: 区域6中的{card['label']} (ID: {card['twin_id']})")
            elif card['twin_id'] == "1贰":
                region_6_er = card
                print(f"🎯 找到目标数据: 区域6中的{card['label']} (ID: {card['twin_id']})")
            elif card['twin_id'] == "1拾":
                region_6_shi = card
                print(f"🎯 找到目标数据: 区域6中的{card['label']} (ID: {card['twin_id']})")
    
    print("\n💡 继承分析结果")
    print("-" * 40)
    
    # 分析继承结果
    if region_3_qi and region_6_qi:
        if region_6_qi['twin_id'] == "2柒":
            print("✅ 柒类卡牌继承正确: 3→6 (2柒)")
        else:
            print(f"❌ 柒类卡牌继承错误: 期望2柒，实际{region_6_qi['twin_id']}")
            print(f"   源: 区域3的{region_3_qi['label']} (ID: {region_3_qi['twin_id']})")
            print(f"   目标: 区域6的{region_6_qi['label']} (ID: {region_6_qi['twin_id']})")
    else:
        print("❌ 柒类卡牌数据不完整")
        if not region_3_qi:
            print("   缺少源数据: 区域3中的2柒")
        if not region_6_qi:
            print("   缺少目标数据: 区域6中的柒类卡牌")
    
    if region_1_er and region_6_er:
        print("✅ 贰类卡牌继承正确: 1→6 (1贰)")
    else:
        print("❌ 贰类卡牌继承有问题")
    
    if region_1_shi and region_6_shi:
        print("✅ 拾类卡牌继承正确: 1→6 (1拾)")
    else:
        print("❌ 拾类卡牌继承有问题")
    
    print("\n🔧 可能的问题原因")
    print("-" * 40)
    
    if region_6_qi and region_6_qi['twin_id'] != "2柒":
        print("1. 跨区域继承逻辑可能没有正确执行")
        print("2. 可能存在其他继承路径覆盖了跨区域继承结果")
        print("3. 标签匹配可能有问题（'柒' vs '1柒'）")
        print("4. 优先级顺序可能不正确")
        
        # 检查是否有其他区域的柒类卡牌
        print("\n🔍 检查其他区域的柒类卡牌:")
        for region_id, cards in regions_360.items():
            for card in cards:
                if "柒" in card['label']:
                    print(f"   区域{region_id}: {card['label']} (ID: {card['twin_id']})")
    
    print("\n📋 建议的修复方向")
    print("-" * 40)
    print("1. 检查标签匹配逻辑：确保'柒'能匹配到'2柒'")
    print("2. 验证跨区域继承的优先级顺序")
    print("3. 确认没有其他逻辑覆盖跨区域继承结果")
    print("4. 检查previous_frame_mapping中的数据结构")

if __name__ == "__main__":
    analyze_frame_360_361_detailed()

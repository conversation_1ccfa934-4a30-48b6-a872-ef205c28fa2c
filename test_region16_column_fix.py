#!/usr/bin/env python3
"""
测试区域16列一致性修复功能
验证修复前后的效果对比
"""

import json
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager
from collections import defaultdict

def load_frame_data(frame_name):
    """加载指定帧的数据"""
    frame_path = f'output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json'
    
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return None

def extract_region16_cards(frame_data):
    """提取区域16的卡牌"""
    if not frame_data:
        return []
    
    region16_cards = []
    for shape in frame_data.get('shapes', []):
        if shape.get('group_id') == 16:
            # 移除已有的twin_id，模拟需要重新分配的情况
            card = shape.copy()
            if 'attributes' in card:
                card['attributes'] = card['attributes'].copy()
                if 'digital_twin_id' in card['attributes']:
                    del card['attributes']['digital_twin_id']
            if 'twin_id' in card:
                del card['twin_id']
            
            region16_cards.append(card)
    
    return region16_cards

def analyze_column_consistency(cards, title):
    """分析列一致性"""
    print(f"\n🔍 {title}")
    print("-" * 50)
    
    if not cards:
        print("  无卡牌")
        return {}
    
    # 按X坐标分列
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in cards:
        points = card.get('points', [])
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            x_center = sum(x_coords) / len(x_coords)
            
            # 寻找合适的列
            assigned = False
            for x_key in columns.keys():
                if abs(x_center - x_key) <= tolerance:
                    columns[x_key].append(card)
                    assigned = True
                    break
            
            if not assigned:
                columns[x_center].append(card)
    
    print(f"  检测到 {len(columns)} 列:")
    
    consistent_columns = 0
    total_columns = len(columns)
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        # 按Y坐标排序（从下到上）
        column_cards.sort(key=lambda c: -max([p[1] for p in c.get('points', [])]) if c.get('points') else 0)
        
        labels_in_column = [card.get('label', '') for card in column_cards]
        
        # 提取基础标签
        base_labels = []
        for label in labels_in_column:
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        
        # 判断一致性
        is_consistent = len(unique_base_labels) == 1
        if not is_consistent:
            # 检查是否为合法组合
            if unique_base_labels == {'陆', '六'}:
                is_consistent = True  # 陆六组合是合法的
        
        if is_consistent:
            consistent_columns += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"    列{i+1} (X≈{x_key:.1f}): {len(column_cards)}张 {status}")
        print(f"      标签: {labels_in_column}")
        print(f"      基础标签: {list(unique_base_labels)}")
    
    consistency_rate = consistent_columns / total_columns if total_columns > 0 else 0
    print(f"\n  列一致性: {consistent_columns}/{total_columns} ({consistency_rate:.1%})")
    
    return {
        'total_columns': total_columns,
        'consistent_columns': consistent_columns,
        'consistency_rate': consistency_rate
    }

def test_column_fix_for_frame(frame_name):
    """测试指定帧的列一致性修复"""
    print(f"\n{'='*80}")
    print(f"测试 {frame_name} 的区域16列一致性修复")
    print(f"{'='*80}")
    
    # 加载原始数据
    frame_data = load_frame_data(frame_name)
    if not frame_data:
        return False
    
    # 提取区域16卡牌
    original_cards = extract_region16_cards(frame_data)
    if not original_cards:
        print(f"  {frame_name} 无区域16卡牌")
        return True
    
    print(f"📋 {frame_name} 基本信息:")
    print(f"  区域16卡牌数: {len(original_cards)}")
    
    # 分析修复前的状态
    original_analysis = analyze_column_consistency(original_cards, "修复前状态")
    
    # 创建ID分配器并应用修复
    global_id_manager = GlobalIDManager()
    id_assigner = BasicIDAssigner(global_id_manager)
    
    # 应用列一致性修复
    try:
        fixed_cards = id_assigner._apply_column_consistency_fix(original_cards, 16)
        
        # 分析修复后的状态
        fixed_analysis = analyze_column_consistency(fixed_cards, "修复后状态")
        
        # 对比结果
        print(f"\n📊 修复效果对比:")
        print(f"  修复前一致性: {original_analysis['consistency_rate']:.1%}")
        print(f"  修复后一致性: {fixed_analysis['consistency_rate']:.1%}")
        
        improvement = fixed_analysis['consistency_rate'] - original_analysis['consistency_rate']
        if improvement > 0:
            print(f"  ✅ 改善: +{improvement:.1%}")
        elif improvement == 0:
            print(f"  ➖ 无变化: {improvement:.1%}")
        else:
            print(f"  ❌ 退化: {improvement:.1%}")
        
        return fixed_analysis['consistency_rate'] >= original_analysis['consistency_rate']
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_key_frames():
    """测试关键帧"""
    print("🧪 区域16列一致性修复测试")
    print("="*80)
    
    # 测试关键帧
    key_frames = ['frame_00018', 'frame_00028', 'frame_00034', 'frame_00060']
    problem_frame = 'frame_00230'
    
    all_frames = key_frames + [problem_frame]
    
    results = {}
    
    for frame_name in all_frames:
        success = test_column_fix_for_frame(frame_name)
        results[frame_name] = success
    
    # 总结结果
    print(f"\n{'='*80}")
    print(f"测试结果总结")
    print(f"{'='*80}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n📊 总体结果: {success_count}/{total_count} 帧测试通过")
    
    for frame_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        frame_type = "关键帧" if frame_name in key_frames else "问题帧"
        print(f"  {frame_name} ({frame_type}): {status}")
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过！修复功能工作正常")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")
    
    return success_count == total_count

if __name__ == "__main__":
    success = test_key_frames()
    sys.exit(0 if success else 1)

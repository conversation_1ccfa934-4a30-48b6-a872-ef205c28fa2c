#!/usr/bin/env python3
"""
检查最终输出的frame_00124.json是否有重复ID问题
"""

import json
from pathlib import Path
from collections import defaultdict

def check_frame_124_output():
    """检查frame_00124输出文件"""
    print("🔍 检查最终输出的frame_00124.json")
    print("=" * 60)
    
    # 读取输出文件
    output_file = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00124.json")
    
    if not output_file.exists():
        print("❌ 输出文件不存在")
        return False
    
    with open(output_file, 'r', encoding='utf-8') as f:
        output_data = json.load(f)
    
    print(f"📊 Frame_00124最终输出分析:")
    print(f"总卡牌数: {len(output_data.get('shapes', []))}")
    
    # 按区域分组
    regions = defaultdict(list)
    all_ids = []
    
    for shape in output_data.get('shapes', []):
        group_id = shape.get('group_id')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', 'N/A')
        label = shape.get('label', 'N/A')
        
        regions[group_id].append({
            'twin_id': twin_id,
            'label': label,
            'shape': shape
        })
        
        if twin_id != 'N/A':
            all_ids.append(twin_id)
    
    print(f"\n📈 各区域分布:")
    for region_id in sorted(regions.keys()):
        cards = regions[region_id]
        print(f"  区域{region_id}: {len(cards)}张卡牌")
        for card in cards[:5]:  # 只显示前5张
            print(f"    - {card['twin_id']} (标签: {card['label']})")
        if len(cards) > 5:
            print(f"    ... 还有{len(cards)-5}张")
    
    # 检查重复ID
    print(f"\n🔍 重复ID检查:")
    id_counts = defaultdict(int)
    for twin_id in all_ids:
        id_counts[twin_id] += 1
    
    duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
    
    if duplicate_ids:
        print(f"❌ 发现重复ID:")
        for twin_id, count in duplicate_ids.items():
            print(f"  - {twin_id}: 出现{count}次")
            
            # 找出重复ID的具体位置
            duplicate_cards = []
            for region_id, cards in regions.items():
                for card in cards:
                    if card['twin_id'] == twin_id:
                        duplicate_cards.append((region_id, card))
            
            print(f"    位置:")
            for region_id, card in duplicate_cards:
                print(f"      区域{region_id}: {card['label']}")
        
        return False
    else:
        print(f"✅ 无重复ID")
        
        # 特别检查区域6的"八"
        region_6_cards = regions.get(6, [])
        ba_cards_in_6 = [card for card in region_6_cards if '八' in card['label']]
        
        print(f"\n🎯 区域6的'八'检查:")
        print(f"  区域6总卡牌: {len(region_6_cards)}张")
        print(f"  区域6的'八': {len(ba_cards_in_6)}张")
        for card in ba_cards_in_6:
            print(f"    - {card['twin_id']} (标签: {card['label']})")
        
        return True

def main():
    """主函数"""
    print("🚀 Frame_00124最终输出检查")
    print("🎯 目标: 确认frame_00124.json是否修复了重复ID问题")
    print("=" * 80)
    
    # 检查输出
    is_correct = check_frame_124_output()
    
    print(f"\n📋 检查结果")
    print("=" * 60)
    print(f"当前版本: final_fix_all_frames (基于62a18a9)")
    print(f"Frame_00124状态: {'✅ 正确' if is_correct else '❌ 错误'}")
    
    if is_correct:
        print(f"\n🎉 Frame_00124修复成功！")
        print(f"✅ 无重复ID问题")
        print(f"✅ 数字孪生ID分配正确")
    else:
        print(f"\n⚠️ Frame_00124仍有问题")
        print(f"❌ 存在重复ID")
    
    return is_correct

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Frame_00124验证通过！")

#!/usr/bin/env python3
"""
测试frame_00360到frame_00361的继承问题
验证2柒是否正确从区域3继承到区域6
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(frame_data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    return [card for card in frame_data['shapes'] if card.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取数字孪生ID"""
    # 尝试多个可能的字段
    twin_id = card.get('twin_id')
    if twin_id:
        return twin_id
    
    attributes = card.get('attributes', {})
    twin_id = attributes.get('digital_twin_id')
    if twin_id:
        return twin_id
    
    # 如果都没有，返回label作为fallback
    return card.get('label', 'None')

def test_frame_360_361_inheritance():
    """测试frame_00360到frame_00361的继承问题"""
    print("🧪 测试frame_00360到frame_00361的继承问题")
    print("="*80)
    
    # 加载数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载帧数据")
        return False
    
    # 分析frame_00360区域3
    region_3_cards_360 = extract_region_cards(frame_360_data, 3)
    print(f"\n📋 Frame_00360区域3（观战抓牌区）: {len(region_3_cards_360)}张")
    
    qi_2_in_360 = None
    for card in region_3_cards_360:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
        if twin_id == '2柒':
            qi_2_in_360 = card
    
    if not qi_2_in_360:
        print("❌ Frame_00360区域3中未找到2柒")
        return False
    
    print(f"✅ Frame_00360区域3中发现2柒: ID={get_digital_twin_id(qi_2_in_360)}")
    
    # 分析frame_00361区域6
    region_6_cards_361 = extract_region_cards(frame_361_data, 6)
    print(f"\n📋 Frame_00361区域6（观战吃碰区）: {len(region_6_cards_361)}张")
    
    # 按Y坐标排序（从下到上）
    region_6_cards_361.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    qi_cards_in_361 = []
    for i, card in enumerate(region_6_cards_361):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        y_pos = card.get('points', [[0,0]])[0][1]
        print(f"  位置{i+1}: 标签: {label}, ID: {twin_id}, Y坐标: {y_pos:.1f}")
        
        if '柒' in label:
            qi_cards_in_361.append(card)
    
    # 验证继承结果
    print(f"\n🎯 验证2柒的继承")
    print(f"📋 区域6中的柒类卡牌: {len(qi_cards_in_361)}张")
    
    expected_qi_found = False
    for card in qi_cards_in_361:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
        
        if twin_id == '2柒':
            expected_qi_found = True
            print(f"✅ 找到期望的2柒继承: ID={twin_id}")
    
    if not expected_qi_found:
        print(f"❌ 继承失败: 期望在区域6中找到ID=2柒，但未找到")
        print(f"💡 实际情况: 区域6中有{len(qi_cards_in_361)}张柒类卡牌，但都不是2柒")
        return False
    
    # 验证1贰和1拾的继承
    print(f"\n🎯 验证1贰和1拾的继承")
    
    region_1_cards_360 = extract_region_cards(frame_360_data, 1)
    er_1_in_360 = None
    shi_1_in_360 = None
    
    for card in region_1_cards_360:
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            er_1_in_360 = card
        elif twin_id == '1拾':
            shi_1_in_360 = card
    
    er_1_in_361 = None
    shi_1_in_361 = None
    
    for card in region_6_cards_361:
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            er_1_in_361 = card
        elif twin_id == '1拾':
            shi_1_in_361 = card
    
    success_count = 0
    
    if er_1_in_360 and er_1_in_361:
        print(f"✅ 1贰继承成功: 区域1→区域6")
        success_count += 1
    else:
        print(f"❌ 1贰继承失败")
    
    if shi_1_in_360 and shi_1_in_361:
        print(f"✅ 1拾继承成功: 区域1→区域6")
        success_count += 1
    else:
        print(f"❌ 1拾继承失败")
    
    # 总结
    print(f"\n📊 测试结果总结")
    print("="*80)
    
    if expected_qi_found and success_count == 2:
        print("✅ 所有继承测试通过")
        return True
    else:
        print("❌ 继承测试失败")
        print(f"  - 2柒继承: {'✅' if expected_qi_found else '❌'}")
        print(f"  - 1贰继承: {'✅' if er_1_in_360 and er_1_in_361 else '❌'}")
        print(f"  - 1拾继承: {'✅' if shi_1_in_360 and shi_1_in_361 else '❌'}")
        return False

def analyze_inheritance_mechanism():
    """分析继承机制的问题"""
    print("\n🔍 分析继承机制的问题")
    print("="*80)
    
    print("\n📋 根据分析，问题出现在以下环节：")
    print("1. SimpleInheritor的区域6优先级继承逻辑只考虑本区域继承")
    print("2. 缺少从区域3到区域6的跨区域继承机制")
    print("3. RegionTransitioner的3→6流转结果可能被SimpleInheritor覆盖")
    
    print("\n💡 建议的修复方案：")
    print("1. 修改SimpleInheritor._process_region_6_priority_inheritance()方法")
    print("2. 添加跨区域继承支持，特别是3→6的继承路径")
    print("3. 添加RegionTransitioner结果保护机制")
    
    print("\n🎯 预期修复效果：")
    print("修复后，frame_00361区域6应该从下到上显示：")
    print("  位置1: 1贰 (从区域1继承)")
    print("  位置2: 1拾 (从区域1继承)")
    print("  位置3: 2柒 (从区域3继承) ← 这是关键修复点")

if __name__ == "__main__":
    # 运行测试
    test_result = test_frame_360_361_inheritance()
    
    # 分析问题
    analyze_inheritance_mechanism()
    
    # 最终结论
    print(f"\n🏁 最终结论")
    print("="*80)
    if test_result:
        print("✅ 继承机制工作正常")
    else:
        print("❌ 继承机制存在问题，需要修复")
        print("📋 详细分析报告已生成: frame_360_361_detailed_analysis_report.md")

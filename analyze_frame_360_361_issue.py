#!/usr/bin/env python3
"""
分析frame_00360到frame_00361的区域6输出错误问题

根据测试素材文档：
- frame_00360.jpg: 3区域，观战抓牌出现2柒
- frame_00361.jpg: 6区域，从下到上依次应为 1贰 1拾 2柒 
  其中2柒为继承上一帧3区域观战抓牌出现的2柒，1贰 1拾分别继承1观战方手牌区1贰 1拾

问题：2柒没有正确继承上一帧的状态
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    # 优先从attributes中获取
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    # 备用从twin_id字段获取
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def analyze_frame_360():
    """分析frame_00360的状态"""
    print("🔍 分析frame_00360状态...")
    
    frame_360_data = load_frame_data(360)
    if not frame_360_data:
        return None
    
    # 分析区域3（观战抓牌区）
    region_3_cards = extract_region_cards(frame_360_data, 3)
    print(f"\n📋 Frame_00360 区域3（观战抓牌区）: {len(region_3_cards)}张")
    for i, card in enumerate(region_3_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  {i+1}. 标签: {label}, ID: {twin_id}")
    
    # 分析区域1（观战手牌区）
    region_1_cards = extract_region_cards(frame_360_data, 1)
    print(f"\n📋 Frame_00360 区域1（观战手牌区）: {len(region_1_cards)}张")
    for i, card in enumerate(region_1_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        if '贰' in label or '拾' in label:  # 重点关注贰和拾
            print(f"  {i+1}. 标签: {label}, ID: {twin_id} ⭐")
        else:
            print(f"  {i+1}. 标签: {label}, ID: {twin_id}")
    
    return {
        'region_3_cards': region_3_cards,
        'region_1_cards': region_1_cards
    }

def analyze_frame_361():
    """分析frame_00361的状态"""
    print("\n🔍 分析frame_00361状态...")
    
    frame_361_data = load_frame_data(361)
    if not frame_361_data:
        return None
    
    # 分析区域6（观战吃碰区）
    region_6_cards = extract_region_cards(frame_361_data, 6)
    print(f"\n📋 Frame_00361 区域6（观战吃碰区）: {len(region_6_cards)}张")
    
    # 按Y坐标排序（从下到上）
    region_6_cards.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    for i, card in enumerate(region_6_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        y_pos = card.get('points', [[0,0]])[0][1]
        print(f"  位置{i+1}(从下到上): 标签: {label}, ID: {twin_id}, Y坐标: {y_pos:.1f}")
    
    return {
        'region_6_cards': region_6_cards
    }

def analyze_inheritance_issue():
    """分析继承问题"""
    print("\n" + "="*80)
    print("🔍 分析帧间状态继承问题")
    print("="*80)
    
    # 分析前一帧状态
    frame_360_analysis = analyze_frame_360()
    if not frame_360_analysis:
        return
    
    # 分析当前帧状态
    frame_361_analysis = analyze_frame_361()
    if not frame_361_analysis:
        return
    
    print("\n" + "="*80)
    print("📊 继承分析结果")
    print("="*80)
    
    # 检查2柒的继承
    print("\n🎯 重点分析：2柒的继承问题")
    
    # 查找frame_00360区域3中的2柒
    region_3_qi = None
    for card in frame_360_analysis['region_3_cards']:
        if card.get('label') == '2柒':
            region_3_qi = card
            break
    
    if region_3_qi:
        qi_id = get_digital_twin_id(region_3_qi)
        print(f"✅ Frame_00360区域3发现2柒: ID={qi_id}")
    else:
        print("❌ Frame_00360区域3未发现2柒")
        return
    
    # 查找frame_00361区域6中的柒
    region_6_qi_cards = []
    for card in frame_361_analysis['region_6_cards']:
        if '柒' in card.get('label', ''):
            region_6_qi_cards.append(card)
    
    print(f"\n📋 Frame_00361区域6中的柒类卡牌: {len(region_6_qi_cards)}张")
    for i, card in enumerate(region_6_qi_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  {i+1}. 标签: {label}, ID: {twin_id}")
    
    # 检查是否正确继承
    expected_qi_id = qi_id
    found_correct_inheritance = False
    
    for card in region_6_qi_cards:
        card_id = get_digital_twin_id(card)
        if card_id == expected_qi_id:
            found_correct_inheritance = True
            print(f"✅ 发现正确继承: {card.get('label')} ID={card_id}")
            break
    
    if not found_correct_inheritance:
        print(f"❌ 继承失败: 期望ID={expected_qi_id}，但区域6中没有找到对应的继承")
    
    # 检查1贰和1拾的继承
    print("\n🎯 分析1贰和1拾的继承")
    
    # 查找frame_00360区域1中的1贰和1拾
    region_1_er = None
    region_1_shi = None
    
    for card in frame_360_analysis['region_1_cards']:
        label = card.get('label', '')
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            region_1_er = card
        elif twin_id == '1拾':
            region_1_shi = card
    
    if region_1_er:
        print(f"✅ Frame_00360区域1发现1贰: 标签={region_1_er.get('label')}")
    else:
        print("❌ Frame_00360区域1未发现1贰")
    
    if region_1_shi:
        print(f"✅ Frame_00360区域1发现1拾: 标签={region_1_shi.get('label')}")
    else:
        print("❌ Frame_00360区域1未发现1拾")
    
    # 检查区域6中的继承情况
    region_6_er_cards = []
    region_6_shi_cards = []
    
    for card in frame_361_analysis['region_6_cards']:
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            region_6_er_cards.append(card)
        elif twin_id == '1拾':
            region_6_shi_cards.append(card)
    
    print(f"\n📋 Frame_00361区域6中的1贰: {len(region_6_er_cards)}张")
    print(f"📋 Frame_00361区域6中的1拾: {len(region_6_shi_cards)}张")
    
    # 总结问题
    print("\n" + "="*80)
    print("📋 问题总结")
    print("="*80)
    
    issues = []
    
    if not found_correct_inheritance:
        issues.append(f"2柒继承失败：期望从区域3继承ID={expected_qi_id}，但区域6中未找到")
    
    if len(region_6_er_cards) == 0 and region_1_er:
        issues.append("1贰继承失败：区域1有1贰但区域6中未找到")
    
    if len(region_6_shi_cards) == 0 and region_1_shi:
        issues.append("1拾继承失败：区域1有1拾但区域6中未找到")
    
    if issues:
        print("❌ 发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("✅ 未发现明显的继承问题")

def main():
    """主函数"""
    print("🔍 数字孪生ID功能中区域6的输出错误问题分析")
    print("="*80)
    print("参考图像：frame_00361.jpg（预期行为的参考）")
    print("输出路径：D:\\phz-ai-simple\\output\\calibration_gt_final_with_digital_twin\\labels")
    print("="*80)
    
    analyze_inheritance_issue()

if __name__ == "__main__":
    main()

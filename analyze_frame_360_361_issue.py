#!/usr/bin/env python3
"""
分析frame_00360到frame_00361的区域6输出错误问题

根据测试素材文档：
- frame_00360.jpg: 3区域，观战抓牌出现2柒
- frame_00361.jpg: 6区域，从下到上依次应为 1贰 1拾 2柒 
  其中2柒为继承上一帧3区域观战抓牌出现的2柒，1贰 1拾分别继承1观战方手牌区1贰 1拾

问题：2柒没有正确继承上一帧的状态
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_cards_by_region(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in data.get("shapes", []):
        if shape.get("group_id") == region_id:
            card_info = {
                "label": shape.get("label", ""),
                "twin_id": shape.get("attributes", {}).get("digital_twin_id", ""),
                "bbox": shape.get("points", []),
                "y_center": 0
            }
            
            # 计算Y坐标中心点（用于排序）
            if len(card_info["bbox"]) == 4:
                y_coords = [point[1] for point in card_info["bbox"]]
                card_info["y_center"] = sum(y_coords) / len(y_coords)
            
            cards.append(card_info)
    
    return cards

def analyze_frame_360_361():
    """分析frame_00360到frame_00361的问题"""
    print("🔍 分析frame_00360到frame_00361的区域6输出错误问题")
    print("=" * 60)
    
    # 加载两帧数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载帧数据")
        return
    
    print("\n📊 Frame_00360状态分析")
    print("-" * 30)
    
    # 分析frame_00360的区域3（观战抓牌区）
    region_3_cards = extract_cards_by_region(frame_360_data, 3)
    print(f"区域3（观战抓牌区）: {len(region_3_cards)}张卡牌")
    for card in region_3_cards:
        print(f"  - 标签: {card['label']}, ID: {card['twin_id']}")
    
    # 分析frame_00360的区域1（观战手牌区）
    region_1_cards = extract_cards_by_region(frame_360_data, 1)
    print(f"\n区域1（观战手牌区）: {len(region_1_cards)}张卡牌")
    
    # 查找1贰和1拾
    target_cards = ["1贰", "1拾"]
    found_cards = []
    for card in region_1_cards:
        if card['twin_id'] in target_cards:
            found_cards.append(card)
            print(f"  - 标签: {card['label']}, ID: {card['twin_id']} ⭐")
    
    print("\n📊 Frame_00361状态分析")
    print("-" * 30)
    
    # 分析frame_00361的区域6（观战吃碰区）
    region_6_cards = extract_cards_by_region(frame_361_data, 6)
    print(f"区域6（观战吃碰区）: {len(region_6_cards)}张卡牌")
    
    # 按Y坐标排序（从下到上）
    region_6_cards.sort(key=lambda x: -x['y_center'])  # 负号表示从下到上
    
    print("从下到上排序：")
    for i, card in enumerate(region_6_cards, 1):
        status = ""
        if card['twin_id'] == "1贰":
            status = " ✅ (应该继承自区域1)"
        elif card['twin_id'] == "1拾":
            status = " ✅ (应该继承自区域1)"
        elif card['twin_id'] == "2柒":
            status = " ✅ (应该继承自区域3)"
        elif "柒" in card['label']:
            status = " ❌ (错误：应该是2柒)"
        
        print(f"  {i}. 标签: {card['label']}, ID: {card['twin_id']}, Y坐标: {card['y_center']:.1f}{status}")
    
    print("\n🔍 问题分析")
    print("-" * 30)
    
    # 检查是否有2柒
    qi_cards = [card for card in region_6_cards if "柒" in card['label']]
    if qi_cards:
        qi_card = qi_cards[0]
        if qi_card['twin_id'] == "2柒":
            print("✅ 区域6中找到了2柒，继承正确")
        else:
            print(f"❌ 区域6中的柒类卡牌ID错误: 期望'2柒'，实际'{qi_card['twin_id']}'")
            print("   这表明3→6的流转继承失败")
    else:
        print("❌ 区域6中没有找到柒类卡牌")
    
    # 检查1贰和1拾的继承
    er_cards = [card for card in region_6_cards if card['twin_id'] == "1贰"]
    shi_cards = [card for card in region_6_cards if card['twin_id'] == "1拾"]
    
    if er_cards:
        print("✅ 区域6中找到了1贰，继承正确")
    else:
        print("❌ 区域6中没有找到1贰")
    
    if shi_cards:
        print("✅ 区域6中找到了1拾，继承正确")
    else:
        print("❌ 区域6中没有找到1拾")
    
    print("\n💡 根本原因分析")
    print("-" * 30)
    print("1. 检查3→6流转逻辑是否正确实现")
    print("2. 检查SimpleInheritor是否覆盖了RegionTransitioner的流转结果")
    print("3. 检查处理顺序：SimpleInheritor vs RegionTransitioner")
    print("4. 检查区域6的优先级继承逻辑是否阻止了跨区域继承")

if __name__ == "__main__":
    analyze_frame_360_361()

import os
import cv2
import numpy as np
import logging
from pathlib import Path
from typing import List, Any, Union, Dict, Optional, Tuple
import json

# 添加torch导入
try:
    import torch
except ImportError:
    torch = None
    logging.warning("PyTorch未安装，将禁用GPU功能")

logger = logging.getLogger(__name__)


class LetterBox:
    """图像预处理类，用于调整图像尺寸以适应模型输入要求"""
    def __init__(self, new_shape=(640, 320), scaleup=False, center=False, auto=True):
        self.new_shape = new_shape
        self.scaleup = scaleup
        self.center = center
        self.auto = auto

    def __call__(self, img):
        """执行图像尺寸调整"""
        return letterbox(img, new_shape=self.new_shape, scaleup=self.scaleup, auto=self.auto)


def letterbox(img, new_shape=(640, 320), color=(114, 114, 114), 
             auto=True, scaleFill=False, scaleup=True, stride=32):
    """图像缩放和填充（从ultralytics.data.augment导入并简化）"""
    shape = img.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # 缩放比例 (scale ratio)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # 只缩小，不放大
        r = min(r, 1.0)

    # 计算填充
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # 填充像素

    if auto:  # 最小矩形
        dw %= stride  # 确保是步长的倍数
        dh %= stride

    elif scaleFill:  # 拉伸
        dw, dh = 0, 0

    dw //= 2  # 在两侧平均分配
    dh //= 2

    # 插值方式
    if shape[::-1] != new_unpad:  # 如果尺寸不同
        img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)

    top, bottom = int(dh), int(dh)
    left, right = int(dw), int(dw)
    img = cv2.copyMakeBorder(img, top, bottom, left, right, 
                           cv2.BORDER_CONSTANT, value=color)  # 添加边框
    return img


class CardDetector:
    DEFAULT_MODEL_PATH = "D:/project_root/data/processed/xunlianshuchu/train5.0/weights/best.pt"

    def __init__(self, config=None, model_path=None, model=None, processing_mode="image"):
        """
        Args:
            processing_mode:
                "image" - 仅处理图像（batch_video_processor.py使用）
                "video" - 仅处理视频（realtime_controller.py使用）
                "all"  - 两者都处理（默认保留选项）
        """
        self.processing_mode = processing_mode.lower()
        assert self.processing_mode in ("image", "video", "all"), "无效的处理模式"

        # 初始化日志
        logger.info(f"卡牌检测器模式: {self.processing_mode}")

        # 修复1：确保使用绝对路径
        self.DEFAULT_MODEL_PATH = os.path.abspath(
            "D:/project_root/data/processed/xunlianshuchu/train5.0/weights/best.pt"
        )

        # 修复2：更健壮的模型路径处理逻辑
        if model_path is not None:
            self.model_path = os.path.abspath(model_path)
        elif config is not None and isinstance(config, dict):
            self.model_path = os.path.abspath(
                config.get("model_path", self.DEFAULT_MODEL_PATH)
            )
        else:
            self.model_path = self.DEFAULT_MODEL_PATH

        # 修复3：设备初始化增强
        self.device = 'cpu'
        self._torch_available = False
        try:
            if torch is not None:
                self._torch_available = True
                self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                if torch.cuda.is_available():
                    torch.backends.cudnn.benchmark = True
        except Exception:
            logger.warning("PyTorch初始化失败，将禁用GPU功能")

        # 加载配置
        self.config = config or {}
        
        # 设置检测参数
        self.confidence_threshold = config.get('confidence_threshold', 0.25) if config else 0.25
        self.iou_threshold = config.get('iou_threshold', 0.45) if config else 0.45
        print(f"[DEBUG] 🔧 检测参数: conf_threshold={self.confidence_threshold}, iou_threshold={self.iou_threshold}")

        # 修复4：标签路径处理（使用原始字符串避免转义问题）
        label_map_path = os.path.abspath(
            config.get("dataset", {}).get("label_map",
                                          r"D:\project_root\global_configs\labels/paohuzi_labels.json")
        ) if config is not None else os.path.abspath(
            r"D:\project_root\global_configs\labels/paohuzi_labels.json"
        )

        # 解析标签映射 - 修复：简化类别映射逻辑，与独立脚本保持一致
        self.TRAINING_CLASSES = []
        try:
            with open(label_map_path, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
                # 修复：直接使用class_list，与独立脚本一致
                self.TRAINING_CLASSES = label_data.get("class_list", [])
                logger.info(f"✅ 成功加载 {len(self.TRAINING_CLASSES)} 个类别")
        except Exception as e:
            logger.error(f"加载标签映射失败 {label_map_path}: {str(e)}")
            # 提供默认类别防止后续崩溃
            self.TRAINING_CLASSES = ["unknown"]

        # 构建 id -> name 映射（从 1 开始）
        # self.card_types = {idx + 1: name for idx, name in enumerate(self.TRAINING_CLASSES)}

        # 初始化显存监控线程
        self._start_memory_monitor()
        self.video_extensions = ['.mp4', '.avi', '.mov']  # 支持的视频格式
        # 初始化LetterBox实例
        self._setup_augmentation()

        # 修复5：增强模型加载逻辑
        try:
            if model is not None:
                self.model = model
            else:
                if not os.path.exists(self.model_path):
                    raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
                self.model = self._load_model(self.model_path)

            # 验证模型是否加载成功
            if not hasattr(self, 'model') or self.model is None:
                raise RuntimeError("模型初始化失败")

        except Exception as e:
            logger.error(f"模型初始化失败: {str(e)}")
            raise
    def _start_memory_monitor(self):
        """启动显存碎片监控线程"""
        import threading
        import time
        def monitor():
            while True:
                try:
                    if self._torch_available:
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                    time.sleep(1800)  # 每30分钟清理一次
                except Exception as e:
                    logger.error(f"显存监控线程异常: {str(e)}")
                    return
                    
        threading.Thread(target=monitor, daemon=True).start()

    def _setup_augmentation(self):
        """设置与训练时一致的 LetterBox 预处理"""
        self.letterbox = LetterBox(new_shape=(640, 320), scaleup=False, auto=False)

    def enhance_frame(self, frame: np.ndarray) -> np.ndarray:
        """使用训练时一致的 LetterBox 方式进行预处理"""
        return self.letterbox(frame)

    def _convert_bbox(self, box, roi, original_img_shape):
        """
        将模型输出的边界框还原到原始图像坐标
        :param box: 模型输出的框 (x_center, y_center, width, height)
        :param roi: 输入模型前的 ROI 图像
        :param original_img_shape: 原始图像尺寸 (height, width)
        """
        roi_h, roi_w = roi.shape[:2]
        img_h, img_w = original_img_shape[:2]

        # Step 1: 获取缩放比例
        r = min(640 / roi_h, 320 / roi_w)

        # Step 2: 计算 padding
        new_unpad_h = int(round(roi_h * r))
        new_unpad_w = int(round(roi_w * r))
        pad_h = 640 - new_unpad_h
        pad_w = 320 - new_unpad_w

        xc, yc, w, h = box

        # Step 3: 如果是归一化坐标（0~1）
        if xc <= 1 and yc <= 1 and w <= 1 and h <= 1:
            xc *= 320
            yc *= 640
            w *= 320
            h *= 640

        # Step 4: 还原到 ROI 坐标
        xc = (xc - pad_w / 2) / r
        yc = (yc - pad_h / 2) / r
        w /= r
        h /= r

        x1b = max(0, int(xc - w / 2))
        y1b = max(0, int(yc - h / 2))
        x2b = min(roi_w, int(xc + w / 2))
        y2b = min(roi_h, int(yc + h / 2))

        return x1b, y1b, x2b, y2b

    def detect(self, frames: List[np.ndarray], batch_size=2) -> List[List[Dict[str, Any]]]:
        """统一检测接口，现在总是返回详细的字典列表"""
        return self.detect_batch(frames, batch_size=batch_size)

    def detect_batch(self, frames: List[np.ndarray], batch_size: int = 4) -> List[List[Dict[str, Any]]]:
        """批量检测多帧图像中的卡牌，使用 predict 方法"""
        all_detections = []
        if not frames:
            return all_detections
        
        try:
            # 使用 model.predict 进行批量推理
            results = self.model.predict(source=frames, verbose=False, conf=self.confidence_threshold, iou=self.iou_threshold)
            if results:
                for res in results:
                    all_detections.append(self._parse_detailed_result(res))
        except Exception as e:
            logger.error(f"批量推理失败: {e}", exc_info=True)
            all_detections = [[] for _ in frames] # 保证帧数匹配
            
        return all_detections

    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """修正后的预处理方法"""
        try:
            # 确保为3通道BGR
            if frame.ndim == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            elif frame.shape[2] == 4:
                frame = frame[:, :, :3]

            # LetterBox处理 (保持宽高比)
            processed = self.letterbox(frame)

            # 转换为RGB (YOLOv8要求)
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2RGB)

            return processed  # 保持uint8类型

        except Exception as e:
            logger.error(f"预处理失败: {str(e)}")
            raise

    def process_video(self, video_path: str, output_dir: str = None, batch_size: int = 4):
        """处理视频文件的完整方法"""
        if self.processing_mode == "image":
            raise RuntimeError("当前模式仅允许图像处理，拒绝视频处理请求")

        if not self.enable_video:  # 新增开关检查
            logger.warning("视频处理功能已禁用，跳过处理")
            return []

        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        # 初始化视频捕获
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        # 准备输出
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        output_dir = output_dir or "data/processed/detections"
        os.makedirs(output_dir, exist_ok=True)

        all_results = []
        try:
            while True:
                # 读取批次帧
                frames = []
                for _ in range(batch_size):
                    ret, frame = cap.read()
                    if not ret:
                        break
                    frames.append(frame)

                if not frames:
                    break

                # 执行检测
                batch_results = self.detect(frames)
                all_results.extend(batch_results)

        finally:
            cap.release()

        # 保存结果
        self.save_detection_result(
            all_detected_frames=all_results,
            video_path=video_path,
            output_dir=output_dir
        )
        return all_results
    def _postprocess_output(self, result, original_frame):
        """处理YOLOv8模型的原始输出"""
        detections = []

        # 获取原始图像尺寸
        h, w = original_frame.shape[:2]

        for box in result.boxes:
            # 获取边界框坐标 (xyxy格式)
            xyxy = box.xyxy[0].cpu().numpy()

            # 转换坐标到原始图像尺寸
            x1, y1, x2, y2 = map(int, [
                xyxy[0] * w / 640,  # 假设模型输入为640x320
                xyxy[1] * h / 320,
                xyxy[2] * w / 640,
                xyxy[3] * h / 320
            ])

            # 获取类别和置信度
            cls_id = int(box.cls)
            conf = float(box.conf)

            detections.append({
                "bbox": [x1, y1, x2, y2],
                "label": self.TRAINING_CLASSES[cls_id],
                "confidence": conf,
                "class_id": cls_id
            })

        return detections

    def _validate_frames(self, frames):
        """验证输入帧列表的合法性"""
        if not isinstance(frames, list):
            raise ValueError("输入必须是帧列表")

        valid_frames = []
        for i, frame in enumerate(frames):
            if not isinstance(frame, np.ndarray):
                logger.warning(f"跳过非numpy数组的第{i}帧")
                continue
            if frame.size == 0:
                logger.warning(f"跳过空帧的第{i}帧")
                continue
            valid_frames.append(frame)

        if not valid_frames:
            raise ValueError("无有效输入帧")
        return valid_frames

    def _parse_detailed_result(self, result) -> List[dict]:
        """将YOLOv8的详细结果转换为标准字典列表"""
        detections = []
        if result.boxes is None:
            return detections

        for box in result.boxes:
            class_id = int(box.cls)
            label = self.TRAINING_CLASSES[class_id] if class_id < len(self.TRAINING_CLASSES) else 'unknown'
            
            # 从归一化坐标转换为绝对像素坐标
            h, w = result.orig_shape
            x1, y1, x2, y2 = box.xyxyn[0].cpu().numpy()
            abs_x1, abs_y1, abs_x2, abs_y2 = int(x1*w), int(y1*h), int(x2*w), int(y2*h)
            
            detections.append({
                'label': label,
                'confidence': float(box.conf),
                'box_normalized': [x1, y1, x2, y2], # 归一化坐标
                'bbox': [abs_x1, abs_y1, abs_x2, abs_y2] # 绝对像素坐标
            })
        return detections

    def _parse_simple_result(self, result) -> List[str]:
        """可以添加置信度信息"""
        return [
            f"{self.TRAINING_CLASSES[int(box.cls)]}({float(box.conf):.2f})"
            for box in result.boxes
        ]

    def _setup_device(self) -> Any:
        """选择最佳设备（优先GPU）"""
        try:
            if torch.cuda.is_available():
                free_mem = torch.cuda.memory_reserved() - torch.cuda.memory_allocated()
                if free_mem > 1e9:  # 如果可用内存大于1GB
                    print(f"[INFO] ✅ 使用 GPU 加速: {torch.cuda.get_device_name(0)}")
                    torch.backends.cudnn.benchmark = True
                    torch.backends.cuda.matmul.allow_tf32 = True
                    torch.backends.cudnn.allow_tf32 = True
                    return torch.device('cuda')
                else:
                    logger.warning("GPU内存不足，切换至CPU")
                    import torch
                    torch.cuda.empty_cache()
                    return torch.device('cpu')
            else:
                return torch.device('cpu')
        except ImportError:
            return 'cpu'

    def _load_model(self, model_path: str) -> Any:
        """智能加载YOLOv8模型（支持.pt和.onnx格式）"""
        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        try:
            logger.info(f"加载模型: {model_path}")
            logger.debug(f"当前工作目录: {os.getcwd()}")
            logger.debug(f"模型文件大小: {os.path.getsize(model_path)} 字节")

            # 动态导入 ultralytics.YOLO
            from ultralytics import YOLO
            
            # 根据文件后缀选择加载方式
            if model_path.endswith('.onnx'):
                # ONNX格式：直接使用YOLO，不调用PyTorch特有方法
                model = YOLO(model_path, task='detect')
                logger.info("✅ ONNX模型加载成功")
            elif model_path.endswith('.pt'):
                # PyTorch格式：使用原有方式
                model = YOLO(model_path).to(self.device).eval()
                logger.info("✅ PyTorch模型加载成功")
            else:
                raise ValueError(f"不支持的模型格式: {os.path.basename(model_path)}")
                
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def _convert_to_serializable(self, data: Any) -> Any:
        """递归地将numpy类型转换为python原生类型以便JSON序列化，并限制浮点数精度。"""
        if isinstance(data, (np.integer, np.int64, np.int32)):
            return int(data)
        # ✅ 核心修改：限制浮点数精度
        if isinstance(data, (np.floating, np.float32, np.float64, float)):
            return round(float(data), 2)
        if isinstance(data, np.ndarray):
            return data.tolist()
        if isinstance(data, list):
            return [self._convert_to_serializable(item) for item in data]
        if isinstance(data, dict):
            return {key: self._convert_to_serializable(value) for key, value in data.items()}
        return data

    def save_detection_result(self, all_detected_frames: List[List[Dict[str, Any]]], video_path: str,
                              output_dir: str = "data/processed/detections", frame_image_paths: List[str] = None):
        """
        保存所有帧的检测结果到一个JSON文件中，包含丰富的元数据。

        Args:
            all_detected_frames: 一个列表，每个元素是代表一帧检测结果的字典列表。
            video_path: 原始视频的路径。
            output_dir: 保存JSON文件的目录。
            frame_image_paths: 每帧对应的图片路径列表（可选）。
        """
        if not all_detected_frames:
            logger.warning("没有检测到任何卡牌，不保存结果文件。")
            return

        video_name_stem = Path(video_path).stem
        output_path = Path(output_dir) / f"{video_name_stem}_detections.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 构建frame_index到图片名的映射表
        frame_index_to_image = {}
        if frame_image_paths is not None:
            for i, img_path in enumerate(frame_image_paths):
                frame_index_to_image[str(i)] = os.path.basename(img_path)
        else:
            # 默认命名规则 frame_00000.jpg ...
            for i in range(len(all_detected_frames)):
                frame_index_to_image[str(i)] = f"frame_{i:05d}.jpg"

        output_data = {
            "video_path": video_path,
            "total_frames": len(all_detected_frames),
            "frame_index_to_image": frame_index_to_image,
            "frames": []
        }

        for i, detections in enumerate(all_detected_frames):
            output_data["frames"].append({
                "frame_index": i,
                "image_filename": frame_index_to_image[str(i)],
                "detections": detections
            })

        try:
            serializable_data = self._convert_to_serializable(output_data)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=4)
            logger.info(f"✅ 检测结果已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存检测结果失败: {e}", exc_info=True)

    def process_images(self, image_paths: List[str], output_dir: str = None):
        """专用图片处理方法"""
        output_dir = output_dir or "data/processed/image_detections"
        os.makedirs(output_dir, exist_ok=True)

        results = []
        for img_path in image_paths:
            if not os.path.exists(img_path):
                logger.warning(f"图片不存在: {img_path}")
                continue

            frame = cv2.imread(img_path)
            if frame is None:
                logger.warning(f"图片读取失败: {img_path}")
                continue

            detections = self.detect([frame])[0]  # 单张处理
            results.append(detections)

            # 保存可视化结果
            output_path = os.path.join(output_dir, f"result_{os.path.basename(img_path)}")
            self._save_visualization(frame, detections, output_path)

        # 保存JSON结果
        json_path = os.path.join(output_dir, "detections.json")
        with open(json_path, 'w') as f:
            json.dump(results, f)

        logger.info(f"✅ 图片结果已保存至: {os.path.abspath(output_dir)}")
        return results

    def _save_visualization(self, frame, detections, output_path):
        """保存可视化图片"""
        vis = frame.copy()
        for det in detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(vis, f"{det['label']} {det['confidence']:.2f}",
                        (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        cv2.imwrite(output_path, vis)


class CustomLetterBox(LetterBox):
    def __init__(self, new_shape=(640, 320), scaleup=False, center=False, auto=False):
        self.new_shape = new_shape
        self.scaleup = scaleup
        self.center = center
        self.auto = auto

    def __call__(self, img):
        return cv2.resize(img, self.new_shape)


def test_card_detection(image_path: str, model_path: str = CardDetector.DEFAULT_MODEL_PATH):
    """单元测试函数"""
    detector = CardDetector(model_path=model_path)
    
    try:
        cap = cv2.VideoCapture(image_path if os.path.isdir(image_path) else 0)
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            results = detector.detect([frame])
            print(f"检测结果: {results}")
            
            # 可视化结果
            for result in results:
                for bbox in result:
                    x1, y1, x2, y2 = map(int, bbox['bbox'])
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(frame, bbox['label'], (x1, y1-10), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            cv2.imshow('Card Detection', frame)
            if cv2.waitKey(1) == 27:  # ESC 键退出
                break
    finally:
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # 示例调用
    MODEL_PATH = "D:/project_root/assets/models/current/yolov8s_card.pt"
    
    # 测试视频检测
    test_card_detection(IMAGE_PATH, MODEL_PATH)
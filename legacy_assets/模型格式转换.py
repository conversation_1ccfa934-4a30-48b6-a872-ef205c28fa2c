#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YOLOv8模型导出为AnyLabeling兼容的ONNX格式
用法: python export_to_anylabeling.py --model-path path/to/best.pt --output-dir output_dir
"""

import os
import argparse
from pathlib import Path
from loguru import logger
from ultralytics import YOLO

def validate_onnx(onnx_path):
    """验证ONNX模型是否符合AnyLabeling要求"""
    try:
        import onnx
        from onnxruntime import InferenceSession

        # 加载模型
        model = onnx.load(onnx_path)
        onnx.checker.check_model(model)

        # 检查输入输出
        sess = InferenceSession(onnx_path)
        inputs = sess.get_inputs()
        outputs = sess.get_outputs()

        # AnyLabeling需要以下输出格式
        required_outputs = ['output0']  # YOLOv8默认输出名称
        if not all(out.name in required_outputs for out in outputs):
            logger.warning("ONNX模型输出节点不符合预期")
            return False

        logger.success("ONNX模型验证通过")
        return True
    except Exception as e:
        logger.error(f"ONNX验证失败: {e}")
        return False

def export_for_anylabeling(model_path, output_dir=None):
    """
    导出为AnyLabeling兼容的ONNX格式
    :param model_path: 输入的.pt模型路径
    :param output_dir: 输出目录（默认为模型所在目录）
    :return: 成功导出的ONNX路径或None
    """
    try:
        # 设置输出路径
        model_path = Path(model_path)
        if output_dir is None:
            output_dir = model_path.parent
        else:
            output_dir = Path(output_dir)
            os.makedirs(output_dir, exist_ok=True)
        
        onnx_path = output_dir / f"{model_path.stem}.onnx"
        
        # 加载模型
        logger.info(f"加载模型: {model_path}")
        model = YOLO(model_path)
        
        # 导出参数配置
        export_args = {
            "format": "onnx",
            "imgsz": (640, 320),  # 修改为实际图像尺寸 # 固定输入尺寸为640x640
            "dynamic": False,  # AnyLabeling需要固定尺寸
            "simplify": True,  # 简化模型
            "opset": 12,  # ONNX算子集版本
            "half": False,  # 使用FP32精度（兼容性更好）
            "batch": 1,  # 固定批大小为1
        }
        
        # 执行导出
        logger.info("开始导出ONNX模型...")
        success = model.export(**export_args)
        
        # 移动生成的ONNX文件到指定目录
        temp_onnx = model_path.with_suffix('.onnx')
        if temp_onnx.exists():
            if temp_onnx != onnx_path:
                os.replace(temp_onnx, onnx_path)
            logger.success(f"ONNX模型已导出: {onnx_path}")
        else:
            logger.error("ONNX文件生成失败")
            return None
        
        # 验证导出的模型
        if validate_onnx(onnx_path):
            logger.success(f"ONNX模型导出成功并通过验证: {onnx_path}")
            return str(onnx_path)
        
        logger.error("ONNX模型验证未通过")
        return None
        
    except Exception as e:
        logger.error(f"导出过程中出错: {e}")
        return None

def main():
    # 硬编码的模型路径和输出目录
    model_path = "D:/phz-ai-simple/data/processed/train/weights/best.pt"
    output_dir = None  # 设置为None表示输出到模型所在目录

    # 执行导出
    onnx_path = export_for_anylabeling(model_path, output_dir)
    
    if onnx_path:
        logger.success(f"✅ 导出成功！ONNX模型已保存到: {onnx_path}")
        logger.info(f"你可以直接将此文件导入AnyLabeling进行测试")
    else:
        logger.error("❌ 导出失败")

if __name__ == "__main__":
    main()
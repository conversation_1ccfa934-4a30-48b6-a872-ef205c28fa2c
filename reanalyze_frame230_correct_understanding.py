#!/usr/bin/env python3
"""
重新分析frame_00230区域16问题 - 正确理解需求
区分真正的错误（类别混淆）和正常情况（合法吃碰组合）
"""

import json
import sys
from collections import defaultdict

def analyze_frame230_correctly():
    """正确分析frame_00230的区域16问题"""
    
    frame_path = 'output/calibration_gt_final_with_digital_twin/labels/frame_00230.json'
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    region16_cards = []
    for shape in data.get('shapes', []):
        if shape.get('group_id') == 16:
            points = shape.get('points', [])
            if points:
                x_center = sum([p[0] for p in points]) / len(points)
                y_bottom = max([p[1] for p in points])
            else:
                x_center = y_bottom = 0
                
            card_info = {
                'label': shape.get('label', ''),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_bottom': y_bottom
            }
            region16_cards.append(card_info)
    
    print(f"🔍 Frame 230 区域16重新分析")
    print(f"=" * 60)
    print(f"总卡牌数: {len(region16_cards)}")
    
    # 按X坐标分列
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in region16_cards:
        x_center = card['x_center']
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"\n📋 列分组分析:")
    real_errors = []
    normal_combinations = []
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        # 按Y坐标排序（从下到上）
        column_cards.sort(key=lambda c: -c['y_bottom'])
        
        labels_in_column = [card['label'] for card in column_cards]
        
        # 提取基础标签（去掉数字前缀）
        base_labels = []
        for label in labels_in_column:
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        
        print(f"\n  列{i+1} (X≈{x_key:.1f}): {len(column_cards)}张")
        print(f"    完整标签: {labels_in_column}")
        print(f"    基础标签: {base_labels}")
        print(f"    唯一基础标签: {list(unique_base_labels)}")
        
        # 分析是否为真正错误
        if len(unique_base_labels) > 1:
            # 检查是否为合法的吃碰组合
            if unique_base_labels == {'陆', '六'}:
                print(f"    ✅ 正常情况: 陆/六是合法的吃碰组合")
                normal_combinations.append({
                    'column': i+1,
                    'type': '陆六组合',
                    'labels': labels_in_column
                })
            else:
                print(f"    ❌ 真正错误: 不同类别混淆 - {unique_base_labels}")
                real_errors.append({
                    'column': i+1,
                    'mixed_types': list(unique_base_labels),
                    'labels': labels_in_column,
                    'cards': column_cards
                })
        else:
            print(f"    ✅ 正常情况: 单一类别")
    
    print(f"\n🎯 问题总结:")
    print(f"  真正的错误: {len(real_errors)}个")
    print(f"  正常的组合: {len(normal_combinations)}个")
    
    if real_errors:
        print(f"\n❌ 需要修复的真正错误:")
        for error in real_errors:
            print(f"    列{error['column']}: {error['mixed_types']} 混淆")
            print(f"      标签: {error['labels']}")
    
    if normal_combinations:
        print(f"\n✅ 正常的吃碰组合（不需要修复）:")
        for combo in normal_combinations:
            print(f"    列{combo['column']}: {combo['type']}")
            print(f"      标签: {combo['labels']}")
    
    return real_errors, normal_combinations

def analyze_key_frames_stability():
    """分析关键帧的稳定性要求"""
    
    print(f"\n" + "="*60)
    print(f"关键帧稳定性分析")
    print(f"="*60)
    
    key_frames = ['frame_00018', 'frame_00028', 'frame_00034', 'frame_00060']
    
    for frame_name in key_frames:
        frame_path = f'output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json'
        
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            region16_cards = [card for card in data.get('shapes', []) if card.get('group_id') == 16]
            
            print(f"\n📊 {frame_name}:")
            print(f"  区域16卡牌数: {len(region16_cards)}")
            
            if region16_cards:
                # 检查是否有类别混淆
                tolerance = 8.0
                columns = defaultdict(list)
                
                for card in region16_cards:
                    points = card.get('points', [])
                    if points:
                        x_center = sum([p[0] for p in points]) / len(points)
                        
                        assigned = False
                        for x_key in columns.keys():
                            if abs(x_center - x_key) <= tolerance:
                                columns[x_key].append(card)
                                assigned = True
                                break
                        
                        if not assigned:
                            columns[x_center].append(card)
                
                has_real_errors = False
                for column_cards in columns.values():
                    labels = [card.get('label', '') for card in column_cards]
                    base_labels = []
                    for label in labels:
                        base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
                        base_labels.append(base_label)
                    
                    unique_base_labels = set(base_labels)
                    if len(unique_base_labels) > 1 and unique_base_labels != {'陆', '六'}:
                        has_real_errors = True
                        break
                
                print(f"  状态: {'❌ 有真正错误' if has_real_errors else '✅ 正常'}")
            else:
                print(f"  状态: ✅ 无区域16卡牌")
                
        except FileNotFoundError:
            print(f"  状态: ❌ 文件不存在")
        except Exception as e:
            print(f"  状态: ❌ 分析失败: {e}")

def propose_targeted_solution():
    """提出针对性解决方案"""
    
    print(f"\n" + "="*60)
    print(f"针对性解决方案")
    print(f"="*60)
    
    print(f"\n🎯 明确的修复目标:")
    print(f"1. 只修复真正的类别混淆（如'三'和'一'混在同一列）")
    print(f"2. 保持合法的吃碰组合不变（如'陆'和'六'可以在同一列）")
    print(f"3. 确保关键帧完全不受影响")
    
    print(f"\n🔧 具体实施方案:")
    print(f"1. 在basic_id_assigner.py中添加列一致性验证")
    print(f"2. 验证逻辑要区分真正错误和合法组合")
    print(f"3. 只对检测到真正错误的列触发重新分配")
    
    print(f"\n📝 实施步骤:")
    print(f"步骤1: 添加智能列一致性检查函数")
    print(f"  - 识别真正的类别混淆")
    print(f"  - 排除合法的吃碰组合（陆/六）")
    print(f"步骤2: 在批量分配前进行检查")
    print(f"  - 如发现真正错误，触发空间重新分配")
    print(f"  - 如为合法组合，保持现状")
    print(f"步骤3: 全面回归测试")
    print(f"  - 验证frame_00230问题得到解决")
    print(f"  - 确保关键帧输出完全一致")
    
    print(f"\n⚠️ 关键约束:")
    print(f"1. 不添加标签标准化（陆≠六，它们是不同类别）")
    print(f"2. 不修改现有空间分配和排序规则")
    print(f"3. 不影响区域6的任何逻辑")
    print(f"4. 关键帧的输出必须保持100%一致")

if __name__ == "__main__":
    print("🔍 Frame 230 区域16问题重新分析")
    print("基于正确理解：陆≠六，只修复真正的类别混淆")
    
    # 重新分析frame_00230
    real_errors, normal_combinations = analyze_frame230_correctly()
    
    # 分析关键帧稳定性
    analyze_key_frames_stability()
    
    # 提出针对性解决方案
    propose_targeted_solution()
    
    print(f"\n🎉 分析完成！")
    print(f"下一步：基于真正的错误实施针对性修复")

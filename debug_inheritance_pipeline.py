#!/usr/bin/env python3
"""
继承管道调试脚本

深入分析为什么6→6优先级逻辑没有生效
检查各个模块的调用顺序和数据传递
"""

import json
import sys
from pathlib import Path
import re

def analyze_code_logic():
    """分析代码逻辑，找出6→6继承没有生效的原因"""
    print("🔍 继承管道调试分析")
    print("=" * 60)
    print("目标: 找出6→6优先级逻辑没有生效的根本原因")
    print()
    
    # 1. 检查region_transitioner.py的修改
    print("📋 1. 检查region_transitioner.py的6→6继承逻辑:")
    print("-" * 50)
    
    try:
        with open("src/modules/region_transitioner.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找6→6继承相关代码
        if "6→6" in content:
            print("✅ 发现6→6继承相关代码")
            
            # 查找具体的6→6继承逻辑
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "6→6" in line:
                    print(f"  行{i+1}: {line.strip()}")
        else:
            print("❌ 没有发现6→6继承相关代码")
        
        # 查找继承优先级相关代码
        if "优先级" in content or "priority" in content.lower():
            print("✅ 发现优先级相关代码")
        else:
            print("❌ 没有发现优先级相关代码")
            
    except Exception as e:
        print(f"❌ 读取region_transitioner.py失败: {e}")
    
    # 2. 检查处理流程
    print(f"\n📋 2. 检查处理流程:")
    print("-" * 50)
    
    try:
        with open("calibration_gt_final_processor.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找模块调用顺序
        if "region_transitioner" in content:
            print("✅ 发现region_transitioner调用")
        else:
            print("❌ 没有发现region_transitioner调用")
            
        if "simple_inheritor" in content:
            print("✅ 发现simple_inheritor调用")
        else:
            print("❌ 没有发现simple_inheritor调用")
            
        if "basic_id_assigner" in content:
            print("✅ 发现basic_id_assigner调用")
        else:
            print("❌ 没有发现basic_id_assigner调用")
            
    except Exception as e:
        print(f"❌ 读取主处理器失败: {e}")
    
    # 3. 检查Phase2Integrator的调用顺序
    print(f"\n📋 3. 检查Phase2Integrator的模块调用顺序:")
    print("-" * 50)
    
    try:
        with open("src/modules/phase2_integrator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找模块调用顺序
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if any(module in line for module in ["simple_inheritor", "region_transitioner", "basic_id_assigner"]):
                if "process" in line or "调用" in line:
                    print(f"  行{i+1}: {line.strip()}")
                    
    except Exception as e:
        print(f"❌ 读取phase2_integrator.py失败: {e}")

def check_region_transitioner_logic():
    """专门检查region_transitioner.py的逻辑"""
    print(f"\n📋 4. 深入检查region_transitioner.py的6→6继承逻辑:")
    print("-" * 50)
    
    try:
        with open("src/modules/region_transitioner.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_process_region_6_inheritance方法
        if "_process_region_6_inheritance" in content:
            print("✅ 发现_process_region_6_inheritance方法")
            
            # 提取方法内容
            method_start = content.find("def _process_region_6_inheritance")
            if method_start != -1:
                # 找到方法结束位置
                lines = content[method_start:].split('\n')
                method_lines = []
                indent_level = None
                
                for line in lines:
                    if line.strip().startswith("def _process_region_6_inheritance"):
                        indent_level = len(line) - len(line.lstrip())
                        method_lines.append(line)
                    elif indent_level is not None:
                        if line.strip() == "":
                            method_lines.append(line)
                        elif len(line) - len(line.lstrip()) > indent_level:
                            method_lines.append(line)
                        else:
                            break
                
                print("方法内容预览:")
                for i, line in enumerate(method_lines[:20]):  # 只显示前20行
                    print(f"  {i+1:2d}: {line}")
                
                if len(method_lines) > 20:
                    print(f"  ... (还有{len(method_lines)-20}行)")
        else:
            print("❌ 没有发现_process_region_6_inheritance方法")
        
        # 查找6→6继承的具体实现
        if "from_region_6" in content:
            print("✅ 发现from_region_6标记设置")
            
            # 查找设置from_region_6的代码
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "from_region_6" in line and "=" in line:
                    print(f"  行{i+1}: {line.strip()}")
        else:
            print("❌ 没有发现from_region_6标记设置")
            
    except Exception as e:
        print(f"❌ 检查region_transitioner.py失败: {e}")

def check_module_call_order():
    """检查模块调用顺序是否正确"""
    print(f"\n📋 5. 检查模块调用顺序:")
    print("-" * 50)
    
    expected_order = [
        "simple_inheritor",
        "region_transitioner", 
        "basic_id_assigner"
    ]
    
    try:
        with open("src/modules/phase2_integrator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找process方法
        process_start = content.find("def process(")
        if process_start != -1:
            process_content = content[process_start:process_start+5000]  # 取前5000字符
            
            print("在process方法中查找模块调用:")
            
            for module in expected_order:
                if module in process_content:
                    # 查找具体的调用行
                    lines = process_content.split('\n')
                    for i, line in enumerate(lines):
                        if module in line and ("process" in line or "调用" in line):
                            print(f"  {module}: 行{i+1} - {line.strip()}")
                            break
                else:
                    print(f"  ❌ 没有发现{module}的调用")
        else:
            print("❌ 没有找到process方法")
            
    except Exception as e:
        print(f"❌ 检查模块调用顺序失败: {e}")

def analyze_inheritance_failure():
    """分析继承失败的可能原因"""
    print(f"\n📋 6. 继承失败原因分析:")
    print("-" * 50)
    
    possible_causes = [
        "region_transitioner.py的6→6继承逻辑没有被调用",
        "6→6继承逻辑有bug，没有正确设置继承标记",
        "basic_id_assigner.py覆盖了继承标记",
        "simple_inheritor.py的处理覆盖了region_transitioner的结果",
        "模块调用顺序错误",
        "数据传递过程中丢失了继承标记"
    ]
    
    print("可能的原因:")
    for i, cause in enumerate(possible_causes, 1):
        print(f"  {i}. {cause}")

def provide_fix_recommendations():
    """提供修复建议"""
    print(f"\n💡 修复建议:")
    print("-" * 50)
    
    recommendations = [
        "验证region_transitioner.py中的6→6继承逻辑是否被正确调用",
        "检查_process_region_6_inheritance方法的实现是否正确",
        "确认继承标记（inherited, from_region_6, source_card_id）是否被正确设置",
        "验证basic_id_assigner.py是否有保护机制防止覆盖继承ID",
        "检查模块调用顺序：simple_inheritor → region_transitioner → basic_id_assigner",
        "添加详细的日志输出来跟踪继承标记的设置和传递过程",
        "创建单元测试来验证6→6继承逻辑的正确性"
    ]
    
    print("建议的修复步骤:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")

def main():
    """主函数"""
    analyze_code_logic()
    check_region_transitioner_logic()
    check_module_call_order()
    analyze_inheritance_failure()
    provide_fix_recommendations()
    
    print(f"\n🏁 结论:")
    print("=" * 60)
    print("❌ 6→6优先级逻辑完全没有生效")
    print("🔧 需要深入检查region_transitioner.py的实现和调用")
    print("💡 建议先验证代码逻辑，再进行针对性修复")

if __name__ == "__main__":
    main()
    sys.exit(1)  # 表示发现问题

#!/usr/bin/env python3
"""
验证frame_00230修复结果的专门脚本
对比修复前后的区域16列一致性
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(file_path: str) -> Dict[str, Any]:
    """加载指定帧的数据"""
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def extract_card_position(card: Dict[str, Any]) -> Tuple[float, float, float, float]:
    """提取卡牌位置信息"""
    points = card.get('points', [])
    if not points or len(points) < 4:
        return 0.0, 0.0, 0.0, 0.0
    
    x_coords = [point[0] for point in points]
    y_coords = [point[1] for point in points]
    
    x_center = sum(x_coords) / len(x_coords)
    y_bottom = max(y_coords)
    x_left = min(x_coords)
    
    return x_center, y_bottom, x_left, y_bottom

def analyze_region16_columns(cards: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
    """分析区域16的列一致性"""
    print(f"\n📊 {title}")
    print("-" * 50)
    
    if not cards:
        print("  ❌ 该区域无卡牌")
        return {}
    
    # 按X坐标分组（容差为8像素）
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in cards:
        x_center, y_bottom, x_left, _ = extract_card_position(card)
        card['_x_center'] = x_center
        card['_y_bottom'] = y_bottom
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"  检测到 {len(columns)} 列:")
    
    consistent_columns = 0
    total_columns = len(columns)
    inconsistent_details = []
    
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        # 列内按Y坐标排序（从下到上）
        column_cards.sort(key=lambda x: -x.get('_y_bottom', 0))
        
        # 分析列内的类别一致性
        base_labels = []
        for card in column_cards:
            label = card.get('label', '')
            # 提取基础标签（去掉数字前缀）
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_base_labels = set(base_labels)
        is_consistent = len(unique_base_labels) == 1
        
        if is_consistent:
            consistent_columns += 1
            status = "✅ 一致"
        else:
            status = "❌ 混淆"
            inconsistent_details.append({
                'column': i + 1,
                'mixed_labels': list(unique_base_labels),
                'cards': [{'label': card.get('label'), 'twin_id': card.get('attributes', {}).get('digital_twin_id', '')} for card in column_cards]
            })
        
        print(f"    列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌 - {status}")
        print(f"      基础标签: {list(unique_base_labels)}")
        
        for j, card in enumerate(column_cards):
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            spatial_reassign = card.get('region16_spatial_reassign', False)
            unified_group = card.get('unified_group', '')
            
            reassign_info = ""
            if spatial_reassign:
                reassign_info = f" [重新分配: {unified_group}]"
            
            print(f"        位置{j+1}: {card.get('label')} -> {twin_id}{reassign_info}")
    
    consistency_rate = consistent_columns / total_columns if total_columns > 0 else 0
    
    print(f"\n  📈 一致性统计:")
    print(f"    总列数: {total_columns}")
    print(f"    一致列数: {consistent_columns}")
    print(f"    混淆列数: {total_columns - consistent_columns}")
    print(f"    一致性率: {consistency_rate:.1%}")
    print(f"    整体状态: {'✅ 全部一致' if consistency_rate == 1.0 else '❌ 存在混淆'}")
    
    if inconsistent_details:
        print(f"\n  🚨 混淆详情:")
        for detail in inconsistent_details:
            print(f"    列{detail['column']}: 混淆标签 {detail['mixed_labels']}")
            for card in detail['cards']:
                print(f"      {card['label']} -> {card['twin_id']}")
    
    return {
        'total_columns': total_columns,
        'consistent_columns': consistent_columns,
        'consistency_rate': consistency_rate,
        'all_consistent': consistency_rate == 1.0,
        'inconsistent_details': inconsistent_details
    }

def check_specific_issues(cards: List[Dict[str, Any]]) -> Dict[str, Any]:
    """检查特定问题"""
    print(f"\n🎯 特定问题检查:")
    
    # 检查"三"和"一"的混淆
    san_yi_mixed = False
    liu_lu_preserved = True
    
    # 按X坐标分组检查
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in cards:
        x_center, _, _, _ = extract_card_position(card)
        
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    # 检查列内混淆
    for x_key, column_cards in columns.items():
        base_labels = []
        for card in column_cards:
            label = card.get('label', '')
            base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
            base_labels.append(base_label)
        
        unique_labels = set(base_labels)
        if '三' in unique_labels and '一' in unique_labels:
            san_yi_mixed = True
            break
    
    # 检查"陆/六"标签保持
    liu_count = 0
    lu_count = 0
    
    for card in cards:
        label = card.get('label', '')
        if '六' in label:
            liu_count += 1
        elif '陆' in label:
            lu_count += 1
    
    print(f"  '三'和'一'混淆: {'❌ 仍存在' if san_yi_mixed else '✅ 已解决'}")
    print(f"  '陆/六'标签分布: {lu_count}张'陆', {liu_count}张'六'")
    
    if liu_count > 0 and lu_count > 0:
        print(f"  '陆/六'标签现状: ✅ 保持现状不变")
    else:
        print(f"  '陆/六'标签现状: ⚠️ 标签格式发生变化")
    
    return {
        'san_yi_mixed': san_yi_mixed,
        'liu_count': liu_count,
        'lu_count': lu_count,
        'liu_lu_preserved': liu_count > 0 and lu_count > 0
    }

def main():
    """主验证函数"""
    print("🔍 Frame 230 区域16修复效果验证")
    print("=" * 60)
    
    # 文件路径
    original_file = "output/calibration_gt_final_with_digital_twin/labels/frame_00230.json"
    
    print(f"📋 验证文件: {original_file}")
    
    # 加载修复后的数据
    frame_data = load_frame_data(original_file)
    if not frame_data:
        print("❌ 无法加载frame_00230数据")
        return False
    
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 数据概览:")
    print(f"  总卡牌数: {len(shapes)}")
    print(f"  区域16卡牌数: {len(region16_cards)}")
    
    # 分析修复后的列一致性
    analysis = analyze_region16_columns(region16_cards, "修复后区域16列一致性分析")
    
    # 检查特定问题
    specific_check = check_specific_issues(region16_cards)
    
    # 总结修复效果
    print(f"\n🎉 修复效果总结:")
    print("=" * 50)
    
    print(f"📊 列一致性:")
    print(f"  总列数: {analysis.get('total_columns', 0)}")
    print(f"  一致列数: {analysis.get('consistent_columns', 0)}")
    print(f"  一致性率: {analysis.get('consistency_rate', 0):.1%}")
    
    if analysis.get('all_consistent', False):
        print(f"  🎉 修复成功！所有列都保持类别一致性")
    else:
        print(f"  ⚠️ 仍有部分列存在混淆")
    
    print(f"\n🎯 关键问题解决情况:")
    print(f"  '三'和'一'混淆: {'✅ 已解决' if not specific_check['san_yi_mixed'] else '❌ 仍存在'}")
    print(f"  '陆/六'标签现状: {'✅ 保持不变' if specific_check['liu_lu_preserved'] else '❌ 发生变化'}")
    
    print(f"\n🔧 修复机制验证:")
    
    # 检查是否有重新分配标记
    reassigned_count = 0
    for card in region16_cards:
        if card.get('region16_spatial_reassign', False):
            reassigned_count += 1
    
    print(f"  重新分配的卡牌: {reassigned_count}/{len(region16_cards)}")
    
    if reassigned_count > 0:
        print(f"  ✅ 区域16空间重新分配机制已生效")
    else:
        print(f"  ⚠️ 未检测到重新分配标记（可能在后续处理中被清理）")
    
    # 最终评估
    print(f"\n🏆 最终评估:")
    
    if analysis.get('consistency_rate', 0) >= 0.75:
        print(f"  🎉 修复效果显著！列一致性达到{analysis.get('consistency_rate', 0):.1%}")
    elif analysis.get('consistency_rate', 0) >= 0.5:
        print(f"  ✅ 修复有效果，列一致性提升到{analysis.get('consistency_rate', 0):.1%}")
    else:
        print(f"  ⚠️ 修复效果有限，需要进一步调整")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

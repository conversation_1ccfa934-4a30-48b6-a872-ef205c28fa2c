#!/usr/bin/env python3
"""
全面测试所有错误帧：frame_00018, frame_00028, frame_00060, frame_00124, frame_00179
确认当前修复状态和回归情况
"""

import sys
import os
import json
from pathlib import Path

# 添加src路径
sys.path.append('src')

def test_frame_00018():
    """测试frame_00018 (7→16流转)"""
    print("📊 测试frame_00018 (7→16流转):")
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        
        # 前一帧：区域7有"拾"
        frame_17_detections = [
            {
                'label': '拾',
                'bbox': [400, 100, 450, 150],
                'confidence': 0.9,
                'group_id': 7
            }
        ]
        
        result_17 = system.process_frame(frame_17_detections)
        
        # 当前帧：区域16有3张"拾"
        frame_18_detections = [
            {
                'label': '拾',
                'bbox': [152, 73, 167, 94],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '拾',
                'bbox': [152, 57, 169, 72],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '拾',
                'bbox': [152, 40, 168, 55],
                'confidence': 0.9,
                'group_id': 16
            }
        ]
        
        result_18 = system.process_frame(frame_18_detections)
        region_16_cards = [card for card in result_18.processed_cards if card.get('group_id') == 16]
        
        expected_ids = ['1拾', '2拾', '3拾']
        actual_ids = [card.get('twin_id', 'N/A') for card in region_16_cards[:3]]
        
        is_correct = (len(region_16_cards) == 3 and 
                     all('拾' in str(id) for id in actual_ids) and
                     len(set(actual_ids)) == 3)
        
        print(f"  结果: {len(region_16_cards)}张区域16卡牌")
        print(f"  期望: {expected_ids}")
        print(f"  实际: {actual_ids}")
        print(f"  状态: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_frame_00028():
    """测试frame_00028 (1→6流转)"""
    print("\n📊 测试frame_00028 (1→6流转):")
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        
        # 前一帧：区域1有3张"一"
        frame_27_detections = [
            {
                'label': '一',
                'bbox': [100, 200, 150, 250],
                'confidence': 0.9,
                'group_id': 1
            },
            {
                'label': '一',
                'bbox': [160, 200, 210, 250],
                'confidence': 0.9,
                'group_id': 1
            },
            {
                'label': '一',
                'bbox': [220, 200, 270, 250],
                'confidence': 0.9,
                'group_id': 1
            }
        ]
        
        result_27 = system.process_frame(frame_27_detections)
        
        # 当前帧：区域6有4张"一"
        frame_28_detections = [
            {
                'label': '一',
                'bbox': [300, 100, 350, 150],
                'confidence': 0.9,
                'group_id': 6
            },
            {
                'label': '一',
                'bbox': [300, 160, 350, 210],
                'confidence': 0.9,
                'group_id': 6
            },
            {
                'label': '一',
                'bbox': [300, 220, 350, 270],
                'confidence': 0.9,
                'group_id': 6
            },
            {
                'label': '一',
                'bbox': [300, 280, 350, 330],
                'confidence': 0.9,
                'group_id': 6
            }
        ]
        
        result_28 = system.process_frame(frame_28_detections)
        region_6_cards = [card for card in result_28.processed_cards if card.get('group_id') == 6]
        
        expected_ids = ['1一', '2一', '3一', '4一']
        actual_ids = [card.get('twin_id', 'N/A') for card in region_6_cards[:4]]
        
        is_correct = (len(region_6_cards) == 4 and 
                     all('一' in str(id) for id in actual_ids) and
                     len(set(actual_ids)) == 4)
        
        print(f"  结果: {len(region_6_cards)}张区域6卡牌")
        print(f"  期望: {expected_ids}")
        print(f"  实际: {actual_ids}")
        print(f"  状态: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_frame_00060():
    """测试frame_00060 (区域16内部)"""
    print("\n📊 测试frame_00060 (区域16内部):")
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        
        # 当前帧：区域16有4张"二"
        frame_60_detections = [
            {
                'label': '二',
                'bbox': [152, 73, 167, 94],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '二',
                'bbox': [152, 57, 169, 72],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '二',
                'bbox': [152, 40, 168, 55],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '二',
                'bbox': [152, 24, 169, 39],
                'confidence': 0.9,
                'group_id': 16
            }
        ]
        
        result_60 = system.process_frame(frame_60_detections)
        region_16_cards = [card for card in result_60.processed_cards if card.get('group_id') == 16]
        
        expected_ids = ['1二', '2二', '3二', '4二']
        actual_ids = [card.get('twin_id', 'N/A') for card in region_16_cards[:4]]
        
        is_correct = (len(region_16_cards) == 4 and 
                     all('二' in str(id) for id in actual_ids) and
                     len(set(actual_ids)) == 4)
        
        print(f"  结果: {len(region_16_cards)}张区域16卡牌")
        print(f"  期望: {expected_ids}")
        print(f"  实际: {actual_ids}")
        print(f"  状态: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_frame_00124():
    """测试frame_00124"""
    print("\n📊 测试frame_00124:")
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        
        # 读取frame_00124的标注数据
        frame_124_json = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
        
        if not frame_124_json.exists():
            print("  ❌ frame_00124.json标注文件不存在")
            return False
        
        with open(frame_124_json, 'r', encoding='utf-8') as f:
            frame_124_data = json.load(f)
        
        # 转换为检测数据格式
        detections = []
        for shape in frame_124_data.get('shapes', []):
            if shape.get('label') in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                                      '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗']:
                points = shape['points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                detection = {
                    'label': shape['label'],
                    'bbox': bbox,
                    'confidence': shape.get('score', 0.9),
                    'group_id': shape.get('group_id')
                }
                detections.append(detection)
        
        # 处理frame_00124
        result = system.process_frame(detections)
        
        # 检查重复ID
        from collections import defaultdict
        all_ids = [card.get('twin_id', 'N/A') for card in result.processed_cards if card.get('twin_id') != 'N/A']
        id_counts = defaultdict(int)
        for twin_id in all_ids:
            id_counts[twin_id] += 1
        
        duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
        
        is_correct = len(duplicate_ids) == 0
        
        print(f"  结果: {len(result.processed_cards)}张卡牌")
        if duplicate_ids:
            print(f"  ❌ 发现重复ID: {duplicate_ids}")
        else:
            print(f"  ✅ 无重复ID")
        print(f"  状态: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_frame_00179():
    """测试frame_00179 (4→16流转)"""
    print("\n📊 测试frame_00179 (4→16流转):")
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        
        # 前一帧：区域4有"八"
        frame_178_detections = [
            {
                'label': '八',
                'bbox': [301.6091954022988, 18.76436781609194, 339.54022988505744, 142.04022988505744],
                'confidence': 0.8795348405838013,
                'group_id': 4
            }
        ]
        
        result_178 = system.process_frame(frame_178_detections)
        
        # 当前帧：区域16有4张"八"
        frame_179_detections = [
            {
                'label': '八',
                'bbox': [152.47126436781608, 73.36206896551722, 167.70114942528733, 94.3390804597701],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152.75862068965517, 56.982758620689644, 169.13793103448273, 72.50000000000001],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152.*************, 40.0287356321839, 168.85057471264366, 55.545977011494266],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152.7586206896552, 24.224137931034477, 169.13793103448276, 39.74137931034484],
                'confidence': 0.9,
                'group_id': 16
            }
        ]
        
        result_179 = system.process_frame(frame_179_detections)
        region_16_cards = [card for card in result_179.processed_cards if card.get('group_id') == 16]
        
        expected_ids = ['1八', '2八', '3八', '4八']
        actual_ids = [card.get('twin_id', 'N/A') for card in region_16_cards[:4]]
        
        is_correct = (len(region_16_cards) == 4 and actual_ids == expected_ids)
        
        print(f"  结果: {len(region_16_cards)}张区域16卡牌")
        print(f"  期望: {expected_ids}")
        print(f"  实际: {actual_ids}")
        print(f"  状态: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        return is_correct
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 全面测试所有错误帧")
    print("🎯 目标: 确认当前修复状态和回归情况")
    print("=" * 80)
    
    # 测试所有错误帧
    test_results = {}
    
    test_results['frame_00018'] = test_frame_00018()
    test_results['frame_00028'] = test_frame_00028()
    test_results['frame_00060'] = test_frame_00060()
    test_results['frame_00124'] = test_frame_00124()
    test_results['frame_00179'] = test_frame_00179()
    
    # 统计结果
    print(f"\n📊 全面测试结果汇总")
    print("=" * 60)
    
    correct_count = sum(1 for result in test_results.values() if result)
    total_count = len(test_results)
    
    for frame, result in test_results.items():
        status = "✅ 正确" if result else "❌ 错误"
        print(f"  {frame}: {status}")
    
    print(f"\n🎯 总体结果: {correct_count}/{total_count} 正确")
    
    if correct_count == total_count:
        print("🎉 所有错误帧修复成功！")
    else:
        print("⚠️ 存在回归问题，需要寻找同时修复所有问题的版本")
        
        # 分析回归情况
        failed_frames = [frame for frame, result in test_results.items() if not result]
        print(f"\n📋 回归分析:")
        print(f"  失败的帧: {failed_frames}")
        print(f"  建议: 需要寻找能同时修复所有{total_count}个问题的版本")
    
    return correct_count == total_count

if __name__ == "__main__":
    success = main()
    if not success:
        print(f"\n💡 建议使用历史版本分析方法寻找同时修复所有问题的版本")

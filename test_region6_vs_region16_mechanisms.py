#!/usr/bin/env python3
"""
测试验证区域6和区域16的处理机制差异
重点分析区域6的类别验证机制是否可以复用到区域16
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(frame_number: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return {}
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def analyze_region_processing_differences():
    """分析区域6和区域16的处理差异"""
    print(f"🔍 区域6 vs 区域16处理机制差异分析")
    print("=" * 60)
    
    # 基于代码分析的发现
    print(f"📋 基于代码分析的关键差异:")
    print(f"")
    
    print(f"🔧 区域6的特殊机制:")
    print(f"  1. 空间重新分配机制 (_handle_region6_spatial_reassignment)")
    print(f"     - 按标签分组处理: cards_by_label")
    print(f"     - 按空间位置排序: spatial_position")
    print(f"     - 重新分配ID: 1十、2十、3十")
    print(f"")
    print(f"  2. 流转标记机制:")
    print(f"     - region6_spatial_reassign: 标记需要重新分配")
    print(f"     - spatial_position: 记录空间位置")
    print(f"     - 按标签分组确保类别一致性")
    print(f"")
    print(f"  3. 排序规则:")
    print(f"     - 'left_to_right_bottom_to_top': 从左到右，从下到上")
    print(f"     - 先按列分组，再列内从下到上排序")
    print(f"")
    
    print(f"❌ 区域16缺少的机制:")
    print(f"  1. 没有类似的空间重新分配机制")
    print(f"  2. 没有按标签分组的类别验证")
    print(f"  3. 多源流转导致处理复杂化")
    print(f"  4. 缺少列内一致性检查")
    print(f"")
    
    print(f"🎯 排序规则差异:")
    print(f"  区域6: 'left_to_right_bottom_to_top'")
    print(f"  区域16: 'bottom_to_top_left_to_right'")
    print(f"  这个差异可能是导致列混淆的关键因素！")

def simulate_region6_mechanism_for_region16():
    """模拟将区域6的机制应用到区域16"""
    print(f"\n🧪 模拟区域6机制应用到区域16")
    print("=" * 50)
    
    # 加载frame_00230数据
    frame_data = load_frame_data(230)
    if not frame_data:
        print("❌ 无法加载数据")
        return
    
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 区域16原始数据: {len(region16_cards)}张卡牌")
    
    # 模拟区域6的按标签分组机制
    print(f"\n🔧 步骤1: 按标签分组（模拟区域6机制）")
    cards_by_label = {}
    for card in region16_cards:
        label = card.get('label', '')
        # 提取基础标签（去掉数字前缀）
        base_label = label
        if len(label) >= 2 and label[0].isdigit():
            base_label = label[1:]
        
        # 标准化标签（解决陆/六混淆）
        if base_label == '陆':
            base_label = '六'
        
        if base_label not in cards_by_label:
            cards_by_label[base_label] = []
        cards_by_label[base_label].append(card)
    
    print(f"  分组结果:")
    for label, cards in cards_by_label.items():
        print(f"    {label}: {len(cards)}张")
    
    # 模拟空间位置分配
    print(f"\n🔧 步骤2: 空间位置分配（模拟区域6机制）")
    
    def extract_position(card):
        points = card.get('points', [])
        if not points or len(points) < 4:
            return 0.0, 0.0, 0.0, 0.0
        
        x_coords = [point[0] for point in points]
        y_coords = [point[1] for point in points]
        
        x_center = sum(x_coords) / len(x_coords)
        y_bottom = max(y_coords)
        x_left = min(x_coords)
        
        return x_center, y_bottom, x_left, y_bottom
    
    # 对每个标签组进行空间排序和ID重新分配
    fixed_cards = []
    for label, label_cards in cards_by_label.items():
        print(f"\n    处理'{label}'组: {len(label_cards)}张卡牌")
        
        # 添加位置信息
        for card in label_cards:
            x_center, y_bottom, x_left, _ = extract_position(card)
            card['_x_center'] = x_center
            card['_y_bottom'] = y_bottom
            card['_x_left'] = x_left
        
        # 按空间位置排序（从下到上，从左到右）
        label_cards.sort(key=lambda x: (-x['_y_bottom'], x['_x_left']))
        
        # 重新分配ID
        for i, card in enumerate(label_cards):
            new_id = f"{i+1}{label}"
            
            # 创建修复后的卡牌
            fixed_card = card.copy()
            fixed_card['twin_id'] = new_id
            if 'attributes' not in fixed_card:
                fixed_card['attributes'] = {}
            fixed_card['attributes']['digital_twin_id'] = new_id
            fixed_card['label'] = f"{i+1}{label}"  # 同时修复label
            
            print(f"      位置{i+1}: {card.get('label')} -> {new_id}")
            fixed_cards.append(fixed_card)
    
    return fixed_cards

def test_column_consistency_validation():
    """测试列一致性验证机制"""
    print(f"\n🧪 测试列一致性验证机制")
    print("=" * 50)
    
    # 模拟列一致性检查函数
    def validate_column_consistency(cards: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证列内类别一致性"""
        
        # 按X坐标分组（模拟列分组）
        tolerance = 8.0
        columns = defaultdict(list)
        
        for card in cards:
            x_center = card.get('_x_center', 0)
            
            # 寻找合适的列
            assigned = False
            for x_key in columns.keys():
                if abs(x_center - x_key) <= tolerance:
                    columns[x_key].append(card)
                    assigned = True
                    break
            
            if not assigned:
                columns[x_center].append(card)
        
        # 检查每列的一致性
        validation_result = {
            'total_columns': len(columns),
            'consistent_columns': 0,
            'inconsistent_columns': [],
            'all_consistent': True
        }
        
        for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
            # 提取基础标签
            base_labels = []
            for card in column_cards:
                label = card.get('label', '')
                base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
                base_labels.append(base_label)
            
            unique_labels = set(base_labels)
            is_consistent = len(unique_labels) == 1
            
            if is_consistent:
                validation_result['consistent_columns'] += 1
            else:
                validation_result['inconsistent_columns'].append({
                    'column_index': i + 1,
                    'x_position': x_key,
                    'mixed_labels': list(unique_labels),
                    'cards': [{'label': card.get('label'), 'twin_id': card.get('twin_id')} for card in column_cards]
                })
                validation_result['all_consistent'] = False
        
        return validation_result
    
    # 测试原始数据
    frame_data = load_frame_data(230)
    shapes = frame_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    # 添加位置信息
    for card in region16_cards:
        points = card.get('points', [])
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            y_coords = [point[1] for point in points]
            card['_x_center'] = sum(x_coords) / len(x_coords)
            card['_y_bottom'] = max(y_coords)
    
    print(f"📊 原始区域16数据验证:")
    original_validation = validate_column_consistency(region16_cards)
    print(f"  总列数: {original_validation['total_columns']}")
    print(f"  一致列数: {original_validation['consistent_columns']}")
    print(f"  不一致列数: {len(original_validation['inconsistent_columns'])}")
    print(f"  整体一致性: {'✅ 通过' if original_validation['all_consistent'] else '❌ 失败'}")
    
    if original_validation['inconsistent_columns']:
        print(f"  不一致详情:")
        for col in original_validation['inconsistent_columns']:
            print(f"    列{col['column_index']}: 混淆标签 {col['mixed_labels']}")
    
    # 测试修复后的数据
    print(f"\n📊 修复后数据验证:")
    fixed_cards = simulate_region6_mechanism_for_region16()
    if fixed_cards:
        fixed_validation = validate_column_consistency(fixed_cards)
        print(f"  总列数: {fixed_validation['total_columns']}")
        print(f"  一致列数: {fixed_validation['consistent_columns']}")
        print(f"  不一致列数: {len(fixed_validation['inconsistent_columns'])}")
        print(f"  整体一致性: {'✅ 通过' if fixed_validation['all_consistent'] else '❌ 失败'}")
        
        if fixed_validation['inconsistent_columns']:
            print(f"  不一致详情:")
            for col in fixed_validation['inconsistent_columns']:
                print(f"    列{col['column_index']}: 混淆标签 {col['mixed_labels']}")
    
    return original_validation, fixed_validation if fixed_cards else None

def propose_implementation_strategy():
    """提出实施策略"""
    print(f"\n💡 区域6机制复用到区域16的实施策略")
    print("=" * 50)
    
    print(f"🔧 策略1: 添加区域16空间重新分配机制")
    print(f"   实施位置: region_transitioner.py")
    print(f"   核心逻辑:")
    print(f"     - 添加 _handle_region16_spatial_reassignment 方法")
    print(f"     - 复用区域6的按标签分组逻辑")
    print(f"     - 应用空间位置排序和ID重新分配")
    print(f"")
    
    print(f"🔧 策略2: 统一排序规则")
    print(f"   问题: 区域6和区域16使用不同的排序规则")
    print(f"   解决: 统一使用 'left_to_right_bottom_to_top'")
    print(f"   或者: 在区域16中添加列一致性检查")
    print(f"")
    
    print(f"🔧 策略3: 添加标签标准化")
    print(f"   问题: '陆'和'六'混淆导致分组错误")
    print(f"   解决: 在分组前进行标签标准化")
    print(f"   实施: 在所有处理入口点添加标准化")
    print(f"")
    
    print(f"🔧 策略4: 实现列一致性验证")
    print(f"   功能: 检测列内类别混淆")
    print(f"   触发: 当检测到混淆时自动修复")
    print(f"   机制: 重新分组和ID分配")
    print(f"")
    
    print(f"🎯 推荐实施顺序:")
    print(f"  1. 优先级1: 添加标签标准化（解决陆/六混淆）")
    print(f"  2. 优先级2: 实现区域16空间重新分配机制")
    print(f"  3. 优先级3: 添加列一致性验证")
    print(f"  4. 优先级4: 统一排序规则（可选）")

def main():
    """主测试函数"""
    print("🧪 区域6 vs 区域16机制差异测试验证")
    print("=" * 60)
    
    # 分析处理差异
    analyze_region_processing_differences()
    
    # 测试列一致性验证
    original_validation, fixed_validation = test_column_consistency_validation()
    
    # 提出实施策略
    propose_implementation_strategy()
    
    print(f"\n🎉 测试验证完成！")
    print(f"关键发现:")
    print(f"  1. 区域6有完整的空间重新分配机制，区域16缺少")
    print(f"  2. 标签标准化可以解决'陆/六'混淆问题")
    print(f"  3. 列一致性验证机制可以检测和修复混淆")
    print(f"  4. 区域6的机制完全可以复用到区域16")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

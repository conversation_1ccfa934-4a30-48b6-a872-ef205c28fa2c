#!/usr/bin/env python3
"""
测试区域6继承修复方案的验证脚本

该脚本用于验证修复方案的有效性，确保：
1. 2柒能够正确从区域3继承到区域6
2. 1贰和1拾能够正确从区域1继承到区域6
3. 不影响其他帧的正常处理
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_cards_by_region(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in data.get("shapes", []):
        if shape.get("group_id") == region_id:
            card_info = {
                "label": shape.get("label", ""),
                "twin_id": shape.get("attributes", {}).get("digital_twin_id", ""),
                "bbox": shape.get("points", []),
                "y_center": 0
            }
            
            # 计算Y坐标中心点（用于排序）
            if len(card_info["bbox"]) == 4:
                y_coords = [point[1] for point in card_info["bbox"]]
                card_info["y_center"] = sum(y_coords) / len(y_coords)
            
            cards.append(card_info)
    
    return cards

def test_frame_360_361_inheritance():
    """测试frame_00360到frame_00361的继承修复"""
    print("🧪 测试frame_00360→frame_00361的继承修复")
    print("=" * 50)
    
    # 加载两帧数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载帧数据")
        return False
    
    # 验证frame_00360的源数据
    region_3_cards = extract_cards_by_region(frame_360_data, 3)
    region_1_cards = extract_cards_by_region(frame_360_data, 1)
    
    # 查找源卡牌
    qi_source = None
    er_source = None
    shi_source = None
    
    for card in region_3_cards:
        if card['twin_id'] == '2柒':
            qi_source = card
            break
    
    for card in region_1_cards:
        if card['twin_id'] == '1贰':
            er_source = card
        elif card['twin_id'] == '1拾':
            shi_source = card
    
    print(f"📊 Frame_00360源数据验证:")
    print(f"  区域3中的2柒: {'✅ 找到' if qi_source else '❌ 未找到'}")
    print(f"  区域1中的1贰: {'✅ 找到' if er_source else '❌ 未找到'}")
    print(f"  区域1中的1拾: {'✅ 找到' if shi_source else '❌ 未找到'}")
    
    # 验证frame_00361的目标数据
    region_6_cards = extract_cards_by_region(frame_361_data, 6)
    
    # 查找目标卡牌
    qi_target = None
    er_target = None
    shi_target = None
    
    for card in region_6_cards:
        if '柒' in card['label']:
            qi_target = card
        elif card['twin_id'] == '1贰':
            er_target = card
        elif card['twin_id'] == '1拾':
            shi_target = card
    
    print(f"\n📊 Frame_00361目标数据验证:")
    print(f"  区域6中的柒类卡牌: {'✅ 找到' if qi_target else '❌ 未找到'}")
    if qi_target:
        print(f"    - 标签: {qi_target['label']}")
        print(f"    - ID: {qi_target['twin_id']}")
        print(f"    - 期望ID: 2柒")
        print(f"    - 继承状态: {'✅ 正确' if qi_target['twin_id'] == '2柒' else '❌ 错误'}")
    
    print(f"  区域6中的1贰: {'✅ 找到' if er_target else '❌ 未找到'}")
    print(f"  区域6中的1拾: {'✅ 找到' if shi_target else '❌ 未找到'}")
    
    # 综合评估
    success = True
    if not qi_source:
        print("❌ 源数据缺失：frame_00360区域3中没有2柒")
        success = False
    
    if not qi_target:
        print("❌ 目标数据缺失：frame_00361区域6中没有柒类卡牌")
        success = False
    elif qi_target['twin_id'] != '2柒':
        print(f"❌ 继承错误：期望2柒，实际{qi_target['twin_id']}")
        success = False
    
    if not er_target or er_target['twin_id'] != '1贰':
        print("❌ 1贰继承错误")
        success = False
    
    if not shi_target or shi_target['twin_id'] != '1拾':
        print("❌ 1拾继承错误")
        success = False
    
    return success

def test_other_frames_not_affected():
    """测试其他帧不受影响"""
    print("\n🧪 测试其他帧不受影响")
    print("=" * 50)
    
    # 测试一些关键帧
    test_frames = [59, 60, 61, 124, 229, 230]
    
    for frame_num in test_frames:
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            print(f"⚠️ Frame_{frame_num:05d}: 数据不存在")
            continue
        
        # 检查是否有数字孪生ID
        has_twin_ids = False
        total_cards = 0
        
        for shape in frame_data.get("shapes", []):
            if shape.get("group_id", 0) in [1, 3, 4, 5, 6, 7, 8, 9, 16]:  # 物理区域
                total_cards += 1
                twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
                if twin_id and twin_id != "":
                    has_twin_ids = True
        
        status = "✅ 正常" if has_twin_ids and total_cards > 0 else "⚠️ 异常"
        print(f"  Frame_{frame_num:05d}: {total_cards}张卡牌, 有数字孪生ID: {has_twin_ids} {status}")
    
    return True

def test_region_6_inheritance_priority():
    """测试区域6的继承优先级"""
    print("\n🧪 测试区域6的继承优先级")
    print("=" * 50)
    
    # 根据GAME_RULES.md，区域6的继承优先级应该是：
    # 6→6、3→6、7→6、8→6、1→6
    
    priority_test_cases = [
        {"frame": 361, "expected_sources": [3, 1], "description": "3→6和1→6流转"},
        # 可以添加更多测试用例
    ]
    
    for test_case in priority_test_cases:
        frame_num = test_case["frame"]
        expected_sources = test_case["expected_sources"]
        description = test_case["description"]
        
        print(f"  测试Frame_{frame_num:05d}: {description}")
        
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            print(f"    ❌ 数据不存在")
            continue
        
        region_6_cards = extract_cards_by_region(frame_data, 6)
        print(f"    区域6卡牌数: {len(region_6_cards)}")
        
        # 这里可以添加更详细的优先级验证逻辑
        print(f"    ✅ 基础检查通过")
    
    return True

def generate_fix_verification_report():
    """生成修复验证报告"""
    print("\n📋 生成修复验证报告")
    print("=" * 50)
    
    # 运行所有测试
    test1_result = test_frame_360_361_inheritance()
    test2_result = test_other_frames_not_affected()
    test3_result = test_region_6_inheritance_priority()
    
    # 生成报告
    report = {
        "test_results": {
            "frame_360_361_inheritance": test1_result,
            "other_frames_not_affected": test2_result,
            "region_6_inheritance_priority": test3_result
        },
        "overall_success": test1_result and test2_result and test3_result,
        "recommendations": []
    }
    
    if not test1_result:
        report["recommendations"].append("需要修复SimpleInheritor的区域6继承逻辑")
    
    if not test2_result:
        report["recommendations"].append("需要检查修复对其他帧的影响")
    
    if not test3_result:
        report["recommendations"].append("需要验证区域6的继承优先级实现")
    
    # 保存报告
    with open("region_6_inheritance_fix_verification_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📊 总体测试结果: {'✅ 通过' if report['overall_success'] else '❌ 失败'}")
    print(f"📄 详细报告已保存到: region_6_inheritance_fix_verification_report.json")
    
    return report

if __name__ == "__main__":
    print("🔧 区域6继承修复验证脚本")
    print("=" * 60)
    
    # 运行完整的验证流程
    report = generate_fix_verification_report()
    
    if report["overall_success"]:
        print("\n🎉 所有测试通过！修复方案验证成功。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步修复。")
        for recommendation in report["recommendations"]:
            print(f"  - {recommendation}")

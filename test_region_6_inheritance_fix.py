#!/usr/bin/env python3
"""
测试区域6继承修复方案的验证脚本
用于验证修复后的继承逻辑是否正确工作
"""

import json
import os
from typing import Dict, List, Any, Tuple

class Region6InheritanceValidator:
    """区域6继承验证器"""
    
    def __init__(self):
        self.test_cases = []
        self.results = []
    
    def add_test_case(self, frame_prev: int, frame_curr: int, 
                     expected_inheritances: List[Tuple[str, int, int]]):
        """
        添加测试用例
        
        Args:
            frame_prev: 前一帧编号
            frame_curr: 当前帧编号
            expected_inheritances: 期望的继承列表 [(twin_id, source_region, target_region)]
        """
        self.test_cases.append({
            'frame_prev': frame_prev,
            'frame_curr': frame_curr,
            'expected_inheritances': expected_inheritances
        })
    
    def load_frame_data(self, frame_num: int) -> Dict[str, Any]:
        """加载指定帧的JSON数据"""
        frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ 文件不存在: {frame_path}")
            return {}
        except Exception as e:
            print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
            return {}
    
    def extract_region_cards(self, frame_data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
        """提取指定区域的卡牌"""
        if not frame_data or 'shapes' not in frame_data:
            return []
        
        return [card for card in frame_data['shapes'] if card.get('group_id') == region_id]
    
    def get_digital_twin_id(self, card: Dict[str, Any]) -> str:
        """获取数字孪生ID"""
        twin_id = card.get('twin_id')
        if twin_id:
            return twin_id
        
        attributes = card.get('attributes', {})
        twin_id = attributes.get('digital_twin_id')
        if twin_id:
            return twin_id
        
        return card.get('label', 'None')
    
    def validate_inheritance(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """验证单个测试用例的继承"""
        frame_prev = test_case['frame_prev']
        frame_curr = test_case['frame_curr']
        expected_inheritances = test_case['expected_inheritances']
        
        print(f"\n🧪 验证继承: frame_{frame_prev:05d} → frame_{frame_curr:05d}")
        print("-" * 60)
        
        # 加载数据
        prev_data = self.load_frame_data(frame_prev)
        curr_data = self.load_frame_data(frame_curr)
        
        if not prev_data or not curr_data:
            return {'success': False, 'error': '无法加载帧数据'}
        
        results = []
        
        for twin_id, source_region, target_region in expected_inheritances:
            # 在前一帧的源区域查找卡牌
            source_cards = self.extract_region_cards(prev_data, source_region)
            source_card = None
            
            for card in source_cards:
                if self.get_digital_twin_id(card) == twin_id:
                    source_card = card
                    break
            
            # 在当前帧的目标区域查找卡牌
            target_cards = self.extract_region_cards(curr_data, target_region)
            target_card = None
            
            for card in target_cards:
                if self.get_digital_twin_id(card) == twin_id:
                    target_card = card
                    break
            
            # 验证继承
            inheritance_success = source_card is not None and target_card is not None
            
            result = {
                'twin_id': twin_id,
                'source_region': source_region,
                'target_region': target_region,
                'source_found': source_card is not None,
                'target_found': target_card is not None,
                'inheritance_success': inheritance_success
            }
            
            results.append(result)
            
            # 打印结果
            status = "✅" if inheritance_success else "❌"
            print(f"{status} {twin_id}: 区域{source_region}→区域{target_region}")
            
            if not inheritance_success:
                if not source_card:
                    print(f"   ❌ 前一帧区域{source_region}中未找到{twin_id}")
                if not target_card:
                    print(f"   ❌ 当前帧区域{target_region}中未找到{twin_id}")
        
        # 计算成功率
        success_count = sum(1 for r in results if r['inheritance_success'])
        total_count = len(results)
        success_rate = success_count / total_count if total_count > 0 else 0
        
        return {
            'success': success_rate == 1.0,
            'success_rate': success_rate,
            'results': results,
            'success_count': success_count,
            'total_count': total_count
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试用例"""
        print("🧪 开始运行区域6继承验证测试")
        print("=" * 80)
        
        all_results = []
        total_success = 0
        total_tests = 0
        
        for i, test_case in enumerate(self.test_cases):
            print(f"\n📋 测试用例 {i+1}/{len(self.test_cases)}")
            result = self.validate_inheritance(test_case)
            all_results.append(result)
            
            if result['success']:
                total_success += 1
            total_tests += 1
        
        # 总结
        overall_success_rate = total_success / total_tests if total_tests > 0 else 0
        
        print(f"\n📊 测试总结")
        print("=" * 80)
        print(f"总测试用例: {total_tests}")
        print(f"成功用例: {total_success}")
        print(f"成功率: {overall_success_rate:.1%}")
        
        if overall_success_rate == 1.0:
            print("✅ 所有测试用例通过")
        else:
            print("❌ 部分测试用例失败")
        
        return {
            'overall_success': overall_success_rate == 1.0,
            'success_rate': overall_success_rate,
            'results': all_results,
            'total_success': total_success,
            'total_tests': total_tests
        }

def create_test_cases() -> Region6InheritanceValidator:
    """创建测试用例"""
    validator = Region6InheritanceValidator()
    
    # 测试用例1: frame_00360 → frame_00361
    # 期望: 2柒从区域3继承到区域6, 1贰和1拾从区域1继承到区域6
    validator.add_test_case(
        frame_prev=360,
        frame_curr=361,
        expected_inheritances=[
            ('2柒', 3, 6),  # 2柒: 区域3→区域6
            ('1贰', 1, 6),  # 1贰: 区域1→区域6
            ('1拾', 1, 6),  # 1拾: 区域1→区域6
        ]
    )
    
    return validator

def test_current_implementation():
    """测试当前实现"""
    print("🔍 测试当前实现的继承机制")
    print("=" * 80)
    
    validator = create_test_cases()
    results = validator.run_all_tests()
    
    return results

def simulate_fixed_implementation():
    """模拟修复后的实现"""
    print("\n🔧 模拟修复后的实现")
    print("=" * 80)
    
    print("📋 修复方案模拟:")
    print("1. SimpleInheritor._process_region_6_priority_inheritance()添加跨区域继承")
    print("2. 支持3→6, 1→6等继承路径")
    print("3. 保护RegionTransitioner的继承结果")
    
    print("\n💡 预期修复效果:")
    print("✅ 2柒: 区域3→区域6 (新增支持)")
    print("✅ 1贰: 区域1→区域6 (现有功能)")
    print("✅ 1拾: 区域1→区域6 (现有功能)")
    
    print("\n🎯 修复后的处理流程:")
    print("1. RegionTransitioner处理3→6流转，设置twin_id=2柒")
    print("2. SimpleInheritor检测到已继承标记，保持结果")
    print("3. 最终输出: 区域6包含正确的2柒继承")

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 具体修复建议")
    print("=" * 80)
    
    print("\n📋 需要修改的文件:")
    print("1. src/modules/simple_inheritor.py")
    print("   - 修改 _process_region_6_priority_inheritance() 方法")
    print("   - 添加 _try_cross_region_inheritance_for_region_6() 方法")
    
    print("\n📋 修改要点:")
    print("1. 检查卡牌是否已被RegionTransitioner处理")
    print("2. 添加3→6跨区域继承支持")
    print("3. 保持现有1→6继承功能")
    
    print("\n📋 测试验证:")
    print("1. 运行修复后的代码")
    print("2. 使用本脚本验证继承结果")
    print("3. 确保frame_00361区域6包含正确的2柒")

if __name__ == "__main__":
    # 测试当前实现
    current_results = test_current_implementation()
    
    # 模拟修复后的实现
    simulate_fixed_implementation()
    
    # 生成修复建议
    generate_fix_recommendations()
    
    # 最终建议
    print(f"\n🏁 最终建议")
    print("=" * 80)
    
    if current_results['overall_success']:
        print("✅ 当前实现已经正确，无需修复")
    else:
        print("❌ 当前实现存在问题，建议按照上述方案修复")
        print("📋 关键修复点:")
        print("  1. 添加SimpleInheritor对3→6继承的支持")
        print("  2. 确保RegionTransitioner的结果不被覆盖")
        print("  3. 验证修复后frame_00361区域6包含2柒")
    
    print(f"\n📄 详细分析报告: frame_360_361_detailed_analysis_report.md")
    print(f"🧪 测试脚本: test_frame_360_361_inheritance_issue.py")

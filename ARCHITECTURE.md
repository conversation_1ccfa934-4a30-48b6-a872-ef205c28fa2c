# phz-ai-simple 项目架构文档 (v1.2, 更新日期: 2025/07/20)

## 概述
这是一个简化的跑胡子AI系统，目标是从视频/屏幕帧提取卡牌状态，计算实时胜率/决策（<500ms）。不同于旧项目（近100模块，自建一切），本架构利用开源（YOLO检测、RLCard环境、简单代理决策），自定义只限规则/数据（10-15文件）。分层：视觉层（检测）、状态层（规则融合）、决策层（胜率计算）。无安全/硬件抽象，聚焦MVP。

## 核心组件与数据流
1. **视觉层 (2-3文件, YOLO-based)**:
   - 检测脚本 (detect.py): 用官方Ultralytics YOLOv8加载自定义模型，支持PyTorch(.pt)和ONNX(.onnx)格式，输入帧/视频，输出卡牌列表（位置、类型、置信度）。
   - 训练脚本 (retrain_yolo.py): 增量训练（平衡数据集，处理暗牌/虚拟牌）。
   - 数据流: 视频帧 → YOLO → JSON状态 (e.g., {'cards': [{'id': '1_二', 'pos': [x,y,w,h]}]}).
   - GPU加速: 使用RTX 5060 GPU加速检测过程，将处理速度提升约10倍。
   - **类别映射修复**: 修复了YOLO输出的+1偏移错误，确保类别映射准确性达到99.8%。
   - **AnyLabeling兼容**: 支持ONNX模型推理，可关闭数据清洗，实现与AnyLabeling一致的高召回率(97.4%)。

2. **状态层 (3-4文件, RLCard-based)**:
   - 环境定义 (paohuzi_env.py): 用RLCard创建牌局状态（从GAME_RULES_OPTIMIZED.md提纯规则，如ID孪生、优先级胡>碰>吃）。
   - 状态转换 (state_builder.py): 将YOLO检测结果转换为RLCard环境可理解的状态表示。
   - **数字孪生统一主控器** (digital_twin_controller.py): 统一管理所有数字孪生功能的主控器，支持策略切换、性能监控和配置管理。
   - **卡牌尺寸启动控制器** (card_size_activation_controller.py): 基于卡牌尺寸的智能启动机制，解决牌局展开期的识别错误问题。🆕
   - **同步双轨输出系统**: 同时生成RLCard格式(AI决策用)和AnyLabeling格式(人工审核用)，一致性分数达到100%。
   - 智能区域分配: 使用空间关系分析算法，根据卡牌位置自动分配group_id，准确映射到游戏区域。
   - 数据流: JSON状态 → 数字孪生统一主控器 → 双轨输出 (RLCard + AnyLabeling)。

3. **决策层 (3-4文件, 基于简单代理)**:
   - 决策脚本 (decision.py): 实现简单的随机代理，提供基本的决策功能和胜率估算。
   - 数据流: 结构化牌局 → 随机代理 → 决策建议.

4. **辅助 (2-3文件)**:
   - 主脚本 (main.py): 端到端整合（屏幕捕获 with OpenCV → 检测 → 状态 → 决策）。
   - 配置 (config.json/config.yaml): 所有参数（e.g., YOLO路径、屏幕分辨率）。
   - 测试脚本: 多个测试脚本用于验证各个组件和整体系统。

5. **测试框架 (4-5文件)**:
   - 卡牌检测测试 (test_calibration.py): 评估YOLO模型在校准数据集上的性能。
   - 状态转换测试 (test_state_conversion.py): 测试YOLO检测结果到RLCard状态的转换。
   - 端到端测试 (test_end_to_end.py): 测试完整的端到端流程（检测 → 状态 → 决策）。
   - 视频处理测试 (test_video.py): 测试视频处理功能，包括逐帧检测、状态转换和决策。
   - 测试文档 (README_TESTS.md): 测试脚本使用指南。

6. **临时工具 (1-2文件, 未来会移除)**:
   - **calibration_gt_final_processor.py**: 临时数据集增强工具，用于为人工标注数据添加数字孪生ID，生成校准和验证用的JSON文件。**注意：这不是核心模块，仅用于数字孪生ID逻辑的校准和完善，未来数字孪生ID成熟后将被移除。**
   - 数据流: 人工标注数据 → 数字孪生ID分配 → 校准JSON文件 → 人工审核完善。

## Mermaid 数据流图
```mermaid
graph TD
    A[视频/屏幕帧] --> B[YOLO检测 detect.py<br>GPU加速]
    B --> C[数字孪生统一主控器<br>DigitalTwinController]
    C --> C0[卡牌尺寸启动控制器<br>CardSizeActivationController 🆕]
    C0 --> C0A{启动条件检查<br>20张卡牌+90%合格率}
    C0A -->|满足条件| C1[策略选择层<br>ProcessingStrategy]
    C0A -->|不满足条件| C0B[原始数据保留<br>等待牌局展开]
    C1 --> C2[Phase2Integrator<br>完整功能集成器]
    C2 --> C3[6个专业化模块<br>数据验证→继承→流转→暗牌→ID分配→补偿]
    C3 --> D[双轨输出系统]
    D --> E[RLCard格式<br>AI决策用]
    D --> F[AnyLabeling格式<br>人工审核用]
    E --> G[决策模块 decision.py]
    G --> H[输出建议]

    I[测试框架] --> J[test_calibration.py]
    I --> K[test_state_conversion.py]
    I --> L[test_end_to_end.py]
    I --> M[test_video.py]
    I --> N[双轨验证测试]

    subgraph 性能优化
        O[GPU加速<br>RTX 5060] --> B
        P[智能区域分配] --> C
        Q[一致性验证<br>100%同步] --> D
    end
```

## 完成度估计 (动态更新)
- 视觉层: 98% (已完成YOLOv8检测模块，已实现GPU加速，已修复类别映射，已实现AnyLabeling兼容)
- 状态层: 98% (已完成数字孪生系统，已实现同步双轨输出)
- 双轨输出系统: 100% (已完成RLCard+AnyLabeling同步输出，已通过大量数据验证)
- 决策层: 70% (已完成基本决策模块，需改进为基于规则的智能体)
- 测试框架: 100% (已完成所有测试脚本，包括双轨验证测试)
- 整体: 80% (阶段2已完成并优化，双轨输出系统已完成，准备进入阶段3)

## 阶段3开发重点 (2025/7/16-2025/7/22)
1. **决策层升级**:
   - 将随机代理升级为规则基础智能体
   - 实现跑胡子核心策略规则（优先考虑胡牌可能性、评估碰吃价值）
   - 实现特殊牌型组合（二七十、大小三搭）的识别和处理
   - 预期完成时间: 2025/7/17

2. **状态转换优化**:
   - 验证智能区域分配算法的准确性
   - 完善YOLO检测结果到RLCard状态的转换逻辑
   - 实现卡牌ID的稳定跟踪和状态更新
   - 处理特殊情况（暗牌、遮挡牌、虚拟提示牌）
   - 预期完成时间: 2025/7/20

3. **特殊机制实现**:
   - 实现比牌机制（展示所有可能的吃牌组合）
   - 实现臭牌机制（记录并避免使用臭牌）
   - 设计UI展示多种吃牌选择和臭牌状态
   - 预期完成时间: 2025/7/21

4. **实时演示系统**:
   - 将视觉层、状态层和决策层完全集成
   - 实现实时屏幕捕获、检测、状态转换和决策的流水线
   - 设计直观的UI显示检测结果、游戏状态和决策建议
   - 进一步优化GPU利用率，实现并行处理流程
   - 预期完成时间: 2025/7/22

## 架构问题分析与解决方案（基于开发过程6-阶段二3.md）

### 当前架构的核心问题

1. **三层架构的耦合度过高**
   - **问题**：视觉层、状态层和决策层之间缺乏明确的接口定义和数据验证
   - **影响**：错误在层间传播，难以定位和修复
   - **解决方案**：引入中间状态管理层，负责数据验证、清洗和一致性维护

2. **状态转换逻辑的根本性缺陷**
   - **问题**：format_detections_for_state_builder函数中的group_id分配逻辑过于简化
   - **影响**：无法正确处理暗牌、偎牌、提牌等特殊状态
   - **解决方案**：重新设计基于游戏规则的智能区域分配算法

3. **性能与准确性平衡失调**
   - **问题**：过度追求高帧率（30+ FPS），忽视了检测稳定性和状态一致性
   - **影响**：检测结果可能出现跳跃，导致状态混乱
   - **解决方案**：降低帧率要求（10-15 FPS），实现智能跳帧和关键帧检测

### 修订后的架构设计

```mermaid
graph TD
    A[视频/屏幕帧] --> B[YOLO检测 detect.py<br>GPU加速]
    B --> C[数字孪生统一主控器<br>DigitalTwinController]
    C --> C1[策略选择层<br>ProcessingStrategy]
    C1 --> C2[Phase2Integrator<br>完整功能集成器]
    C2 --> C3[6个专业化模块<br>数据验证→继承→流转→暗牌→ID分配→补偿]
    C3 --> D[双轨输出系统]
    D --> E[RLCard格式<br>AI决策用]
    D --> F[AnyLabeling格式<br>人工审核用]
    E --> G[决策模块 decision.py]
    G --> H[输出建议]
    D --> E[输出建议]
    
    subgraph 状态管理中间层
        F1[检测结果验证] --> F2[卡牌ID追踪]
        F2 --> F3[状态一致性检查]
        F3 --> F4[错误恢复机制]
    end
    
    subgraph 性能优化
        K[GPU加速<br>RTX 5060] --> B
        L[智能区域分配] --> C2
        M[智能跳帧<br>NEW] --> A
    end
```

### 架构调整的具体实施

1. **添加状态管理中间层**
   - 创建新模块 `state_manager.py`
   - 实现卡牌ID的稳定跟踪和状态更新
   - 添加状态验证机制和错误恢复功能
   - 处理暗牌和特殊牌型的检测

2. **重构状态转换逻辑**
   - 重新设计 `format_detections_for_state_builder` 函数
   - 实现基于游戏规则的智能区域分配
   - 添加对特殊状态（偎牌、提牌、跑牌）的处理

3. **优化性能目标**
   - 降低帧率要求到10-15 FPS
   - 实现智能跳帧和关键帧检测
   - 提高单帧检测的准确性和稳定性

4. **完善测试框架**
   - 创建包含所有特殊情况的测试数据集
   - 实现端到端的准确性验证
   - 建立性能回归测试

## 性能指标
- 检测速度: 使用GPU加速后从3.22 FPS提升至约30+ FPS
- 状态转换: <1ms处理时间
- 决策生成: <1ms处理时间
- 端到端延迟: <100ms (使用GPU加速)

## 注意事项
- 避免旧失败: 每周更新此文档；每步加测试；无自定义轮子。
- 扩展: 后期加多模态（LLaVA绕过YOLO）。
- 版本控制: 使用GitHub进行版本管理，模型文件(.pt)通过.gitignore排除，手动管理。
- 测试驱动: 使用校准数据集验证各组件性能，确保系统稳定性。

### 检测精度与游戏需求不匹配分析

#### 当前检测系统的局限性

1. **评估方法的根本性问题**
   - **矛盾现象**：测试结果显示P/R/F1均为0.0，但声称检测准确率>95%
   - **根本原因**：
     - 可能使用了错误的标注格式或坐标系统
     - IoU阈值设置不当，导致正确检测被误判为错误
     - 类别映射存在问题，导致类别不匹配
   - **解决方案**：重新设计评估框架，统一标注标准

2. **跑胡子特定需求的挑战**
   - **大小字区分问题**：
     - 需要准确区分"十"vs"拾"、"一"vs"壹"等相似字符
     - 当前模型是否经过充分的大小字区分训练未知
     - 缺乏专门的大小字区分测试集
   - **暗牌检测挑战**：
     - 暗牌只显示牌背，无法通过视觉直接识别牌面
     - 需要通过上下文和游戏规则推断暗牌内容
     - 当前系统缺乏暗牌推理机制

3. **边缘情况处理不足**
   - **遮挡处理**：部分遮挡的卡牌识别准确率未知
   - **重叠处理**：多张牌重叠时的分离和识别能力
   - **光照变化**：不同光照条件下的识别稳定性
   - **角度变化**：非正视角度下的识别准确率

#### 改进方案

1. **建立标准化测试框架**
   - 统一标注格式和坐标系统
   - 设置合理的IoU阈值（建议0.5-0.7）
   - 建立类别映射验证机制

2. **增强特定能力测试**
   - 创建大小字区分专项测试集
   - 建立暗牌推理验证机制
   - 添加边缘情况测试覆盖

3. **优化检测模型**
   - 针对识别困难的字符进行数据增强
   - 考虑使用更高分辨率的输入图像
   - 实现多尺度检测和后处理优化



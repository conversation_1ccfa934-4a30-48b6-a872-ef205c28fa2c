#!/usr/bin/env python3
"""
测试frame_00360到frame_00361的修复方案验证脚本

该脚本用于验证修复方案的有效性，确保：
1. 2柒能够正确从区域3继承到区域6
2. 1贰和1拾能够正确从区域1继承到区域6
3. 不影响其他帧的正常处理
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    # 优先从attributes中获取
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    # 备用从twin_id字段获取
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def test_frame_360_361_inheritance():
    """测试frame_00360到frame_00361的继承逻辑"""
    print("🧪 测试frame_00360→frame_00361继承逻辑")
    print("="*60)
    
    # 加载数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载测试数据")
        return False
    
    # 测试用例1：验证2柒的继承
    print("\n🎯 测试用例1：验证2柒的3→6继承")
    
    # 查找frame_00360区域3中的2柒
    region_3_cards = extract_region_cards(frame_360_data, 3)
    source_qi = None
    for card in region_3_cards:
        if card.get('label') == '2柒':
            source_qi = card
            break
    
    if not source_qi:
        print("❌ 前置条件失败：frame_00360区域3中未找到2柒")
        return False
    
    source_qi_id = get_digital_twin_id(source_qi)
    print(f"✅ 源卡牌确认：frame_00360区域3中的2柒，ID={source_qi_id}")
    
    # 查找frame_00361区域6中的柒类卡牌
    region_6_cards = extract_region_cards(frame_361_data, 6)
    qi_cards = [card for card in region_6_cards if '柒' in card.get('label', '')]
    
    print(f"📋 目标区域状态：frame_00361区域6中有{len(qi_cards)}张柒类卡牌")
    for i, card in enumerate(qi_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  {i+1}. 标签: {label}, ID: {twin_id}")
    
    # 验证继承结果
    expected_qi_found = False
    for card in qi_cards:
        if get_digital_twin_id(card) == source_qi_id:
            expected_qi_found = True
            print(f"✅ 继承成功：找到期望的ID={source_qi_id}")
            break
    
    if not expected_qi_found:
        print(f"❌ 继承失败：期望ID={source_qi_id}，但区域6中未找到")
        return False
    
    # 测试用例2：验证1贰和1拾的继承
    print("\n🎯 测试用例2：验证1贰和1拾的1→6继承")
    
    # 查找frame_00360区域1中的1贰和1拾
    region_1_cards = extract_region_cards(frame_360_data, 1)
    source_er = None
    source_shi = None
    
    for card in region_1_cards:
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            source_er = card
        elif twin_id == '1拾':
            source_shi = card
    
    if not source_er:
        print("❌ 前置条件失败：frame_00360区域1中未找到1贰")
        return False
    
    if not source_shi:
        print("❌ 前置条件失败：frame_00360区域1中未找到1拾")
        return False
    
    print(f"✅ 源卡牌确认：frame_00360区域1中的1贰和1拾")
    
    # 验证区域6中的继承
    er_found = False
    shi_found = False
    
    for card in region_6_cards:
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            er_found = True
        elif twin_id == '1拾':
            shi_found = True
    
    if er_found:
        print("✅ 1贰继承成功")
    else:
        print("❌ 1贰继承失败")
        return False
    
    if shi_found:
        print("✅ 1拾继承成功")
    else:
        print("❌ 1拾继承失败")
        return False
    
    # 测试用例3：验证空间排序
    print("\n🎯 测试用例3：验证区域6的空间排序")
    
    # 按Y坐标排序（从下到上）
    region_6_cards.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    # 查找1贰、1拾、2柒的位置
    er_position = None
    shi_position = None
    qi_position = None
    
    for i, card in enumerate(region_6_cards):
        twin_id = get_digital_twin_id(card)
        if twin_id == '1贰':
            er_position = i + 1
        elif twin_id == '1拾':
            shi_position = i + 1
        elif twin_id == '2柒':
            qi_position = i + 1
    
    print(f"📋 空间位置（从下到上）：")
    if er_position:
        print(f"  1贰: 位置{er_position}")
    if shi_position:
        print(f"  1拾: 位置{shi_position}")
    if qi_position:
        print(f"  2柒: 位置{qi_position}")
    
    # 根据测试素材文档，期望顺序是：1贰、1拾、2柒（从下到上）
    expected_order = ['1贰', '1拾', '2柒']
    actual_order = []
    
    for card in region_6_cards:
        twin_id = get_digital_twin_id(card)
        if twin_id in expected_order:
            actual_order.append(twin_id)
    
    print(f"📋 期望顺序：{expected_order}")
    print(f"📋 实际顺序：{actual_order}")
    
    if actual_order == expected_order:
        print("✅ 空间排序正确")
    else:
        print("⚠️ 空间排序与期望不符，但继承逻辑正确")
    
    return True

def test_regression_frames():
    """回归测试：确保修复不影响其他帧"""
    print("\n🧪 回归测试：验证其他关键帧")
    print("="*60)
    
    # 测试关键帧列表
    test_frames = [
        (34, "7→16流转测试"),
        (60, "3→16流转测试"),
        (61, "16→16继承测试"),
        (227, "区域3新卡牌测试"),
        (228, "区域3继承测试")
    ]
    
    success_count = 0
    
    for frame_num, description in test_frames:
        print(f"\n📋 测试frame_{frame_num:05d}: {description}")
        
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            print(f"❌ 无法加载frame_{frame_num:05d}")
            continue
        
        # 基本验证：检查是否有有效的数字孪生ID
        shapes = frame_data.get('shapes', [])
        valid_ids = 0
        
        for shape in shapes:
            twin_id = get_digital_twin_id(shape)
            if twin_id and twin_id != 'None' and not twin_id.startswith('虚拟'):
                valid_ids += 1
        
        if valid_ids > 0:
            print(f"✅ frame_{frame_num:05d}正常：{valid_ids}个有效数字孪生ID")
            success_count += 1
        else:
            print(f"❌ frame_{frame_num:05d}异常：无有效数字孪生ID")
    
    print(f"\n📊 回归测试结果：{success_count}/{len(test_frames)}个测试通过")
    return success_count == len(test_frames)

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 修复建议")
    print("="*60)
    
    recommendations = [
        "1. 修改SimpleInheritor的_process_region_6_priority_inheritance方法",
        "   - 添加跨区域继承逻辑，支持3→6流转",
        "   - 在本区域继承失败时，尝试从区域3继承相同基础标签的卡牌",
        "",
        "2. 增强RegionTransitioner的3→6流转逻辑",
        "   - 确保3→6流转的卡牌能够正确标记",
        "   - 防止被SimpleInheritor覆盖",
        "",
        "3. 调整模块调用顺序（可选）",
        "   - 考虑让RegionTransitioner先于SimpleInheritor执行",
        "   - 或者在两个模块间增加协调机制",
        "",
        "4. 添加详细的日志记录",
        "   - 记录3→6流转的详细过程",
        "   - 便于调试和验证修复效果"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """主函数"""
    print("🔍 Frame_00360→Frame_00361继承问题修复验证")
    print("="*80)
    
    # 执行主要测试
    main_test_passed = test_frame_360_361_inheritance()
    
    # 执行回归测试
    regression_test_passed = test_regression_frames()
    
    # 生成修复建议
    generate_fix_recommendations()
    
    # 总结
    print("\n📊 测试总结")
    print("="*60)
    
    if main_test_passed:
        print("✅ 主要功能测试：通过")
    else:
        print("❌ 主要功能测试：失败")
    
    if regression_test_passed:
        print("✅ 回归测试：通过")
    else:
        print("⚠️ 回归测试：部分失败")
    
    if main_test_passed and regression_test_passed:
        print("\n🎉 所有测试通过！修复方案有效。")
    else:
        print("\n⚠️ 存在问题，需要进一步修复。")

if __name__ == "__main__":
    main()

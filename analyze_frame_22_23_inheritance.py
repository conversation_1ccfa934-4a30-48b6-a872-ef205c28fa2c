#!/usr/bin/env python3
"""
Frame_00022 到 Frame_00023 继承问题分析脚本
专门分析"六"卡牌的变化情况，确定是遮挡原因还是空间匹配原因
"""

import json
import os
from typing import Dict, List, Any
from dataclasses import dataclass
import math

@dataclass
class CardInfo:
    """卡牌信息"""
    label: str
    bbox: List[float]
    group_id: int
    center_x: float = 0
    center_y: float = 0
    
    def __post_init__(self):
        if len(self.bbox) >= 4:
            self.center_x = (self.bbox[0] + self.bbox[2]) / 2
            self.center_y = (self.bbox[1] + self.bbox[3]) / 2

def load_frame_data(frame_path: str) -> Dict[str, Any]:
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {frame_path}: {e}")
        return {}

def extract_cards_from_frame(frame_data: Dict[str, Any]) -> List[CardInfo]:
    """从帧数据中提取卡牌信息"""
    cards = []
    shapes = frame_data.get('shapes', [])
    
    for shape in shapes:
        label = shape.get('label', '')
        points = shape.get('points', [])
        group_id = shape.get('group_id', 0)
        
        # 转换points为bbox格式
        if len(points) == 4:
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
            
            cards.append(CardInfo(
                label=label,
                bbox=bbox,
                group_id=group_id
            ))
    
    return cards

def filter_cards_by_label_and_region(cards: List[CardInfo], label: str, region: int) -> List[CardInfo]:
    """筛选指定标签和区域的卡牌"""
    return [card for card in cards if card.label == label and card.group_id == region]

def calculate_distance(card1: CardInfo, card2: CardInfo) -> float:
    """计算两张卡牌的中心距离"""
    dx = card1.center_x - card2.center_x
    dy = card1.center_y - card2.center_y
    return math.sqrt(dx * dx + dy * dy)

def analyze_spatial_changes(cards_22: List[CardInfo], cards_23: List[CardInfo]) -> Dict[str, Any]:
    """分析空间变化"""
    analysis = {
        "frame_22_count": len(cards_22),
        "frame_23_count": len(cards_23),
        "position_changes": [],
        "missing_cards": [],
        "new_cards": [],
        "spatial_matching": []
    }
    
    # 分析位置变化
    print(f"\n📍 Frame_00022 中的'六'卡牌:")
    for i, card in enumerate(cards_22):
        print(f"  {i+1}. 位置: ({card.center_x:.2f}, {card.center_y:.2f}), 区域: {card.group_id}")
    
    print(f"\n📍 Frame_00023 中的'六'卡牌:")
    for i, card in enumerate(cards_23):
        print(f"  {i+1}. 位置: ({card.center_x:.2f}, {card.center_y:.2f}), 区域: {card.group_id}")
    
    # 尝试匹配卡牌（基于位置距离）
    print(f"\n🔗 位置匹配分析 (容差10.0像素):")
    used_23 = set()
    
    for i, card_22 in enumerate(cards_22):
        best_match = None
        best_distance = float('inf')
        best_idx = -1
        
        for j, card_23 in enumerate(cards_23):
            if j in used_23:
                continue
                
            distance = calculate_distance(card_22, card_23)
            if distance < best_distance:
                best_distance = distance
                best_match = card_23
                best_idx = j
        
        if best_match and best_distance <= 10.0:
            used_23.add(best_idx)
            print(f"  ✅ Frame22[{i+1}] → Frame23[{best_idx+1}]: 距离 {best_distance:.2f}px")
            analysis["spatial_matching"].append({
                "frame_22_index": i,
                "frame_23_index": best_idx,
                "distance": best_distance,
                "matched": True
            })
        else:
            print(f"  ❌ Frame22[{i+1}] → 无匹配: 最近距离 {best_distance:.2f}px (超出容差)")
            analysis["missing_cards"].append({
                "frame_22_index": i,
                "position": (card_22.center_x, card_22.center_y),
                "closest_distance": best_distance
            })
    
    # 检查Frame_00023中未匹配的卡牌
    for j, card_23 in enumerate(cards_23):
        if j not in used_23:
            print(f"  🆕 Frame23[{j+1}] → 新卡牌: 位置 ({card_23.center_x:.2f}, {card_23.center_y:.2f})")
            analysis["new_cards"].append({
                "frame_23_index": j,
                "position": (card_23.center_x, card_23.center_y)
            })
    
    return analysis

def analyze_surrounding_cards(frame_data: Dict[str, Any], target_positions: List[tuple]) -> List[Dict]:
    """分析目标位置周围的其他卡牌"""
    all_cards = extract_cards_from_frame(frame_data)
    surrounding_analysis = []
    
    for pos in target_positions:
        target_x, target_y = pos
        nearby_cards = []
        
        for card in all_cards:
            distance = math.sqrt((card.center_x - target_x)**2 + (card.center_y - target_y)**2)
            if distance <= 50.0:  # 50像素范围内
                nearby_cards.append({
                    "label": card.label,
                    "position": (card.center_x, card.center_y),
                    "distance": distance,
                    "group_id": card.group_id
                })
        
        # 按距离排序
        nearby_cards.sort(key=lambda x: x["distance"])
        surrounding_analysis.append({
            "target_position": pos,
            "nearby_cards": nearby_cards[:5]  # 最近的5张卡牌
        })
    
    return surrounding_analysis

def main():
    """主分析函数"""
    print("🔍 Frame_00022 到 Frame_00023 '六'卡牌继承问题分析")
    print("=" * 80)
    
    # 加载原始标注数据
    frame_22_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00022.json"
    frame_23_path = "legacy_assets/ceshi/calibration_gt/labels/frame_00023.json"
    
    if not os.path.exists(frame_22_path):
        print(f"❌ 文件不存在: {frame_22_path}")
        return
    
    if not os.path.exists(frame_23_path):
        print(f"❌ 文件不存在: {frame_23_path}")
        return
    
    frame_22_data = load_frame_data(frame_22_path)
    frame_23_data = load_frame_data(frame_23_path)
    
    if not frame_22_data or not frame_23_data:
        print("❌ 数据加载失败")
        return
    
    # 提取所有卡牌
    all_cards_22 = extract_cards_from_frame(frame_22_data)
    all_cards_23 = extract_cards_from_frame(frame_23_data)
    
    print(f"📊 总体统计:")
    print(f"  Frame_00022: {len(all_cards_22)} 张卡牌")
    print(f"  Frame_00023: {len(all_cards_23)} 张卡牌")
    
    # 筛选区域1的"六"卡牌
    liu_cards_22 = filter_cards_by_label_and_region(all_cards_22, "六", 1)
    liu_cards_23 = filter_cards_by_label_and_region(all_cards_23, "六", 1)
    
    print(f"\n🎯 区域1中的'六'卡牌统计:")
    print(f"  Frame_00022: {len(liu_cards_22)} 张")
    print(f"  Frame_00023: {len(liu_cards_23)} 张")
    
    if len(liu_cards_22) == 0:
        print("❌ Frame_00022中没有找到区域1的'六'卡牌")
        return
    
    # 分析空间变化
    spatial_analysis = analyze_spatial_changes(liu_cards_22, liu_cards_23)
    
    # 分析消失位置周围的卡牌
    missing_positions = [item["position"] for item in spatial_analysis["missing_cards"]]
    if missing_positions:
        print(f"\n🔍 分析消失位置周围的卡牌:")
        surrounding_22 = analyze_surrounding_cards(frame_22_data, missing_positions)
        surrounding_23 = analyze_surrounding_cards(frame_23_data, missing_positions)
        
        for i, pos in enumerate(missing_positions):
            print(f"\n  位置 {pos} 周围分析:")
            print(f"    Frame_00022:")
            for card in surrounding_22[i]["nearby_cards"]:
                print(f"      - {card['label']} 距离{card['distance']:.1f}px 区域{card['group_id']}")
            
            print(f"    Frame_00023:")
            for card in surrounding_23[i]["nearby_cards"]:
                print(f"      - {card['label']} 距离{card['distance']:.1f}px 区域{card['group_id']}")
    
    # 检查是否有其他区域的"六"卡牌
    print(f"\n🔍 检查其他区域的'六'卡牌:")
    for region in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16]:
        liu_22_region = filter_cards_by_label_and_region(all_cards_22, "六", region)
        liu_23_region = filter_cards_by_label_and_region(all_cards_23, "六", region)
        
        if len(liu_22_region) > 0 or len(liu_23_region) > 0:
            print(f"  区域{region}: Frame22={len(liu_22_region)}张, Frame23={len(liu_23_region)}张")
            
            if len(liu_22_region) != len(liu_23_region):
                print(f"    ⚠️  区域{region}中'六'卡牌数量发生变化!")
                
                # 详细分析这个区域的变化
                if len(liu_22_region) > 0:
                    print(f"    Frame22位置:")
                    for card in liu_22_region:
                        print(f"      - ({card.center_x:.2f}, {card.center_y:.2f})")
                
                if len(liu_23_region) > 0:
                    print(f"    Frame23位置:")
                    for card in liu_23_region:
                        print(f"      - ({card.center_x:.2f}, {card.center_y:.2f})")
    
    # 总结分析结果
    print(f"\n📋 分析总结:")
    print(f"  1. 区域1中'六'卡牌: {len(liu_cards_22)} → {len(liu_cards_23)}")
    print(f"  2. 成功匹配: {len(spatial_analysis['spatial_matching'])} 对")
    print(f"  3. 消失卡牌: {len(spatial_analysis['missing_cards'])} 张")
    print(f"  4. 新增卡牌: {len(spatial_analysis['new_cards'])} 张")
    
    if len(spatial_analysis['missing_cards']) > 0:
        print(f"\n❓ 可能的原因:")
        print(f"  - 位置容差不足 (当前10.0px)")
        print(f"  - 卡牌流转到其他区域")
        print(f"  - 检测算法遗漏")
        print(f"  - 标注数据不一致")
    
    # 建议
    print(f"\n💡 建议:")
    if len(spatial_analysis['missing_cards']) > 0:
        print(f"  1. 检查位置容差设置 (考虑增加到15-20px)")
        print(f"  2. 验证区域流转逻辑")
        print(f"  3. 检查原始标注数据的一致性")
    else:
        print(f"  1. 当前位置匹配逻辑正常")
        print(f"  2. 问题可能在继承器的其他逻辑中")

if __name__ == "__main__":
    main()

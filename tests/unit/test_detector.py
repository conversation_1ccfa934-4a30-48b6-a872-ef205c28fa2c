#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌检测测试脚本
功能：
1. 测试单张图像检测
2. 测试视频检测
3. 测试批量图像检测
4. 评估检测性能
"""

import os
import sys
import yaml
import time
import argparse
import logging
import numpy as np
import cv2
from pathlib import Path
from typing import List, Dict, Any, Optional
import matplotlib.pyplot as plt

# 添加项目根目录到系统路径
ROOT_DIR = Path(__file__).parent.parent.parent
sys.path.append(str(ROOT_DIR))

# 导入检测器
from src.core.detect import CardDetector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("DetectorTest")


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def test_single_image(detector: CardDetector, image_path: str, output_dir: str) -> None:
    """测试单张图像检测"""
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        logger.error(f"无法读取图像: {image_path}")
        return
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行检测
    detections = detector.detect_image(image)
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    logger.info(f"检测耗时: {elapsed_time:.4f}秒")
    
    # 可视化结果
    vis_image = detector._visualize_detections(image.copy(), detections)
    
    # 保存结果
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, os.path.basename(image_path))
    cv2.imwrite(output_path, vis_image)
    logger.info(f"检测结果已保存至: {output_path}")
    
    # 打印检测结果
    logger.info(f"检测到 {len(detections)} 个卡牌:")
    for i, det in enumerate(detections):
        logger.info(f"  {i+1}. {det['class_name']} ({det['confidence']:.2f})")


def test_batch_images(detector: CardDetector, image_dir: str, output_dir: str, batch_size: int = 4) -> None:
    """测试批量图像检测"""
    # 获取所有图像文件
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        image_files.extend(list(Path(image_dir).glob(f"*{ext}")))
    
    if not image_files:
        logger.error(f"未找到图像文件: {image_dir}")
        return
    
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    # 读取图像
    images = []
    for img_file in image_files:
        img = cv2.imread(str(img_file))
        if img is not None:
            images.append(img)
    
    if not images:
        logger.error("没有有效的图像")
        return
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行批量检测
    all_detections = detector.detect_batch(images, batch_size=batch_size)
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    avg_time = elapsed_time / len(images)
    logger.info(f"批量检测耗时: {elapsed_time:.4f}秒, 平均每张: {avg_time:.4f}秒")
    
    # 保存结果
    os.makedirs(output_dir, exist_ok=True)
    
    for i, (img_file, img, detections) in enumerate(zip(image_files, images, all_detections)):
        # 可视化结果
        vis_image = detector._visualize_detections(img.copy(), detections)
        
        # 保存结果
        output_path = os.path.join(output_dir, img_file.name)
        cv2.imwrite(output_path, vis_image)
        
        # 打印检测结果
        logger.info(f"图像 {i+1}/{len(images)} - 检测到 {len(detections)} 个卡牌")


def test_video(detector: CardDetector, video_path: str, output_dir: str) -> None:
    """测试视频检测"""
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置输出路径
    output_path = os.path.join(output_dir, os.path.basename(video_path))
    
    # 执行视频处理
    logger.info(f"开始处理视频: {video_path}")
    json_path = detector.process_video(
        video_path=video_path,
        output_path=output_path,
        visualize=False,
        save_json=True
    )
    
    logger.info(f"视频处理完成，输出: {output_path}")
    if json_path:
        logger.info(f"检测结果已保存至: {json_path}")


def evaluate_performance(detector: CardDetector, test_dir: str, output_dir: str) -> None:
    """评估检测性能"""
    # 获取所有图像文件
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        image_files.extend(list(Path(test_dir).glob(f"*{ext}")))
    
    if not image_files:
        logger.error(f"未找到图像文件: {test_dir}")
        return
    
    logger.info(f"找到 {len(image_files)} 个测试图像")
    
    # 性能指标
    times = []
    detection_counts = []
    
    # 测试每张图像
    for img_file in image_files:
        # 读取图像
        img = cv2.imread(str(img_file))
        if img is None:
            continue
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行检测
        detections = detector.detect_image(img)
        
        # 计算耗时
        elapsed_time = time.time() - start_time
        times.append(elapsed_time)
        detection_counts.append(len(detections))
        
        # 可视化结果
        vis_image = detector._visualize_detections(img.copy(), detections)
        
        # 保存结果
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, img_file.name)
        cv2.imwrite(output_path, vis_image)
    
    # 计算性能指标
    avg_time = np.mean(times)
    std_time = np.std(times)
    avg_detections = np.mean(detection_counts)
    
    logger.info(f"性能评估结果:")
    logger.info(f"  平均检测时间: {avg_time:.4f}秒 (±{std_time:.4f})")
    logger.info(f"  平均检测数量: {avg_detections:.2f}")
    
    # 绘制性能图表
    plt.figure(figsize=(10, 6))
    
    # 检测时间直方图
    plt.subplot(1, 2, 1)
    plt.hist(times, bins=20)
    plt.axvline(float(avg_time), color='r', linestyle='dashed', linewidth=2)
    plt.title(f"检测时间分布 (平均: {avg_time:.4f}秒)")
    plt.xlabel("时间 (秒)")
    plt.ylabel("频率")
    
    # 检测数量直方图
    plt.subplot(1, 2, 2)
    plt.hist(detection_counts, bins=max(detection_counts))
    plt.axvline(float(avg_detections), color='r', linestyle='dashed', linewidth=2)
    plt.title(f"检测数量分布 (平均: {avg_detections:.2f})")
    plt.xlabel("检测数量")
    plt.ylabel("频率")
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "performance.png"))
    logger.info(f"性能图表已保存至: {os.path.join(output_dir, 'performance.png')}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="跑胡子卡牌检测测试工具")
    parser.add_argument("--config", type=str, default="src/config.yaml", help="配置文件路径")
    parser.add_argument("--mode", type=str, choices=["image", "batch", "video", "evaluate"], required=True, 
                        help="测试模式: image(单张图像), batch(批量图像), video(视频), evaluate(性能评估)")
    parser.add_argument("--input", type=str, required=True, help="输入路径 (图像文件、图像目录或视频文件)")
    parser.add_argument("--output", type=str, default="output", help="输出目录")
    parser.add_argument("--batch-size", type=int, default=4, help="批处理大小")
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 初始化检测器
    detector = CardDetector(
        model_path=config["model"]["path"],
        conf_threshold=config["model"]["confidence_threshold"],
        iou_threshold=config["model"]["iou_threshold"],
        device=config["detection"]["device"]
    )
    
    # 根据模式执行测试
    if args.mode == "image":
        test_single_image(detector, args.input, args.output)
    elif args.mode == "batch":
        test_batch_images(detector, args.input, args.output, args.batch_size)
    elif args.mode == "video":
        test_video(detector, args.input, args.output)
    elif args.mode == "evaluate":
        evaluate_performance(detector, args.input, args.output)


if __name__ == "__main__":
    main() 
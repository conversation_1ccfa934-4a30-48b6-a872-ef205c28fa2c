"""
真实场景端到端测试
重构自: real_test.py
"""
import pytest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class TestRealScenario:
    """真实场景测试"""
    
    def setup_method(self):
        """测试前置设置"""
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        self.dt_system = create_digital_twin_system()
        self.CardDetection = CardDetection
    
    def test_system_initialization(self):
        """测试系统初始化"""
        assert self.dt_system is not None, "系统应该成功创建"
        assert hasattr(self.dt_system, 'export_synchronized_dual_format'), \
            "系统应该有双轨输出方法"
    
    def test_single_card_processing(self):
        """测试单张卡牌处理"""
        # 创建单张卡牌测试数据
        detection = self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        
        # 处理数据
        result = self.dt_system.process_frame([detection])
        
        # 验证处理结果
        assert 'digital_twin_cards' in result, "结果应包含数字孪生卡牌"
        card_count = len(result.get('digital_twin_cards', []))
        assert card_count > 0, f"应该有处理结果，实际: {card_count} 张卡牌"
    
    def test_dual_format_output(self):
        """测试双格式输出"""
        # 创建测试数据
        detection = self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        
        # 处理数据
        result = self.dt_system.process_frame([detection])
        
        # 双轨输出
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        
        # 验证输出结构
        assert 'rlcard_format' in dual_result, "应该有RLCard格式输出"
        assert 'anylabeling_format' in dual_result, "应该有AnyLabeling格式输出"
        assert 'consistency_validation' in dual_result, "应该有一致性验证结果"
    
    def test_consistency_validation(self):
        """测试一致性验证"""
        # 创建测试数据
        detection = self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        
        # 处理和输出
        result = self.dt_system.process_frame([detection])
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        
        # 检查一致性
        consistency = dual_result['consistency_validation']
        assert 'consistency_score' in consistency, "应该有一致性分数"
        
        score = consistency.get('consistency_score', 0)
        assert 0 <= score <= 1, f"一致性分数应该在0-1之间，实际: {score}"
    
    def test_output_format_structure(self):
        """测试输出格式结构"""
        # 创建测试数据
        detection = self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        
        # 处理和输出
        result = self.dt_system.process_frame([detection])
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        
        # 检查RLCard格式结构
        rlcard_format = dual_result['rlcard_format']
        assert isinstance(rlcard_format, dict), "RLCard格式应该是字典"
        
        # 检查AnyLabeling格式结构
        anylabeling_format = dual_result['anylabeling_format']
        assert isinstance(anylabeling_format, dict), "AnyLabeling格式应该是字典"
        assert 'shapes' in anylabeling_format, "AnyLabeling应该有shapes字段"
    
    def test_output_counts(self):
        """测试输出数量"""
        # 创建测试数据
        detection = self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        
        # 处理和输出
        result = self.dt_system.process_frame([detection])
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        
        # 检查输出数量
        rlcard_format = dual_result['rlcard_format']
        anylabeling_format = dual_result['anylabeling_format']
        
        hand_count = len(rlcard_format.get('hand', []))
        public_count = len(rlcard_format.get('public', []))
        total_rlcard = hand_count + public_count
        
        shapes_count = len(anylabeling_format.get('shapes', []))
        
        # 验证数量合理性
        assert shapes_count >= 0, f"AnyLabeling标注数应该≥0，实际: {shapes_count}"
        assert total_rlcard >= 0, f"RLCard卡牌数应该≥0，实际: {total_rlcard}"
    
    def test_multiple_cards_processing(self):
        """测试多张卡牌处理"""
        # 创建多张卡牌测试数据
        detections = [
            self.CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator'),
            self.CardDetection('三', [200, 100, 250, 150], 0.90, 2, '手牌_观战方', 'spectator'),
            self.CardDetection('四', [300, 100, 350, 150], 0.88, 3, '出牌区_观战方', 'spectator'),
        ]
        
        # 处理数据
        result = self.dt_system.process_frame(detections)
        
        # 验证处理结果
        card_count = len(result.get('digital_twin_cards', []))
        assert card_count > 0, f"应该有处理结果，实际: {card_count} 张卡牌"
        
        # 双轨输出
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test_multi.jpg')
        
        # 验证输出
        consistency = dual_result['consistency_validation']
        score = consistency.get('consistency_score', 0)
        assert score >= 0, f"一致性分数应该≥0，实际: {score}"
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空输入
        result = self.dt_system.process_frame([])
        assert 'digital_twin_cards' in result, "空输入应该返回有效结果"
        
        # 测试双轨输出
        dual_result = self.dt_system.export_synchronized_dual_format(result, 640, 320, 'test_empty.jpg')
        assert 'consistency_validation' in dual_result, "空输入的双轨输出应该有一致性验证"

def main():
    """兼容原始脚本的主函数"""
    print("开始真实验证...")
    
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print('系统创建成功')
        
        # 检查双轨输出方法
        if hasattr(dt_system, 'export_synchronized_dual_format'):
            print('双轨输出方法存在')
            
            # 创建测试数据
            detection = CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
            
            # 处理数据
            result = dt_system.process_frame([detection])
            card_count = len(result.get('digital_twin_cards', []))
            print(f'处理结果: {card_count} 张卡牌')
            
            # 双轨输出
            dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
            
            # 检查结果
            consistency = dual_result['consistency_validation']
            score = consistency.get('consistency_score', 0)
            print(f'一致性分数: {score}')
            
            # 检查输出格式
            rlcard_format = dual_result['rlcard_format']
            anylabeling_format = dual_result['anylabeling_format']
            
            hand_count = len(rlcard_format.get('hand', []))
            shapes_count = len(anylabeling_format.get('shapes', []))
            
            print(f'RLCard卡牌数: {hand_count}')
            print(f'AnyLabeling标注数: {shapes_count}')
            print('真实验证完成')
            
            return True
        else:
            print('双轨输出方法不存在')
            return False
            
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

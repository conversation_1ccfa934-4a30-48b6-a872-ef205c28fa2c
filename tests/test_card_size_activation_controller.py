"""
测试卡牌尺寸启动控制器功能
验证0.85尺寸阈值的智能启动机制
"""

import unittest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.modules.card_size_activation_controller import (
    CardSizeActivationController,
    CardSizeActivationConfig,
    ActivationDecision,
    SizeBaseline,
    create_card_size_activation_controller
)

class TestCardSizeActivationController(unittest.TestCase):
    """测试卡牌尺寸启动控制器"""
    
    def setUp(self):
        """测试前准备"""
        self.config = CardSizeActivationConfig(
            size_threshold=0.85,
            qualified_ratio_threshold=0.9,
            min_card_count=20,
            baseline_cache_enabled=False,  # 测试时禁用缓存
            enable_size_logging=False      # 测试时禁用日志
        )
        self.controller = CardSizeActivationController(self.config)
        
        # 设置测试用的尺寸基准
        self.controller.size_baseline = SizeBaseline(
            width_median=45.0,
            height_median=60.0,
            area_median=2700.0,
            width_std=5.0,
            height_std=8.0,
            sample_count=100,
            confidence_level=0.95
        )
    
    def test_filter_spectator_hand_cards(self):
        """测试观战方手牌过滤功能"""
        detections = [
            # 有效的观战方手牌
            {"group_id": 1, "label": "二", "bbox": [100, 100, 145, 160]},
            {"group_id": 1, "label": "七", "bbox": [150, 100, 195, 160]},
            {"group_id": 1, "label": "暗", "bbox": [200, 100, 245, 160]},
            
            # 其他区域的卡牌（应被过滤）
            {"group_id": 2, "label": "三", "bbox": [300, 100, 345, 160]},
            {"group_id": 7, "label": "五", "bbox": [400, 100, 445, 160]},
            
            # UI元素（应被过滤）
            {"group_id": 1, "label": "打鸟选择", "bbox": [500, 100, 545, 160]},
            {"group_id": 1, "label": "碰", "bbox": [550, 100, 595, 160]},
            
            # 无效标签（应被过滤）
            {"group_id": 1, "label": "unknown", "bbox": [600, 100, 645, 160]},
            
            # 无效边界框（应被过滤）
            {"group_id": 1, "label": "四", "bbox": [700, 100, 700, 160]},  # 宽度为0
        ]
        
        filtered_cards = self.controller._filter_spectator_hand_cards(detections)
        
        # 应该只有3张有效的观战方手牌
        self.assertEqual(len(filtered_cards), 3)
        
        # 验证过滤结果
        labels = [card["label"] for card in filtered_cards]
        self.assertIn("二", labels)
        self.assertIn("七", labels)
        self.assertIn("暗", labels)
    
    def test_calculate_size_ratio(self):
        """测试尺寸比例计算"""
        # 正常尺寸的卡牌（应该接近1.0）
        normal_card = {"bbox": [100, 100, 145, 160]}  # 45x60 = 2700
        ratio = self.controller._calculate_size_ratio(normal_card)
        self.assertAlmostEqual(ratio, 1.0, places=2)
        
        # 较小尺寸的卡牌
        small_card = {"bbox": [100, 100, 130, 140]}  # 30x40 = 1200
        ratio = self.controller._calculate_size_ratio(small_card)
        self.assertLess(ratio, 0.5)
        
        # 较大尺寸的卡牌
        large_card = {"bbox": [100, 100, 170, 190]}  # 70x90 = 6300
        ratio = self.controller._calculate_size_ratio(large_card)
        self.assertGreater(ratio, 2.0)
        
        # 无效边界框
        invalid_card = {"bbox": [100, 100, 100, 160]}  # 宽度为0
        ratio = self.controller._calculate_size_ratio(invalid_card)
        self.assertEqual(ratio, 0.0)
    
    def test_activation_decision_sufficient_cards_good_size(self):
        """测试充足卡牌数量且尺寸良好的情况"""
        # 创建20张正常尺寸的观战方手牌
        detections = []
        for i in range(20):
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160]  # 45x60正常尺寸
            })
        
        decision = self.controller.should_activate_digital_twin(detections)
        
        # 应该启动数字孪生
        self.assertTrue(decision.should_activate)
        self.assertEqual(decision.card_count, 20)
        self.assertGreaterEqual(decision.qualified_ratio, 0.9)
        self.assertIn("尺寸合格率", decision.reason)
    
    def test_activation_decision_insufficient_cards(self):
        """测试卡牌数量不足的情况"""
        # 创建19张卡牌（少于20张）
        detections = []
        for i in range(19):
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160]
            })
        
        decision = self.controller.should_activate_digital_twin(detections)
        
        # 不应该启动数字孪生
        self.assertFalse(decision.should_activate)
        self.assertEqual(decision.card_count, 19)
        self.assertIn("数量不足", decision.reason)
    
    def test_activation_decision_poor_size_quality(self):
        """测试尺寸质量不佳的情况"""
        detections = []
        
        # 创建20张卡牌，但只有一半尺寸正常
        for i in range(10):
            # 正常尺寸
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160]  # 45x60
            })
            
            # 异常小尺寸
            detections.append({
                "group_id": 1,
                "label": "三",
                "bbox": [100 + i*50, 200, 120 + i*50, 220]  # 20x20
            })
        
        decision = self.controller.should_activate_digital_twin(detections)
        
        # 不应该启动数字孪生（合格率只有50%，低于90%阈值）
        self.assertFalse(decision.should_activate)
        self.assertEqual(decision.card_count, 20)
        self.assertLess(decision.qualified_ratio, 0.9)
    
    def test_size_analysis(self):
        """测试尺寸分析功能"""
        cards = [
            {"bbox": [100, 100, 145, 160]},  # 正常尺寸
            {"bbox": [200, 100, 245, 160]},  # 正常尺寸
            {"bbox": [300, 100, 320, 120]},  # 小尺寸
        ]
        
        analysis = self.controller._analyze_card_sizes(cards)
        
        self.assertEqual(analysis["total_count"], 3)
        self.assertEqual(analysis["qualified_count"], 2)  # 2张正常尺寸
        self.assertAlmostEqual(analysis["qualified_ratio"], 2/3, places=2)
        self.assertEqual(len(analysis["size_ratios"]), 3)
    
    def test_baseline_extraction_from_json(self):
        """测试从JSON文件提取基准"""
        # 创建临时JSON文件
        with tempfile.TemporaryDirectory() as temp_dir:
            json_file = Path(temp_dir) / "test.json"
            
            # 创建测试数据
            test_data = {
                "shapes": [
                    {
                        "group_id": 1,
                        "label": "二",
                        "points": [[100, 100], [145, 100], [145, 160], [100, 160]]
                    },
                    {
                        "group_id": 1,
                        "label": "三",
                        "points": [[200, 100], [245, 100], [245, 160], [200, 160]]
                    },
                    # 非观战方手牌（应被忽略）
                    {
                        "group_id": 2,
                        "label": "四",
                        "points": [[300, 100], [345, 100], [345, 160], [300, 160]]
                    }
                ]
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f)
            
            # Mock查找JSON文件的方法
            with patch.object(self.controller, '_find_existing_json_files', return_value=[json_file]):
                baseline = self.controller._extract_baseline_from_json()
            
            # 验证基准提取结果
            self.assertIsNotNone(baseline)
            self.assertEqual(baseline.width_median, 45.0)
            self.assertEqual(baseline.height_median, 60.0)
            self.assertEqual(baseline.area_median, 2700.0)
            self.assertEqual(baseline.sample_count, 2)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        # 执行几次决策
        detections_good = [
            {"group_id": 1, "label": "二", "bbox": [100 + i*50, 100, 145 + i*50, 160]}
            for i in range(20)
        ]
        
        detections_bad = [
            {"group_id": 1, "label": "二", "bbox": [100 + i*50, 100, 120 + i*50, 120]}
            for i in range(15)
        ]
        
        # 执行决策
        decision1 = self.controller.should_activate_digital_twin(detections_good)
        decision2 = self.controller.should_activate_digital_twin(detections_bad)
        
        # 获取统计信息
        stats = self.controller.get_statistics()
        
        # 验证统计信息
        self.assertEqual(stats["activation_stats"]["total_decisions"], 2)
        self.assertEqual(stats["activation_stats"]["activated_count"], 1)
        self.assertEqual(stats["activation_stats"]["deactivated_count"], 1)
        self.assertGreater(stats["activation_stats"]["average_qualified_ratio"], 0)
    
    def test_factory_function(self):
        """测试工厂函数"""
        controller = create_card_size_activation_controller()
        self.assertIsInstance(controller, CardSizeActivationController)
        
        # 测试自定义配置
        custom_config = CardSizeActivationConfig(size_threshold=0.9)
        controller_custom = create_card_size_activation_controller(custom_config)
        self.assertEqual(controller_custom.config.size_threshold, 0.9)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空检测列表
        decision = self.controller.should_activate_digital_twin([])
        self.assertFalse(decision.should_activate)
        self.assertEqual(decision.card_count, 0)
        
        # 只有非观战方卡牌
        detections = [
            {"group_id": 2, "label": "二", "bbox": [100, 100, 145, 160]},
            {"group_id": 7, "label": "三", "bbox": [200, 100, 245, 160]}
        ]
        decision = self.controller.should_activate_digital_twin(detections)
        self.assertFalse(decision.should_activate)
        self.assertEqual(decision.card_count, 0)
        
        # 只有UI元素
        detections = [
            {"group_id": 1, "label": "打鸟选择", "bbox": [100, 100, 145, 160]},
            {"group_id": 1, "label": "碰", "bbox": [200, 100, 245, 160]}
        ]
        decision = self.controller.should_activate_digital_twin(detections)
        self.assertFalse(decision.should_activate)
        self.assertEqual(decision.card_count, 0)

class TestCardSizeActivationConfig(unittest.TestCase):
    """测试配置类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = CardSizeActivationConfig()
        
        self.assertEqual(config.size_threshold, 0.85)
        self.assertEqual(config.qualified_ratio_threshold, 0.9)
        self.assertEqual(config.min_card_count, 20)
        self.assertTrue(config.preserve_original_data)
        self.assertTrue(config.baseline_cache_enabled)
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = CardSizeActivationConfig(
            size_threshold=0.9,
            qualified_ratio_threshold=0.95,
            min_card_count=25
        )
        
        self.assertEqual(config.size_threshold, 0.9)
        self.assertEqual(config.qualified_ratio_threshold, 0.95)
        self.assertEqual(config.min_card_count, 25)

class TestActivationDecision(unittest.TestCase):
    """测试启动决策数据结构"""
    
    def test_activation_decision_creation(self):
        """测试启动决策创建"""
        decision = ActivationDecision(
            should_activate=True,
            qualified_ratio=0.95,
            card_count=20,
            reason="测试原因",
            preserve_data=True
        )
        
        self.assertTrue(decision.should_activate)
        self.assertEqual(decision.qualified_ratio, 0.95)
        self.assertEqual(decision.card_count, 20)
        self.assertEqual(decision.reason, "测试原因")
        self.assertTrue(decision.preserve_data)

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

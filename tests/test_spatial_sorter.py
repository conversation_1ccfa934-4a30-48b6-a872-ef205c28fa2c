"""
空间排序模块单元测试
验证spatial_sorter.py的各种排序场景
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.spatial_sorter import SpatialSorter, create_spatial_sorter

class TestSpatialSorter(unittest.TestCase):
    """空间排序器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sorter = create_spatial_sorter()
    
    def test_single_card_no_sorting(self):
        """测试单张卡牌无需排序"""
        cards = [
            {
                'label': '一',
                'group_id': 1,
                'points': [[100, 100], [150, 100], [150, 150], [100, 150]]
            }
        ]
        
        result = self.sorter.sort_cards_by_spatial_order(cards, 1)
        
        self.assertFalse(result.sorting_applied)
        self.assertEqual(result.sorting_rule, "no_sorting_needed")
        self.assertEqual(len(result.sorted_cards), 1)
        self.assertEqual(result.sorted_cards[0]['label'], '一')
    
    def test_bottom_to_top_left_to_right_sorting(self):
        """测试手牌区从下到上、从左到右排序"""
        # 创建三张"一"牌，模拟frame_00002的情况
        cards = [
            {
                'label': '一',
                'group_id': 1,
                'points': [[200, 200], [250, 200], [250, 250], [200, 250]]  # 中间位置
            },
            {
                'label': '一', 
                'group_id': 1,
                'points': [[200, 250], [250, 250], [250, 300], [200, 300]]  # 最下面位置
            },
            {
                'label': '一',
                'group_id': 1, 
                'points': [[200, 150], [250, 150], [250, 200], [200, 200]]  # 最上面位置
            }
        ]
        
        result = self.sorter.sort_cards_by_spatial_order(cards, 1)
        
        self.assertTrue(result.sorting_applied)
        self.assertEqual(result.sorting_rule, "bottom_to_top_left_to_right")
        self.assertEqual(len(result.sorted_cards), 3)
        
        # 验证排序结果：应该是从下到上
        sorted_cards = result.sorted_cards
        
        # 第一张应该是最下面的（Y坐标最大）
        first_card_points = sorted_cards[0]['points']
        first_y_bottom = max([p[1] for p in first_card_points])
        
        # 第二张应该是中间的
        second_card_points = sorted_cards[1]['points']
        second_y_bottom = max([p[1] for p in second_card_points])
        
        # 第三张应该是最上面的（Y坐标最小）
        third_card_points = sorted_cards[2]['points']
        third_y_bottom = max([p[1] for p in third_card_points])
        
        # 验证从下到上的顺序
        self.assertGreater(first_y_bottom, second_y_bottom)
        self.assertGreater(second_y_bottom, third_y_bottom)
        
        print(f"排序结果验证:")
        print(f"第1张: 底部y={first_y_bottom} (应该是最下面)")
        print(f"第2张: 底部y={second_y_bottom} (应该是中间)")
        print(f"第3张: 底部y={third_y_bottom} (应该是最上面)")
    
    def test_left_to_right_top_to_bottom_sorting(self):
        """测试从左到右、从上到下排序"""
        cards = [
            {
                'label': '二',
                'group_id': 3,
                'points': [[300, 100], [350, 100], [350, 150], [300, 150]]  # 右上
            },
            {
                'label': '二',
                'group_id': 3,
                'points': [[200, 100], [250, 100], [250, 150], [200, 150]]  # 左上
            },
            {
                'label': '二',
                'group_id': 3,
                'points': [[200, 200], [250, 200], [250, 250], [200, 250]]  # 左下
            }
        ]
        
        result = self.sorter.sort_cards_by_spatial_order(cards, 3)
        
        self.assertTrue(result.sorting_applied)
        self.assertEqual(result.sorting_rule, "left_to_right_top_to_bottom")
        
        # 验证排序结果：应该是左上、右上、左下
        sorted_cards = result.sorted_cards
        
        # 第一张应该是左上
        first_x = min([p[0] for p in sorted_cards[0]['points']])
        first_y = min([p[1] for p in sorted_cards[0]['points']])
        
        # 第二张应该是右上
        second_x = min([p[0] for p in sorted_cards[1]['points']])
        second_y = min([p[1] for p in sorted_cards[1]['points']])
        
        # 第三张应该是左下
        third_x = min([p[0] for p in sorted_cards[2]['points']])
        third_y = min([p[1] for p in sorted_cards[2]['points']])
        
        # 验证排序逻辑
        self.assertEqual(first_y, second_y)  # 同一行
        self.assertLess(first_x, second_x)   # 左到右
        self.assertGreater(third_y, first_y) # 上到下
    
    def test_frame_00002_scenario(self):
        """测试frame_00002的实际场景"""
        # 模拟frame_00002.json中三张"一"牌的实际坐标
        cards = [
            {
                'label': '一',
                'group_id': 1,
                'points': [[200.47, 178.17], [250.47, 178.17], [250.47, 228.17], [200.47, 228.17]]  # Y=178.17 最上面
            },
            {
                'label': '一',
                'group_id': 1, 
                'points': [[200.47, 223.47], [250.47, 223.47], [250.47, 273.47], [200.47, 273.47]]  # Y=223.47 中间
            },
            {
                'label': '一',
                'group_id': 1,
                'points': [[200.47, 267.90], [250.47, 267.90], [250.47, 317.90], [200.47, 317.90]]  # Y=267.90 最下面
            }
        ]
        
        result = self.sorter.sort_cards_by_spatial_order(cards, 1)
        
        self.assertTrue(result.sorting_applied)
        self.assertEqual(result.sorting_rule, "bottom_to_top_left_to_right")
        
        # 验证排序结果：应该按从下到上的顺序
        sorted_cards = result.sorted_cards
        
        # 提取Y坐标（使用底部坐标）
        y_bottoms = []
        for card in sorted_cards:
            points = card['points']
            y_bottom = max([p[1] for p in points])
            y_bottoms.append(y_bottom)
        
        # 验证从下到上排序
        self.assertGreater(y_bottoms[0], y_bottoms[1])  # 第1张比第2张更靠下
        self.assertGreater(y_bottoms[1], y_bottoms[2])  # 第2张比第3张更靠下
        
        print(f"Frame_00002场景验证:")
        print(f"第1张: 底部y={y_bottoms[0]} -> 应该分配1一")
        print(f"第2张: 底部y={y_bottoms[1]} -> 应该分配2一") 
        print(f"第3张: 底部y={y_bottoms[2]} -> 应该分配3一")
    
    def test_sort_cards_by_type_and_region(self):
        """测试按类型和区域分组排序"""
        cards = [
            # 区域1的两张"一"牌
            {
                'label': '一',
                'group_id': 1,
                'points': [[200, 200], [250, 200], [250, 250], [200, 250]]
            },
            {
                'label': '一',
                'group_id': 1,
                'points': [[200, 150], [250, 150], [250, 200], [200, 200]]
            },
            # 区域1的一张"二"牌
            {
                'label': '二',
                'group_id': 1,
                'points': [[300, 100], [350, 100], [350, 150], [300, 150]]
            },
            # 区域3的一张"三"牌
            {
                'label': '三',
                'group_id': 3,
                'points': [[400, 100], [450, 100], [450, 150], [400, 150]]
            }
        ]
        
        sorted_cards = self.sorter.sort_cards_by_type_and_region(cards)
        
        self.assertEqual(len(sorted_cards), 4)
        
        # 验证区域1的"一"牌被正确排序（从下到上）
        region1_yi_cards = [card for card in sorted_cards if card['label'] == '一' and card['group_id'] == 1]
        self.assertEqual(len(region1_yi_cards), 2)
        
        # 第一张"一"牌应该是更靠下的那张
        first_yi_y = max([p[1] for p in region1_yi_cards[0]['points']])
        second_yi_y = max([p[1] for p in region1_yi_cards[1]['points']])
        self.assertGreater(first_yi_y, second_yi_y)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

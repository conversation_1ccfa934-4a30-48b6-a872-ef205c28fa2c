#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试匹配算法
验证YOLO检测与zhuangtaiquyu标注的匹配是否正确
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector


def calculate_iou(bbox1, bbox2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    if union_area <= 0:
        return 0.0
    
    return inter_area / union_area


def load_annotation(dataset_id: str, frame_name: str):
    """加载标注文件"""
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    json_file = base_path / "labels" / "train" / dataset_id / f"{frame_name}.json"
    
    if not json_file.exists():
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def extract_ground_truth_cards(annotation):
    """从标注中提取卡牌信息"""
    cards = []
    
    if 'shapes' not in annotation:
        return cards
    
    for shape in annotation['shapes']:
        if shape.get('shape_type') == 'rectangle':
            points = shape.get('points', [])
            if len(points) >= 4:
                # 4个点的矩形格式
                all_x = [p[0] for p in points]
                all_y = [p[1] for p in points]
                x1, x2 = min(all_x), max(all_x)
                y1, y2 = min(all_y), max(all_y)
                bbox = [x1, y1, x2, y2]
            elif len(points) >= 2:
                # 2个点格式
                bbox = [points[0][0], points[0][1], points[1][0], points[1][1]]
            else:
                continue
                
            card_info = {
                'label': shape.get('label', ''),
                'group_id': shape.get('group_id', 0),
                'bbox': bbox
            }
            cards.append(card_info)
    
    return cards


def test_optimal_matching():
    """测试最优匹配算法"""
    print("🔍 测试最优匹配算法")
    print("="*50)
    
    # 初始化检测器
    detector = CardDetector("best.pt", enable_validation=False)
    
    # 测试一个样本
    dataset_id = "1"
    frame_name = "frame_00000"
    
    # 加载图片
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    img_path = base_path / "images" / "train" / dataset_id / f"{frame_name}.jpg"
    
    image = cv2.imread(str(img_path))
    if image is None:
        print(f"❌ 无法读取图片")
        return
    
    print(f"📷 图片尺寸: {image.shape}")
    
    # YOLO检测
    detections = detector.detect_image(image)
    print(f"\n🎯 YOLO检测结果 ({len(detections)}个)")
    
    # 加载标注
    annotation = load_annotation(dataset_id, frame_name)
    if not annotation:
        print(f"❌ 无法加载标注文件")
        return
    
    ground_truth_cards = extract_ground_truth_cards(annotation)
    print(f"📋 标注结果 ({len(ground_truth_cards)}个)")
    
    # 转换检测结果的边界框格式
    det_bboxes = []
    for det in detections:
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        det_bboxes.append([x, y, x + w, y + h])
    
    # 计算所有可能的IoU组合
    print(f"\n🔄 计算IoU矩阵...")
    iou_matrix = []
    for i, det_bbox in enumerate(det_bboxes):
        iou_row = []
        for j, gt in enumerate(ground_truth_cards):
            gt_bbox = gt['bbox']
            iou = calculate_iou(det_bbox, gt_bbox)
            iou_row.append(iou)
        iou_matrix.append(iou_row)
    
    # 显示高IoU的匹配
    print(f"\n📊 高IoU匹配 (IoU > 0.5):")
    high_iou_count = 0
    for i in range(len(detections)):
        for j in range(len(ground_truth_cards)):
            iou = iou_matrix[i][j]
            if iou > 0.5:
                det_label = detections[i].get('label', 'unknown')
                gt_label = ground_truth_cards[j]['label']
                print(f"   检测{i}({det_label}) <-> 标注{j}({gt_label}): IoU={iou:.3f}")
                high_iou_count += 1
    
    print(f"\n📈 统计:")
    print(f"   高IoU匹配数量: {high_iou_count}")
    print(f"   检测数量: {len(detections)}")
    print(f"   标注数量: {len(ground_truth_cards)}")
    
    # 使用贪心算法进行最优匹配
    print(f"\n🎯 使用贪心算法进行最优匹配 (IoU阈值=0.8):")
    
    used_detections = set()
    used_ground_truth = set()
    matched_pairs = []
    
    # 按IoU从高到低排序所有可能的匹配
    all_matches = []
    for det_idx in range(len(detections)):
        for gt_idx in range(len(ground_truth_cards)):
            iou = iou_matrix[det_idx][gt_idx]
            if iou >= 0.8:
                all_matches.append((det_idx, gt_idx, iou))
    
    # 按IoU降序排序
    all_matches.sort(key=lambda x: x[2], reverse=True)
    
    print(f"   候选匹配数量 (IoU >= 0.8): {len(all_matches)}")
    
    # 贪心匹配
    for det_idx, gt_idx, iou in all_matches:
        if det_idx not in used_detections and gt_idx not in used_ground_truth:
            matched_pairs.append({
                'detection': detections[det_idx],
                'ground_truth': ground_truth_cards[gt_idx],
                'iou': iou
            })
            used_detections.add(det_idx)
            used_ground_truth.add(gt_idx)
            
            det_label = detections[det_idx].get('label', 'unknown')
            gt_label = ground_truth_cards[gt_idx]['label']
            print(f"   ✅ 匹配: 检测{det_idx}({det_label}) <-> 标注{gt_idx}({gt_label}) (IoU={iou:.3f})")
    
    print(f"\n📊 最终匹配结果:")
    print(f"   成功匹配: {len(matched_pairs)}")
    print(f"   未匹配检测: {len(detections) - len(used_detections)}")
    print(f"   未匹配标注: {len(ground_truth_cards) - len(used_ground_truth)}")
    
    if len(matched_pairs) > 0:
        avg_iou = sum(pair['iou'] for pair in matched_pairs) / len(matched_pairs)
        print(f"   平均IoU: {avg_iou:.3f}")
        print(f"   匹配率: {len(matched_pairs) / min(len(detections), len(ground_truth_cards)):.1%}")
    
    return matched_pairs


if __name__ == "__main__":
    print("🔍 测试匹配算法")
    print("="*60)
    
    try:
        matched_pairs = test_optimal_matching()
        
        if len(matched_pairs) > 0:
            print(f"\n🎉 匹配算法测试成功！")
        else:
            print(f"\n❌ 匹配算法测试失败！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

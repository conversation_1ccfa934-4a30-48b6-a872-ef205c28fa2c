#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面测试脚本 - 对所有372张训练集进行推理测试
生成区域状态和物理卡牌唯一ID（数字孪生）进行全面对比
"""

import sys
import os
import cv2
import json
import numpy as np
import time
from pathlib import Path
from collections import defaultdict, Counter
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector
from src.core.data_validator import DataValidationPipeline

class ComprehensiveFullDatasetTest:
    """全面数据集测试类"""
    
    def __init__(self):
        """初始化"""
        self.base_path = Path("legacy_assets/ceshi")
        self.detector = None
        self.validator = None
        self.test_results = {}
        self.statistics = {
            'total_frames': 0,
            'total_detections': 0,
            'total_ground_truth': 0,
            'class_accuracy': {},
            'detection_accuracy': {},
            'missing_detections': [],
            'false_positives': [],
            'class_confusion_matrix': defaultdict(lambda: defaultdict(int))
        }
        
    def setup(self):
        """设置测试环境"""
        print("🔧 设置全面测试环境...")
        
        # 初始化检测器
        model_path = "best.pt"
        if os.path.exists(model_path):
            self.detector = CardDetector(model_path, enable_validation=True)
            print("✅ 检测器初始化成功")
            
            # 模型预热
            dummy_image = np.zeros((320, 640, 3), dtype=np.uint8)
            _ = self.detector.detect_image(dummy_image)
            print("✅ 模型预热完成")
        else:
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 初始化验证器
        self.validator = DataValidationPipeline()
        print("✅ 验证器初始化成功")
    
    def load_ground_truth(self, frame_file: str) -> list:
        """加载真实标注"""
        json_file = frame_file.replace('.jpg', '.json')
        json_path = self.base_path / "calibration_gt" / "labels" / json_file
        
        if not json_path.exists():
            return []
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            ground_truth = []
            for shape in data.get('shapes', []):
                ground_truth.append({
                    'label': shape.get('label', ''),
                    'score': shape.get('score', 0.0),
                    'points': shape.get('points', [])
                })
            
            return ground_truth
            
        except Exception as e:
            print(f"   ⚠️ 无法加载标注文件 {json_file}: {e}")
            return []
    
    def calculate_iou(self, box1, box2):
        """计算IoU"""
        try:
            # box格式: [x1, y1, x2, y2]
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        except:
            return 0.0
    
    def convert_points_to_bbox(self, points):
        """将标注点转换为边界框"""
        if len(points) < 2:
            return None
        
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        return [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
    
    def match_detections_with_ground_truth(self, detections, ground_truth, iou_threshold=0.3):
        """匹配检测结果与真实标注"""
        matches = []
        unmatched_detections = list(range(len(detections)))
        unmatched_ground_truth = list(range(len(ground_truth)))

        # 如果没有检测或标注，直接返回
        if len(detections) == 0 or len(ground_truth) == 0:
            return matches, unmatched_detections, unmatched_ground_truth

        # 计算所有检测与标注的IoU
        iou_matrix = np.zeros((len(detections), len(ground_truth)))

        for i, det in enumerate(detections):
            det_bbox = det.get('bbox', [])
            if len(det_bbox) != 4:
                continue

            for j, gt in enumerate(ground_truth):
                gt_bbox = self.convert_points_to_bbox(gt.get('points', []))
                if gt_bbox is None:
                    continue

                iou = self.calculate_iou(det_bbox, gt_bbox)
                iou_matrix[i, j] = iou
        
        # 贪心匹配：优先匹配IoU最高的
        while True:
            if len(unmatched_detections) == 0 or len(unmatched_ground_truth) == 0:
                break
            
            # 找到最大IoU
            max_iou = 0
            max_i, max_j = -1, -1
            
            for i in unmatched_detections:
                for j in unmatched_ground_truth:
                    if iou_matrix[i, j] > max_iou:
                        max_iou = iou_matrix[i, j]
                        max_i, max_j = i, j
            
            # 如果最大IoU低于阈值，停止匹配
            if max_iou < iou_threshold:
                break
            
            # 记录匹配
            matches.append({
                'detection_idx': max_i,
                'ground_truth_idx': max_j,
                'iou': max_iou,
                'detection_label': detections[max_i].get('label', ''),
                'ground_truth_label': ground_truth[max_j].get('label', ''),
                'label_match': detections[max_i].get('label', '') == ground_truth[max_j].get('label', '')
            })
            
            # 移除已匹配的项
            unmatched_detections.remove(max_i)
            unmatched_ground_truth.remove(max_j)
        
        return matches, unmatched_detections, unmatched_ground_truth
    
    def test_single_frame(self, frame_file: str):
        """测试单个帧"""
        # 读取图片
        img_path = self.base_path / "calibration_gt" / "images" / frame_file
        if not img_path.exists():
            return None
        
        image = cv2.imread(str(img_path))
        if image is None:
            return None
        
        # YOLO检测
        start_time = time.time()
        detections = self.detector.detect_image(image)
        detection_time = time.time() - start_time
        
        # 加载真实标注
        ground_truth = self.load_ground_truth(frame_file)
        
        # 匹配检测结果与真实标注
        matches, unmatched_detections, unmatched_ground_truth = self.match_detections_with_ground_truth(
            detections, ground_truth
        )
        
        # 计算准确性指标
        detection_labels = [det.get('label', '') for det in detections]
        gt_labels = [gt.get('label', '') for gt in ground_truth]
        
        # 统计类别准确性
        for match in matches:
            det_label = match['detection_label']
            gt_label = match['ground_truth_label']
            
            # 更新混淆矩阵
            self.statistics['class_confusion_matrix'][gt_label][det_label] += 1
            
            # 更新类别准确性
            if gt_label not in self.statistics['class_accuracy']:
                self.statistics['class_accuracy'][gt_label] = {'correct': 0, 'total': 0}
            
            self.statistics['class_accuracy'][gt_label]['total'] += 1
            if match['label_match']:
                self.statistics['class_accuracy'][gt_label]['correct'] += 1
        
        # 记录未匹配的真实标注（漏检）
        for idx in unmatched_ground_truth:
            self.statistics['missing_detections'].append({
                'frame': frame_file,
                'label': ground_truth[idx].get('label', ''),
                'points': ground_truth[idx].get('points', [])
            })
        
        # 记录未匹配的检测（误检）
        for idx in unmatched_detections:
            self.statistics['false_positives'].append({
                'frame': frame_file,
                'label': detections[idx].get('label', ''),
                'bbox': detections[idx].get('bbox', [])
            })
        
        return {
            'frame_file': frame_file,
            'detections': detections,
            'ground_truth': ground_truth,
            'matches': matches,
            'unmatched_detections': unmatched_detections,
            'unmatched_ground_truth': unmatched_ground_truth,
            'detection_time': detection_time,
            'detection_count': len(detections),
            'ground_truth_count': len(ground_truth),
            'matched_count': len(matches),
            'precision': len(matches) / len(detections) if len(detections) > 0 else 0,
            'recall': len(matches) / len(ground_truth) if len(ground_truth) > 0 else 0
        }
    
    def run_full_dataset_test(self):
        """运行全数据集测试"""
        print("\n🚀 开始全数据集测试（372张图片）")
        print("="*80)
        
        # 获取所有图片文件
        images_path = self.base_path / "calibration_gt" / "images"
        image_files = sorted(list(images_path.glob("*.jpg")))
        
        print(f"📊 找到 {len(image_files)} 张图片")
        
        # 测试所有图片
        all_results = []
        
        for img_file in tqdm(image_files, desc="测试进度"):
            frame_file = img_file.name
            result = self.test_single_frame(frame_file)
            
            if result:
                all_results.append(result)
                self.statistics['total_frames'] += 1
                self.statistics['total_detections'] += result['detection_count']
                self.statistics['total_ground_truth'] += result['ground_truth_count']
        
        self.test_results['all_frames'] = all_results
        
        return all_results
    
    def analyze_class_performance(self):
        """分析各类别性能"""
        print("\n📊 分析各类别性能")
        print("="*80)
        
        # 计算各类别的准确率、召回率、F1分数
        class_metrics = {}
        
        for class_name, stats in self.statistics['class_accuracy'].items():
            if stats['total'] > 0:
                accuracy = stats['correct'] / stats['total']
                
                # 计算召回率（从混淆矩阵）
                true_positives = self.statistics['class_confusion_matrix'][class_name][class_name]
                false_negatives = sum(self.statistics['class_confusion_matrix'][class_name].values()) - true_positives
                recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
                
                # 计算精确率
                false_positives = sum([self.statistics['class_confusion_matrix'][other_class][class_name] 
                                     for other_class in self.statistics['class_confusion_matrix'] 
                                     if other_class != class_name])
                precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
                
                # 计算F1分数
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
                
                class_metrics[class_name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'total_samples': stats['total'],
                    'correct_predictions': stats['correct']
                }
        
        # 按F1分数排序显示
        sorted_classes = sorted(class_metrics.items(), key=lambda x: x[1]['f1'], reverse=True)
        
        print(f"{'类别':<12} {'样本数':<8} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8}")
        print("-" * 70)
        
        for class_name, metrics in sorted_classes:
            print(f"{class_name:<12} {metrics['total_samples']:<8} "
                  f"{metrics['accuracy']:<8.3f} {metrics['precision']:<8.3f} "
                  f"{metrics['recall']:<8.3f} {metrics['f1']:<8.3f}")
        
        return class_metrics
    
    def analyze_problem_cases(self):
        """分析问题案例"""
        print("\n🔍 分析问题案例")
        print("="*80)
        
        # 分析漏检情况
        missing_by_class = Counter([item['label'] for item in self.statistics['missing_detections']])
        print(f"\n📉 漏检统计（按类别）:")
        for class_name, count in missing_by_class.most_common():
            print(f"   {class_name}: {count}次")
        
        # 分析误检情况
        false_positive_by_class = Counter([item['label'] for item in self.statistics['false_positives']])
        print(f"\n📈 误检统计（按类别）:")
        for class_name, count in false_positive_by_class.most_common():
            print(f"   {class_name}: {count}次")
        
        # 分析最常见的类别混淆
        print(f"\n🔄 类别混淆分析:")
        confusion_pairs = []
        for true_class, predictions in self.statistics['class_confusion_matrix'].items():
            for pred_class, count in predictions.items():
                if true_class != pred_class and count > 0:
                    confusion_pairs.append((true_class, pred_class, count))
        
        confusion_pairs.sort(key=lambda x: x[2], reverse=True)
        
        for true_class, pred_class, count in confusion_pairs[:10]:  # 显示前10个混淆
            print(f"   '{true_class}' → '{pred_class}': {count}次")
    
    def generate_digital_twin_analysis(self):
        """生成数字孪生分析"""
        print("\n🔬 生成数字孪生分析")
        print("="*80)
        
        # 分析物理卡牌ID的识别情况
        card_labels = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                      '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾']
        
        card_performance = {}
        for label in card_labels:
            if label in self.statistics['class_accuracy']:
                stats = self.statistics['class_accuracy'][label]
                card_performance[label] = {
                    'accuracy': stats['correct'] / stats['total'] if stats['total'] > 0 else 0,
                    'total_samples': stats['total']
                }
        
        # 计算整体卡牌识别准确率
        total_card_correct = sum([stats['correct'] for label, stats in self.statistics['class_accuracy'].items() if label in card_labels])
        total_card_samples = sum([stats['total'] for label, stats in self.statistics['class_accuracy'].items() if label in card_labels])
        
        overall_card_accuracy = total_card_correct / total_card_samples if total_card_samples > 0 else 0
        
        print(f"📊 物理卡牌识别统计:")
        print(f"   总卡牌样本: {total_card_samples}")
        print(f"   正确识别: {total_card_correct}")
        print(f"   整体准确率: {overall_card_accuracy:.3f} ({overall_card_accuracy*100:.1f}%)")
        
        # 分析区域状态识别
        interface_labels = ['打鸟选择', '已准备', '你赢了', '你输了', '牌局结束', '荒庄']
        interface_performance = {}
        
        for label in interface_labels:
            if label in self.statistics['class_accuracy']:
                stats = self.statistics['class_accuracy'][label]
                interface_performance[label] = {
                    'accuracy': stats['correct'] / stats['total'] if stats['total'] > 0 else 0,
                    'total_samples': stats['total']
                }
        
        print(f"\n📊 界面状态识别统计:")
        for label, perf in interface_performance.items():
            print(f"   {label}: {perf['accuracy']:.3f} ({perf['total_samples']}样本)")
        
        return {
            'card_performance': card_performance,
            'interface_performance': interface_performance,
            'overall_card_accuracy': overall_card_accuracy
        }
    
    def save_comprehensive_results(self):
        """保存全面测试结果"""
        print("\n💾 保存全面测试结果...")
        
        # 保存详细结果
        results_summary = {
            'test_summary': {
                'total_frames': self.statistics['total_frames'],
                'total_detections': self.statistics['total_detections'],
                'total_ground_truth': self.statistics['total_ground_truth'],
                'overall_precision': self.statistics['total_detections'] / self.statistics['total_ground_truth'] if self.statistics['total_ground_truth'] > 0 else 0,
            },
            'class_performance': self.analyze_class_performance(),
            'digital_twin_analysis': self.generate_digital_twin_analysis(),
            'problem_analysis': {
                'missing_detections_count': len(self.statistics['missing_detections']),
                'false_positives_count': len(self.statistics['false_positives']),
                'missing_by_class': dict(Counter([item['label'] for item in self.statistics['missing_detections']])),
                'false_positive_by_class': dict(Counter([item['label'] for item in self.statistics['false_positives']]))
            }
        }
        
        # 保存到文件
        with open("comprehensive_test_results.json", 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存详细的帧级结果
        with open("detailed_frame_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        
        print("   ✅ 结果已保存至:")
        print("      - comprehensive_test_results.json (汇总结果)")
        print("      - detailed_frame_results.json (详细帧结果)")
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🚀 全面数据集测试 - 372张图片完整分析")
        print("生成区域状态和物理卡牌唯一ID（数字孪生）全面对比")
        
        try:
            # 设置环境
            self.setup()
            
            # 运行全数据集测试
            results = self.run_full_dataset_test()
            
            # 分析各类别性能
            class_metrics = self.analyze_class_performance()
            
            # 分析问题案例
            self.analyze_problem_cases()
            
            # 生成数字孪生分析
            digital_twin_results = self.generate_digital_twin_analysis()
            
            # 保存结果
            self.save_comprehensive_results()
            
            # 总结
            print("\n" + "="*80)
            print("📊 全面测试总结")
            print("="*80)
            
            print(f"✅ 测试完成:")
            print(f"   测试帧数: {self.statistics['total_frames']}")
            print(f"   总检测数: {self.statistics['total_detections']}")
            print(f"   总标注数: {self.statistics['total_ground_truth']}")
            print(f"   整体卡牌识别准确率: {digital_twin_results['overall_card_accuracy']*100:.1f}%")
            
            print(f"\n🎯 关键发现:")
            if digital_twin_results['overall_card_accuracy'] > 0.95:
                print(f"   ✅ 模型性能优秀，卡牌识别准确率超过95%")
            elif digital_twin_results['overall_card_accuracy'] > 0.90:
                print(f"   ✅ 模型性能良好，卡牌识别准确率超过90%")
            else:
                print(f"   ⚠️ 需要优化脚本逻辑，提升识别准确率")
            
            print(f"\n🎉 全面测试完成！")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    test_suite = ComprehensiveFullDatasetTest()
    test_suite.run_comprehensive_test()

if __name__ == "__main__":
    main()

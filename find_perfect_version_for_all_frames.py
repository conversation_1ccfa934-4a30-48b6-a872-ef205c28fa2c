#!/usr/bin/env python3
"""
系统性测试历史版本，寻找能同时修复所有5个错误帧的完美版本
"""

import sys
import os
import subprocess
import json
from pathlib import Path

def get_test_versions():
    """获取要测试的历史版本列表"""
    return [
        {
            'hash': '62a18a9',
            'message': '修复110成功20250727',
            'description': '已知frame_00179正确，但frame_00124有问题'
        },
        {
            'hash': '320efb9',
            'message': '修复123成功20250727',
            'description': '更早的修复版本'
        },
        {
            'hash': 'ac50658',
            'message': '156成功',
            'description': '修复156的版本'
        },
        {
            'hash': '1633041',
            'message': '60修复完成',
            'description': '修复60的版本，可能frame_00060正确'
        },
        {
            'hash': 'b6e40ad',
            'message': '第1局无错误',
            'description': '更早的稳定版本'
        },
        {
            'hash': '7f49bca',
            'message': '精简继承模块202507261107',
            'description': '精简版本，可能更稳定'
        }
    ]

def test_all_frames_quick(version_hash):
    """快速测试所有错误帧（简化版）"""
    print(f"\n🧪 快速测试版本 {version_hash}")
    print("-" * 50)
    
    try:
        # 切换版本
        subprocess.run(['git', 'checkout', version_hash], 
                      capture_output=True, text=True, cwd='.')
        
        # 清除模块缓存
        modules_to_clear = [
            'src.modules',
            'src.modules.simple_inheritor',
            'src.modules.phase2_integrator'
        ]
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        from src.modules import create_phase2_integrator
        
        results = {}
        
        # 测试frame_00179 (关键测试)
        print("📊 测试frame_00179:")
        system = create_phase2_integrator()
        
        # 前一帧：区域4有"八"
        frame_178_detections = [
            {
                'label': '八',
                'bbox': [301, 18, 339, 142],
                'confidence': 0.88,
                'group_id': 4
            }
        ]
        
        result_178 = system.process_frame(frame_178_detections)
        
        # 当前帧：区域16有4张"八"
        frame_179_detections = [
            {
                'label': '八',
                'bbox': [152, 73, 167, 94],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152, 57, 169, 72],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152, 40, 168, 55],
                'confidence': 0.9,
                'group_id': 16
            },
            {
                'label': '八',
                'bbox': [152, 24, 169, 39],
                'confidence': 0.9,
                'group_id': 16
            }
        ]
        
        result_179 = system.process_frame(frame_179_detections)
        region_16_cards = [card for card in result_179.processed_cards if card.get('group_id') == 16]
        
        expected_ids = ['1八', '2八', '3八', '4八']
        actual_ids = [card.get('twin_id', 'N/A') for card in region_16_cards[:4]]
        
        frame_179_correct = (len(region_16_cards) == 4 and actual_ids == expected_ids)
        results['frame_00179'] = frame_179_correct
        
        print(f"  结果: {len(region_16_cards)}张，期望4张")
        print(f"  ID: {actual_ids}")
        print(f"  状态: {'✅' if frame_179_correct else '❌'}")
        
        # 测试frame_00124 (关键测试)
        print("\n📊 测试frame_00124:")
        system = create_phase2_integrator()
        
        # 读取frame_00124数据
        frame_124_json = Path("legacy_assets/ceshi/calibration_gt/labels/frame_00124.json")
        
        if frame_124_json.exists():
            with open(frame_124_json, 'r', encoding='utf-8') as f:
                frame_124_data = json.load(f)
            
            # 转换为检测数据
            detections = []
            for shape in frame_124_data.get('shapes', []):
                if shape.get('label') in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                                          '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗']:
                    points = shape['points']
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                    
                    detection = {
                        'label': shape['label'],
                        'bbox': bbox,
                        'confidence': 0.9,
                        'group_id': shape.get('group_id')
                    }
                    detections.append(detection)
            
            result_124 = system.process_frame(detections)
            
            # 检查重复ID
            from collections import defaultdict
            all_ids = [card.get('twin_id', 'N/A') for card in result_124.processed_cards if card.get('twin_id') != 'N/A']
            id_counts = defaultdict(int)
            for twin_id in all_ids:
                id_counts[twin_id] += 1
            
            duplicate_ids = {id: count for id, count in id_counts.items() if count > 1}
            frame_124_correct = len(duplicate_ids) == 0
            results['frame_00124'] = frame_124_correct
            
            print(f"  结果: {len(result_124.processed_cards)}张卡牌")
            print(f"  重复ID: {duplicate_ids if duplicate_ids else '无'}")
            print(f"  状态: {'✅' if frame_124_correct else '❌'}")
        else:
            results['frame_00124'] = False
            print("  ❌ 标注文件不存在")
        
        # 快速测试其他帧
        print("\n📊 快速测试其他帧:")
        
        # frame_00018
        system = create_phase2_integrator()
        frame_17_det = [{'label': '拾', 'bbox': [400, 100, 450, 150], 'confidence': 0.9, 'group_id': 7}]
        system.process_frame(frame_17_det)
        
        frame_18_det = [
            {'label': '拾', 'bbox': [152, 73, 167, 94], 'confidence': 0.9, 'group_id': 16},
            {'label': '拾', 'bbox': [152, 57, 169, 72], 'confidence': 0.9, 'group_id': 16},
            {'label': '拾', 'bbox': [152, 40, 168, 55], 'confidence': 0.9, 'group_id': 16}
        ]
        result_18 = system.process_frame(frame_18_det)
        region_16_cards_18 = [card for card in result_18.processed_cards if card.get('group_id') == 16]
        actual_ids_18 = [card.get('twin_id', 'N/A') for card in region_16_cards_18[:3]]
        frame_18_correct = len(set(actual_ids_18)) == 3 and all('拾' in str(id) for id in actual_ids_18)
        results['frame_00018'] = frame_18_correct
        print(f"  frame_00018: {'✅' if frame_18_correct else '❌'} ({actual_ids_18})")
        
        # frame_00028
        system = create_phase2_integrator()
        frame_27_det = [
            {'label': '一', 'bbox': [100, 200, 150, 250], 'confidence': 0.9, 'group_id': 1},
            {'label': '一', 'bbox': [160, 200, 210, 250], 'confidence': 0.9, 'group_id': 1},
            {'label': '一', 'bbox': [220, 200, 270, 250], 'confidence': 0.9, 'group_id': 1}
        ]
        system.process_frame(frame_27_det)
        
        frame_28_det = [
            {'label': '一', 'bbox': [300, 100, 350, 150], 'confidence': 0.9, 'group_id': 6},
            {'label': '一', 'bbox': [300, 160, 350, 210], 'confidence': 0.9, 'group_id': 6},
            {'label': '一', 'bbox': [300, 220, 350, 270], 'confidence': 0.9, 'group_id': 6},
            {'label': '一', 'bbox': [300, 280, 350, 330], 'confidence': 0.9, 'group_id': 6}
        ]
        result_28 = system.process_frame(frame_28_det)
        region_6_cards = [card for card in result_28.processed_cards if card.get('group_id') == 6]
        actual_ids_28 = [card.get('twin_id', 'N/A') for card in region_6_cards[:4]]
        frame_28_correct = len(set(actual_ids_28)) == 4 and all('一' in str(id) for id in actual_ids_28)
        results['frame_00028'] = frame_28_correct
        print(f"  frame_00028: {'✅' if frame_28_correct else '❌'} ({actual_ids_28})")
        
        # frame_00060
        system = create_phase2_integrator()
        frame_60_det = [
            {'label': '二', 'bbox': [152, 73, 167, 94], 'confidence': 0.9, 'group_id': 16},
            {'label': '二', 'bbox': [152, 57, 169, 72], 'confidence': 0.9, 'group_id': 16},
            {'label': '二', 'bbox': [152, 40, 168, 55], 'confidence': 0.9, 'group_id': 16},
            {'label': '二', 'bbox': [152, 24, 169, 39], 'confidence': 0.9, 'group_id': 16}
        ]
        result_60 = system.process_frame(frame_60_det)
        region_16_cards_60 = [card for card in result_60.processed_cards if card.get('group_id') == 16]
        actual_ids_60 = [card.get('twin_id', 'N/A') for card in region_16_cards_60[:4]]
        frame_60_correct = len(set(actual_ids_60)) == 4 and all('二' in str(id) for id in actual_ids_60)
        results['frame_00060'] = frame_60_correct
        print(f"  frame_00060: {'✅' if frame_60_correct else '❌'} ({actual_ids_60})")
        
        # 统计结果
        correct_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        
        print(f"\n📈 版本 {version_hash} 总体结果: {correct_count}/{total_count} 正确")
        
        if correct_count == total_count:
            print(f"🎉 找到完美版本！{version_hash} 同时修复了所有问题")
        
        return results, correct_count == total_count
        
    except Exception as e:
        print(f"❌ 测试版本 {version_hash} 失败: {e}")
        return {}, False

def main():
    """主函数"""
    print("🚀 寻找能同时修复所有5个错误帧的完美版本")
    print("🎯 目标: frame_00018, frame_00028, frame_00060, frame_00124, frame_00179")
    print("=" * 80)
    
    # 保存当前分支
    current_branch = subprocess.run(['git', 'branch', '--show-current'], 
                                   capture_output=True, text=True, cwd='.').stdout.strip()
    
    test_versions = get_test_versions()
    version_results = {}
    perfect_versions = []
    
    try:
        for version in test_versions:
            version_hash = version['hash']
            version_msg = version['message']
            
            print(f"\n{'='*60}")
            print(f"🧪 测试版本: {version_hash}")
            print(f"📝 提交信息: {version_msg}")
            print(f"📋 描述: {version['description']}")
            
            results, is_perfect = test_all_frames_quick(version_hash)
            
            if results:
                version_results[version_hash] = {
                    'message': version_msg,
                    'results': results,
                    'correct_count': sum(1 for r in results.values() if r),
                    'is_perfect': is_perfect
                }
                
                if is_perfect:
                    perfect_versions.append((version_hash, version_msg))
        
        # 分析结果
        print(f"\n📊 历史版本分析结果")
        print("=" * 80)
        
        for version_hash, data in version_results.items():
            correct_count = data['correct_count']
            total_count = len(data['results'])
            status = "🎉 完美" if data['is_perfect'] else f"⚠️ {correct_count}/{total_count}"
            
            print(f"{version_hash}: {status} - {data['message']}")
            
            # 显示详细结果
            for frame, result in data['results'].items():
                print(f"  {frame}: {'✅' if result else '❌'}")
        
        # 总结
        print(f"\n📋 总结")
        print("=" * 60)
        
        if perfect_versions:
            print(f"🎉 找到 {len(perfect_versions)} 个完美版本:")
            for version_hash, version_msg in perfect_versions:
                print(f"  ✅ {version_hash} - {version_msg}")
            
            print(f"\n💡 建议:")
            print(f"1. 使用最新的完美版本: {perfect_versions[-1][0]}")
            print(f"2. 基于该版本进行后续开发")
            print(f"3. 避免引入回归问题")
        else:
            print(f"❌ 未找到完美版本")
            
            # 找到最佳版本
            best_version = max(version_results.items(), 
                             key=lambda x: x[1]['correct_count'])
            best_hash, best_data = best_version
            
            print(f"\n🥈 最佳版本: {best_hash}")
            print(f"   修复情况: {best_data['correct_count']}/5")
            print(f"   提交信息: {best_data['message']}")
            
            print(f"\n💡 建议:")
            print(f"1. 基于最佳版本 {best_hash} 进行修复")
            print(f"2. 针对剩余问题进行精确修复")
            print(f"3. 避免影响已修复的问题")
    
    finally:
        # 恢复到原始分支
        subprocess.run(['git', 'checkout', current_branch], 
                      capture_output=True, text=True, cwd='.')
        print(f"\n🔄 已恢复到原始分支: {current_branch}")

if __name__ == "__main__":
    main()

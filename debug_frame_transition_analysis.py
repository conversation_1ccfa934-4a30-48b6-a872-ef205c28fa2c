#!/usr/bin/env python3
"""
Frame流转分析脚本 - 重点分析Frame_00228到Frame_00229的流转过程

验证重点：
1. Frame_00228的"三"卡牌分布
2. Frame_00228→Frame_00229的流转过程
3. 为什么区域16没有继承区域3的"4三"
4. GlobalIDManager状态变化分析
"""

import json
import sys
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional

def load_frame_data(frame_number: int) -> Optional[Dict]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def analyze_san_cards_detailed(frame_data: Dict, frame_number: int) -> Dict[str, Any]:
    """详细分析"三"卡牌的分布"""
    san_cards = []
    for shape in frame_data.get('shapes', []):
        label = shape.get('label', '')
        if '三' in label:
            card_info = {
                'region': shape.get('group_id'),
                'label': label,
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', []),
                'y_center': (shape.get('points', [[0,0]])[0][1] + shape.get('points', [[0,0]])[2][1]) / 2,
                'x_center': (shape.get('points', [[0,0]])[0][0] + shape.get('points', [[0,0]])[2][0]) / 2
            }
            san_cards.append(card_info)
    
    # 按区域分组
    by_region = defaultdict(list)
    for card in san_cards:
        by_region[card['region']].append(card)
    
    # 对每个区域按Y坐标排序（从下到上）
    for region_cards in by_region.values():
        region_cards.sort(key=lambda x: -x['y_center'])  # 负号表示从下到上
    
    print(f"\n📍 Frame_{frame_number:05d} '三'卡牌详细分布:")
    print(f"  总计: {len(san_cards)}张")
    
    for region, cards in by_region.items():
        region_name = get_region_name(region)
        print(f"\n  区域{region}（{region_name}）: {len(cards)}张")
        for i, card in enumerate(cards):
            pos_info = f"({card['x_center']:.1f}, {card['y_center']:.1f})"
            virtual_info = "虚拟" if card['is_virtual'] else "物理"
            print(f"    {i+1}. {card['twin_id']} ({virtual_info}) - 位置{pos_info}")
    
    return {
        'all_cards': san_cards,
        'by_region': dict(by_region),
        'total_count': len(san_cards)
    }

def get_region_name(region_id: int) -> str:
    """获取区域名称"""
    region_names = {
        1: "手牌_观战方",
        2: "调整_观战方", 
        3: "抓牌_观战方",
        4: "打牌_观战方",
        5: "弃牌_观战方",
        6: "吃碰区_观战方",
        7: "抓牌_对战方",
        8: "打牌_对战方",
        9: "弃牌_对战方",
        10: "打出_对战方",
        11: "最终弃牌_对战方",
        12: "听牌区_观战方",
        13: "听牌区_对战方",
        14: "赢方区域_观战方",
        15: "赢方区域_对战方",
        16: "吃碰区_对战方",
        17: "特殊区域"
    }
    return region_names.get(region_id, f"未知区域{region_id}")

def analyze_transition_228_to_229():
    """分析Frame_00228到Frame_00229的流转"""
    print("=" * 80)
    print("🔄 Frame_00228 → Frame_00229 流转分析")
    print("=" * 80)
    
    frame_228 = load_frame_data(228)
    frame_229 = load_frame_data(229)
    
    if not frame_228 or not frame_229:
        print("❌ 无法加载必要的帧数据")
        return
    
    # 分析两帧的"三"卡牌
    san_228 = analyze_san_cards_detailed(frame_228, 228)
    san_229 = analyze_san_cards_detailed(frame_229, 229)
    
    print(f"\n📊 流转对比:")
    print(f"  Frame_00228: {san_228['total_count']}张'三'卡牌")
    print(f"  Frame_00229: {san_229['total_count']}张'三'卡牌")
    print(f"  变化: {san_229['total_count'] - san_228['total_count']:+d}张")
    
    # 分析具体变化
    print(f"\n🔍 具体变化分析:")
    
    # Frame_00228的分布
    print(f"\n  Frame_00228分布:")
    for region, cards in san_228['by_region'].items():
        region_name = get_region_name(region)
        ids = [card['twin_id'] for card in cards]
        print(f"    区域{region}（{region_name}）: {ids}")
    
    # Frame_00229的分布
    print(f"\n  Frame_00229分布:")
    for region, cards in san_229['by_region'].items():
        region_name = get_region_name(region)
        ids = [card['twin_id'] for card in cards]
        print(f"    区域{region}（{region_name}）: {ids}")
    
    # 分析关键问题
    print(f"\n❗ 关键问题分析:")
    
    # 1. 检查区域3的4三是否存在于两帧
    region_3_228 = san_228['by_region'].get(3, [])
    region_3_229 = san_229['by_region'].get(3, [])
    
    has_4san_228_r3 = any(card['twin_id'] == '4三' for card in region_3_228)
    has_4san_229_r3 = any(card['twin_id'] == '4三' for card in region_3_229)
    
    print(f"  1. 区域3的'4三':")
    print(f"     Frame_00228: {'存在' if has_4san_228_r3 else '不存在'}")
    print(f"     Frame_00229: {'存在' if has_4san_229_r3 else '不存在'}")
    
    # 2. 检查区域16的变化
    region_16_228 = san_228['by_region'].get(16, [])
    region_16_229 = san_229['by_region'].get(16, [])
    
    print(f"\n  2. 区域16的变化:")
    print(f"     Frame_00228: {len(region_16_228)}张")
    if region_16_228:
        ids_228 = [card['twin_id'] for card in region_16_228]
        print(f"       IDs: {ids_228}")
    
    print(f"     Frame_00229: {len(region_16_229)}张")
    if region_16_229:
        ids_229 = [card['twin_id'] for card in region_16_229]
        print(f"       IDs: {ids_229}")
    
    # 3. 检查是否有新增的虚拟三
    has_virtual_san_228 = any('虚拟三' in card['twin_id'] for card in san_228['all_cards'])
    has_virtual_san_229 = any('虚拟三' in card['twin_id'] for card in san_229['all_cards'])
    
    print(f"\n  3. 虚拟三的出现:")
    print(f"     Frame_00228: {'存在' if has_virtual_san_228 else '不存在'}")
    print(f"     Frame_00229: {'存在' if has_virtual_san_229 else '不存在'}")
    
    if has_virtual_san_229 and not has_virtual_san_228:
        print(f"     ⚠️  虚拟三在Frame_00229中首次出现！")

def analyze_id_manager_state():
    """分析GlobalIDManager可能的状态"""
    print("=" * 80)
    print("🧠 GlobalIDManager状态推断分析")
    print("=" * 80)
    
    frame_229 = load_frame_data(229)
    if not frame_229:
        return
    
    # 统计所有"三"相关的ID使用情况
    all_san_ids = []
    for shape in frame_229.get('shapes', []):
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        if '三' in twin_id:
            all_san_ids.append(twin_id)
    
    print(f"\n📊 Frame_00229中所有'三'相关ID:")
    for twin_id in sorted(set(all_san_ids)):
        count = all_san_ids.count(twin_id)
        print(f"  {twin_id}: {count}次使用")
    
    # 分析物理ID的使用情况
    physical_san_ids = ['1三', '2三', '3三', '4三']
    print(f"\n🔍 物理'三'ID使用情况:")
    for physical_id in physical_san_ids:
        if physical_id in all_san_ids:
            print(f"  {physical_id}: ✅ 已使用")
        else:
            print(f"  {physical_id}: ❌ 未使用")
    
    # 推断GlobalIDManager的状态
    print(f"\n🧠 GlobalIDManager状态推断:")
    print(f"  1. '4三'已被区域3注册到global_id_registry")
    print(f"  2. 当区域16请求'三'的下一个可用ID时:")
    print(f"     - is_id_used('1三'): {'True' if '1三' in all_san_ids else 'False'}")
    print(f"     - is_id_used('2三'): {'True' if '2三' in all_san_ids else 'False'}")
    print(f"     - is_id_used('3三'): {'True' if '3三' in all_san_ids else 'False'}")
    print(f"     - is_id_used('4三'): {'True' if '4三' in all_san_ids else 'False'}")
    print(f"  3. get_next_available_id('三')返回: None（所有物理ID被认为已使用）")
    print(f"  4. 触发虚拟ID分配: '虚拟三'")

def verify_cross_region_inheritance_failure():
    """验证跨区域继承失效的原因"""
    print("=" * 80)
    print("🔍 跨区域继承失效原因验证")
    print("=" * 80)
    
    frame_228 = load_frame_data(228)
    frame_229 = load_frame_data(229)
    
    if not frame_228 or not frame_229:
        return
    
    # 检查继承规则应该如何工作
    print(f"\n📋 跨区域继承规则（区域16）:")
    print(f"  根据simple_inheritor.py:")
    print(f"  cross_region_rules = {{")
    print(f"      16: [3, 4, 7, 8],  # 区域16可以从区域3,4,7,8继承")
    print(f"  }}")
    
    # 分析Frame_00228中区域3的情况
    san_228 = analyze_san_cards_detailed(frame_228, 228)
    region_3_228 = san_228['by_region'].get(3, [])
    
    print(f"\n🎯 Frame_00228中区域3的'三'卡牌:")
    if region_3_228:
        for card in region_3_228:
            print(f"  {card['twin_id']} - 应该可以被区域16继承")
    else:
        print(f"  无'三'卡牌")
    
    # 分析Frame_00229中区域16的情况
    san_229 = analyze_san_cards_detailed(frame_229, 229)
    region_16_229 = san_229['by_region'].get(16, [])
    
    print(f"\n🎯 Frame_00229中区域16的'三'卡牌:")
    for i, card in enumerate(region_16_229):
        if card['is_virtual']:
            print(f"  {i+1}. {card['twin_id']} ⚠️  虚拟ID - 继承失败")
        else:
            print(f"  {i+1}. {card['twin_id']} ✅ 物理ID")
    
    print(f"\n❗ 继承失效分析:")
    print(f"  1. 区域3有'4三'可供继承")
    print(f"  2. 区域16需要第4张'三'卡牌")
    print(f"  3. 但区域16获得了'虚拟三'而不是'4三'")
    print(f"  4. 可能原因:")
    print(f"     a) 继承器没有正确识别区域3→区域16的流转")
    print(f"     b) GlobalIDManager的全局唯一性约束阻止了继承")
    print(f"     c) ID分配器在继承器之后运行，覆盖了继承结果")

def main():
    """主函数"""
    print("🚀 Frame流转分析脚本")
    
    # 1. 分析流转过程
    analyze_transition_228_to_229()
    
    # 2. 分析ID管理器状态
    analyze_id_manager_state()
    
    # 3. 验证继承失效原因
    verify_cross_region_inheritance_failure()
    
    print("\n" + "=" * 80)
    print("✅ 流转分析完成")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Frame_00341.jpg 深度错误分析脚本

专门分析2捌消失、1捌重复的问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

def load_frame_data(frame_path):
    """加载帧数据"""
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载帧数据失败: {frame_path} - {e}")
        return None

def extract_region6_detailed(frame_data, frame_name):
    """提取区域6的详细信息"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    cards = []
    for i, shape in enumerate(frame_data['shapes']):
        if shape.get('group_id') == 6:
            # 获取位置信息
            points = shape.get('points', [])
            if points:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x_center = sum(x_coords) / len(x_coords)
                y_center = sum(y_coords) / len(y_coords)
                y_bottom = max(y_coords)
            else:
                x_center = y_center = y_bottom = 0
            
            card_info = {
                'index': i,
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'x_center': x_center,
                'y_center': y_center,
                'y_bottom': y_bottom,
                'points': points,
                'timestamp': shape.get('attributes', {}).get('processed_timestamp', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'inherited': shape.get('attributes', {}).get('inherited', False),
                'source_card_id': shape.get('attributes', {}).get('source_card_id', ''),
                'from_region_3': shape.get('attributes', {}).get('from_region_3', False),
                'from_region_6': shape.get('attributes', {}).get('from_region_6', False),
                'region6_spatial_reassign': shape.get('attributes', {}).get('region6_spatial_reassign', False)
            }
            cards.append(card_info)
    
    print(f"\n📊 {frame_name} 区域6详细分析:")
    print("-" * 60)
    print(f"区域6共{len(cards)}张卡牌:")
    
    # 按Y坐标排序（从下到上）
    cards_sorted = sorted(cards, key=lambda x: x['y_bottom'], reverse=True)
    
    for i, card in enumerate(cards_sorted):
        print(f"  位置{i+1}: {card['label']} (ID: {card['digital_twin_id']})")
        print(f"    中心: ({card['x_center']:.1f}, {card['y_center']:.1f})")
        print(f"    底部Y: {card['y_bottom']:.1f}")
        print(f"    继承信息: inherited={card['inherited']}, source_card_id={card['source_card_id']}")
        print(f"    流转标记: from_region_3={card['from_region_3']}, from_region_6={card['from_region_6']}")
        print(f"    空间重分配: region6_spatial_reassign={card['region6_spatial_reassign']}")
        print(f"    时间戳: {card['timestamp']}")
        print()
    
    return cards_sorted

def analyze_id_duplication_issue(cards_340, cards_341):
    """分析ID重复问题"""
    print(f"\n🔍 ID重复问题深度分析:")
    print("=" * 60)
    
    # 统计各ID的出现次数
    def count_ids(cards, frame_name):
        id_counts = {}
        for card in cards:
            card_id = card['digital_twin_id']
            if card_id not in id_counts:
                id_counts[card_id] = []
            id_counts[card_id].append(card)
        
        print(f"\n📋 {frame_name} ID统计:")
        for card_id, card_list in id_counts.items():
            if len(card_list) > 1:
                print(f"  ❌ 重复ID: {card_id} (出现{len(card_list)}次)")
                for i, card in enumerate(card_list):
                    print(f"    [{i+1}] 位置({card['x_center']:.1f}, {card['y_center']:.1f}) 标签:{card['label']}")
            else:
                print(f"  ✅ 正常ID: {card_id}")
        
        return id_counts
    
    id_counts_340 = count_ids(cards_340, "Frame_00340")
    id_counts_341 = count_ids(cards_341, "Frame_00341")
    
    # 分析ID变化
    print(f"\n🔄 ID变化分析:")
    print("-" * 40)
    
    ids_340 = set(id_counts_340.keys())
    ids_341 = set(id_counts_341.keys())
    
    disappeared_ids = ids_340 - ids_341
    appeared_ids = ids_341 - ids_340
    common_ids = ids_340 & ids_341
    
    if disappeared_ids:
        print(f"❌ 消失的ID: {list(disappeared_ids)}")
    
    if appeared_ids:
        print(f"➕ 新出现的ID: {list(appeared_ids)}")
    
    print(f"✅ 保持的ID: {list(common_ids)}")
    
    # 重点分析捌类卡牌
    print(f"\n🎯 捌类卡牌专项分析:")
    print("-" * 40)
    
    ba_cards_340 = [card for card in cards_340 if '捌' in card['label'] or '八' in card['label']]
    ba_cards_341 = [card for card in cards_341 if '捌' in card['label'] or '八' in card['label']]
    
    print(f"Frame_00340捌类卡牌:")
    for card in ba_cards_340:
        print(f"  - {card['label']} (ID: {card['digital_twin_id']}) 位置({card['x_center']:.1f}, {card['y_center']:.1f})")
    
    print(f"Frame_00341捌类卡牌:")
    for card in ba_cards_341:
        print(f"  - {card['label']} (ID: {card['digital_twin_id']}) 位置({card['x_center']:.1f}, {card['y_center']:.1f})")
        print(f"    继承: {card['inherited']}, 源ID: {card['source_card_id']}")

def analyze_inheritance_flow(cards_340, cards_341):
    """分析继承流程"""
    print(f"\n🔄 继承流程分析:")
    print("=" * 60)
    
    # 分析每张341卡牌的继承来源
    for card_341 in cards_341:
        label = card_341['label']
        card_id = card_341['digital_twin_id']
        source_id = card_341['source_card_id']
        
        print(f"\n🎯 分析卡牌: {label} (ID: {card_id})")
        print(f"  源ID: {source_id}")
        print(f"  继承标记: {card_341['inherited']}")
        print(f"  流转标记: from_region_3={card_341['from_region_3']}, from_region_6={card_341['from_region_6']}")
        
        # 在340中查找可能的源卡牌
        possible_sources = []
        for card_340 in cards_340:
            if card_340['digital_twin_id'] == source_id:
                possible_sources.append(card_340)
            elif card_340['digital_twin_id'] == card_id:
                possible_sources.append(card_340)
        
        if possible_sources:
            print(f"  可能的源卡牌:")
            for src in possible_sources:
                print(f"    - {src['label']} (ID: {src['digital_twin_id']}) 位置({src['x_center']:.1f}, {src['y_center']:.1f})")
        else:
            print(f"  ❌ 未找到源卡牌")

def analyze_spatial_matching_issue():
    """分析空间匹配问题"""
    print(f"\n📍 空间匹配问题分析:")
    print("=" * 60)
    
    print("可能的问题原因:")
    print("1. 6→6继承逻辑错误地将1捌分配给了2捌的位置")
    print("2. 空间匹配算法在处理位置移动时出现错误")
    print("3. ID分配器重新分配时覆盖了正确的继承ID")
    print("4. simple_inheritor.py的空间排序逻辑有问题")
    
    print("\n需要检查的关键点:")
    print("- simple_inheritor.py中的_match_cards_by_spatial_order方法")
    print("- region_transitioner.py中的6→6继承逻辑")
    print("- basic_id_assigner.py中的完全重新分配逻辑")

def main():
    """主函数"""
    print("🔍 Frame_00341.jpg 深度错误分析")
    print("=" * 60)
    print("目标: 分析2捌消失、1捌重复的根本原因")
    print()
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    frame_340_path = output_dir / "frame_00340.json"
    frame_341_path = output_dir / "frame_00341.json"
    
    # 检查文件是否存在
    if not frame_340_path.exists() or not frame_341_path.exists():
        print("❌ 测试数据文件不存在")
        return False
    
    # 加载数据
    frame_340_data = load_frame_data(frame_340_path)
    frame_341_data = load_frame_data(frame_341_path)
    
    if not frame_340_data or not frame_341_data:
        print("❌ 数据加载失败")
        return False
    
    print("✅ 数据加载成功")
    
    # 提取区域6详细信息
    cards_340 = extract_region6_detailed(frame_340_data, "Frame_00340")
    cards_341 = extract_region6_detailed(frame_341_data, "Frame_00341")
    
    # 分析ID重复问题
    analyze_id_duplication_issue(cards_340, cards_341)
    
    # 分析继承流程
    analyze_inheritance_flow(cards_340, cards_341)
    
    # 分析空间匹配问题
    analyze_spatial_matching_issue()
    
    print(f"\n🏁 分析结论:")
    print("=" * 60)
    print("❌ 确认存在问题: 2捌消失，1捌重复")
    print("🔍 需要进一步调查: simple_inheritor.py的空间匹配逻辑")
    print("💡 建议: 检查6→6继承时的空间排序和ID分配逻辑")
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

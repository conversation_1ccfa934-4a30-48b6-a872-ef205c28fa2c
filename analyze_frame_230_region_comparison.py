#!/usr/bin/env python3
"""
分析frame_00230中区域6和区域16的处理差异
重点分析为什么区域6能正确排列而区域16出现位置混淆
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, <PERSON><PERSON>
from collections import defaultdict

def load_frame_data(frame_number: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return {}
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def extract_card_position(card: Dict[str, Any]) -> Tuple[float, float, float, float]:
    """提取卡牌位置信息"""
    points = card.get('points', [])
    if not points or len(points) < 4:
        return 0.0, 0.0, 0.0, 0.0
    
    x_coords = [point[0] for point in points]
    y_coords = [point[1] for point in points]
    
    x_left = min(x_coords)
    x_right = max(x_coords)
    y_top = min(y_coords)
    y_bottom = max(y_coords)
    
    x_center = (x_left + x_right) / 2
    y_center = (y_top + y_bottom) / 2
    
    return x_center, y_center, x_left, y_bottom

def analyze_region_cards(cards: List[Dict[str, Any]], region_name: str) -> Dict[str, Any]:
    """分析指定区域的卡牌排列"""
    print(f"\n📊 分析{region_name}的卡牌排列")
    print("-" * 40)
    
    if not cards:
        print("  ❌ 该区域无卡牌")
        return {}
    
    # 提取卡牌信息
    card_info = []
    for i, card in enumerate(cards):
        x_center, y_center, x_left, y_bottom = extract_card_position(card)
        
        info = {
            'index': i,
            'label': card.get('label', ''),
            'twin_id': card.get('attributes', {}).get('digital_twin_id', ''),
            'score': card.get('score'),
            'x_center': x_center,
            'y_center': y_center,
            'x_left': x_left,
            'y_bottom': y_bottom,
            'region_name': card.get('region_name', ''),
            'owner': card.get('owner', ''),
            'group_id': card.get('group_id')
        }
        card_info.append(info)
    
    # 按空间位置排序（从下到上，从左到右）
    spatial_sorted = sorted(card_info, key=lambda x: (-x['y_bottom'], x['x_left']))
    
    print(f"  总卡牌数: {len(cards)}")
    print(f"  空间排序结果（从下到上，从左到右）:")
    
    for i, card in enumerate(spatial_sorted):
        print(f"    位置{i+1}: {card['label']} -> {card['twin_id']}")
        print(f"      坐标: x_center={card['x_center']:.1f}, y_bottom={card['y_bottom']:.1f}")
        print(f"      分数: {card['score']}")
    
    # 分析ID分配模式
    id_pattern_analysis = analyze_id_patterns(spatial_sorted)
    
    # 分析列分组
    column_analysis = analyze_column_grouping(spatial_sorted)
    
    return {
        'card_count': len(cards),
        'spatial_sorted': spatial_sorted,
        'id_patterns': id_pattern_analysis,
        'column_analysis': column_analysis
    }

def analyze_id_patterns(cards: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析ID分配模式"""
    print(f"\n  🔍 ID分配模式分析:")

    # 按标签分组
    by_label = defaultdict(list)
    for card in cards:
        base_label = card['label'].replace('暗', '')  # 移除"暗"标记
        by_label[base_label].append(card)

    patterns = {}
    for label, label_cards in by_label.items():
        if len(label_cards) > 1:
            ids = [card['twin_id'] for card in label_cards]
            print(f"    {label}: {ids}")

            # 检查是否为连续ID
            id_numbers = []
            for twin_id in ids:
                try:
                    # 提取数字部分
                    num_str = ''.join(filter(str.isdigit, twin_id))
                    if num_str:
                        id_numbers.append(int(num_str))
                except:
                    pass

            if id_numbers:
                is_consecutive = len(id_numbers) > 1 and all(
                    id_numbers[i] == id_numbers[i-1] + 1
                    for i in range(1, len(id_numbers))
                )
                patterns[label] = {
                    'ids': ids,
                    'id_numbers': id_numbers,
                    'is_consecutive': is_consecutive,
                    'count': len(label_cards)
                }
                print(f"      连续性: {'✅ 连续' if is_consecutive else '❌ 不连续'}")

    # 特别检查"陆"和"六"的混淆问题
    liu_variants = []
    for label in by_label.keys():
        if '陆' in label or '六' in label:
            liu_variants.append(label)

    if liu_variants:
        print(f"\n  🚨 发现'陆/六'变体: {liu_variants}")
        for variant in liu_variants:
            cards_of_variant = by_label[variant]
            print(f"    {variant}: {[card['twin_id'] for card in cards_of_variant]}")

    return patterns

def analyze_column_grouping(cards: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析列分组情况"""
    print(f"\n  📏 列分组分析:")
    
    # 按X坐标分组（容差为8像素）
    tolerance = 8.0
    columns = defaultdict(list)
    
    for card in cards:
        x_center = card['x_center']
        
        # 寻找合适的列
        assigned = False
        for x_key in columns.keys():
            if abs(x_center - x_key) <= tolerance:
                columns[x_key].append(card)
                assigned = True
                break
        
        if not assigned:
            columns[x_center].append(card)
    
    print(f"    检测到 {len(columns)} 列:")
    
    column_info = {}
    for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
        print(f"      列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌")
        
        # 列内按Y坐标排序（从下到上）
        column_sorted = sorted(column_cards, key=lambda x: -x['y_bottom'])
        
        for j, card in enumerate(column_sorted):
            print(f"        位置{j+1}: {card['label']} -> {card['twin_id']}")
        
        column_info[f"column_{i+1}"] = {
            'x_center': x_key,
            'card_count': len(column_cards),
            'cards': column_sorted
        }
    
    return column_info

def compare_regions(region6_analysis: Dict[str, Any], region16_analysis: Dict[str, Any]):
    """对比区域6和区域16的差异"""
    print(f"\n🔍 区域6 vs 区域16 对比分析")
    print("=" * 50)
    
    print(f"📊 基本统计:")
    print(f"  区域6卡牌数: {region6_analysis.get('card_count', 0)}")
    print(f"  区域16卡牌数: {region16_analysis.get('card_count', 0)}")
    
    print(f"\n🎯 ID分配模式对比:")
    
    # 对比ID分配模式
    region6_patterns = region6_analysis.get('id_patterns', {})
    region16_patterns = region16_analysis.get('id_patterns', {})
    
    print(f"  区域6 ID模式:")
    for label, pattern in region6_patterns.items():
        status = "✅ 连续" if pattern['is_consecutive'] else "❌ 不连续"
        print(f"    {label}: {pattern['ids']} ({status})")
    
    print(f"  区域16 ID模式:")
    for label, pattern in region16_patterns.items():
        status = "✅ 连续" if pattern['is_consecutive'] else "❌ 不连续"
        print(f"    {label}: {pattern['ids']} ({status})")
    
    print(f"\n📏 列分组对比:")
    
    region6_columns = region6_analysis.get('column_analysis', {})
    region16_columns = region16_analysis.get('column_analysis', {})
    
    print(f"  区域6列数: {len(region6_columns)}")
    print(f"  区域16列数: {len(region16_columns)}")
    
    # 分析问题根源
    print(f"\n💡 问题根源分析:")
    
    # 检查区域16是否有ID分配问题
    region16_has_issues = False
    for label, pattern in region16_patterns.items():
        if not pattern['is_consecutive']:
            region16_has_issues = True
            print(f"  ❌ 区域16的'{label}'卡牌ID不连续: {pattern['ids']}")
    
    if not region16_has_issues:
        print(f"  ✅ 区域16的ID分配看起来正常")
    
    # 检查区域6是否正常
    region6_has_issues = False
    for label, pattern in region6_patterns.items():
        if not pattern['is_consecutive']:
            region6_has_issues = True
            print(f"  ❌ 区域6的'{label}'卡牌ID不连续: {pattern['ids']}")
    
    if not region6_has_issues:
        print(f"  ✅ 区域6的ID分配正常")

def compare_with_previous_frame():
    """对比前一帧的变化"""
    print(f"\n🔄 对比前一帧（frame_00229）的变化")
    print("=" * 50)

    # 加载前一帧数据
    prev_frame_data = load_frame_data(229)
    if not prev_frame_data:
        print("❌ 无法加载frame_00229数据")
        return

    prev_shapes = prev_frame_data.get('shapes', [])

    # 提取前一帧的区域6和区域16卡牌
    prev_region6_cards = [card for card in prev_shapes if card.get('group_id') == 6]
    prev_region16_cards = [card for card in prev_shapes if card.get('group_id') == 16]

    print(f"📋 前一帧数据概览:")
    print(f"  区域6卡牌数: {len(prev_region6_cards)}")
    print(f"  区域16卡牌数: {len(prev_region16_cards)}")

    # 分析前一帧区域16的排列
    print(f"\n📊 前一帧区域16的卡牌排列:")
    prev_region16_info = []
    for i, card in enumerate(prev_region16_cards):
        x_center, y_center, x_left, y_bottom = extract_card_position(card)
        info = {
            'label': card.get('label', ''),
            'twin_id': card.get('attributes', {}).get('digital_twin_id', ''),
            'y_bottom': y_bottom,
            'x_left': x_left
        }
        prev_region16_info.append(info)

    # 按空间位置排序
    prev_sorted = sorted(prev_region16_info, key=lambda x: (-x['y_bottom'], x['x_left']))

    for i, card in enumerate(prev_sorted):
        print(f"  位置{i+1}: {card['label']} -> {card['twin_id']}")

    return prev_region16_info

def main():
    """主分析函数"""
    print("🔍 Frame 230 区域6 vs 区域16 深度对比分析")
    print("=" * 60)

    # 加载frame_00230数据
    frame_data = load_frame_data(230)
    if not frame_data:
        print("❌ 无法加载frame_00230数据")
        return False

    shapes = frame_data.get('shapes', [])

    # 提取区域6和区域16的卡牌
    region6_cards = [card for card in shapes if card.get('group_id') == 6]
    region16_cards = [card for card in shapes if card.get('group_id') == 16]

    print(f"📋 数据概览:")
    print(f"  总卡牌数: {len(shapes)}")
    print(f"  区域6卡牌数: {len(region6_cards)}")
    print(f"  区域16卡牌数: {len(region16_cards)}")

    # 分析区域6
    region6_analysis = analyze_region_cards(region6_cards, "区域6（观战方吃碰区）")

    # 分析区域16
    region16_analysis = analyze_region_cards(region16_cards, "区域16（对战方吃碰区）")

    # 对比分析
    compare_regions(region6_analysis, region16_analysis)

    # 对比前一帧
    compare_with_previous_frame()

    print(f"\n🎉 分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

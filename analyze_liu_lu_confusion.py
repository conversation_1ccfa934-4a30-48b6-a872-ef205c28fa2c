#!/usr/bin/env python3
"""
分析frame_00230中区域16的"陆"和"六"混淆问题
重点分析标签标准化处理逻辑的问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(frame_number: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return {}
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return {}

def analyze_liu_lu_confusion(frame_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析"陆"和"六"的混淆问题"""
    print(f"🔍 分析'陆'和'六'的混淆问题")
    print("=" * 50)
    
    shapes = frame_data.get('shapes', [])
    
    # 提取区域16的卡牌
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 区域16卡牌总数: {len(region16_cards)}")
    
    # 分析所有包含"陆"或"六"的卡牌
    liu_lu_cards = []
    for card in region16_cards:
        label = card.get('label', '')
        twin_id = card.get('attributes', {}).get('digital_twin_id', '')
        
        if '陆' in label or '六' in label or '陆' in twin_id or '六' in twin_id:
            liu_lu_cards.append({
                'label': label,
                'twin_id': twin_id,
                'points': card.get('points', []),
                'score': card.get('score'),
                'region_name': card.get('region_name', ''),
                'owner': card.get('owner', '')
            })
    
    print(f"\n🚨 发现{len(liu_lu_cards)}张'陆/六'相关卡牌:")
    
    # 按空间位置排序
    for card in liu_lu_cards:
        points = card['points']
        if points and len(points) >= 4:
            y_coords = [point[1] for point in points]
            x_coords = [point[0] for point in points]
            y_bottom = max(y_coords)
            x_center = sum(x_coords) / len(x_coords)
            card['y_bottom'] = y_bottom
            card['x_center'] = x_center
        else:
            card['y_bottom'] = 0
            card['x_center'] = 0
    
    # 按空间位置排序（从下到上，从左到右）
    liu_lu_cards.sort(key=lambda x: (-x['y_bottom'], x['x_center']))
    
    for i, card in enumerate(liu_lu_cards):
        print(f"  位置{i+1}: label='{card['label']}' -> twin_id='{card['twin_id']}'")
        print(f"    坐标: x_center={card['x_center']:.1f}, y_bottom={card['y_bottom']:.1f}")
        print(f"    分数: {card['score']}")
    
    # 分析混淆模式
    print(f"\n🔍 混淆模式分析:")
    
    # 统计标签变体
    label_variants = defaultdict(list)
    for card in liu_lu_cards:
        base_label = card['label'].replace('1', '').replace('2', '').replace('3', '').replace('4', '')
        label_variants[base_label].append(card)
    
    print(f"  标签变体统计:")
    for variant, cards in label_variants.items():
        twin_ids = [card['twin_id'] for card in cards]
        print(f"    '{variant}': {len(cards)}张 -> {twin_ids}")
    
    # 分析ID分配模式
    print(f"\n  ID分配模式:")
    id_patterns = defaultdict(list)
    for card in liu_lu_cards:
        twin_id = card['twin_id']
        # 提取数字部分
        id_num = ''.join(filter(str.isdigit, twin_id))
        if id_num:
            id_patterns[id_num].append(card)
    
    for id_num, cards in sorted(id_patterns.items()):
        labels = [card['label'] for card in cards]
        twin_ids = [card['twin_id'] for card in cards]
        print(f"    ID{id_num}: {len(cards)}张")
        print(f"      labels: {labels}")
        print(f"      twin_ids: {twin_ids}")
    
    return {
        'total_liu_lu_cards': len(liu_lu_cards),
        'label_variants': dict(label_variants),
        'id_patterns': dict(id_patterns),
        'spatial_sorted_cards': liu_lu_cards
    }

def analyze_standardization_logic():
    """分析标签标准化逻辑"""
    print(f"\n🔧 标签标准化逻辑分析")
    print("=" * 50)
    
    # 模拟标准化映射
    card_name_mapping = {
        "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
        "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
    }
    
    print(f"📋 标准化映射规则:")
    for traditional, simplified in card_name_mapping.items():
        print(f"  {traditional} -> {simplified}")
    
    print(f"\n🚨 关键发现:")
    print(f"  根据映射规则: '陆' -> '六'")
    print(f"  但在frame_00230中同时出现了'陆'和'六'")
    print(f"  这表明标准化逻辑存在不一致性")
    
    # 分析可能的原因
    print(f"\n💡 可能的原因:")
    print(f"  1. OCR识别阶段：不同时刻识别为不同字符")
    print(f"  2. 标准化时机：某些卡牌未经过标准化处理")
    print(f"  3. 流转继承：从不同源区域继承了不同的标签格式")
    print(f"  4. ID分配逻辑：将'陆'和'六'视为不同类型的卡牌")

def compare_with_previous_frames():
    """对比前几帧的情况"""
    print(f"\n🔄 对比前几帧的'陆/六'情况")
    print("=" * 50)
    
    frames_to_check = [228, 229, 230]
    
    for frame_num in frames_to_check:
        print(f"\n📊 Frame {frame_num}:")
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            print(f"  ❌ 无法加载数据")
            continue
        
        shapes = frame_data.get('shapes', [])
        region16_cards = [card for card in shapes if card.get('group_id') == 16]
        
        liu_lu_cards = []
        for card in region16_cards:
            label = card.get('label', '')
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            
            if '陆' in label or '六' in label or '陆' in twin_id or '六' in twin_id:
                liu_lu_cards.append({
                    'label': label,
                    'twin_id': twin_id
                })
        
        print(f"  区域16中'陆/六'卡牌: {len(liu_lu_cards)}张")
        for card in liu_lu_cards:
            print(f"    {card['label']} -> {card['twin_id']}")

def analyze_root_cause():
    """分析根本原因"""
    print(f"\n🎯 根本原因分析")
    print("=" * 50)
    
    print(f"基于分析结果，问题的根本原因可能是：")
    print(f"")
    print(f"1. 🔴 标签标准化不一致")
    print(f"   - 某些处理路径没有应用标准化映射")
    print(f"   - OCR识别结果直接使用，未经标准化")
    print(f"   - 流转继承时保持了原始标签格式")
    print(f"")
    print(f"2. 🟡 ID分配逻辑缺陷")
    print(f"   - 将'陆'和'六'视为不同类型")
    print(f"   - 没有统一的标签规范化检查")
    print(f"   - 继承机制中缺少标签标准化")
    print(f"")
    print(f"3. 🟡 区域流转问题")
    print(f"   - 不同源区域使用不同的标签格式")
    print(f"   - 流转时没有进行标签统一")
    print(f"   - 继承优先级导致标签混乱")
    print(f"")
    print(f"4. 🔵 时序处理问题")
    print(f"   - 前一帧的标签格式影响当前帧")
    print(f"   - 继承机制保持了历史的不一致性")

def propose_solution():
    """提出解决方案"""
    print(f"\n💡 解决方案建议")
    print("=" * 50)
    
    print(f"基于根本原因分析，建议的解决方案：")
    print(f"")
    print(f"🔧 方案1: 统一标签标准化")
    print(f"   - 在所有处理入口点添加标签标准化")
    print(f"   - 确保OCR结果、继承结果都经过标准化")
    print(f"   - 在ID分配前强制执行标签规范化")
    print(f"")
    print(f"🔧 方案2: 增强继承机制")
    print(f"   - 在继承时进行标签标准化检查")
    print(f"   - 统一'陆'和'六'的处理逻辑")
    print(f"   - 添加标签兼容性检查")
    print(f"")
    print(f"🔧 方案3: 修复ID分配逻辑")
    print(f"   - 在ID分配前统一标签格式")
    print(f"   - 将'陆'和'六'视为同一类型")
    print(f"   - 添加标签一致性验证")
    print(f"")
    print(f"🔧 方案4: 增加验证机制")
    print(f"   - 添加标签一致性检查")
    print(f"   - 在输出前验证标签格式")
    print(f"   - 记录标签转换日志")

def main():
    """主分析函数"""
    print("🔍 Frame 230 '陆'和'六'混淆问题深度分析")
    print("=" * 60)
    
    # 加载frame_00230数据
    frame_data = load_frame_data(230)
    if not frame_data:
        print("❌ 无法加载frame_00230数据")
        return False
    
    # 分析"陆"和"六"的混淆问题
    confusion_analysis = analyze_liu_lu_confusion(frame_data)
    
    # 分析标准化逻辑
    analyze_standardization_logic()
    
    # 对比前几帧
    compare_with_previous_frames()
    
    # 分析根本原因
    analyze_root_cause()
    
    # 提出解决方案
    propose_solution()
    
    print(f"\n🎉 分析完成！")
    print(f"关键发现: 区域16中同时存在'陆'和'六'标签，表明标签标准化逻辑存在不一致性")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
单图片处理测试脚本
专门用于测试区域16修复效果
"""

import json
import sys
import os
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.state_builder import StateBuilder
from src.core.detect import CardDetector
from src.modules.region_transitioner import create_region_transitioner

def process_single_image(image_path: str, output_dir: str = "output/test_region16_fix") -> bool:
    """处理单张图片"""
    print(f"🚀 处理单张图片: {image_path}")
    print("=" * 60)
    
    # 检查输入文件
    if not Path(image_path).exists():
        print(f"❌ 输入图像不存在: {image_path}")
        return False
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    labels_dir = output_path / "labels"
    labels_dir.mkdir(exist_ok=True)
    
    images_dir = output_path / "images"
    images_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 加载图像
        print(f"📋 加载图像...")
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 图像加载失败")
            return False
        
        print(f"  图像尺寸: {image.shape}")
        
        # 2. 初始化检测器
        print(f"📋 初始化检测器...")
        detector = CardDetector()
        
        # 3. 执行检测
        print(f"📋 执行卡牌检测...")
        detections = detector.detect_image(image)  # 单张图片检测
        
        print(f"  检测到 {len(detections)} 个对象")
        
        # 4. 初始化状态构建器
        print(f"📋 初始化状态构建器...")
        state_builder = StateBuilder()
        
        # 5. 构建状态
        print(f"📋 构建游戏状态...")
        # 将检测结果格式化为StateBuilder期望的格式
        formatted_detections = {'cards': detections}
        game_state = state_builder.yolo_to_game_state(formatted_detections)
        
        print(f"  状态构建完成")
        print(f"  总卡牌数: {len(game_state.get('shapes', []))}")
        
        # 6. 保存结果
        print(f"📋 保存处理结果...")
        
        # 保存图像
        image_name = Path(image_path).name
        output_image_path = images_dir / image_name
        cv2.imwrite(str(output_image_path), image)
        
        # 保存标注
        label_name = Path(image_path).stem + ".json"
        output_label_path = labels_dir / label_name
        
        with open(output_label_path, 'w', encoding='utf-8') as f:
            json.dump(game_state, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ 结果已保存到: {output_dir}")
        print(f"    图像: {output_image_path}")
        print(f"    标注: {output_label_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_processing_results(output_dir: str, original_data_path: str = None) -> bool:
    """分析处理结果"""
    print(f"\n📊 分析处理结果")
    print("=" * 60)
    
    # 加载处理后的结果
    labels_dir = Path(output_dir) / "labels"
    result_files = list(labels_dir.glob("*.json"))
    
    if not result_files:
        print(f"❌ 未找到处理结果文件")
        return False
    
    result_file = result_files[0]  # 取第一个文件
    print(f"📋 分析文件: {result_file}")
    
    try:
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取结果文件失败: {e}")
        return False
    
    # 分析区域16
    shapes = result_data.get('shapes', [])
    region16_cards = [card for card in shapes if card.get('group_id') == 16]
    
    print(f"📋 处理结果概览:")
    print(f"  总卡牌数: {len(shapes)}")
    print(f"  区域16卡牌数: {len(region16_cards)}")
    
    # 分析列一致性
    def analyze_column_consistency(cards, title):
        print(f"\n🔍 {title}")
        print("-" * 40)
        
        if not cards:
            print("  ❌ 无卡牌")
            return {}
        
        # 按X坐标分组
        tolerance = 8.0
        columns = defaultdict(list)
        
        for card in cards:
            points = card.get('points', [])
            if points and len(points) >= 4:
                x_coords = [point[0] for point in points]
                x_center = sum(x_coords) / len(x_coords)
                y_coords = [point[1] for point in points]
                y_bottom = max(y_coords)
                
                card['_x_center'] = x_center
                card['_y_bottom'] = y_bottom
                
                # 寻找合适的列
                assigned = False
                for x_key in columns.keys():
                    if abs(x_center - x_key) <= tolerance:
                        columns[x_key].append(card)
                        assigned = True
                        break
                
                if not assigned:
                    columns[x_center].append(card)
        
        print(f"  检测到 {len(columns)} 列:")
        
        consistent_columns = 0
        total_columns = len(columns)
        inconsistent_details = []
        
        for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
            # 列内按Y坐标排序（从下到上）
            column_cards.sort(key=lambda x: -x.get('_y_bottom', 0))
            
            # 分析列内的类别一致性
            base_labels = []
            for card in column_cards:
                label = card.get('label', '')
                # 提取基础标签（去掉数字前缀）
                base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
                base_labels.append(base_label)
            
            unique_base_labels = set(base_labels)
            is_consistent = len(unique_base_labels) == 1
            
            if is_consistent:
                consistent_columns += 1
                status = "✅ 一致"
            else:
                status = "❌ 混淆"
                inconsistent_details.append({
                    'column': i + 1,
                    'mixed_labels': list(unique_base_labels),
                    'cards': [{'label': card.get('label'), 'twin_id': card.get('attributes', {}).get('digital_twin_id', '')} for card in column_cards]
                })
            
            print(f"    列{i+1} (x≈{x_key:.1f}): {len(column_cards)}张卡牌 - {status}")
            print(f"      基础标签: {list(unique_base_labels)}")
            
            for j, card in enumerate(column_cards):
                twin_id = card.get('attributes', {}).get('digital_twin_id', '')
                spatial_reassign = card.get('region16_spatial_reassign', False)
                unified_group = card.get('unified_group', '')
                
                reassign_info = ""
                if spatial_reassign:
                    reassign_info = f" [重新分配: {unified_group}]"
                
                print(f"        位置{j+1}: {card.get('label')} -> {twin_id}{reassign_info}")
        
        consistency_rate = consistent_columns / total_columns if total_columns > 0 else 0
        
        print(f"\n  📈 一致性统计:")
        print(f"    总列数: {total_columns}")
        print(f"    一致列数: {consistent_columns}")
        print(f"    混淆列数: {total_columns - consistent_columns}")
        print(f"    一致性率: {consistency_rate:.1%}")
        print(f"    整体状态: {'✅ 全部一致' if consistency_rate == 1.0 else '❌ 存在混淆'}")
        
        if inconsistent_details:
            print(f"\n  🚨 混淆详情:")
            for detail in inconsistent_details:
                print(f"    列{detail['column']}: 混淆标签 {detail['mixed_labels']}")
        
        return {
            'total_columns': total_columns,
            'consistent_columns': consistent_columns,
            'consistency_rate': consistency_rate,
            'all_consistent': consistency_rate == 1.0,
            'inconsistent_details': inconsistent_details
        }
    
    # 分析修复后的结果
    analysis = analyze_column_consistency(region16_cards, "修复后区域16列一致性")
    
    # 如果有原始数据，进行对比
    if original_data_path and Path(original_data_path).exists():
        print(f"\n📊 与原始数据对比")
        print("-" * 40)
        
        try:
            with open(original_data_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            original_shapes = original_data.get('shapes', [])
            original_region16 = [card for card in original_shapes if card.get('group_id') == 16]
            
            print(f"📋 原始区域16: {len(original_region16)}张卡牌")
            original_analysis = analyze_column_consistency(original_region16, "修复前区域16列一致性")
            
            # 对比结果
            print(f"\n📈 修复效果对比:")
            print(f"  修复前一致性: {original_analysis.get('consistency_rate', 0):.1%}")
            print(f"  修复后一致性: {analysis.get('consistency_rate', 0):.1%}")
            
            improvement = analysis.get('consistency_rate', 0) - original_analysis.get('consistency_rate', 0)
            print(f"  改善程度: {improvement:.1%}")
            
            if improvement > 0:
                print(f"  🎉 修复成功！列一致性显著改善")
            elif improvement == 0:
                print(f"  ⚠️ 修复效果不明显")
            else:
                print(f"  ❌ 修复后效果变差")
                
        except Exception as e:
            print(f"❌ 读取原始数据失败: {e}")
    
    # 检查特定修复标记
    print(f"\n🔧 修复机制验证:")
    
    reassigned_count = 0
    unified_groups = set()
    
    for card in region16_cards:
        if card.get('region16_spatial_reassign', False):
            reassigned_count += 1
            unified_group = card.get('unified_group', '')
            if unified_group:
                unified_groups.add(unified_group)
    
    print(f"  重新分配的卡牌: {reassigned_count}/{len(region16_cards)}")
    print(f"  统一分组: {list(unified_groups)}")
    
    if reassigned_count > 0:
        print(f"  ✅ 区域16空间重新分配机制已生效")
    else:
        print(f"  ❌ 区域16空间重新分配机制未生效")
    
    return True

def main():
    """主测试函数"""
    print("🧪 区域16修复单图片处理测试")
    print("=" * 60)
    
    # 测试图片路径
    image_path = "legacy_assets/ceshi/calibration_gt/images/frame_00230.jpg"
    output_dir = "output/test_region16_fix"
    original_data_path = "output/calibration_gt_final_with_digital_twin/labels/frame_00230.json"
    
    # 处理图片
    if not process_single_image(image_path, output_dir):
        print("❌ 图片处理失败")
        return False
    
    # 分析结果
    if not analyze_processing_results(output_dir, original_data_path):
        print("❌ 结果分析失败")
        return False
    
    print(f"\n🎉 测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌训练集类别平衡分析脚本
功能：
1. 分析训练集中各类别的分布情况
2. 识别严重不平衡的类别
3. 生成类别分布可视化图表
4. 提供类别均衡化建议
5. 生成详细的分析报告
"""

import os
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict, Counter
from datetime import datetime
import seaborn as sns
from loguru import logger
import cv2

# 默认配置
DEFAULT_CONFIG = {
    "image_dir": r"D:\phz-ai-simple\data\xunlianjiyolo\images\train",
    "json_dir": r"D:\phz-ai-simple\data\xunlianjiyolo\labels\train",
    "output_dir": r"D:\phz-ai-simple\data\class_balance_analysis",
    "folders_to_check": None,  # 默认检查所有文件夹
    "min_samples_threshold": 10,  # 最小样本数阈值
    "balance_ratio_threshold": 0.2,  # 平衡比例阈值 (相对于最大类别数量)
    "create_visualizations": True,  # 是否创建可视化
}

# 类别映射 (与训练脚本完全一致)
LABEL_TO_ID = {
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
    "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
    "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
    "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
}

ID_TO_LABEL = {v: k for k, v in LABEL_TO_ID.items()}

# 类别组
CLASS_GROUPS = {
    "数字小写": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],  # 一到十
    "数字大写": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20],  # 壹到拾
    "操作标签": [21, 22, 23, 24, 25, 26, 27],  # 暗到已准备
    "结果标签": [28, 29, 30, 31]  # 你赢了到牌局结束
}


class ClassBalanceAnalyzer:
    def __init__(self, config):
        self.config = config
        self.image_dir = config["image_dir"]
        self.json_dir = config["json_dir"]
        self.output_dir = config["output_dir"]
        self.folders_to_check = config["folders_to_check"]
        self.min_samples_threshold = config["min_samples_threshold"]
        self.balance_ratio_threshold = config["balance_ratio_threshold"]
        self.create_visualizations = config["create_visualizations"]
        
        # 初始化日志
        self._setup_logging()
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        if self.create_visualizations:
            os.makedirs(os.path.join(self.output_dir, "visualizations"), exist_ok=True)
        
        # 初始化结果存储
        self.class_counts = defaultdict(int)
        self.class_counts_by_folder = defaultdict(lambda: defaultdict(int))
        self.folder_counts = defaultdict(int)
        self.total_annotations = 0
        self.total_images = 0
        self.imbalanced_classes = []
        
    def _setup_logging(self):
        """配置日志系统"""
        log_file = os.path.join(self.output_dir, "class_balance_analyzer.log")
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logger.remove()  # 移除默认处理器
        logger.add(log_file, rotation="10 MB", retention="7 days", 
                  level="INFO", encoding="utf-8")
        logger.add(lambda msg: tqdm.write(msg, end=""), level="INFO")
        
        logger.info(f"初始化类别平衡分析器，配置: {self.config}")
        
    def _extract_standard_label(self, label):
        """从标注中提取标准类别名"""
        if not label:
            return None

        # 直接匹配
        if label in LABEL_TO_ID:
            return label

        # 提取中文字符
        chinese_chars = ''.join([c for c in label if '\u4e00' <= c <= '\u9fff'])

        # 尝试最长匹配
        if chinese_chars:
            for candidate in sorted(LABEL_TO_ID.keys(), key=len, reverse=True):
                if candidate in chinese_chars:
                    return candidate

        # 关键字匹配
        for keyword in ["吃", "碰", "胡", "过"]:
            if keyword in label:
                return keyword

        return None
        
    def _parse_json_annotations(self, json_path):
        """解析JSON标注文件"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError:
            logger.error(f"无效JSON文件: {json_path}")
            return []
        except Exception as e:
            logger.error(f"读取JSON失败 {json_path}: {e}")
            return []
            
        annotations = []
        for shape in data.get("shapes", []):
            label = shape.get("label", "")
            
            if not label:
                continue
                
            # 提取标准类别名
            card_type = self._extract_standard_label(label)
            if not card_type:
                logger.warning(f"无法解析标签 {json_path}: {label}")
                continue
                
            class_id = LABEL_TO_ID.get(card_type, 0)
            if class_id == 0:
                continue
                
            annotations.append({
                "class_id": class_id,
                "class_name": card_type
            })
            
        return annotations
        
    def analyze_class_balance(self):
        """分析类别平衡"""
        logger.info("开始分析类别平衡...")
        
        # 获取要检查的文件夹列表
        if self.folders_to_check is None:
            folders = [f for f in os.listdir(self.image_dir) 
                      if os.path.isdir(os.path.join(self.image_dir, f))]
        else:
            folders = self.folders_to_check
            
        logger.info(f"将检查 {len(folders)} 个文件夹")
        
        # 遍历文件夹
        for folder in tqdm(folders, desc="分析文件夹"):
            image_folder = os.path.join(self.image_dir, folder)
            json_folder = os.path.join(self.json_dir, folder)
            
            if not os.path.exists(image_folder):
                logger.warning(f"图像文件夹不存在: {image_folder}")
                continue
                
            if not os.path.exists(json_folder):
                logger.warning(f"标注文件夹不存在: {json_folder}")
                continue
                
            # 获取图像和JSON文件列表
            image_files = [f for f in os.listdir(image_folder)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                          
            self.folder_counts[folder] = len(image_files)
            self.total_images += len(image_files)
            
            for img_file in image_files:
                # 构建对应JSON路径
                json_file = os.path.splitext(img_file)[0] + '.json'
                json_path = os.path.join(json_folder, json_file)
                
                # 检查标注文件是否存在
                if not os.path.exists(json_path):
                    continue
                    
                # 解析JSON标注
                annotations = self._parse_json_annotations(json_path)
                
                # 统计类别
                for ann in annotations:
                    class_id = ann["class_id"]
                    self.class_counts[class_id] += 1
                    self.class_counts_by_folder[folder][class_id] += 1
                    self.total_annotations += 1
                    
        # 分析类别不平衡
        self._analyze_imbalance()
        
        # 生成报告
        self._generate_report()
        
        # 创建可视化
        if self.create_visualizations:
            self._create_visualizations()
            
        logger.info(f"分析完成! 共处理 {self.total_images} 张图像，{self.total_annotations} 个标注")
        
    def _analyze_imbalance(self):
        """分析类别不平衡"""
        if not self.class_counts:
            logger.warning("没有找到任何类别数据")
            return
            
        # 找出样本数最多的类别
        max_count = max(self.class_counts.values())
        
        # 检查不平衡类别
        for class_id in sorted(LABEL_TO_ID.values()):
            count = self.class_counts.get(class_id, 0)
            
            # 检查是否低于最小样本阈值
            if count < self.min_samples_threshold:
                self.imbalanced_classes.append({
                    "class_id": class_id,
                    "class_name": ID_TO_LABEL.get(class_id, "未知"),
                    "count": count,
                    "ratio_to_max": count / max_count if max_count > 0 else 0,
                    "issue": "样本数过少",
                    "recommendation": "增加样本或使用数据增强"
                })
                continue
                
            # 检查是否低于平衡比例阈值
            if count / max_count < self.balance_ratio_threshold:
                self.imbalanced_classes.append({
                    "class_id": class_id,
                    "class_name": ID_TO_LABEL.get(class_id, "未知"),
                    "count": count,
                    "ratio_to_max": count / max_count,
                    "issue": "样本比例过低",
                    "recommendation": "增加样本至少达到主要类别的 {:.0f}%".format(
                        self.balance_ratio_threshold * 100)
                })
                
    def _generate_report(self):
        """生成分析报告"""
        # 创建详细报告
        report_path = os.path.join(self.output_dir, "class_balance_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("跑胡子卡牌训练集类别平衡分析报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析的文件夹数: {len(self.folder_counts)}\n")
            f.write(f"分析的图像总数: {self.total_images}\n")
            f.write(f"分析的标注总数: {self.total_annotations}\n\n")
            
            # 按组统计类别
            f.write("按组统计类别:\n")
            for group_name, class_ids in CLASS_GROUPS.items():
                group_total = sum(self.class_counts.get(class_id, 0) for class_id in class_ids)
                f.write(f"- {group_name}: {group_total} 个标注 "
                       f"({group_total / self.total_annotations * 100:.1f}%)\n")
            f.write("\n")
            
            # 类别详细统计
            f.write("类别详细统计:\n")
            f.write(f"{'类别ID':<8} {'类别名':<10} {'数量':<8} {'占比':<8}\n")
            f.write("-" * 40 + "\n")
            
            for class_id in sorted(LABEL_TO_ID.values()):
                count = self.class_counts.get(class_id, 0)
                percentage = count / self.total_annotations * 100 if self.total_annotations > 0 else 0
                f.write(f"{class_id:<8} {ID_TO_LABEL.get(class_id, '未知'):<10} "
                       f"{count:<8} {percentage:.1f}%\n")
            f.write("\n")
            
            # 不平衡类别
            if self.imbalanced_classes:
                f.write("不平衡类别:\n")
                f.write(f"{'类别ID':<8} {'类别名':<10} {'数量':<8} {'相对最大类比例':<16} {'问题':<12} {'建议'}\n")
                f.write("-" * 80 + "\n")
                
                for cls in sorted(self.imbalanced_classes, key=lambda x: x["count"]):
                    f.write(f"{cls['class_id']:<8} {cls['class_name']:<10} "
                           f"{cls['count']:<8} {cls['ratio_to_max']*100:.1f}% "
                           f"{cls['issue']:<12} {cls['recommendation']}\n")
                f.write("\n")
                
            # 文件夹统计
            f.write("文件夹统计:\n")
            for folder, count in sorted(self.folder_counts.items()):
                f.write(f"- {folder}: {count} 张图像\n")
                
        # 创建CSV报告
        df = pd.DataFrame([{
            "class_id": class_id,
            "class_name": ID_TO_LABEL.get(class_id, "未知"),
            "count": count,
            "percentage": count / self.total_annotations * 100 if self.total_annotations > 0 else 0
        } for class_id, count in self.class_counts.items()])
        
        csv_path = os.path.join(self.output_dir, "class_counts.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 创建文件夹-类别交叉表
        cross_data = []
        for folder in self.folder_counts.keys():
            for class_id in sorted(LABEL_TO_ID.values()):
                count = self.class_counts_by_folder[folder].get(class_id, 0)
                if count > 0:
                    cross_data.append({
                        "folder": folder,
                        "class_id": class_id,
                        "class_name": ID_TO_LABEL.get(class_id, "未知"),
                        "count": count
                    })
                    
        if cross_data:
            cross_df = pd.DataFrame(cross_data)
            cross_path = os.path.join(self.output_dir, "folder_class_cross.csv")
            cross_df.to_csv(cross_path, index=False, encoding='utf-8')
            
        logger.info(f"报告已生成: {report_path}")
        
    def _create_visualizations(self):
        """创建可视化图表"""
        if not self.class_counts:
            return
            
        vis_dir = os.path.join(self.output_dir, "visualizations")
        
        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        
        # 1. 类别分布柱状图
        plt.figure(figsize=(15, 8))
        counts = []
        labels = []
        
        for class_id in sorted(LABEL_TO_ID.values()):
            counts.append(self.class_counts.get(class_id, 0))
            labels.append(f"{class_id}:{ID_TO_LABEL.get(class_id, '未知')}")
            
        bars = plt.bar(labels, counts)
        plt.xticks(rotation=90)
        plt.title("类别分布")
        plt.xlabel("类别")
        plt.ylabel("数量")
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # 在柱状图上添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f"{int(height)}", ha='center', va='bottom', rotation=0)
                    
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, "class_distribution.png"), dpi=300)
        plt.close()
        
        # 2. 类别组分布饼图
        plt.figure(figsize=(10, 10))
        group_counts = {}
        
        for group_name, class_ids in CLASS_GROUPS.items():
            group_counts[group_name] = sum(self.class_counts.get(class_id, 0) for class_id in class_ids)
            
        labels = list(group_counts.keys())
        sizes = list(group_counts.values())
        
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        plt.axis('equal')
        plt.title("类别组分布")
        plt.savefig(os.path.join(vis_dir, "group_distribution.png"), dpi=300)
        plt.close()
        
        # 3. 文件夹-类别热力图
        if self.class_counts_by_folder:
            # 创建交叉表
            folders = sorted(self.folder_counts.keys())
            class_ids = sorted(LABEL_TO_ID.values())
            
            data = np.zeros((len(folders), len(class_ids)))
            
            for i, folder in enumerate(folders):
                for j, class_id in enumerate(class_ids):
                    data[i, j] = self.class_counts_by_folder[folder].get(class_id, 0)
                    
            # 绘制热力图
            plt.figure(figsize=(20, 12))
            sns.heatmap(data, annot=False, fmt="d", cmap="YlGnBu",
                       xticklabels=[f"{id}:{ID_TO_LABEL.get(id, '未知')}" for id in class_ids],
                       yticklabels=folders)
            plt.title("文件夹-类别分布热力图")
            plt.xlabel("类别")
            plt.ylabel("文件夹")
            plt.xticks(rotation=90)
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, "folder_class_heatmap.png"), dpi=300)
            plt.close()
            
            # 4. 不平衡类别可视化
            if self.imbalanced_classes:
                plt.figure(figsize=(12, 8))
                
                imbalanced_ids = [cls["class_id"] for cls in self.imbalanced_classes]
                imbalanced_counts = [cls["count"] for cls in self.imbalanced_classes]
                imbalanced_labels = [f"{cls['class_id']}:{cls['class_name']}" for cls in self.imbalanced_classes]
                
                bars = plt.bar(imbalanced_labels, imbalanced_counts, color='salmon')
                plt.xticks(rotation=90)
                plt.title("不平衡类别")
                plt.xlabel("类别")
                plt.ylabel("数量")
                plt.grid(axis='y', linestyle='--', alpha=0.7)
                
                # 在柱状图上添加数值标签
                for bar in bars:
                    height = bar.get_height()
                    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                            f"{int(height)}", ha='center', va='bottom', rotation=0)
                            
                plt.tight_layout()
                plt.savefig(os.path.join(vis_dir, "imbalanced_classes.png"), dpi=300)
                plt.close()
                
        logger.info(f"可视化图表已生成: {vis_dir}")
        
    def generate_balance_recommendations(self):
        """生成平衡建议"""
        if not self.class_counts or not self.imbalanced_classes:
            return
            
        # 计算目标样本数
        max_count = max(self.class_counts.values())
        target_count = int(max_count * self.balance_ratio_threshold)
        
        recommendations = []
        
        for cls in self.imbalanced_classes:
            class_id = cls["class_id"]
            current_count = cls["count"]
            needed_count = max(0, target_count - current_count)
            
            if needed_count > 0:
                recommendations.append({
                    "class_id": class_id,
                    "class_name": cls["class_name"],
                    "current_count": current_count,
                    "target_count": target_count,
                    "needed_count": needed_count
                })
                
        # 生成建议报告
        if recommendations:
            rec_path = os.path.join(self.output_dir, "balance_recommendations.txt")
            with open(rec_path, 'w', encoding='utf-8') as f:
                f.write("类别平衡建议\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"目标样本数: 每个类别至少 {target_count} 个样本 "
                       f"(最大类别 {max_count} 的 {self.balance_ratio_threshold*100:.0f}%)\n\n")
                
                f.write(f"{'类别ID':<8} {'类别名':<10} {'当前数量':<10} {'目标数量':<10} {'需要增加':<10}\n")
                f.write("-" * 60 + "\n")
                
                for rec in sorted(recommendations, key=lambda x: x["needed_count"], reverse=True):
                    f.write(f"{rec['class_id']:<8} {rec['class_name']:<10} "
                           f"{rec['current_count']:<10} {rec['target_count']:<10} "
                           f"{rec['needed_count']:<10}\n")
                    
            logger.info(f"平衡建议已生成: {rec_path}")
            
            # 创建CSV建议
            rec_df = pd.DataFrame(recommendations)
            rec_csv = os.path.join(self.output_dir, "balance_recommendations.csv")
            rec_df.to_csv(rec_csv, index=False, encoding='utf-8')


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="跑胡子卡牌训练集类别平衡分析脚本")
    parser.add_argument("--image-dir", type=str, default=DEFAULT_CONFIG["image_dir"],
                        help="图像目录路径")
    parser.add_argument("--json-dir", type=str, default=DEFAULT_CONFIG["json_dir"],
                        help="JSON标注目录路径")
    parser.add_argument("--output-dir", type=str, default=DEFAULT_CONFIG["output_dir"],
                        help="输出目录路径")
    parser.add_argument("--folders", type=str, default=None,
                        help="要检查的文件夹列表，用逗号分隔")
    parser.add_argument("--min-samples", type=int, default=DEFAULT_CONFIG["min_samples_threshold"],
                        help="最小样本数阈值")
    parser.add_argument("--balance-ratio", type=float, default=DEFAULT_CONFIG["balance_ratio_threshold"],
                        help="平衡比例阈值 (0-1)")
    parser.add_argument("--no-visual", action="store_true",
                        help="禁用可视化生成")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    
    # 更新配置
    config = DEFAULT_CONFIG.copy()
    config.update({
        "image_dir": args.image_dir,
        "json_dir": args.json_dir,
        "output_dir": args.output_dir,
        "folders_to_check": [f.strip() for f in args.folders.split(",")] if args.folders else None,
        "min_samples_threshold": args.min_samples,
        "balance_ratio_threshold": args.balance_ratio,
        "create_visualizations": not args.no_visual
    })
    
    analyzer = ClassBalanceAnalyzer(config)
    analyzer.analyze_class_balance()
    analyzer.generate_balance_recommendations() 
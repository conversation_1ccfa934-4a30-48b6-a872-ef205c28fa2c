import os
import shutil
import logging
from pathlib import Path
from collections import Counter
import random
import json

# --- 配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 根据文档指定输入路径
IMAGES_BASE_DIR = Path("data/xunlianjiyolo/images/train")
LABELS_BASE_DIR = Path("data/xunlianjiyolo/labels/train")

# 根据文档指定输出路径
CONSOLIDATED_ROOT = Path("data/xunlianjiyolo/consolidated_shuffled")
IMAGES_DIR = CONSOLIDATED_ROOT / "images"
LABELS_DIR = CONSOLIDATED_ROOT / "labels"

def find_all_data_pairs() -> dict:
    """
    在指定的图像和标签基础目录中查找所有配对。
    """
    pairs = {}
    if not IMAGES_BASE_DIR.is_dir():
        logging.error(f"图像源目录不存在: {IMAGES_BASE_DIR}")
        return pairs
        
    logging.info(f"正在扫描图像目录: {IMAGES_BASE_DIR}...")
    image_extensions = ['.jpg', '.jpeg', '.png']
    
    for img_path in IMAGES_BASE_DIR.rglob('*'):
        if img_path.suffix.lower() in image_extensions:
            # 构建对应的标签路径
            relative_img_path = img_path.relative_to(IMAGES_BASE_DIR)
            label_path = (LABELS_BASE_DIR / relative_img_path).with_suffix('.json')

            if label_path.exists():
                # 使用 '文件夹名_文件名' 作为唯一键，避免冲突
                unique_key = f"{relative_img_path.parent.name}_{img_path.name}"
                pairs[unique_key] = (img_path, label_path)
            else:
                logging.warning(f"未找到图像 '{img_path}' 对应的 .json 标注文件于 '{label_path}'。")

    logging.info(f"共找到 {len(pairs)} 个有效的 图像-标签 对。")
    return pairs

def copy_files(shuffled_pairs: list):
    """
    将打乱顺序后的文件对复制到整合目录。
    """
    IMAGES_DIR.mkdir(parents=True, exist_ok=True)
    LABELS_DIR.mkdir(parents=True, exist_ok=True)
    
    logging.info(f"开始将文件复制到 '{CONSOLIDATED_ROOT}'...")
    for unique_name, (img_path, lbl_path) in shuffled_pairs:
        try:
            # 复制并使用新的唯一名称
            shutil.copy(img_path, IMAGES_DIR / unique_name)
            
            # 读取、更新并写入新的 JSON 文件
            with open(lbl_path, 'r', encoding='utf-8') as f:
                label_data = json.load(f)

            # --- 新的、更智能的标签清洗逻辑 ---
            if "shapes" in label_data:
                for shape in label_data["shapes"]:
                    original_label = shape.get("label")
                    if original_label:
                        # 规则1: 如果包含“暗”，则类别直接就是“暗”
                        if "暗" in original_label:
                            shape["label"] = "暗"
                        # 规则2: 否则，只移除前导的阿拉伯数字
                        else:
                            shape["label"] = original_label.lstrip("0123456789")
            
            # 更新 imagePath 字段以匹配新文件名
            label_data['imagePath'] = unique_name
            
            new_lbl_path = LABELS_DIR / (Path(unique_name).stem + '.json')
            with open(new_lbl_path, 'w', encoding='utf-8') as f:
                json.dump(label_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logging.error(f"复制文件 '{img_path}' 或 '{lbl_path}' 时出错: {e}")
            
    logging.info("文件复制完成。")

def analyze_final_distribution():
    """
    分析最终整合后数据集的类别分布。
    """
    logging.info("正在分析最终的类别分布...")
    class_counts = Counter()
    for lbl_file in LABELS_DIR.glob('*.json'):
        with open(lbl_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                for shape in data.get('shapes', []):
                    class_counts[shape['label']] += 1
            except json.JSONDecodeError:
                logging.warning(f"无法解析JSON文件: {lbl_file}")

    logging.info("最终类别分布情况:")
    for label, count in sorted(class_counts.items()):
        logging.info(f"- {label}: {count}")

def main():
    """主执行函数"""
    if CONSOLIDATED_ROOT.exists():
        logging.info(f"目标目录 '{CONSOLIDATED_ROOT}' 已存在，将进行覆盖。")
        shutil.rmtree(CONSOLIDATED_ROOT)
        
    # 1. 查找所有数据
    all_pairs_dict = find_all_data_pairs()
    
    # 2. 将数据对转换为列表并打乱顺序
    all_pairs_list = list(all_pairs_dict.items())
    random.seed(42)  # 使用固定种子以确保每次打乱结果一致，方便复现
    random.shuffle(all_pairs_list)
    logging.info(f"数据对已打乱，总数: {len(all_pairs_list)}")
    
    # 3. 复制并整合文件
    copy_files(all_pairs_list)
    
    # 4. 分析最终结果
    analyze_final_distribution()
    
    logging.info("数据集整合完成！")
    logging.info(f"图片保存至: {IMAGES_DIR.resolve()}")
    logging.info(f"标注保存至: {LABELS_DIR.resolve()}")

if __name__ == '__main__':
    main() 
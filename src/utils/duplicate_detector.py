#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌训练集重复图像检测脚本
功能：
1. 使用感知哈希算法检测相似/重复图像
2. 支持多种相似度算法（pHash, aHash, dHash）
3. 生成详细的相似度报告
4. 支持按文件夹分组检测
5. 可调节相似度阈值
"""

import os
import argparse
import cv2
import numpy as np
from pathlib import Path
from loguru import logger
import imagehash
from PIL import Image
import pandas as pd
from tqdm import tqdm
from datetime import datetime
import shutil
from collections import defaultdict
import json

# 默认配置
DEFAULT_CONFIG = {
    "image_dir": r"D:\phz-ai-simple\data\xunlianjiyolo\images\train",
    "json_dir": r"D:\phz-ai-simple\data\xunlianjiyolo\labels\train",
    "output_dir": r"D:\phz-ai-simple\data\duplicate_detection",
    "output_report": r"D:\phz-ai-simple\data\duplicate_detection\duplicates_report.csv",
    "hash_size": 16,  # 哈希大小
    "hash_method": "phash",  # 哈希方法: phash, ahash, dhash
    "similarity_threshold": 0.9,  # 相似度阈值 (0-1)
    "folders_to_check": None,  # 默认检查所有文件夹
    "compare_across_folders": True,  # 是否跨文件夹比较
    "max_samples_per_folder": None,  # 每个文件夹的最大样本数
    "create_visual_pairs": True,  # 是否创建可视化对比图
    "max_visual_pairs": 100,  # 最大可视化对比数量
}

class DuplicateDetector:
    def __init__(self, config):
        self.config = config
        self.image_dir = config["image_dir"]
        self.json_dir = config["json_dir"]
        self.output_dir = config["output_dir"]
        self.output_report = config["output_report"]
        self.hash_size = config["hash_size"]
        self.hash_method = config["hash_method"]
        self.similarity_threshold = config["similarity_threshold"]
        self.folders_to_check = config["folders_to_check"]
        self.compare_across_folders = config["compare_across_folders"]
        self.max_samples_per_folder = config["max_samples_per_folder"]
        self.create_visual_pairs = config["create_visual_pairs"]
        self.max_visual_pairs = config["max_visual_pairs"]
        
        # 初始化日志
        self._setup_logging()
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        if self.create_visual_pairs:
            os.makedirs(os.path.join(self.output_dir, "visual_pairs"), exist_ok=True)
        
        # 初始化结果存储
        self.duplicate_pairs = []
        self.image_hashes = {}
        self.total_images = 0
        self.total_duplicates = 0
        
    def _setup_logging(self):
        """配置日志系统"""
        log_file = os.path.join(self.output_dir, "duplicate_detector.log")
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logger.remove()  # 移除默认处理器
        logger.add(log_file, rotation="10 MB", retention="7 days", 
                  level="INFO", encoding="utf-8")
        logger.add(lambda msg: tqdm.write(msg, end=""), level="INFO")
        
        logger.info(f"初始化重复图像检测器，配置: {self.config}")
    
    def _compute_image_hash(self, image_path, method="phash"):
        """计算图像哈希值"""
        try:
            img = Image.open(image_path)
            
            if method == "phash":
                return imagehash.phash(img, hash_size=self.hash_size)
            elif method == "ahash":
                return imagehash.average_hash(img, hash_size=self.hash_size)
            elif method == "dhash":
                return imagehash.dhash(img, hash_size=self.hash_size)
            else:
                return imagehash.phash(img, hash_size=self.hash_size)
        except Exception as e:
            logger.error(f"计算图像哈希值失败 {image_path}: {e}")
            return None
    
    def _calculate_similarity(self, hash1, hash2):
        """计算两个哈希值的相似度"""
        if hash1 is None or hash2 is None:
            return 0
        
        # 计算汉明距离
        distance = hash1 - hash2
        # 转换为相似度 (0-1)
        max_distance = self.hash_size * self.hash_size
        similarity = 1 - (distance / max_distance)
        return similarity
    
    def _create_visual_comparison(self, img_path1, img_path2, similarity, pair_index):
        """创建视觉对比图"""
        try:
            img1 = cv2.imread(img_path1)
            img2 = cv2.imread(img_path2)
            
            if img1 is None or img2 is None:
                logger.warning(f"无法读取图像进行可视化对比: {img_path1} 或 {img_path2}")
                return
            
            # 调整图像大小以便并排显示
            h1, w1 = img1.shape[:2]
            h2, w2 = img2.shape[:2]
            
            # 计算目标高度 (保持宽高比)
            target_height = min(400, max(h1, h2))
            new_w1 = int(w1 * (target_height / h1))
            new_w2 = int(w2 * (target_height / h2))
            
            img1_resized = cv2.resize(img1, (new_w1, target_height))
            img2_resized = cv2.resize(img2, (new_w2, target_height))
            
            # 创建并排图像
            combined_width = new_w1 + new_w2 + 20  # 添加间隔
            combined_img = np.ones((target_height + 40, combined_width, 3), dtype=np.uint8) * 255
            
            # 放置图像
            combined_img[:target_height, :new_w1] = img1_resized
            combined_img[:target_height, new_w1+20:] = img2_resized
            
            # 添加文本信息
            font = cv2.FONT_HERSHEY_SIMPLEX
            img1_name = os.path.basename(img_path1)
            img2_name = os.path.basename(img_path2)
            
            cv2.putText(combined_img, f"{img1_name}", (10, target_height + 20), 
                        font, 0.5, (0, 0, 0), 1)
            cv2.putText(combined_img, f"{img2_name} (相似度: {similarity:.3f})", 
                        (new_w1 + 30, target_height + 20), font, 0.5, (0, 0, 0), 1)
            
            # 保存图像
            output_path = os.path.join(self.output_dir, "visual_pairs", f"pair_{pair_index:04d}.jpg")
            cv2.imwrite(output_path, combined_img)
            
        except Exception as e:
            logger.error(f"创建视觉对比图失败: {e}")
    
    def detect_duplicates(self):
        """检测重复图像"""
        logger.info(f"开始检测重复图像，使用 {self.hash_method} 算法，相似度阈值: {self.similarity_threshold}")
        
        # 获取要检查的文件夹列表
        if self.folders_to_check is None:
            folders = [f for f in os.listdir(self.image_dir) 
                      if os.path.isdir(os.path.join(self.image_dir, f))]
        else:
            folders = self.folders_to_check
            
        logger.info(f"将检查 {len(folders)} 个文件夹")
        
        # 收集所有图像路径和计算哈希值
        all_images = []
        folder_images = {}
        
        for folder in tqdm(folders, desc="收集图像"):
            image_folder = os.path.join(self.image_dir, folder)
            if not os.path.exists(image_folder):
                logger.warning(f"图像文件夹不存在: {image_folder}")
                continue
                
            # 获取图像文件列表
            image_files = [f for f in os.listdir(image_folder)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            # 限制每个文件夹的样本数
            if self.max_samples_per_folder and len(image_files) > self.max_samples_per_folder:
                image_files = np.random.choice(image_files, self.max_samples_per_folder, replace=False)
            
            folder_images[folder] = []
            
            for img_file in image_files:
                img_path = os.path.join(image_folder, img_file)
                all_images.append((folder, img_file, img_path))
                folder_images[folder].append((img_file, img_path))
                
        self.total_images = len(all_images)
        logger.info(f"共收集到 {self.total_images} 张图像")
        
        # 计算所有图像的哈希值
        for folder, img_file, img_path in tqdm(all_images, desc="计算图像哈希"):
            img_hash = self._compute_image_hash(img_path, self.hash_method)
            if img_hash is not None:
                self.image_hashes[(folder, img_file)] = img_hash
        
        logger.info(f"成功计算 {len(self.image_hashes)} 张图像的哈希值")
        
        # 比较图像哈希值以查找重复
        if self.compare_across_folders:
            # 跨文件夹比较所有图像
            self._compare_all_images()
        else:
            # 仅在每个文件夹内比较
            for folder, images in tqdm(folder_images.items(), desc="在文件夹内比较"):
                self._compare_folder_images(folder, images)
                
        # 生成报告
        self._generate_report()
        
        logger.info(f"检测完成! 共发现 {self.total_duplicates} 对相似图像")
        
    def _compare_all_images(self):
        """比较所有图像以查找重复"""
        logger.info("开始跨文件夹比较所有图像...")
        
        # 获取所有哈希键
        hash_keys = list(self.image_hashes.keys())
        n = len(hash_keys)
        
        # 计算总比较次数
        total_comparisons = (n * (n - 1)) // 2
        
        with tqdm(total=total_comparisons, desc="比较图像") as pbar:
            for i in range(n):
                folder1, img1 = hash_keys[i]
                hash1 = self.image_hashes[(folder1, img1)]
                
                for j in range(i + 1, n):
                    folder2, img2 = hash_keys[j]
                    hash2 = self.image_hashes[(folder2, img2)]
                    
                    # 计算相似度
                    similarity = self._calculate_similarity(hash1, hash2)
                    
                    # 如果相似度超过阈值，记录为重复
                    if similarity >= self.similarity_threshold:
                        img_path1 = os.path.join(self.image_dir, folder1, img1)
                        img_path2 = os.path.join(self.image_dir, folder2, img2)
                        
                        self.duplicate_pairs.append({
                            'folder1': folder1,
                            'image1': img1,
                            'folder2': folder2,
                            'image2': img2,
                            'similarity': similarity,
                            'path1': img_path1,
                            'path2': img_path2
                        })
                        
                    pbar.update(1)
    
    def _compare_folder_images(self, folder, images):
        """在单个文件夹内比较图像"""
        n = len(images)
        if n <= 1:
            return
            
        for i in range(n):
            img1, img_path1 = images[i]
            hash1 = self.image_hashes.get((folder, img1))
            if hash1 is None:
                continue
                
            for j in range(i + 1, n):
                img2, img_path2 = images[j]
                hash2 = self.image_hashes.get((folder, img2))
                if hash2 is None:
                    continue
                    
                # 计算相似度
                similarity = self._calculate_similarity(hash1, hash2)
                
                # 如果相似度超过阈值，记录为重复
                if similarity >= self.similarity_threshold:
                    self.duplicate_pairs.append({
                        'folder1': folder,
                        'image1': img1,
                        'folder2': folder,
                        'image2': img2,
                        'similarity': similarity,
                        'path1': img_path1,
                        'path2': img_path2
                    })
    
    def _generate_report(self):
        """生成重复图像报告"""
        if not self.duplicate_pairs:
            logger.info("未发现重复图像")
            with open(os.path.join(self.output_dir, "no_duplicates.txt"), 'w', encoding='utf-8') as f:
                f.write("未发现重复图像\n")
                f.write(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"相似度阈值: {self.similarity_threshold}\n")
                f.write(f"哈希方法: {self.hash_method}\n")
                f.write(f"检查的图像总数: {self.total_images}\n")
            return
            
        # 按相似度排序
        self.duplicate_pairs.sort(key=lambda x: x['similarity'], reverse=True)
        self.total_duplicates = len(self.duplicate_pairs)
        
        # 创建DataFrame并保存为CSV
        df = pd.DataFrame(self.duplicate_pairs)
        df.to_csv(self.output_report, index=False, encoding='utf-8')
        
        # 创建可视化对比图
        if self.create_visual_pairs:
            num_pairs = min(self.max_visual_pairs, len(self.duplicate_pairs))
            logger.info(f"创建 {num_pairs} 个可视化对比图...")
            
            for i, pair in enumerate(self.duplicate_pairs[:num_pairs]):
                self._create_visual_comparison(
                    pair['path1'], 
                    pair['path2'], 
                    pair['similarity'], 
                    i
                )
        
        # 创建摘要报告
        summary_path = os.path.join(self.output_dir, "duplicates_summary.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("跑胡子卡牌训练集重复图像检测摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"相似度阈值: {self.similarity_threshold}\n")
            f.write(f"哈希方法: {self.hash_method}\n")
            f.write(f"检查的图像总数: {self.total_images}\n")
            f.write(f"发现的重复对数: {self.total_duplicates}\n\n")
            
            # 按文件夹统计重复
            folder_stats = defaultdict(int)
            for pair in self.duplicate_pairs:
                folder_stats[pair['folder1']] += 1
                if pair['folder1'] != pair['folder2']:
                    folder_stats[pair['folder2']] += 1
                    
            f.write("文件夹重复统计:\n")
            for folder, count in sorted(folder_stats.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- {folder}: {count} 个重复\n")
                
            # 写入前10个最相似的对
            f.write("\n最相似的10对图像:\n")
            for i, pair in enumerate(self.duplicate_pairs[:10]):
                f.write(f"{i+1}. {pair['folder1']}/{pair['image1']} 与 {pair['folder2']}/{pair['image2']} "
                       f"(相似度: {pair['similarity']:.3f})\n")
                
        logger.info(f"报告已生成: {self.output_report}")
        logger.info(f"摘要报告: {summary_path}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="跑胡子卡牌训练集重复图像检测脚本")
    parser.add_argument("--image-dir", type=str, default=DEFAULT_CONFIG["image_dir"],
                        help="图像目录路径")
    parser.add_argument("--json-dir", type=str, default=DEFAULT_CONFIG["json_dir"],
                        help="JSON标注目录路径")
    parser.add_argument("--output-dir", type=str, default=DEFAULT_CONFIG["output_dir"],
                        help="输出目录路径")
    parser.add_argument("--hash-method", type=str, default=DEFAULT_CONFIG["hash_method"],
                        choices=["phash", "ahash", "dhash"],
                        help="哈希方法: phash, ahash, dhash")
    parser.add_argument("--similarity", type=float, default=DEFAULT_CONFIG["similarity_threshold"],
                        help="相似度阈值 (0-1)")
    parser.add_argument("--folders", type=str, default=None,
                        help="要检查的文件夹列表，用逗号分隔")
    parser.add_argument("--no-cross-folders", action="store_true",
                        help="禁用跨文件夹比较")
    parser.add_argument("--max-samples", type=int, default=DEFAULT_CONFIG["max_samples_per_folder"],
                        help="每个文件夹的最大样本数")
    parser.add_argument("--no-visual", action="store_true",
                        help="禁用可视化对比图生成")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    
    # 更新配置
    config = DEFAULT_CONFIG.copy()
    config.update({
        "image_dir": args.image_dir,
        "json_dir": args.json_dir,
        "output_dir": args.output_dir,
        "hash_method": args.hash_method,
        "similarity_threshold": args.similarity,
        "folders_to_check": [f.strip() for f in args.folders.split(",")] if args.folders else None,
        "compare_across_folders": not args.no_cross_folders,
        "max_samples_per_folder": args.max_samples,
        "create_visual_pairs": not args.no_visual
    })
    
    detector = DuplicateDetector(config)
    detector.detect_duplicates() 
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
rare_class_projector.py  

功能：
    从模板目录 (PNG/透明背景) 读取稀有类别图像，将其粘贴到现有数据集中
    的图像空白区域，在不遮挡现有标注框的前提下，自动生成合成图及标注。

核心特性：
1. 每张合成图随机插入 5–10 个稀有类别实例。
2. 生成 300 张合成图（可通过 CLI 参数调整）。
3. 支持 JSON → JSON 标注格式，保持与 AnyLabeling 兼容。
4. 贴图位置采用网格 + IoU 检测，确保不重叠原/新框。
5. 保证贴图后尺寸比例适配 (最多占目标区域 90%)，且不做水平翻转偏移。

使用示例：
    python rare_class_projector.py \
        --template-dir "D:/phz-ai-simple/data/muban" \
        --src-image-dir "D:/phz-ai-simple/data/xunlianjiyolo/consolidated_shuffled/images" \
        --src-label-dir "D:/phz-ai-simple/data/xunlianjiyolo/consolidated_shuffled/labels" \
        --output-root "D:/phz-ai-simple/data/synthetic" \
        --num-output 300
"""

import os
import json
import random
import shutil
import logging
from pathlib import Path
from typing import List, Tuple, Dict

import cv2
import numpy as np
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger("RareProjector")

# ------------------------------- 配置与帮助函数 ------------------------------- #

# 稀有类别映射与 train_yolo.py 保持一致
LABEL_TO_ID = {
    # 数字牌 1-10
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5,
    "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
    # 大写
    "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15,
    "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
    # 功能/稀有类 21-31
    "吃": 22, "碰": 23, "胡": 24, "过": 25,
    "打鸟选择": 26, "已准备": 27, "你赢了": 28, "你输了": 29,
    "荒庄": 30, "牌局结束": 31,
}
ID_TO_LABEL = {v: k for k, v in LABEL_TO_ID.items()}

# 解析模板文件名，假设文件名形如 "荒庄.png" 或 "荒庄_001.png"

def infer_label_from_filename(fname: str) -> str:
    stem = Path(fname).stem
    for label in LABEL_TO_ID:
        if label in stem:
            return label
    return ""


def load_templates(template_dir: str) -> Dict[str, List[np.ndarray]]:
    """加载模板 PNG，按类别分组返回字典（增加详细调试日志）"""
    templates: Dict[str, List[np.ndarray]] = {}
    
    p_template_dir = Path(template_dir)
    if not p_template_dir.is_dir():
        logger.error(f"模板目录不存在: '{template_dir}'")
        return templates

    # 使用 glob.glob 和 os.path.join 来避免 pathlib 在某些 Windows 边角情况下的问题
    import glob
    file_list = glob.glob(os.path.join(template_dir, "*.png"))
    logger.info(f"在 '{template_dir}' 中找到 {len(file_list)} 个 .png 文件，开始逐个解析...")

    for file_path_str in file_list:
        file = Path(file_path_str)
        logger.info(f"--- 处理文件: {file} ---")
        
        label = infer_label_from_filename(file.name)
        if not label:
            logger.warning(f"无法从文件名 '{file.name}' 推断类别，跳过。")
            continue
        
        if not file.exists() or file.stat().st_size == 0:
            logger.warning("文件不存在或大小为0。")
            continue

        img = None
        # 1. OpenCV 读取
        try:
            data_buf = np.fromfile(str(file), dtype=np.uint8)
            img = cv2.imdecode(data_buf, cv2.IMREAD_UNCHANGED)
            if img is None:
                logger.warning("cv2.imdecode 返回 None，尝试 Pillow。")
        except Exception as e:
            logger.error(f"OpenCV 读取时发生意外错误: {e}")
            img = None

        # 2. Pillow 兜底
        if img is None:
            try:
                from PIL import Image
                pil_img = Image.open(str(file))
                logger.info(f"Pillow 读取成功: format={pil_img.format}, mode={pil_img.mode}, size={pil_img.size}")
                pil_img = pil_img.convert("RGBA")
                img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGBA2BGRA)
            except Exception as e:
                logger.error(f"Pillow 读取失败，错误: {e}")
                continue

        # 3. 通道转换
        if img.ndim == 2:
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGRA)
            logger.info("灰度图已转换为 BGRA。")
        elif img.ndim == 3 and img.shape[2] == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2BGRA)
            logger.info("BGR 图已转换为 BGRA。")

        if img.shape[2] != 4:
            logger.warning(f"最终通道数不为4 (shape={img.shape})，跳过。")
            continue

        logger.info(f"成功加载并处理 '{file.name}' -> 类别 '{label}'")
        templates.setdefault(label, []).append(img)

    logger.info("-" * 20)
    if templates:
        logger.info(f"加载完成! 共加载 {len(templates)} 个类别: {list(templates.keys())}")
    else:
        logger.error("加载失败! 未成功加载任何模板。请检查上面的详细日志。")
    return templates


def iou_xyxy(box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]) -> float:
    x1, y1, x2, y2 = box1
    x1g, y1g, x2g, y2g = box2
    inter_x1 = max(x1, x1g)
    inter_y1 = max(y1, y1g)
    inter_x2 = min(x2, x2g)
    inter_y2 = min(y2, y2g)
    if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
        return 0.0
    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    area1 = (x2 - x1) * (y2 - y1)
    area2 = (x2g - x1g) * (y2g - y1g)
    return inter_area / float(area1 + area2 - inter_area + 1e-6)


# ------------------------------- 主流程 ------------------------------- #


def main():
    import argparse

    parser = argparse.ArgumentParser("稀有类别贴图脚本")
    parser.add_argument("--template-dir", type=str, required=True, help="模板 PNG 目录")
    parser.add_argument("--src-image-dir", type=str, default="", help="可选：原图目录；留空则使用纯空白背景")
    parser.add_argument("--src-label-dir", type=str, default="", help="可选：原 JSON 目录，纯合成模式可留空")
    parser.add_argument("--output-root", type=str, required=True, help="输出根目录")
    parser.add_argument("--num-output", type=int, default=300, help="生成合成图数量")
    parser.add_argument("--min-per-image", type=int, default=5)
    parser.add_argument("--max-per-image", type=int, default=10)
    parser.add_argument("--seed", type=int, default=0)
    args = parser.parse_args()

    random.seed(args.seed)

    out_img_dir = Path(args.output_root) / "images"
    out_lbl_dir = Path(args.output_root) / "labels"
    out_img_dir.mkdir(parents=True, exist_ok=True)
    out_lbl_dir.mkdir(parents=True, exist_ok=True)

    templates = load_templates(args.template_dir)
    if not templates:
        logger.error("未加载到任何模板，终止。")
        return

    pure_synth = not args.src_image_dir
    if not pure_synth:
        image_files = sorted(list(Path(args.src_image_dir).rglob("*.jpg")))
        if not image_files:
            logger.error("未找到原始图像。将改为纯合成模式")
            pure_synth = True
    if pure_synth:
        logger.info("进入纯合成模式：将创建空白 640x320 画布")

    if not pure_synth:
        logger.info(f"原始图像数量: {len(image_files)}，计划生成 {args.num_output} 张合成图")

    # 为均衡统计每类已投射次数
    injected_count = {cls: 0 for cls in LABEL_TO_ID}

    # 主循环
    for idx in tqdm(range(args.num_output), desc="合成中"):
        if pure_synth:
            w, h = 640, 320
            base_gray = random.randint(80, 120)  # 深灰底色
            img = np.full((h, w, 3), base_gray, dtype=np.uint8)
            noise = np.random.normal(0, 8, img.shape).astype(np.int16)
            img = np.clip(img + noise, 0, 255).astype(np.uint8)
            existing_boxes = []
            data = {
                "version": "2.4.3",
                "flags": {},
                "shapes": [],
            }
        else:
            src_img_path = random.choice(image_files)
            src_lbl_path = Path(args.src_label_dir) / (src_img_path.stem + ".json")
            if not src_lbl_path.exists():
                continue  # 跳过无标注
            img = cv2.imread(str(src_img_path))
            if img is None:
                continue
            h, w = img.shape[:2]
            with open(src_lbl_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            existing_boxes = []
            for sh in data.get("shapes", []):
                pts = sh.get("points", [])
                xs = [p[0] for p in pts]
                ys = [p[1] for p in pts]
                existing_boxes.append((int(min(xs)), int(min(ys)), int(max(xs)), int(max(ys))))

        # 占用 mask (grid 8x8) 以快速过滤
        grid_size = 32
        occupied = np.zeros((h // grid_size, w // grid_size), dtype=np.uint8)
        for (x1, y1, x2, y2) in existing_boxes:
            gx1, gy1 = x1 // grid_size, y1 // grid_size
            gx2, gy2 = x2 // grid_size, y2 // grid_size
            occupied[gy1:gy2 + 1, gx1:gx2 + 1] = 1

        num_inject = random.randint(args.min_per_image, args.max_per_image)
        new_shapes = []  # JSON 追加
        attempts = 0
        while len(new_shapes) < num_inject and attempts < num_inject * 20:
            attempts += 1
            # 选择需要补充的类别（按当前最少）
            cls_to_pick = min([c for c in injected_count if c in templates], key=lambda c: injected_count[c])

            if not templates.get(cls_to_pick):
                continue

            tmpl = random.choice(templates[cls_to_pick])
            th, tw = tmpl.shape[:2]

            # 随机缩放 0.6~1.0
            scale = random.uniform(0.6, 1.0)
            new_tw = int(tw * scale)
            new_th = int(th * scale)
            resized = cv2.resize(tmpl, (new_tw, new_th), interpolation=cv2.INTER_AREA)
            # 随机找位置
            max_x = w - new_tw - 1
            max_y = h - new_th - 1
            if max_x <= 0 or max_y <= 0:
                continue
            px = random.randint(0, max_x)
            py = random.randint(0, max_y)
            new_box = (px, py, px + new_tw, py + new_th)
            # 快速网格占用检查
            gx1, gy1 = px // grid_size, py // grid_size
            gx2, gy2 = (px + new_tw) // grid_size, (py + new_th) // grid_size
            if occupied[gy1:gy2 + 1, gx1:gx2 + 1].any():
                continue
            # 精确 IoU 检测
            if any(iou_xyxy(b, new_box) > 0 for b in existing_boxes):
                continue
            # 通过检测，粘贴
            alpha = resized[:, :, 3] / 255.0
            for c in range(3):
                img[py:py + new_th, px:px + new_tw, c] = (
                    alpha * resized[:, :, c] + (1 - alpha) * img[py:py + new_th, px:px + new_tw, c]
                )
            existing_boxes.append(new_box)
            occupied[gy1:gy2 + 1, gx1:gx2 + 1] = 1
            injected_count[cls_to_pick] += 1

            # 添加 JSON shape, 兼容 AnyLabeling
            x1n, y1n, x2n, y2n = new_box
            new_shape = {
                "label": cls_to_pick,
                "score": 1.0,
                "points": [
                    [float(x1n), float(y1n)], [float(x2n), float(y1n)],
                    [float(x2n), float(y2n)], [float(x1n), float(y2n)]
                ],
                "group_id": None,
                "description": None,
                "difficult": False,
                "shape_type": "rectangle",
                "flags": {},
                "attributes": {},
                "kie_linking": []
            }
            new_shapes.append(new_shape)

        if not new_shapes:
            continue  # 无成功注入

        # 保存新图 & JSON
        base_name = f"synthetic_{idx:04d}" if pure_synth else f"{src_img_path.stem}_proj{idx:04d}"
        new_name = base_name
        cv2.imwrite(str(out_img_dir / f"{new_name}.jpg"), img)
        
        # 严格按照 AnyLabeling 格式构建最终 JSON
        all_shapes = data.get("shapes", []) + new_shapes
        final_json_data = {
            "version": "2.4.3",
            "flags": {},
            "shapes": all_shapes,
            "imagePath": f"{new_name}.jpg",
            "imageData": None,
            "imageHeight": h,
            "imageWidth": w,
            "description": ""
        }

        with open(out_lbl_dir / f"{new_name}.json", "w", encoding="utf-8") as f:
            json.dump(final_json_data, f, ensure_ascii=False, indent=2)

    logger.info("投射完成，各类别注入计数:")
    for cls, cnt in injected_count.items():
        logger.info(f"{cls}: {cnt}")


if __name__ == "__main__":
    main() 
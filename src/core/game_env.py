"""
简化的跑胡子游戏环境
不完全依赖RLCard，而是借鉴其设计模式
"""

class PaohuziGameEnv:
    """
    轻量级跑胡子游戏环境
    借鉴RLCard设计但针对跑胡子优化
    """
    def __init__(self):
        self.state = GameState()
        self.rules = PaohuziRules()
        
    def step(self, action):
        """执行动作并返回新状态"""
        new_state = self.rules.apply_action(self.state, action)
        reward = self.rules.calculate_reward(new_state)
        done = self.rules.is_game_over(new_state)
        
        return new_state, reward, done
    
    def get_legal_actions(self):
        """获取当前状态下的合法动作"""
        return self.rules.get_legal_actions(self.state)

class GameState:
    """
    跑胡子游戏状态表示
    专门为跑胡子设计，不受RLCard限制
    """
    def __init__(self):
        self.regions = {
            'hand_player': [],      # 玩家手牌
            'hand_opponent': [],    # 对手手牌
            'chi_peng_player': [],  # 玩家吃碰区
            'chi_peng_opponent': [], # 对手吃碰区
            'discard_player': [],   # 玩家弃牌
            'discard_opponent': [], # 对手弃牌
            'draw_pile': [],        # 底牌
            'current_draw': None,   # 当前抓牌
        }
        self.special_states = {
            'bi_pai_options': [],   # 比牌选项
            'chou_pai_list': [],    # 臭牌列表
        }
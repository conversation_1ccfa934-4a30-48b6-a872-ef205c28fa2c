"""
增强区域分类器

基于zhuangtaiquyu数据集的深度分析结果，开发精确的区域推断算法。
目标：将区域分配准确率从72%提升到90%+

核心改进：
1. 基于真实数据的精确边界定义
2. 优先级驱动的区域分配策略
3. 重叠区域的智能处理
4. 多特征融合的分类决策
"""

import json
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

class EnhancedRegionClassifier:
    """增强区域分类器"""
    
    def __init__(self):
        self.region_rules = self._load_region_rules()
        self.confidence_threshold = 0.8
        
    def _load_region_rules(self) -> Dict[int, Dict[str, Any]]:
        """加载基于数据分析的区域规则"""
        # 基于analysis/region_analysis_results.json的精确数据
        rules = {
            1: {  # 手牌区_观战方
                "name": "手牌区_观战方",
                "x_boundary": (0.188, 0.783),
                "y_boundary": (0.595, 1.000),
                "x_center": 0.485,
                "y_center": 0.808,
                "priority": 1,
                "confidence_boost": 0.1  # 主要区域，提升置信度
            },
            6: {  # 吃碰区_观战方
                "name": "吃碰区_观战方", 
                "x_boundary": (0.377, 0.525),
                "y_boundary": (0.239, 0.421),
                "x_center": 0.451,
                "y_center": 0.330,
                "priority": 2,
                "confidence_boost": 0.05
            },
            5: {  # 弃牌区_观战方
                "name": "弃牌区_观战方",
                "x_boundary": (0.840, 1.000),
                "y_boundary": (0.889, 1.000),
                "x_center": 0.927,
                "y_center": 0.967,
                "priority": 3,
                "confidence_boost": 0.08  # 位置特征明显
            },
            16: {  # 吃碰区_对战方
                "name": "吃碰区_对战方",
                "x_boundary": (0.093, 0.191),
                "y_boundary": (0.087, 0.304),
                "x_center": 0.142,
                "y_center": 0.196,
                "priority": 4,
                "confidence_boost": 0.05
            },
            9: {  # 对战方区域
                "name": "对战方区域",
                "x_boundary": (0.087, 0.230),
                "y_boundary": (0.322, 0.378),
                "x_center": 0.159,
                "y_center": 0.350,
                "priority": 5,
                "confidence_boost": 0.03
            },
            12: {  # 其他区域12
                "name": "其他区域12",
                "x_boundary": (0.121, 0.230),
                "y_boundary": (0.460, 0.505),
                "x_center": 0.176,
                "y_center": 0.483,
                "priority": 6,
                "confidence_boost": 0.0
            }
        }
        
        return rules
    
    def classify_region(self, bbox: List[float], image_width: int = 640, image_height: int = 320) -> Tuple[int, float]:
        """分类卡牌所属区域
        
        Args:
            bbox: 边界框 [x1, y1, x2, y2]
            image_width: 图像宽度
            image_height: 图像高度
            
        Returns:
            (region_id, confidence): 区域ID和置信度
        """
        # 计算归一化中心点
        x_center = (bbox[0] + bbox[2]) / 2 / image_width
        y_center = (bbox[1] + bbox[3]) / 2 / image_height
        
        # 计算每个区域的匹配分数
        region_scores = []
        
        for region_id, rule in self.region_rules.items():
            score = self._calculate_region_score(x_center, y_center, rule)
            if score > 0:  # 只考虑有效分数
                region_scores.append((region_id, score, rule["priority"]))
        
        if not region_scores:
            # 没有匹配的区域，使用默认策略
            return self._fallback_classification(x_center, y_center)
        
        # 按优先级和分数排序
        region_scores.sort(key=lambda x: (-x[1], x[2]))  # 分数降序，优先级升序
        
        best_region, best_score, _ = region_scores[0]
        
        # 转换分数为置信度
        confidence = min(0.95, best_score)
        
        return best_region, confidence
    
    def _calculate_region_score(self, x_norm: float, y_norm: float, rule: Dict[str, Any]) -> float:
        """计算区域匹配分数"""
        x_min, x_max = rule["x_boundary"]
        y_min, y_max = rule["y_boundary"]
        
        # 检查是否在边界内
        if not (x_min <= x_norm <= x_max and y_min <= y_norm <= y_max):
            return 0.0
        
        # 计算到中心点的距离（归一化）
        x_center = rule["x_center"]
        y_center = rule["y_center"]
        
        # 计算相对距离（在边界范围内）
        x_range = x_max - x_min
        y_range = y_max - y_min
        
        x_distance = abs(x_norm - x_center) / x_range if x_range > 0 else 0
        y_distance = abs(y_norm - y_center) / y_range if y_range > 0 else 0
        
        # 距离越小，分数越高
        distance_score = 1.0 - (x_distance + y_distance) / 2
        
        # 应用置信度提升
        confidence_boost = rule.get("confidence_boost", 0.0)
        final_score = min(1.0, distance_score + confidence_boost)
        
        return final_score
    
    def _fallback_classification(self, x_norm: float, y_norm: float) -> Tuple[int, float]:
        """备用分类策略（当没有区域匹配时）"""
        # 基于位置的简单规则
        if y_norm > 0.7:  # 下方
            if x_norm > 0.8:
                return 5, 0.3  # 可能是弃牌区
            else:
                return 1, 0.4  # 可能是手牌区
        elif y_norm < 0.4:  # 上方
            if x_norm < 0.3:
                return 16, 0.3  # 可能是对战方区域
            else:
                return 6, 0.3   # 可能是吃碰区
        else:  # 中间
            return 1, 0.2  # 默认手牌区
    
    def batch_classify(self, bboxes: List[List[float]], image_width: int = 640, image_height: int = 320) -> List[Tuple[int, float]]:
        """批量分类"""
        results = []
        for bbox in bboxes:
            region_id, confidence = self.classify_region(bbox, image_width, image_height)
            results.append((region_id, confidence))
        return results
    
    def get_region_statistics(self) -> Dict[str, Any]:
        """获取区域分类器统计信息"""
        return {
            "total_regions": len(self.region_rules),
            "confidence_threshold": self.confidence_threshold,
            "region_names": {rid: rule["name"] for rid, rule in self.region_rules.items()},
            "region_priorities": {rid: rule["priority"] for rid, rule in self.region_rules.items()}
        }
    
    def visualize_regions(self, image_width: int = 640, image_height: int = 320) -> Dict[str, Any]:
        """可视化区域边界（用于调试）"""
        regions_info = {}
        
        for region_id, rule in self.region_rules.items():
            x_min, x_max = rule["x_boundary"]
            y_min, y_max = rule["y_boundary"]
            
            # 转换为像素坐标
            pixel_bbox = [
                x_min * image_width,
                y_min * image_height,
                x_max * image_width,
                y_max * image_height
            ]
            
            regions_info[region_id] = {
                "name": rule["name"],
                "normalized_bbox": [x_min, y_min, x_max, y_max],
                "pixel_bbox": pixel_bbox,
                "center": [rule["x_center"] * image_width, rule["y_center"] * image_height],
                "priority": rule["priority"]
            }
        
        return regions_info

class RegionClassifierValidator:
    """区域分类器验证器"""
    
    def __init__(self, classifier: EnhancedRegionClassifier):
        self.classifier = classifier
        
    def validate_on_ground_truth(self, data_path: Path) -> Dict[str, Any]:
        """在真实数据上验证分类器性能"""
        print("🔍 验证增强区域分类器...")
        
        total_cards = 0
        correct_predictions = 0
        region_confusion = {}
        
        # 处理数据
        sequence_dirs = [d for d in data_path.iterdir() if d.is_dir()]
        
        for seq_dir in sorted(sequence_dirs)[:3]:  # 验证前3个序列
            print(f"  📁 验证序列: {seq_dir.name}")
            
            json_files = sorted(seq_dir.glob("*.json"))[:5]  # 每序列前5帧
            
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    image_width = data.get("imageWidth", 640)
                    image_height = data.get("imageHeight", 320)
                    
                    for shape in data.get("shapes", []):
                        if len(shape.get("points", [])) >= 4:
                            points = shape["points"]
                            x1, y1 = points[0]
                            x2, y2 = points[2]
                            bbox = [x1, y1, x2, y2]
                            
                            true_region = shape.get("group_id")
                            if true_region is None:
                                continue
                                
                            # 使用分类器预测
                            predicted_region, confidence = self.classifier.classify_region(
                                bbox, image_width, image_height
                            )
                            
                            total_cards += 1
                            
                            if predicted_region == true_region:
                                correct_predictions += 1
                            else:
                                # 记录混淆
                                key = f"{true_region}->{predicted_region}"
                                region_confusion[key] = region_confusion.get(key, 0) + 1
                
                except Exception as e:
                    print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        # 计算准确率
        accuracy = correct_predictions / total_cards if total_cards > 0 else 0
        
        print(f"  📊 验证结果: {accuracy:.1%} ({correct_predictions}/{total_cards})")
        
        return {
            "accuracy": accuracy,
            "total_cards": total_cards,
            "correct_predictions": correct_predictions,
            "region_confusion": region_confusion
        }

def main():
    """测试增强区域分类器"""
    print("🚀 测试增强区域分类器")
    print("=" * 40)
    
    # 创建分类器
    classifier = EnhancedRegionClassifier()
    
    # 显示区域信息
    print("📋 区域配置:")
    stats = classifier.get_region_statistics()
    for region_id, name in stats["region_names"].items():
        priority = stats["region_priorities"][region_id]
        print(f"  区域{region_id}: {name} (优先级: {priority})")
    
    # 测试样例
    print("\n🧪 测试样例:")
    test_cases = [
        ([300, 250, 350, 300], "手牌区中心"),
        ([450, 100, 500, 150], "吃碰区_观战方"),
        ([600, 300, 640, 320], "弃牌区"),
        ([100, 50, 150, 100], "对战方区域")
    ]
    
    for bbox, description in test_cases:
        region_id, confidence = classifier.classify_region(bbox)
        region_name = stats["region_names"].get(region_id, f"未知区域{region_id}")
        print(f"  {description}: 区域{region_id} ({region_name}) 置信度: {confidence:.3f}")
    
    # 在真实数据上验证
    data_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
    if data_path.exists():
        print("\n🔍 真实数据验证:")
        validator = RegionClassifierValidator(classifier)
        results = validator.validate_on_ground_truth(data_path)
        
        print(f"整体准确率: {results['accuracy']:.1%}")
        if results['region_confusion']:
            print("主要错误模式:")
            sorted_errors = sorted(results['region_confusion'].items(), key=lambda x: x[1], reverse=True)
            for error_pattern, count in sorted_errors[:5]:
                print(f"  {error_pattern}: {count}次")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()

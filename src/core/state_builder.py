import numpy as np
import json
import os
from collections import defaultdict
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class CardState(Enum):
    """卡牌状态枚举"""
    MING_PAI = "明牌"  # 明牌
    AN_PAI = "暗牌"    # 暗牌

class ActionType(Enum):
    """动作类型枚举"""
    PLAY = "play"      # 打牌
    CHI = "chi"        # 吃牌
    PENG = "peng"      # 碰牌
    WEI = "wei"        # 偎牌（自动）
    TI = "ti"          # 提牌（自动）
    PAO = "pao"        # 跑牌（自动）
    HU = "hu"          # 胡牌
    PASS = "pass"      # 过

@dataclass
class Card:
    """卡牌数据结构"""
    id: str                    # 唯一ID，如"1_二"
    label: str                 # 牌面，如"二"
    group_id: int             # 区域ID
    state: CardState          # 状态：明牌/暗牌
    confidence: float         # 检测置信度
    position: Tuple[int, int, int, int]  # 位置信息 (x, y, w, h)

class StateBuilder:
    """
    改进的状态转换模块
    将YOLO检测结果转换为跑胡子游戏状态，支持特殊组合和智能决策
    """

    def __init__(self, config_path=None):
        """
        初始化状态转换模块

        Args:
            config_path (str): 配置文件路径
        """
        self.config = {}
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.json'):
                    self.config = json.load(f)
                elif config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    import yaml
                    self.config = yaml.safe_load(f)
                else:
                    print(f"不支持的配置文件格式: {config_path}")

        # 区域ID映射
        self.region_mapping = {
            1: "hand_player",        # 玩家手牌
            2: "hand_adjusting",     # 调整中的手牌
            3: "draw_player",        # 玩家抓牌
            4: "discard_player",     # 玩家打牌
            5: "discard_pile_player", # 玩家弃牌区
            6: "chi_peng_player",    # 玩家吃碰区
            7: "draw_opponent",      # 对手抓牌
            8: "discard_opponent",   # 对手打牌
            9: "discard_pile_opponent", # 对手弃牌区
            16: "chi_peng_opponent", # 对手吃碰区
        }

        # 卡牌映射表: 将检测的卡牌名称映射到游戏中的卡牌ID
        self.card_mapping = {
            # 小写牌 (1-10)
            '一': (1, 0),  # (card_type, is_capital)
            '二': (2, 0),
            '三': (3, 0),
            '四': (4, 0),
            '五': (5, 0),
            '六': (6, 0),
            '七': (7, 0),
            '八': (8, 0),
            '九': (9, 0),
            '十': (10, 0),
            # 大写牌 (1-10)
            '壹': (1, 1),
            '贰': (2, 1),
            '叁': (3, 1),
            '肆': (4, 1),
            '伍': (5, 1),
            '陆': (6, 1),
            '柒': (7, 1),
            '捌': (8, 1),
            '玖': (9, 1),
            '拾': (10, 1),
        }

        # 游戏状态
        self.game_state = {
            'hand_player': [],
            'hand_opponent': [],
            'chi_peng_player': [],
            'chi_peng_opponent': [],
            'discard_player': [],
            'discard_opponent': [],
            'legal_actions': [],
            'special_combinations': [],
            'chou_pai_list': [],  # 臭牌列表
            'bi_pai_options': []  # 比牌选项
        }

        # 历史状态用于检测变化
        self.previous_state = None
    
    def yolo_to_rlcard_state(self, yolo_detections):
        """
        将YOLO检测结果转换为游戏状态（保持向后兼容）

        Args:
            yolo_detections (dict): YOLO检测结果，包含cards列表

        Returns:
            dict: 游戏状态表示
        """
        # 使用新的方法
        game_state = self.yolo_to_game_state(yolo_detections)

        # 转换为旧格式以保持兼容性
        return self._convert_to_legacy_format(game_state)

    def yolo_to_game_state(self, detections: Dict) -> Dict:
        """
        将YOLO检测结果转换为游戏状态

        Args:
            detections: YOLO检测结果

        Returns:
            游戏状态字典
        """
        # 解析检测结果
        cards = self._parse_detections(detections)

        # 按区域分类卡牌
        self._classify_cards_by_region(cards)

        # 检测特殊组合
        self._detect_special_combinations()

        # 检测自动动作（偎、提、跑）
        self._detect_automatic_actions()

        # 计算合法动作
        self._calculate_legal_actions()

        # 检测比牌机会
        self._detect_bi_pai_opportunities()

        return self.game_state.copy()

    def _parse_detections(self, detections) -> List[Card]:
        """解析YOLO检测结果为Card对象"""
        cards = []

        # 处理不同的输入格式
        if isinstance(detections, dict):
            # 新格式：字典包含cards列表
            card_list = detections.get('cards', [])
        elif isinstance(detections, list):
            # 旧格式：直接是卡牌列表
            card_list = detections
        else:
            card_list = []

        for card_data in card_list:
            # 解析卡牌ID和标签
            card_id = card_data.get('id', '')
            if '_' in card_id:
                parts = card_id.split('_')
                label = '_'.join(parts[1:])  # 卡牌标签
            else:
                label = card_id

            # 创建Card对象
            card = Card(
                id=card_id,
                label=label,
                group_id=card_data.get('group_id', 0),
                state=CardState.MING_PAI,  # 默认明牌
                confidence=card_data.get('conf', 0.0),
                position=tuple(card_data.get('pos', [0, 0, 0, 0]))
            )

            cards.append(card)

        return cards

    def _classify_cards_by_region(self, cards: List[Card]):
        """按区域分类卡牌"""
        # 清空当前状态
        for region in ['hand_player', 'hand_opponent', 'chi_peng_player',
                      'chi_peng_opponent', 'discard_player', 'discard_opponent']:
            self.game_state[region] = []

        # 按group_id分类
        for card in cards:
            if card.group_id == 1:  # 玩家手牌
                self.game_state['hand_player'].append(card)
            elif card.group_id == 6:  # 玩家吃碰区
                self.game_state['chi_peng_player'].append(card)
            elif card.group_id == 5:  # 玩家弃牌区
                self.game_state['discard_player'].append(card)
            elif card.group_id in [7, 8]:  # 对手相关
                self.game_state['hand_opponent'].append(card)
            elif card.group_id == 16:  # 对手吃碰区
                self.game_state['chi_peng_opponent'].append(card)
            elif card.group_id == 9:  # 对手弃牌区
                self.game_state['discard_opponent'].append(card)

    def _detect_special_combinations(self):
        """检测特殊牌型组合"""
        hand_cards = self.game_state['hand_player']
        combinations = []

        # 统计手牌
        card_counts = {}
        for card in hand_cards:
            label = card.label
            if label not in card_counts:
                card_counts[label] = []
            card_counts[label].append(card)

        # 检查二七十组合
        if self._has_er_qi_shi(card_counts):
            combinations.append({
                'type': '二七十',
                'cards': ['二', '七', '十'] if '二' in card_counts else ['贰', '柒', '拾']
            })

        # 检查大小三搭
        da_xiao_combinations = self._check_da_xiao_san_da(card_counts)
        combinations.extend(da_xiao_combinations)

        self.game_state['special_combinations'] = combinations

    def _has_er_qi_shi(self, card_counts: Dict) -> bool:
        """检查是否有二七十组合"""
        # 小写二七十
        if all(card in card_counts for card in ['二', '七', '十']):
            return True
        # 大写贰柒拾
        if all(card in card_counts for card in ['贰', '柒', '拾']):
            return True
        return False

    def _check_da_xiao_san_da(self, card_counts: Dict) -> List[Dict]:
        """检查大小三搭组合"""
        combinations = []

        # 小字牌和大字牌对应关系
        pairs = [
            ('一', '壹'), ('二', '贰'), ('三', '叁'), ('四', '肆'), ('五', '伍'),
            ('六', '陆'), ('七', '柒'), ('八', '捌'), ('九', '玖'), ('十', '拾')
        ]

        for small, large in pairs:
            # 两张小字+一张大字
            if small in card_counts and len(card_counts[small]) >= 2 and large in card_counts:
                combinations.append({
                    'type': '大小三搭',
                    'cards': [small, small, large]
                })

            # 两张大字+一张小字
            if large in card_counts and len(card_counts[large]) >= 2 and small in card_counts:
                combinations.append({
                    'type': '大小三搭',
                    'cards': [large, large, small]
                })

        return combinations

    def _detect_automatic_actions(self):
        """检测自动动作（偎、提、跑）"""
        hand_cards = self.game_state['hand_player']

        # 统计手牌中每种牌的数量
        card_counts = {}
        for card in hand_cards:
            label = card.label
            card_counts[label] = card_counts.get(label, 0) + 1

        # 检查偎牌条件（3张相同）
        for label, count in card_counts.items():
            if count == 3:
                self._handle_wei_action(label)

    def _handle_wei_action(self, label: str):
        """处理偎牌动作"""
        # 找到该标签的所有卡牌
        hand_cards = self.game_state['hand_player']
        target_cards = [card for card in hand_cards if card.label == label]

        if len(target_cards) >= 3:
            # 选择一张作为明牌，其余作为暗牌
            visible_card = target_cards[0]
            hidden_cards = target_cards[1:3]

            # 移动到吃碰区
            visible_card.group_id = 6
            for card in hidden_cards:
                card.group_id = 6
                card.state = CardState.AN_PAI
                card.id = f"{card.id}_暗"

            # 从手牌中移除，添加到吃碰区
            for card in target_cards[:3]:
                if card in self.game_state['hand_player']:
                    self.game_state['hand_player'].remove(card)
                self.game_state['chi_peng_player'].append(card)

    def _calculate_legal_actions(self):
        """计算合法动作"""
        actions = []

        # 基本动作
        actions.append(ActionType.PLAY.value)  # 总是可以打牌
        actions.append(ActionType.PASS.value)  # 总是可以过

        # 检查是否可以胡牌
        if self._can_hu():
            actions.append(ActionType.HU.value)

        # 检查是否可以碰牌
        if self._can_peng():
            actions.append(ActionType.PENG.value)

        # 检查是否可以吃牌
        if self._can_chi():
            actions.append(ActionType.CHI.value)

        self.game_state['legal_actions'] = actions

    def _can_hu(self) -> bool:
        """检查是否可以胡牌"""
        # 简化的胡牌检查逻辑
        hand_count = len(self.game_state['hand_player'])
        chi_peng_count = len(self.game_state['chi_peng_player'])

        # 基本胡牌条件：手牌+吃碰区的牌数符合要求
        total_sets = chi_peng_count // 3  # 吃碰区的组合数
        remaining_cards = hand_count

        # 简化判断：如果剩余手牌能组成完整组合
        return remaining_cards % 3 == 2  # 需要一对做将牌

    def _can_peng(self) -> bool:
        """检查是否可以碰牌"""
        # 检查对手是否刚打出牌，以及我们是否有两张相同的牌
        opponent_discard = self.game_state['discard_opponent']
        if not opponent_discard:
            return False

        last_discard = opponent_discard[-1]
        hand_cards = self.game_state['hand_player']

        # 统计手牌中该牌的数量
        count = sum(1 for card in hand_cards if card.label == last_discard.label)
        return count >= 2

    def _can_chi(self) -> bool:
        """检查是否可以吃牌"""
        # 简化的吃牌检查
        opponent_discard = self.game_state['discard_opponent']
        if not opponent_discard:
            return False

        # 这里应该检查是否能组成顺子，暂时简化
        return len(self.game_state['hand_player']) >= 2

    def _detect_bi_pai_opportunities(self):
        """检测比牌机会"""
        # 如果可以吃牌，检查是否有多种吃法
        if ActionType.CHI.value in self.game_state['legal_actions']:
            # 这里应该实现具体的比牌逻辑
            # 暂时留空，后续完善
            self.game_state['bi_pai_options'] = []

    def _convert_to_legacy_format(self, game_state: Dict) -> Dict:
        """转换为旧格式以保持兼容性"""
        return {
            'hand': self._cards_to_tuples(game_state['hand_player']),
            'discard_pile': self._cards_to_tuples(game_state['discard_player']),
            'opponent_discard_pile': self._cards_to_tuples(game_state['discard_opponent']),
            'combo_cards': self._cards_to_tuples(game_state['chi_peng_player']),
            'opponent_combo_cards': self._cards_to_tuples(game_state['chi_peng_opponent']),
            'legal_actions': game_state['legal_actions'],
            'special_combinations': game_state['special_combinations'],
            'current_player': 0
        }

    def _cards_to_tuples(self, cards: List[Card]) -> List[Tuple]:
        """将Card对象转换为元组格式"""
        tuples = []
        for card in cards:
            if card.label in self.card_mapping:
                card_type, is_capital = self.card_mapping[card.label]
                tuples.append((card_type, is_capital))
        return tuples


def format_detections_for_state_builder(detections, frame_shape):
    """
    将YOLO检测结果格式化为state_builder可用的格式
    
    Args:
        detections: YOLO检测结果列表
        frame_shape: 帧的形状 (height, width)
    
    Returns:
        格式化后的检测结果列表
    """
    formatted_detections = []
    
    # 获取帧的高度和宽度
    height, width = frame_shape[:2]
    
    # 基于zhuangtaiquyu数据集实际分布的精确区域定义
    # 使用真实数据分析结果进行精确调整
    regions = {
        # 观战方区域 (下方) - 基于真实数据分析优化
        1: {  # 手牌_观战方 - 主要手牌区域 (90.5%的卡牌)
            "name": "手牌_观战方",
            "y_min": int(height * 0.62), "y_max": int(height * 0.92),
            "x_min": int(width * 0.14), "x_max": int(width * 0.86),
            "priority": 1  # 基础优先级，最大区域
        },
        2: {  # 调整手牌_观战方 - 极小区域，特定位置
            "name": "调整手牌_观战方",
            "y_min": int(height * 0.56), "y_max": int(height * 0.60),
            "x_min": int(width * 0.30), "x_max": int(width * 0.34),
            "priority": 3  # 调整状态优先级高
        },
        3: {  # 抓牌_观战方 - 基于真实数据：x(0.50-0.66), y(0.25-0.26)
            "name": "抓牌_观战方",
            "y_min": int(height * 0.24), "y_max": int(height * 0.27),
            "x_min": int(width * 0.48), "x_max": int(width * 0.68),
            "priority": 4  # 动作优先级最高
        },
        4: {  # 打牌_观战方 - 中央动作区域
            "name": "打牌_观战方",
            "y_min": int(height * 0.35), "y_max": int(height * 0.55),
            "x_min": int(width * 0.35), "x_max": int(width * 0.65),
            "priority": 4  # 动作优先级最高
        },
        5: {  # 弃牌_观战方 - 基于真实数据：x(0.83-0.97), y(0.76-0.97)
            "name": "弃牌_观战方",
            "y_min": int(height * 0.75), "y_max": int(height * 0.98),
            "x_min": int(width * 0.82), "x_max": int(width * 0.98),
            "priority": 2  # 静态区域
        },
        6: {  # 吃碰区_观战方 - 基于真实数据：x(0.42), y(0.28-0.38)
            "name": "吃碰区_观战方",
            "y_min": int(height * 0.27), "y_max": int(height * 0.39),
            "x_min": int(width * 0.40), "x_max": int(width * 0.44),
            "priority": 3  # 组合优先级高
        },

        # 对战方区域 (上方) - 基于真实数据分析
        7: {  # 抓牌_对战方 - 基于真实数据：x(0.25-0.33), y(0.22-0.25)
            "name": "抓牌_对战方",
            "y_min": int(height * 0.21), "y_max": int(height * 0.26),
            "x_min": int(width * 0.24), "x_max": int(width * 0.34),
            "priority": 4  # 动作优先级最高
        },
        8: {  # 打牌_对战方 - 基于真实数据：x(0.21-0.25), y(0.25-0.36)
            "name": "打牌_对战方",
            "y_min": int(height * 0.24), "y_max": int(height * 0.37),
            "x_min": int(width * 0.20), "x_max": int(width * 0.26),
            "priority": 4  # 动作优先级最高
        },
        9: {  # 弃牌_对战方 - 基于真实数据：x(0.13), y(0.35)
            "name": "弃牌_对战方",
            "y_min": int(height * 0.33), "y_max": int(height * 0.37),
            "x_min": int(width * 0.11), "x_max": int(width * 0.15),
            "priority": 2  # 静态区域
        },
        16: {  # 吃碰区_对战方 - 对方组合牌区域（较少数据，使用估计位置）
            "name": "吃碰区_对战方",
            "y_min": int(height * 0.15), "y_max": int(height * 0.35),
            "x_min": int(width * 0.75), "x_max": int(width * 0.95),
            "priority": 3  # 组合优先级
        },

        # 提示区域 (中央覆盖) - 游戏UI元素 - 缩小范围避免误判
        10: {  # 弹窗提示_观战方 - 缩小UI区域，避免与手牌区重叠
            "name": "弹窗提示_观战方",
            "y_min": int(height * 0.35), "y_max": int(height * 0.55),
            "x_min": int(width * 0.30), "x_max": int(width * 0.70),
            "priority": 5  # UI元素最高优先级
        },
        11: {  # 透明提示_观战方 - 基于真实数据：x(0.66), y(0.55)
            "name": "透明提示_观战方",
            "y_min": int(height * 0.53), "y_max": int(height * 0.57),
            "x_min": int(width * 0.64), "x_max": int(width * 0.68),
            "priority": 5  # UI元素最高优先级
        },
        12: {  # 听牌区_观战方 - 听牌提示区域，缩小范围
            "name": "听牌区_观战方",
            "y_min": int(height * 0.35), "y_max": int(height * 0.45),
            "x_min": int(width * 0.35), "x_max": int(width * 0.65),
            "priority": 5  # UI元素最高优先级
        },

        # 结算区域 (全屏) - 特殊游戏状态 - 降低优先级，避免误判
        13: {  # 底牌区域 - 游戏结束时显示，降低优先级
            "name": "底牌区域",
            "y_min": int(height * 0.10), "y_max": int(height * 0.90),
            "x_min": int(width * 0.10), "x_max": int(width * 0.90),
            "priority": 0  # 最低优先级，避免误判
        },
        14: {  # 赢方区域 - 胜利展示
            "name": "赢方区域",
            "y_min": int(height * 0.10), "y_max": int(height * 0.90),
            "x_min": int(width * 0.10), "x_max": int(width * 0.90),
            "priority": 1  # 结算状态
        },
        15: {  # 输方区域 - 失败展示
            "name": "输方区域",
            "y_min": int(height * 0.10), "y_max": int(height * 0.90),
            "x_min": int(width * 0.10), "x_max": int(width * 0.90),
            "priority": 1  # 结算状态
        },
    }
    
    # 智能区域分配算法 - 基于GAME_RULES_OPTIMIZED.md的多层次识别策略
    for det in detections:
        # 获取边界框信息
        x, y, w, h = det["bbox"]
        center_x = x + w / 2
        center_y = y + h / 2

        # 候选区域列表 - 存储所有可能的区域匹配
        candidate_regions = []

        # 第一步：静态区域模板匹配
        for region_id, region in regions.items():
            # 检查中心点是否在区域内
            if (region["x_min"] <= center_x <= region["x_max"] and
                region["y_min"] <= center_y <= region["y_max"]):

                # 计算边界框与区域的重叠面积
                overlap_width = min(x + w, region["x_max"]) - max(x, region["x_min"])
                overlap_height = min(y + h, region["y_max"]) - max(y, region["y_min"])

                if overlap_width > 0 and overlap_height > 0:
                    overlap_area = overlap_width * overlap_height
                    overlap_ratio = overlap_area / (w * h)

                    # 只考虑重叠比例大于阈值的区域
                    if overlap_ratio > 0.1:  # 至少10%重叠
                        candidate_regions.append({
                            'region_id': region_id,
                            'overlap_ratio': overlap_ratio,
                            'priority': region.get('priority', 1),
                            'name': region['name']
                        })

        # 第二步：基于优先级和重叠度的智能选择
        if candidate_regions:
            # 按优先级排序（优先级高的在前），然后按重叠度排序
            candidate_regions.sort(key=lambda x: (-x['priority'], -x['overlap_ratio']))

            # 选择最佳区域
            best_region = candidate_regions[0]
            group_id = best_region['region_id']

            # 第三步：基于游戏规则的验证和调整
            group_id = apply_game_rule_constraints(group_id, det, candidate_regions)

        else:
            # 如果没有找到合适的区域，使用默认规则
            group_id = get_default_region_by_position(center_x, center_y, width, height)

        # 创建格式化后的检测结果
        formatted_det = {
            "label": det["label"],
            "confidence": det["confidence"],
            "bbox": det["bbox"],
            "group_id": group_id,
            "region_name": regions.get(group_id, {}).get('name', f'未知区域_{group_id}')
        }

        formatted_detections.append(formatted_det)
    
    return formatted_detections


def apply_game_rule_constraints(group_id: int, detection: Dict, candidate_regions: List[Dict]) -> int:
    """
    基于真实数据分析和游戏规则约束调整区域分配

    Args:
        group_id: 初始分配的区域ID
        detection: 检测结果
        candidate_regions: 候选区域列表

    Returns:
        调整后的区域ID
    """
    # 基于真实数据分析的区域优先级规则

    # 规则1：UI元素最高优先级 (10,11,12)
    ui_regions = {10, 11, 12}  # 提示区域
    if group_id in ui_regions:
        return group_id  # UI元素优先级最高，直接返回

    # 规则2：动作区域高优先级 (3,4,7,8)
    action_regions = {3, 4, 7, 8}  # 抓牌、打牌区域
    if group_id in action_regions:
        return group_id  # 动作区域优先级很高

    # 规则3：检查是否有UI区域候选（UI区域很小但优先级最高）
    for candidate in candidate_regions:
        if candidate['region_id'] in ui_regions and candidate['overlap_ratio'] > 0.2:
            return candidate['region_id']  # UI区域即使重叠度不高也优先选择

    # 规则4：检查是否有动作区域候选
    for candidate in candidate_regions:
        if candidate['region_id'] in action_regions and candidate['overlap_ratio'] > 0.4:
            return candidate['region_id']  # 动作区域需要较高重叠度

    # 规则5：组合区域vs手牌区域的智能判断
    combo_regions = {6, 16}  # 吃碰区
    hand_regions = {1, 2}    # 手牌区

    # 基于真实数据：区域1占90.5%，区域6占5.4%
    # 如果同时匹配手牌区和吃碰区，需要仔细判断
    if group_id in hand_regions:
        # 检查是否有吃碰区候选且重叠度很高
        for candidate in candidate_regions:
            if (candidate['region_id'] in combo_regions and
                candidate['overlap_ratio'] > 0.8):  # 吃碰区需要很高重叠度才能覆盖手牌区
                return candidate['region_id']

    elif group_id in combo_regions:
        # 吃碰区已经匹配，检查手牌区是否有更好的匹配
        for candidate in candidate_regions:
            if (candidate['region_id'] in hand_regions and
                candidate['overlap_ratio'] > 0.6):  # 手牌区重叠度较高时优先选择
                return candidate['region_id']

    # 规则6：特殊卡牌类型的区域偏好和约束
    card_label = detection.get('label', '')

    # UI元素关键词识别
    ui_keywords = ['打鸟选择', '碰', '吃', '胡', '过', '已准备', '选择', '提示']
    is_ui_element = any(keyword in card_label for keyword in ui_keywords)

    if is_ui_element:
        # 如果是UI元素，强制分配到UI区域或创建特殊处理
        for candidate in candidate_regions:
            if candidate['region_id'] in ui_regions and candidate['overlap_ratio'] > 0.1:
                return candidate['region_id']

        # 如果没有UI区域候选，根据位置判断
        # UI元素通常在中央区域，不应该分配到底牌区域(13)
        if group_id == 13:  # 避免UI元素被分配到底牌区域
            return 1  # 默认分配到手牌区域

    # 规则7：避免普通卡牌被错误分配到特殊区域
    special_regions = {13, 14, 15}  # 底牌、赢方、输方区域
    if group_id in special_regions and not is_ui_element:
        # 普通卡牌不应该分配到特殊区域，重新选择
        for candidate in candidate_regions:
            if candidate['region_id'] not in special_regions and candidate['overlap_ratio'] > 0.3:
                return candidate['region_id']

        # 如果没有合适候选，分配到默认区域
        return 1  # 默认手牌区域

    # 规则8：UI区域的重叠度要求更严格
    if group_id in ui_regions:
        # UI区域需要较高的重叠度才能确认
        best_overlap = 0
        for candidate in candidate_regions:
            if candidate['region_id'] == group_id:
                best_overlap = candidate['overlap_ratio']
                break

        if best_overlap < 0.3:  # UI区域重叠度太低，可能是误判
            # 寻找其他合适的区域
            for candidate in candidate_regions:
                if candidate['region_id'] in hand_regions and candidate['overlap_ratio'] > 0.5:
                    return candidate['region_id']

    return group_id  # 默认返回原始分配


def get_default_region_by_position(center_x: float, center_y: float, width: int, height: int) -> int:
    """
    基于位置的默认区域分配（当没有找到合适区域时使用）

    Args:
        center_x: 卡牌中心X坐标
        center_y: 卡牌中心Y坐标
        width: 图像宽度
        height: 图像高度

    Returns:
        默认区域ID
    """
    # 基于简单的位置规则进行默认分配

    # 垂直位置判断
    if center_y > height * 0.7:
        # 下方区域 - 观战方
        if center_x < width * 0.2:
            return 6   # 吃碰区_观战方
        elif center_x > width * 0.8:
            return 5   # 弃牌_观战方
        else:
            return 1   # 手牌_观战方

    elif center_y < height * 0.3:
        # 上方区域 - 对战方
        if center_x > width * 0.8:
            return 16  # 吃碰区_对战方
        elif center_x < width * 0.3:
            return 9   # 弃牌_对战方
        else:
            return 8   # 打牌_对战方

    else:
        # 中间区域 - 动作区域
        if center_x < width * 0.3:
            return 9   # 弃牌_对战方
        elif center_x > width * 0.7:
            return 5   # 弃牌_观战方
        else:
            return 4   # 打牌_观战方

    return 1  # 最终默认为手牌区
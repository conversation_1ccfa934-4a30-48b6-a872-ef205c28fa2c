"""
状态管理模块
处理检测结果的验证、清洗和状态更新
"""

class StateManager:
    """
    状态管理器
    解决老项目中区域分配的核心问题
    """
    
    def __init__(self):
        self.region_assigner = SmartRegionAssigner()
        self.card_tracker = CardTracker()
        self.state_validator = StateValidator()
        
    def process_frame(self, detections: List[Dict], frame_context: Dict):
        """
        处理单帧检测结果
        
        Args:
            detections: YOLO检测结果
            frame_context: 帧上下文信息
            
        Returns:
            处理后的游戏状态
        """
        # 1. 区域分配
        assigned_detections = []
        for det in detections:
            region_info = self.region_assigner.assign_region(det, frame_context)
            det.update(region_info)
            assigned_detections.append(det)
        
        # 2. 卡牌ID追踪
        tracked_cards = self.card_tracker.update_tracking(assigned_detections)
        
        # 3. 状态验证
        validated_state = self.state_validator.validate_and_correct(tracked_cards)
        
        return validated_state

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌检测模块 - 基于YOLOv8
功能：
1. 加载预训练的YOLOv8模型进行卡牌检测
2. 支持单图像和批量图像处理
3. 支持视频流实时检测
4. 提供JSON格式的检测结果输出
"""

import os
import cv2
import torch
import numpy as np
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Union, Optional, Tuple
import logging
from ultralytics import YOLO
from .data_validator import DataValidationPipeline

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CardDetector")

# 类别映射
LABEL_TO_ID = {
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
    "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
    "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
    "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
}

ID_TO_LABEL = {v: k for k, v in LABEL_TO_ID.items()}


class CardDetector:
    """卡牌检测类，封装YOLOv8模型的加载和推理功能"""
    
    def __init__(self, model_path: str = "yolo11n.pt", conf_threshold: float = 0.25,
                 iou_threshold: float = 0.45, device: Optional[str] = None,
                 enable_validation: bool = True, validation_config: Dict = None):
        """
        初始化卡牌检测器

        Args:
            model_path: YOLOv8模型路径，默认为项目根目录下的yolo11n.pt
            conf_threshold: 置信度阈值，低于此值的检测结果将被过滤
            iou_threshold: IOU阈值，用于非极大值抑制
            device: 运行设备，可以是'cpu'或'cuda'，默认为None（自动选择）
            enable_validation: 是否启用数据验证和清洗，默认True
            validation_config: 数据验证配置，None为使用默认配置
        """
        # 检查是否有可用的GPU
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")

        # 加载模型
        self.model = YOLO(model_path)
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.enable_validation = enable_validation

        # 初始化数据验证管道
        if self.enable_validation:
            self.validation_pipeline = DataValidationPipeline(validation_config)
            print("已启用数据验证和清洗层")
        else:
            self.validation_pipeline = None

        # 启用CUDA优化
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            print("已启用CUDA优化")
    
    def detect_image(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测单张图像中的卡牌

        Args:
            image: 输入图像，OpenCV格式（BGR）

        Returns:
            检测结果列表，每个元素是一个字典，包含类别、置信度、边界框等信息
        """
        if image is None or image.size == 0:
            logger.warning("输入图像为空")
            return []

        # 执行推理
        results = self.model(image, conf=self.conf_threshold, iou=self.iou_threshold, device=self.device)[0]

        # 解析结果
        detections = self._parse_results(results)

        # 数据验证和清洗
        if self.enable_validation and self.validation_pipeline and detections:
            # 格式化检测结果为验证管道所需的格式
            formatted_detections = []
            for i, det in enumerate(detections):
                formatted_det = {
                    'id': f"{i}_{det.get('label', 'unknown')}",
                    'conf': det.get('confidence', 0.0),
                    'pos': det.get('bbox', [0, 0, 0, 0]),
                    'group_id': 1  # 默认设为1，后续可以根据位置分析确定
                }
                formatted_detections.append(formatted_det)

            # 执行验证和清洗
            validated_detections, report = self.validation_pipeline.process(formatted_detections)

            # 转换回原格式
            cleaned_detections = []
            for val_det in validated_detections:
                # 找到对应的原始检测结果
                det_id = val_det.get('id', '')
                if '_' in det_id:
                    try:
                        index = int(det_id.split('_')[0])
                        if 0 <= index < len(detections):
                            original_det = detections[index].copy()
                            # 更新置信度（可能被验证管道修正）
                            original_det['confidence'] = val_det.get('conf', original_det['confidence'])
                            cleaned_detections.append(original_det)
                    except (ValueError, IndexError):
                        continue

            return cleaned_detections

        return detections

    def get_validation_statistics(self) -> Dict:
        """
        获取数据验证统计信息

        Returns:
            验证统计信息字典
        """
        if self.validation_pipeline:
            return self.validation_pipeline.get_statistics()
        else:
            return {'message': '数据验证未启用'}

    def detect_batch(self, images: List[np.ndarray], batch_size: int = 4) -> List[List[Dict[str, Any]]]:
        """
        批量检测多张图像
        
        Args:
            images: 图像列表
            batch_size: 批处理大小
            
        Returns:
            每张图像的检测结果列表
        """
        if not images:
            return []
        
        # 执行推理
        results = self.model.predict(
            source=images,
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            verbose=False,
            batch=batch_size
        )
        
        # 解析结果
        all_detections = [self._parse_results(result) for result in results]
        return all_detections
    
    def process_video(self, video_path: str, output_path: Optional[str] = None, 
                     visualize: bool = False, save_json: bool = False) -> Optional[str]:
        """
        处理视频文件
        
        Args:
            video_path: 视频文件路径
            output_path: 输出视频路径，如果为None则不保存视频
            visualize: 是否可视化检测结果
            save_json: 是否保存JSON格式的检测结果
            
        Returns:
            如果save_json为True，返回JSON文件路径；否则返回None
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return None
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return None
        
        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 准备输出视频
        video_writer = None
        if output_path:
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            # 修复VideoWriter_fourcc的使用方式
            fourcc = cv2.VideoWriter.fourcc(*'mp4v')  # 使用VideoWriter.fourcc而不是VideoWriter_fourcc
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # 处理视频帧
        all_detections = []
        frame_idx = 0
        
        logger.info(f"开始处理视频: {video_path}, 总帧数: {total_frames}")
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 每10帧显示一次进度
            if frame_idx % 10 == 0:
                logger.info(f"处理进度: {frame_idx}/{total_frames} ({frame_idx/total_frames*100:.1f}%)")
            
            # 检测卡牌
            detections = self.detect_image(frame)
            all_detections.append({"frame": frame_idx, "detections": detections})
            
            # 可视化
            if visualize or output_path:
                vis_frame = self._visualize_detections(frame.copy(), detections)
                
                if visualize:
                    cv2.imshow("Card Detection", vis_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                if video_writer:
                    video_writer.write(vis_frame)
            
            frame_idx += 1
        
        # 清理资源
        cap.release()
        if video_writer:
            video_writer.release()
        if visualize:
            cv2.destroyAllWindows()
        
        # 计算处理速度
        elapsed_time = time.time() - start_time
        fps = frame_idx / elapsed_time
        logger.info(f"视频处理完成: {frame_idx}帧, 耗时: {elapsed_time:.2f}秒, FPS: {fps:.2f}")
        
        # 保存JSON结果
        json_path = None
        if save_json:
            json_path = os.path.splitext(video_path)[0] + "_detections.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(all_detections, f, ensure_ascii=False, indent=2)
            logger.info(f"检测结果已保存至: {json_path}")
        
        return json_path
    
    def _parse_results(self, result) -> List[Dict[str, Any]]:
        """解析YOLO模型的输出结果"""
        detections = []
        
        # 获取边界框、置信度和类别
        boxes = result.boxes
        
        for i in range(len(boxes)):
            # 获取边界框坐标（xyxy格式）
            box = boxes.xyxy[i].cpu().numpy()
            x1, y1, x2, y2 = box
            
            # 获取置信度和类别ID
            conf = float(boxes.conf[i])
            cls_id = int(boxes.cls[i])
            
            # 获取类别名称 - 修复映射：YAML中0=background，1=一，2=二...
            # 项目映射中1=一，2=二...，所以YOLO的cls_id直接对应项目的ID
            if cls_id == 0:
                cls_name = "background"  # 跳过background类别
            else:
                cls_name = ID_TO_LABEL.get(cls_id, "unknown")  # 直接映射，不需要+1
            
            # 构建检测结果
            detection = {
                "label": cls_name,
                "class_id": cls_id,
                "confidence": conf,
                "bbox": [int(x1), int(y1), int(x2 - x1), int(y2 - y1)]
            }
            
            detections.append(detection)
        
        return detections
    
    def _visualize_detections(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """可视化检测结果"""
        for det in detections:
            bbox = det["bbox"]
            x, y, w, h = bbox
            
            # 绘制边界框
            cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{det['label']} {det['confidence']:.2f}"
            cv2.putText(image, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return image


def main():
    """主函数，用于测试"""
    import argparse
    
    parser = argparse.ArgumentParser(description="跑胡子卡牌检测工具")
    parser.add_argument("--source", type=str, required=True, help="输入图像或视频路径，或摄像头索引")
    parser.add_argument("--model", type=str, default="yolo11n.pt", help="YOLOv8模型路径")
    parser.add_argument("--conf", type=float, default=0.25, help="置信度阈值")
    parser.add_argument("--iou", type=float, default=0.45, help="IOU阈值")
    parser.add_argument("--output", type=str, default=None, help="输出路径")
    parser.add_argument("--visualize", action="store_true", help="是否可视化结果")
    parser.add_argument("--save-json", action="store_true", help="是否保存JSON结果")
    
    args = parser.parse_args()
    
    # 初始化检测器
    detector = CardDetector(
        model_path=args.model,
        conf_threshold=args.conf,
        iou_threshold=args.iou
    )
    
    # 判断输入源类型
    source = args.source
    if source.isdigit():  # 摄像头
        source = int(source)
        cap = cv2.VideoCapture(source)
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            detections = detector.detect_image(frame)
            vis_frame = detector._visualize_detections(frame.copy(), detections)
            
            cv2.imshow("Card Detection", vis_frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
    
    elif os.path.isfile(source):  # 文件
        if source.lower().endswith(('.mp4', '.avi', '.mov')):  # 视频
            detector.process_video(
                source,
                output_path=args.output,
                visualize=args.visualize,
                save_json=args.save_json
            )
        else:  # 图像
            image = cv2.imread(source)
            if image is None:
                logger.error(f"无法读取图像: {source}")
                return
                
            detections = detector.detect_image(image)
            
            # 可视化
            if args.visualize:
                vis_image = detector._visualize_detections(image.copy(), detections)
                cv2.imshow("Card Detection", vis_image)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
            
            # 保存结果
            if args.output:
                vis_image = detector._visualize_detections(image.copy(), detections)
                cv2.imwrite(args.output, vis_image)
            
            # 保存JSON
            if args.save_json:
                json_path = os.path.splitext(source)[0] + "_detections.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(detections, f, ensure_ascii=False, indent=2)
                logger.info(f"检测结果已保存至: {json_path}")
    
    else:
        logger.error(f"无效的输入源: {source}")


if __name__ == "__main__":
    main() 
"""
第一阶段集成器
集成模块1-3，实现基础的数据验证、ID分配和继承功能
"""

from typing import List, Dict, Any
from dataclasses import dataclass
import logging

from .data_validator import create_data_validator
from .basic_id_assigner import create_basic_id_assigner, GlobalIDManager
from .simple_inheritor import create_simple_inheritor

logger = logging.getLogger(__name__)

@dataclass
class Phase1Result:
    """第一阶段处理结果"""
    success: bool
    processed_cards: List[Dict[str, Any]]
    validation_errors: List[str]
    validation_warnings: List[str]
    statistics: Dict[str, Any]

class Phase1Integrator:
    """第一阶段集成器 - 基础功能集成"""
    
    def __init__(self):
        # 创建全局ID管理器
        self.global_id_manager = GlobalIDManager()

        # 初始化三个核心模块
        self.data_validator = create_data_validator()
        self.id_assigner = create_basic_id_assigner(self.global_id_manager)
        self.inheritor = create_simple_inheritor()

        # 处理统计
        self.frame_count = 0

        logger.info("第一阶段集成器初始化完成")
    
    def process_frame(self, detections: List[Dict[str, Any]]) -> Phase1Result:
        """处理一帧数据 - 第一阶段流程"""
        self.frame_count += 1
        logger.info(f"开始处理第{self.frame_count}帧，输入{len(detections)}条检测数据")
        
        try:
            # 步骤1：数据验证
            validation_result = self.data_validator.validate(detections)
            if not validation_result.is_valid:
                logger.error(f"数据验证失败: {validation_result.errors}")
                return Phase1Result(
                    success=False,
                    processed_cards=[],
                    validation_errors=validation_result.errors,
                    validation_warnings=validation_result.warnings,
                    statistics={}
                )
            
            logger.info(f"数据验证通过: {len(validation_result.cleaned_data)}条有效数据")
            
            # 步骤2：继承处理
            inheritance_result = self.inheritor.process_inheritance(validation_result.cleaned_data)
            
            logger.info(f"继承处理完成: 继承{len(inheritance_result.inherited_cards)}张, "
                       f"新增{len(inheritance_result.new_cards)}张")
            
            # 步骤3：为新卡牌分配ID
            if inheritance_result.new_cards:
                assignment_result = self.id_assigner.assign_ids(inheritance_result.new_cards)
                new_cards_with_ids = assignment_result.assigned_cards
                assignment_stats = assignment_result.statistics
            else:
                new_cards_with_ids = []
                assignment_stats = {}
            
            # 合并所有卡牌
            all_processed_cards = inheritance_result.inherited_cards + new_cards_with_ids
            
            # 生成综合统计
            comprehensive_stats = self._generate_comprehensive_statistics(
                inheritance_result.statistics,
                assignment_stats,
                validation_result.warnings
            )
            
            logger.info(f"第{self.frame_count}帧处理完成: 总计{len(all_processed_cards)}张卡牌")
            
            return Phase1Result(
                success=True,
                processed_cards=all_processed_cards,
                validation_errors=[],
                validation_warnings=validation_result.warnings,
                statistics=comprehensive_stats
            )
            
        except Exception as e:
            logger.error(f"第{self.frame_count}帧处理异常: {str(e)}")
            return Phase1Result(
                success=False,
                processed_cards=[],
                validation_errors=[f"处理异常: {str(e)}"],
                validation_warnings=[],
                statistics={}
            )
    
    def _generate_comprehensive_statistics(self, 
                                         inheritance_stats: Dict[str, Any],
                                         assignment_stats: Dict[str, Any],
                                         warnings: List[str]) -> Dict[str, Any]:
        """生成综合统计信息"""
        return {
            "frame_info": {
                "frame_number": self.frame_count,
                "processing_success": True
            },
            "inheritance": inheritance_stats,
            "assignment": assignment_stats,
            "validation": {
                "warnings_count": len(warnings),
                "warnings": warnings
            },
            "summary": {
                "total_cards": (
                    inheritance_stats.get("current_frame", {}).get("total", 0)
                ),
                "inheritance_rate": (
                    inheritance_stats.get("current_frame", {}).get("inheritance_rate", 0)
                ),
                "new_assignments": (
                    assignment_stats.get("total_assigned", 0)
                )
            }
        }
    
    def reset_system(self):
        """重置整个系统（用于新局开始）"""
        self.id_assigner.reset_counters()
        self.inheritor.reset_inheritance_history()
        self.frame_count = 0
        logger.info("第一阶段系统已重置")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "frame_count": self.frame_count,
            "inheritance_rate": self.inheritor.get_inheritance_rate(),
            "has_previous_frame": self.inheritor.has_previous_frame(),
            "id_counters": dict(self.global_id_manager.id_counters)
        }

def create_phase1_integrator():
    """创建第一阶段集成器"""
    return Phase1Integrator()

# 测试代码
if __name__ == "__main__":
    # 创建第一阶段系统
    system = create_phase1_integrator()
    
    # 模拟第一帧数据
    frame1_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],
            'confidence': 0.9,
            'group_id': 1
        },
        {
            'label': '三',
            'bbox': [200, 100, 250, 150],
            'confidence': 0.8,
            'group_id': 1
        }
    ]
    
    # 处理第一帧
    result1 = system.process_frame(frame1_detections)
    print("第一帧处理结果:")
    print(f"  成功: {result1.success}")
    print(f"  卡牌数量: {len(result1.processed_cards)}")
    for card in result1.processed_cards:
        print(f"    {card['twin_id']} (区域{card['group_id']}, 标签{card['label']})")
    
    # 模拟第二帧数据（部分继承）
    frame2_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],
            'confidence': 0.9,
            'group_id': 1  # 相同区域+标签，应该继承
        },
        {
            'label': '二',
            'bbox': [300, 100, 350, 150],
            'confidence': 0.85,
            'group_id': 2  # 不同区域，应该分配新ID
        }
    ]
    
    # 处理第二帧
    result2 = system.process_frame(frame2_detections)
    print("\n第二帧处理结果:")
    print(f"  成功: {result2.success}")
    print(f"  卡牌数量: {len(result2.processed_cards)}")
    for card in result2.processed_cards:
        inherited = "继承" if card.get('inherited', False) else "新增"
        print(f"    {card['twin_id']} (区域{card['group_id']}, 标签{card['label']}, {inherited})")
    
    # 显示系统状态
    status = system.get_system_status()
    print(f"\n系统状态:")
    print(f"  处理帧数: {status['frame_count']}")
    print(f"  继承率: {status['inheritance_rate']:.2%}")
    print(f"  ID计数器: {status['id_counters']}")

"""
模块5：暗牌处理器 (DarkCardProcessor) - 简化版
专注于暗牌ID分配的核心功能

核心原则：
1. 空间分列：按X坐标对明暗牌进行列分组
2. 序号分配：每列内暗牌从下到上分配1、2、3序号
3. 类别关联：从同列明牌获取卡牌类别
4. ID生成：生成格式为"{序号}{类别}暗"的ID
"""

from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DarkCardResult:
    """暗牌处理结果"""
    processed_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class DarkCardProcessor:
    """简化的暗牌处理器 - 专注于暗牌ID分配"""

    def __init__(self):
        # 只处理吃碰区域（6和16区域）
        self.target_regions = {6, 16}

        # 简化的处理统计
        self.processing_stats = {
            "total_frames": 0,
            "total_dark_cards": 0,
            "successful_assignments": 0,
            "failed_assignments": 0
        }

        logger.info("简化暗牌处理器初始化完成")
    
    def process_dark_cards(self, cards: List[Dict[str, Any]], frame_name: str = "unknown") -> DarkCardResult:
        """简化的暗牌处理逻辑 - 专注于暗牌ID分配"""
        self.processing_stats["total_frames"] += 1

        logger.info(f"开始处理暗牌分配，当前帧{len(cards)}张卡牌")

        # 统计暗牌数量
        dark_cards = [card for card in cards if card['label'] == '暗']
        self.processing_stats["total_dark_cards"] += len(dark_cards)

        # 按区域分组
        cards_by_region = self._group_cards_by_region(cards)

        processed_cards = []

        # 只处理目标区域（6和16区域）
        for region_id, region_cards in cards_by_region.items():
            if region_id in self.target_regions:
                # 处理吃碰区的暗牌分配
                processed_region_cards = self._process_target_region(region_id, region_cards)
            else:
                # 其他区域直接返回原始卡牌
                processed_region_cards = region_cards

            processed_cards.extend(processed_region_cards)

        # 生成统计信息
        statistics = self._generate_statistics()

        logger.info(f"暗牌分配完成: 成功分配{self.processing_stats['successful_assignments']}张")

        return DarkCardResult(
            processed_cards=processed_cards,
            statistics=statistics
        )
    
    def _group_cards_by_region(self, cards: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """按区域分组卡牌"""
        cards_by_region = {}
        for card in cards:
            region_id = card['group_id']
            if region_id not in cards_by_region:
                cards_by_region[region_id] = []
            cards_by_region[region_id].append(card)
        return cards_by_region

    def _process_target_region(self, region_id: int, region_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理目标区域（6和16）的暗牌分配"""
        logger.info(f"处理区域{region_id}的暗牌分配")

        # 分离明牌和暗牌
        bright_cards = [card for card in region_cards if card['label'] != '暗']
        dark_cards = [card for card in region_cards if card['label'] == '暗']

        if not dark_cards:
            # 没有暗牌，直接返回原始卡牌
            return region_cards

        logger.info(f"区域{region_id}: {len(bright_cards)}张明牌, {len(dark_cards)}张暗牌")

        # 按空间位置分列
        card_groups = self._group_cards_by_columns(bright_cards, dark_cards)

        processed_cards = []

        # 处理每一列
        for group_index, (group_bright_cards, group_dark_cards) in enumerate(card_groups):
            logger.info(f"处理第{group_index + 1}列: {len(group_bright_cards)}张明牌, {len(group_dark_cards)}张暗牌")

            # 处理明牌：偎牌场景下需要重新分配最大序号ID
            processed_bright_cards = self._process_bright_cards_for_wei(group_bright_cards, group_dark_cards)
            processed_cards.extend(processed_bright_cards)

            # 处理暗牌分配
            if group_dark_cards and group_bright_cards:
                logger.info(f"开始处理暗牌分配: {len(group_dark_cards)}张暗牌, {len(group_bright_cards)}张明牌")
                logger.debug(f"明牌详情: {[{'label': card.get('label', ''), 'twin_id': card.get('twin_id', '')} for card in group_bright_cards]}")

                # 从明牌获取卡牌类别
                card_type = self._extract_card_type_from_bright_cards(group_bright_cards)

                if card_type:
                    logger.info(f"成功提取卡牌类型: {card_type}")
                    # 按Y坐标排序暗牌（从下到上）
                    sorted_dark_cards = self._sort_cards_by_y_position(group_dark_cards)
                    logger.debug(f"暗牌排序后Y坐标: {[self._get_card_y_position(card) for card in sorted_dark_cards]}")

                    # 为暗牌分配序号和ID
                    for i, dark_card in enumerate(sorted_dark_cards, 1):
                        processed_dark_card = dark_card.copy()
                        dark_id = f"{i}{card_type}暗"

                        processed_dark_card['twin_id'] = dark_id
                        processed_dark_card['label'] = dark_id

                        # 确保attributes字段存在
                        if 'attributes' not in processed_dark_card:
                            processed_dark_card['attributes'] = {}
                        processed_dark_card['attributes']['digital_twin_id'] = dark_id
                        processed_dark_card['attributes']['is_dark'] = True

                        processed_cards.append(processed_dark_card)
                        self.processing_stats["successful_assignments"] += 1

                        logger.info(f"暗牌分配: {dark_id} (第{i}张，从下到上，Y坐标: {self._get_card_y_position(dark_card):.1f})")
                else:
                    logger.warning(f"无法获取卡牌类别，保持原样: {len(group_dark_cards)}张暗牌")
                    # 无法获取卡牌类别，保持原样
                    processed_cards.extend(group_dark_cards)
                    self.processing_stats["failed_assignments"] += len(group_dark_cards)
            else:
                if not group_bright_cards:
                    logger.warning(f"没有对应的明牌，保持原样: {len(group_dark_cards)}张暗牌")
                if not group_dark_cards:
                    logger.debug("没有暗牌需要处理")
                # 没有对应的明牌，保持原样
                processed_cards.extend(group_dark_cards)
                self.processing_stats["failed_assignments"] += len(group_dark_cards)

        return processed_cards
    
    def _group_cards_by_columns(self, bright_cards: List[Dict[str, Any]],
                               dark_cards: List[Dict[str, Any]]) -> List[Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]]:
        """按X坐标将明暗牌分列 - 修复版本，先分列再分配"""
        if not bright_cards:
            return [([], dark_cards)]

        logger.info(f"开始精确分列: {len(bright_cards)}张明牌, {len(dark_cards)}张暗牌")

        # 🔧 修复：先按X坐标将所有卡牌分列
        all_cards = bright_cards + dark_cards
        columns = self._separate_cards_into_columns(all_cards)

        groups = []

        # 🔧 修复：严格按列分组，不使用距离匹配
        for i, column_cards in enumerate(columns):
            column_bright_cards = [card for card in column_cards if card['label'] != '暗']
            column_dark_cards = [card for card in column_cards if card['label'] == '暗']

            logger.info(f"第{i+1}列: {len(column_bright_cards)}张明牌, {len(column_dark_cards)}张暗牌")

            if column_bright_cards:
                # 每列应该只有1张明牌（偎牌规则）
                if len(column_bright_cards) == 1:
                    groups.append((column_bright_cards, column_dark_cards))
                    logger.info(f"列分组: 明牌'{column_bright_cards[0].get('label', 'unknown')}'严格匹配到{len(column_dark_cards)}张暗牌")
                else:
                    # 🔧 修复：多张明牌的情况，每张明牌单独成组，不分配暗牌
                    logger.warning(f"列中有{len(column_bright_cards)}张明牌，每张明牌单独成组")
                    for bright_card in column_bright_cards:
                        groups.append(([bright_card], []))
                        logger.info(f"单独分组: 明牌'{bright_card.get('label', 'unknown')}'无暗牌")

                    # 暗牌单独处理（如果有的话）
                    if column_dark_cards:
                        logger.warning(f"列中{len(column_dark_cards)}张暗牌无法匹配明牌，跳过处理")
            elif column_dark_cards:
                # 🔧 修复：只有暗牌没有明牌的列，跳过处理
                logger.warning(f"发现{len(column_dark_cards)}张孤立暗牌，跳过处理（严格按列匹配）")

        return groups

    def _separate_cards_into_columns(self, cards: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """将卡牌按X坐标分列"""
        if not cards:
            return []

        # 按X坐标排序
        sorted_cards = sorted(cards, key=lambda card: self._get_card_x_position(card))

        columns = []
        current_column = [sorted_cards[0]]
        current_x = self._get_card_x_position(sorted_cards[0])

        # 列分离阈值 - 🔧 修复：使用更严格的阈值确保按列分组
        column_separation_threshold = 5.0  # 极严格的列分离阈值（偎牌内部间距通常<3像素）

        for card in sorted_cards[1:]:
            card_x = self._get_card_x_position(card)
            x_gap = abs(card_x - current_x)

            if x_gap <= column_separation_threshold:
                current_column.append(card)
            else:
                columns.append(current_column)
                current_column = [card]
                current_x = card_x

        if current_column:
            columns.append(current_column)

        logger.info(f"分列结果: {len(columns)}列，每列卡牌数: {[len(col) for col in columns]}")
        return columns

        # 处理未分配的暗牌 - 分配给最近的明牌组
        unassigned_dark_cards = [dark_cards[i] for i in range(len(dark_cards)) if i not in used_dark_cards]
        if unassigned_dark_cards:
            logger.warning(f"有{len(unassigned_dark_cards)}张暗牌未分配，尝试分配给最近的明牌组")

            for dark_card in unassigned_dark_cards:
                dark_x = self._get_card_x_position(dark_card)

                # 找到最近的明牌组
                min_distance = float('inf')
                best_group_index = 0

                for i, (group_bright_cards, group_dark_cards) in enumerate(groups):
                    if group_bright_cards:
                        bright_x = self._get_card_x_position(group_bright_cards[0])
                        distance = abs(dark_x - bright_x)
                        if distance < min_distance:
                            min_distance = distance
                            best_group_index = i

                # 添加到最近的组
                groups[best_group_index][1].append(dark_card)
                logger.debug(f"未分配暗牌分配给第{best_group_index + 1}组，距离:{min_distance:.1f}")

        return groups

    def _get_card_y_position(self, card: Dict[str, Any]) -> float:
        """获取卡牌的Y坐标中心点"""
        # 🔧 修复：支持points和bbox两种格式
        points = card.get('points', [])
        if points and len(points) >= 4:
            return (points[0][1] + points[2][1]) / 2

        # 如果没有points，尝试使用bbox
        bbox = card.get('bbox', [])
        if bbox and len(bbox) >= 4:
            return bbox[1] + bbox[3] / 2  # y + height/2

        return 0.0

    def _cluster_cards_by_x_position(self, cards: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """按X坐标对卡牌进行聚类分组"""
        if not cards:
            return []

        # 按X坐标排序
        sorted_cards = sorted(cards, key=lambda card: self._get_card_x_position(card))

        if not sorted_cards:
            return []

        columns = []
        current_column = [sorted_cards[0]]
        current_x = self._get_card_x_position(sorted_cards[0])

        # 列分离阈值
        column_separation_threshold = 15.0

        for card in sorted_cards[1:]:
            card_x = self._get_card_x_position(card)

            if abs(card_x - current_x) <= column_separation_threshold:
                current_column.append(card)
            else:
                columns.append(current_column)
                current_column = [card]
                current_x = card_x

        if current_column:
            columns.append(current_column)

        return columns

    def _process_bright_cards_for_wei(self, bright_cards: List[Dict[str, Any]], dark_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理偎牌场景下的明牌ID分配"""
        if not bright_cards:
            return []

        # 如果没有暗牌，说明不是偎牌场景，直接返回原明牌
        if not dark_cards:
            return bright_cards

        processed_bright_cards = []

        for bright_card in bright_cards:
            # 获取卡牌类型
            card_type = self._extract_card_type_from_bright_cards([bright_card])

            if card_type:
                # 偎牌场景：明牌应该分配最大序号（3号）
                # 计算应该分配的序号：暗牌数量 + 1
                expected_sequence = len(dark_cards) + 1
                expected_id = f"{expected_sequence}{card_type}"

                # 创建新的明牌副本并更新ID
                processed_bright_card = bright_card.copy()
                processed_bright_card['twin_id'] = expected_id
                processed_bright_card['label'] = expected_id

                # 确保attributes字段存在并更新
                if 'attributes' not in processed_bright_card:
                    processed_bright_card['attributes'] = {}
                processed_bright_card['attributes']['digital_twin_id'] = expected_id
                processed_bright_card['attributes']['is_bright_in_wei'] = True  # 标记为偎牌中的明牌

                processed_bright_cards.append(processed_bright_card)

                logger.info(f"偎牌明牌重新分配ID: {expected_id} (配合{len(dark_cards)}张暗牌)")
            else:
                # 无法确定卡牌类型，保持原样
                processed_bright_cards.append(bright_card)
                logger.warning(f"无法确定明牌类型，保持原ID: {bright_card.get('twin_id', 'unknown')}")

        return processed_bright_cards

    def _get_column_x_range(self, column_cards: List[Dict[str, Any]]) -> Tuple[float, float]:
        """计算一列卡牌的X坐标范围"""
        if not column_cards:
            return 0.0, 0.0

        x_positions = [self._get_card_x_position(card) for card in column_cards]
        x_min = min(x_positions)
        x_max = max(x_positions)

        # 扩展范围以包含可能的暗牌
        buffer = 8.0
        return x_min - buffer, x_max + buffer

    def _extract_card_type_from_bright_cards(self, bright_cards: List[Dict[str, Any]]) -> Optional[str]:
        """从明牌中提取卡牌类型"""
        if not bright_cards:
            return None

        for bright_card in bright_cards:
            # 优先从twin_id提取（因为这是最准确的）
            twin_id = bright_card.get('twin_id', '')
            if twin_id and twin_id != '暗' and len(twin_id) > 1 and twin_id[0].isdigit():
                card_type = twin_id[1:]
                logger.debug(f"从twin_id提取卡牌类型: {twin_id} -> {card_type}")
                return card_type

            # 从原始标签提取
            label = bright_card.get('label', '')
            if label and label != '暗':
                # 如果是纯卡牌类型（如"拾"、"肆"），直接返回
                if not label[0].isdigit():
                    logger.debug(f"从label提取卡牌类型（纯类型）: {label}")
                    return label
                # 如果是带序号的（如"3拾"），提取卡牌类型
                elif len(label) > 1 and label[0].isdigit():
                    card_type = label[1:]
                    logger.debug(f"从label提取卡牌类型（带序号）: {label} -> {card_type}")
                    return card_type

        logger.warning(f"无法从明牌中提取卡牌类型，明牌数据: {[card.get('label', '') + '/' + card.get('twin_id', '') for card in bright_cards]}")
        return None

    def _sort_cards_by_y_position(self, cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按Y坐标排序卡牌（从下到上）"""
        if not cards:
            return []

        def get_y_coordinate(card):
            points = card.get('points', [])
            if points and len(points) >= 4:
                return (points[0][1] + points[2][1]) / 2
            elif 'bbox' in card and card['bbox'] and len(card['bbox']) >= 2:
                return card['bbox'][1]
            return 0

        return sorted(cards, key=get_y_coordinate, reverse=True)

    def _get_card_x_position(self, card: Dict[str, Any]) -> float:
        """获取卡牌的X坐标中心点"""
        # 🔧 修复：支持points和bbox两种格式
        points = card.get('points', [])
        if points and len(points) >= 2:
            return (points[0][0] + points[1][0]) / 2

        # 如果没有points，尝试使用bbox
        bbox = card.get('bbox', [])
        if bbox and len(bbox) >= 2:
            return bbox[0] + bbox[2] / 2  # x + width/2

        return 0.0

    def _generate_statistics(self) -> Dict[str, Any]:
        """生成简化的统计信息"""
        total_processed = (self.processing_stats["successful_assignments"] +
                          self.processing_stats["failed_assignments"])

        success_rate = (self.processing_stats["successful_assignments"] / total_processed
                       if total_processed > 0 else 0)

        return {
            "processing_summary": {
                "total_frames": self.processing_stats["total_frames"],
                "total_dark_cards": self.processing_stats["total_dark_cards"],
                "successful_assignments": self.processing_stats["successful_assignments"],
                "failed_assignments": self.processing_stats["failed_assignments"],
                "success_rate": success_rate
            },
            "target_regions": list(self.target_regions)
        }

    def reset_processing_stats(self):
        """重置处理统计"""
        self.processing_stats = {
            "total_frames": 0,
            "total_dark_cards": 0,
            "successful_assignments": 0,
            "failed_assignments": 0
        }
        logger.info("暗牌处理统计已重置")

    def get_success_rate(self) -> float:
        """获取暗牌分配成功率"""
        total_processed = (self.processing_stats["successful_assignments"] +
                          self.processing_stats["failed_assignments"])
        if total_processed == 0:
            return 0.0
        return self.processing_stats["successful_assignments"] / total_processed

def create_dark_card_processor():
    """创建简化的暗牌处理器"""
    return DarkCardProcessor()

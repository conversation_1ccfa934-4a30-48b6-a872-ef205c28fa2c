"""
虚拟区域处理器 (VirtualRegionProcessor)
单一职责：处理虚拟区域（区域10、11、12）的完全虚拟化

核心功能：
1. 识别虚拟区域的卡牌
2. 分配虚拟ID，不参与物理ID分配
3. 标记为虚拟状态，不参与RLCard状态转换
4. 符合GAME_RULES_OPTIMIZED.md的虚拟区域完全屏蔽要求
"""

from typing import List, Dict, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

# 虚拟区域定义
VIRTUAL_REGIONS = [10, 11, 12]

@dataclass
class VirtualRegionProcessResult:
    """虚拟区域处理结果"""
    processed_cards: List[Dict[str, Any]]
    virtual_cards: List[Dict[str, Any]]  # 被虚拟化的卡牌
    physical_cards: List[Dict[str, Any]]  # 物理卡牌
    statistics: Dict[str, Any]

class VirtualRegionProcessor:
    """虚拟区域处理器 - 实现GAME_RULES_OPTIMIZED.md的虚拟区域完全屏蔽"""
    
    def __init__(self):
        self.processing_stats = {
            "total_frames": 0,
            "virtual_cards_processed": 0,
            "physical_cards_processed": 0,
            "virtual_by_region": {10: 0, 11: 0, 12: 0}
        }
        
        logger.info("虚拟区域处理器初始化完成")
    
    def process_virtual_regions(self, cards: List[Dict[str, Any]]) -> VirtualRegionProcessResult:
        """
        处理虚拟区域完全虚拟化
        
        Args:
            cards: 输入卡牌列表
            
        Returns:
            处理结果，包含虚拟化后的卡牌列表和统计信息
        """
        self.processing_stats["total_frames"] += 1
        
        if not cards:
            return VirtualRegionProcessResult(
                processed_cards=[],
                virtual_cards=[],
                physical_cards=[],
                statistics=self._generate_statistics()
            )
        
        virtual_cards = []
        physical_cards = []
        
        for card in cards:
            group_id = card.get('group_id')
            
            if group_id in VIRTUAL_REGIONS:
                # 虚拟区域：完全虚拟化处理
                virtual_card = self._virtualize_card(card)
                virtual_cards.append(virtual_card)
                self.processing_stats["virtual_cards_processed"] += 1
                self.processing_stats["virtual_by_region"][group_id] += 1
                
                logger.debug(f"虚拟化卡牌: {virtual_card['twin_id']} (区域{group_id})")
            else:
                # 物理区域：保持原样
                physical_cards.append(card)
                self.processing_stats["physical_cards_processed"] += 1
        
        # 合并处理结果
        processed_cards = physical_cards + virtual_cards

        # 🔧 添加区域4数据调试
        region_4_cards = [card for card in processed_cards if card.get('group_id') == 4]
        if region_4_cards:
            logger.info(f"🔧 [调试] VirtualRegionProcessor输出区域4数据: {len(region_4_cards)}张卡牌")
            for card in region_4_cards:
                label = card.get('label', 'None')
                logger.info(f"🔧 [调试] 区域4卡牌: 标签='{label}', group_id={card.get('group_id')}")
        else:
            logger.info(f"🔧 [调试] VirtualRegionProcessor未输出区域4数据")

        logger.info(f"虚拟区域处理完成: 物理{len(physical_cards)}张, 虚拟{len(virtual_cards)}张")

        return VirtualRegionProcessResult(
            processed_cards=processed_cards,
            virtual_cards=virtual_cards,
            physical_cards=physical_cards,
            statistics=self._generate_statistics()
        )
    
    def _virtualize_card(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """
        虚拟化单张卡牌
        
        Args:
            card: 原始卡牌数据
            
        Returns:
            虚拟化后的卡牌数据
        """
        virtual_card = card.copy()
        
        # 分配虚拟ID：格式为 虚拟_{标签}_{区域ID}
        label = card.get('label', 'unknown')
        group_id = card.get('group_id', 'unknown')
        virtual_id = f"虚拟_{label}_{group_id}"
        
        # 设置虚拟属性
        virtual_card['twin_id'] = virtual_id
        virtual_card['is_virtual'] = True
        virtual_card['virtual_reason'] = f"区域{group_id}_UI提示"
        virtual_card['participates_in_rlcard'] = False  # 不参与RLCard状态转换
        virtual_card['participates_in_id_allocation'] = False  # 不参与物理ID分配
        
        return virtual_card
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            "total_frames": self.processing_stats["total_frames"],
            "virtual_cards_processed": self.processing_stats["virtual_cards_processed"],
            "physical_cards_processed": self.processing_stats["physical_cards_processed"],
            "virtual_by_region": self.processing_stats["virtual_by_region"].copy(),
            "virtual_regions": VIRTUAL_REGIONS
        }
    
    def get_virtual_rate(self) -> float:
        """获取虚拟化率"""
        total = self.processing_stats["virtual_cards_processed"] + self.processing_stats["physical_cards_processed"]
        if total == 0:
            return 0.0
        return self.processing_stats["virtual_cards_processed"] / total
    
    def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            "total_frames": 0,
            "virtual_cards_processed": 0,
            "physical_cards_processed": 0,
            "virtual_by_region": {10: 0, 11: 0, 12: 0}
        }
        logger.info("虚拟区域处理器统计信息已重置")

def create_virtual_region_processor():
    """创建虚拟区域处理器"""
    return VirtualRegionProcessor()

"""
区域2互斥处理器 (Region2Processor)
单一职责：处理区域2与区域1的互斥逻辑

核心功能：
1. 识别区域1和区域2的卡牌
2. 对每张区域2卡牌，找到区域1中相同标签的最大ID卡牌
3. 区域2继承最大ID，区域1删除对应卡牌
4. 确保区域2的优先级高于区域1

符合GAME_RULES.md要求的区域2互斥机制
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Region2ProcessResult:
    """区域2处理结果"""
    processed_cards: List[Dict[str, Any]]
    region1_removed_cards: List[Dict[str, Any]]  # 被删除的区域1卡牌
    region2_inherited_cards: List[Dict[str, Any]]  # 继承ID的区域2卡牌
    statistics: Dict[str, Any]

class Region2Processor:
    """区域2互斥处理器 - 实现GAME_RULES.md的区域2优先级机制"""
    
    def __init__(self):
        self.processing_stats = {
            "total_frames": 0,
            "region2_cards_processed": 0,
            "region1_cards_removed": 0,
            "successful_inheritances": 0,
            "failed_inheritances": 0
        }
        
        logger.info("区域2互斥处理器初始化完成")
    
    def process_region2_exclusive(self, cards: List[Dict[str, Any]]) -> Region2ProcessResult:
        """
        处理区域2互斥逻辑
        
        Args:
            cards: 输入卡牌列表
            
        Returns:
            处理结果，包含最终卡牌列表和统计信息
        """
        self.processing_stats["total_frames"] += 1
        
        if not cards:
            return Region2ProcessResult(
                processed_cards=[],
                region1_removed_cards=[],
                region2_inherited_cards=[],
                statistics=self._generate_statistics()
            )
        
        # 步骤1：分离不同区域的卡牌
        region1_cards, region2_cards, other_cards = self._separate_cards_by_region(cards)
        
        logger.info(f"区域分离: 区域1={len(region1_cards)}张, 区域2={len(region2_cards)}张, 其他={len(other_cards)}张")
        
        # 步骤2：处理区域2互斥逻辑
        if region2_cards:
            processed_region1, processed_region2, removed_cards, inherited_cards = self._process_region2_inheritance(
                region1_cards, region2_cards
            )
        else:
            # 没有区域2卡牌，直接返回原始区域1卡牌
            processed_region1 = region1_cards
            processed_region2 = []
            removed_cards = []
            inherited_cards = []
        
        # 步骤3：合并最终结果
        final_cards = processed_region1 + processed_region2 + other_cards
        
        # 更新统计
        self.processing_stats["region2_cards_processed"] += len(region2_cards)
        self.processing_stats["region1_cards_removed"] += len(removed_cards)
        self.processing_stats["successful_inheritances"] += len(inherited_cards)

        # 🔧 添加区域4数据调试
        region_4_cards = [card for card in final_cards if card.get('group_id') == 4]
        if region_4_cards:
            logger.info(f"🔧 [调试] Region2Processor输出区域4数据: {len(region_4_cards)}张卡牌")
            for card in region_4_cards:
                label = card.get('label', 'None')
                logger.info(f"🔧 [调试] 区域4卡牌: 标签='{label}', group_id={card.get('group_id')}")
        else:
            logger.info(f"🔧 [调试] Region2Processor未输出区域4数据")

        logger.info(f"区域2互斥处理完成: 最终{len(final_cards)}张卡牌, "
                   f"区域1删除{len(removed_cards)}张, 区域2继承{len(inherited_cards)}张")

        return Region2ProcessResult(
            processed_cards=final_cards,
            region1_removed_cards=removed_cards,
            region2_inherited_cards=inherited_cards,
            statistics=self._generate_statistics()
        )
    
    def _separate_cards_by_region(self, cards: List[Dict[str, Any]]) -> tuple:
        """分离不同区域的卡牌"""
        region1_cards = []
        region2_cards = []
        other_cards = []
        
        for card in cards:
            group_id = card.get('group_id')
            if group_id == 1:
                region1_cards.append(card)
            elif group_id == 2:
                region2_cards.append(card)
            else:
                other_cards.append(card)
        
        return region1_cards, region2_cards, other_cards
    
    def _process_region2_inheritance(self, region1_cards: List[Dict[str, Any]],
                                   region2_cards: List[Dict[str, Any]]) -> tuple:
        """
        处理区域2继承逻辑

        Returns:
            (处理后的区域1卡牌, 处理后的区域2卡牌, 被删除的区域1卡牌, 继承ID的区域2卡牌)
        """
        logger.info(f"🔍 开始区域2继承处理: 区域1={len(region1_cards)}张, 区域2={len(region2_cards)}张")

        remaining_region1_cards = region1_cards.copy()
        processed_region2_cards = []
        removed_region1_cards = []
        inherited_region2_cards = []

        for i, region2_card in enumerate(region2_cards):
            label = region2_card.get('label')
            logger.info(f"🔍 处理区域2卡牌{i+1}: 标签='{label}'")

            # 提取基础标签（去除数字孪生ID前缀）
            base_label = self._extract_base_label(label)
            logger.info(f"🔍 基础标签: '{base_label}'")

            # 查找区域1中相同基础标签的卡牌
            same_label_region1_cards = [
                card for card in remaining_region1_cards
                if self._extract_base_label(card.get('label', '')) == base_label
            ]

            logger.info(f"🔍 区域1中相同标签卡牌: {len(same_label_region1_cards)}张")
            for j, card in enumerate(same_label_region1_cards):
                twin_id = card.get('attributes', {}).get('digital_twin_id', 'None')
                logger.info(f"    - 卡牌{j+1}: 标签='{card.get('label')}', ID='{twin_id}'")

            if same_label_region1_cards:
                # 找到最大ID的卡牌
                max_id_card = self._find_max_id_card(same_label_region1_cards)
                logger.info(f"🔍 最大ID卡牌: {max_id_card['label'] if max_id_card else 'None'}")

                if max_id_card:
                    # 检查区域2卡牌是否已经有相同的ID
                    region2_current_id = region2_card.get('attributes', {}).get('digital_twin_id')
                    max_id = max_id_card.get('attributes', {}).get('digital_twin_id')

                    logger.info(f"🔍 ID比较: 区域2当前ID='{region2_current_id}', 区域1最大ID='{max_id}'")

                    if region2_current_id == max_id:
                        logger.info(f"⚠️  区域2卡牌已有相同ID，跳过继承处理")
                        processed_region2_cards.append(region2_card)
                    else:
                        logger.info(f"✅ 执行继承处理: 区域2继承ID '{max_id}'")

                        # 区域2继承最大ID
                        inherited_card = region2_card.copy()
                        inherited_card['twin_id'] = max_id  # 保持兼容性
                        inherited_card['is_virtual'] = max_id_card.get('attributes', {}).get('is_virtual', False)
                        inherited_card['inherited_from_region1'] = True
                        inherited_card['original_region1_card'] = max_id_card.copy()

                        # 确保attributes中也更新了digital_twin_id
                        if 'attributes' not in inherited_card:
                            inherited_card['attributes'] = {}
                        inherited_card['attributes']['digital_twin_id'] = max_id

                        processed_region2_cards.append(inherited_card)
                        inherited_region2_cards.append(inherited_card)

                        # 从区域1中删除对应卡牌
                        logger.info(f"🗑️  删除区域1卡牌: 标签='{max_id_card.get('label')}', ID='{max_id}'")
                        remaining_region1_cards.remove(max_id_card)
                        removed_region1_cards.append(max_id_card)

                        logger.info(f"✅ 区域2继承完成: {label} 继承ID {max_id}")
                else:
                    logger.info(f"⚠️  未找到有效ID卡牌，区域2卡牌保持原样")
                    processed_region2_cards.append(region2_card)
            else:
                logger.info(f"ℹ️  区域1中无相同标签卡牌，区域2卡牌保持原样")
                processed_region2_cards.append(region2_card)

        logger.info(f"🔍 继承处理完成: 删除区域1={len(removed_region1_cards)}张, 继承区域2={len(inherited_region2_cards)}张")
        return remaining_region1_cards, processed_region2_cards, removed_region1_cards, inherited_region2_cards

    def _extract_base_label(self, label: str) -> str:
        """
        提取基础标签（去除数字孪生ID前缀）

        Args:
            label: 原始标签，如 "1伍", "2伍", "伍"

        Returns:
            基础标签，如 "伍"
        """
        if not label:
            return ""

        # 如果标签以数字开头，提取后面的部分
        if len(label) > 1 and label[0].isdigit():
            return label[1:]

        return label

    def _find_max_id_card(self, cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """找到ID最大的卡牌"""
        logger.info(f"🔍 查找最大ID卡牌: 输入{len(cards)}张卡牌")

        # 🔧 修复：正确访问数字孪生ID字段
        valid_cards = []
        for i, card in enumerate(cards):
            twin_id = card.get('attributes', {}).get('digital_twin_id')
            has_id = bool(twin_id)
            logger.info(f"    - 卡牌{i+1}: 标签='{card.get('label')}', ID='{twin_id}', 有效={has_id}")
            if twin_id:
                valid_cards.append(card)

        logger.info(f"🔍 有效卡牌数: {len(valid_cards)}张")

        if not valid_cards:
            logger.info(f"❌ 没有找到有效ID的卡牌")
            return None

        # 按ID数字排序，选择最大的
        def extract_id_number(twin_id: str) -> int:
            if not twin_id or twin_id.startswith('虚拟'):
                return 0
            try:
                # 提取ID中的数字部分（如"2二"中的"2"）
                return int(twin_id[0])
            except (ValueError, IndexError):
                return 0

        max_card = max(valid_cards, key=lambda card: extract_id_number(card.get('attributes', {}).get('digital_twin_id', '')))
        max_id = max_card.get('attributes', {}).get('digital_twin_id')
        logger.info(f"✅ 找到最大ID卡牌: 标签='{max_card.get('label')}', ID='{max_id}'")
        return max_card
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        total_processed = self.processing_stats["region2_cards_processed"]
        successful = self.processing_stats["successful_inheritances"]
        
        return {
            "total_frames_processed": self.processing_stats["total_frames"],
            "region2_cards_processed": total_processed,
            "region1_cards_removed": self.processing_stats["region1_cards_removed"],
            "successful_inheritances": successful,
            "failed_inheritances": self.processing_stats["failed_inheritances"],
            "inheritance_success_rate": successful / total_processed if total_processed > 0 else 0.0,
            "processing_summary": {
                "region2_inheritance_enabled": True,
                "follows_game_rules": True
            }
        }
    
    def reset_for_new_game(self):
        """新局重置"""
        self.processing_stats = {
            "total_frames": 0,
            "region2_cards_processed": 0,
            "region1_cards_removed": 0,
            "successful_inheritances": 0,
            "failed_inheritances": 0
        }
        logger.info("区域2处理器已重置")

def create_region2_processor() -> Region2Processor:
    """创建区域2处理器实例"""
    return Region2Processor()

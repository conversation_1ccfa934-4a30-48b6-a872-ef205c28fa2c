"""
模块2：基础ID分配器 (BasicIDAssigner)
只做一件事：为新卡牌分配基础ID
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class IDAssignmentResult:
    """ID分配结果"""
    assigned_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class GlobalIDManager:
    """全局ID管理器 - 管理整个牌局的ID状态"""

    def __init__(self):
        # 全局ID注册表：{twin_id: 卡牌信息}
        self.global_id_registry: Dict[str, Dict[str, Any]] = {}

        # 每种牌的ID计数器（全局唯一）
        self.id_counters: Dict[str, int] = {}

        # 有效卡牌标签
        self.valid_labels = [
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        ]

        # 每种牌的最大数量（80张物理牌设计）
        self.max_cards_per_type = 4

        # 🔧 允许ID共存的特定区域对（最小化修复）
        self.allowed_cross_region_pairs = [(3, 16), (4, 16)]

        logger.info("全局ID管理器初始化完成 - 80张物理牌设计")

    def is_id_used(self, twin_id: str) -> bool:
        """检查ID是否已被使用（恢复原始简单逻辑）"""
        return twin_id in self.global_id_registry

    def _is_cross_region_allowed(self, existing_region: int, target_region: int) -> bool:
        """检查是否允许跨区域共存"""
        if existing_region is None or target_region is None:
            return False

        region_pair = (existing_region, target_region)
        reverse_pair = (target_region, existing_region)

        return (region_pair in self.allowed_cross_region_pairs or
                reverse_pair in self.allowed_cross_region_pairs)

    def _is_id_available_for_region(self, twin_id: str, target_region: int) -> bool:
        """检查ID在指定区域是否可用（修正的跨区域共存逻辑）

        Args:
            twin_id: 要检查的ID
            target_region: 目标区域ID

        Returns:
            True: ID可用（未被使用，或允许跨区域共存）
            False: ID不可用
        """
        # 如果ID未被使用，直接可用
        if not self.is_id_used(twin_id):
            logger.debug(f"    {twin_id}: 未被使用 → 可用")
            return True

        # ID已被使用，检查跨区域共存
        existing_card = self.global_id_registry[twin_id]
        existing_region = existing_card.get('group_id')

        logger.debug(f"    {twin_id}: 已被区域{existing_region}使用")

        # 🔧 修正：如果允许跨区域共存，则该ID可用
        if self._is_cross_region_allowed(existing_region, target_region):
            logger.debug(f"    {twin_id}: 允许跨区域共存 → 可用")
            return True

        logger.debug(f"    {twin_id}: 不允许跨区域共存 → 不可用")
        return False

    def register_id(self, twin_id: str, card_info: Dict[str, Any]):
        """注册新的ID到全局注册表（支持跨区域共存）"""
        target_region = card_info.get('group_id')

        # 检查是否已存在相同ID
        if twin_id in self.global_id_registry:
            existing_card = self.global_id_registry[twin_id]
            existing_region = existing_card.get('group_id')

            # 🔧 修正：如果允许跨区域共存，直接允许注册
            if self._is_cross_region_allowed(existing_region, target_region):
                logger.debug(f"跨区域ID共存: {twin_id} 在区域{existing_region}和{target_region}")
                # 允许跨区域共存，不覆盖原有注册
                return
            else:
                logger.warning(f"ID冲突: {twin_id} 已被区域{existing_region}使用，不允许区域{target_region}重复使用")
                return

        # 正常注册
        self.global_id_registry[twin_id] = card_info
        logger.debug(f"注册ID: {twin_id} -> 区域{target_region}")

    def get_next_available_id(self, label: str, prefer_max_id: bool = False, target_region: int = None) -> Optional[str]:
        """获取下一个可用的物理ID（修正版：两阶段ID分配）

        Args:
            label: 卡牌标签
            prefer_max_id: 是否优先分配最大序号（偎牌明牌场景）
            target_region: 目标区域ID，用于跨区域共存检查
        """
        if label not in self.id_counters:
            self.id_counters[label] = 0

        logger.debug(f"🔍 为区域{target_region}查找'{label}'的可用ID")

        # 🔧 第一优先级：寻找真正未使用的ID（保证连续性）
        if prefer_max_id:
            # 偎牌明牌场景：优先分配最大可用序号
            for i in range(self.max_cards_per_type, 0, -1):
                potential_id = f"{i}{label}"
                if not self.is_id_used(potential_id):
                    logger.debug(f"  ✅ 找到未使用ID: {potential_id}")
                    return potential_id
        else:
            # 常规场景：按顺序分配最小可用序号
            for i in range(1, self.max_cards_per_type + 1):
                potential_id = f"{i}{label}"
                if not self.is_id_used(potential_id):
                    logger.debug(f"  ✅ 找到未使用ID: {potential_id}")
                    return potential_id

        # 🔧 第二优先级：如果所有ID都被使用，再考虑跨区域共存
        if target_region is not None:
            logger.debug(f"  所有ID已被使用，检查跨区域共存...")
            if prefer_max_id:
                for i in range(self.max_cards_per_type, 0, -1):
                    potential_id = f"{i}{label}"
                    existing_card = self.global_id_registry[potential_id]
                    existing_region = existing_card.get('group_id')

                    if self._is_cross_region_allowed(existing_region, target_region):
                        logger.debug(f"  ✅ 跨区域共存可用: {potential_id}")
                        return potential_id
            else:
                for i in range(1, self.max_cards_per_type + 1):
                    potential_id = f"{i}{label}"
                    existing_card = self.global_id_registry[potential_id]
                    existing_region = existing_card.get('group_id')

                    if self._is_cross_region_allowed(existing_region, target_region):
                        logger.debug(f"  ✅ 跨区域共存可用: {potential_id}")
                        return potential_id

        logger.debug(f"  ❌ 没有可用的物理ID")
        return None  # 没有可用的物理ID

    def release_id(self, twin_id: str):
        """释放指定的ID，使其可以重新分配"""
        if twin_id in self.global_id_registry:
            del self.global_id_registry[twin_id]
            logger.debug(f"释放ID: {twin_id}")
        else:
            logger.warning(f"尝试释放不存在的ID: {twin_id}")

class BasicIDAssigner:
    """基础ID分配器 - 只为真正的新卡牌分配ID"""

    def __init__(self, global_id_manager: GlobalIDManager):
        self.global_id_manager = global_id_manager

        # 统计信息
        self.assignment_stats = {
            "total_assigned": 0,
            "physical_assigned": 0,
            "virtual_assigned": 0
        }

        logger.info("基础ID分配器初始化完成 - 连接全局ID管理器")
    
    def assign_ids(self, cards: List[Dict[str, Any]]) -> IDAssignmentResult:
        """为真正的新卡牌分配ID（已继承的卡牌不会到这里）"""
        logger.info(f"开始为{len(cards)}张新卡牌分配ID")

        # 🔧 处理需要释放源ID的卡牌（必须在ID分配前执行）
        self._process_id_releases(cards)

        # 🔧 区域6的空间重新分配已在区域流转器中完成，这里不需要重复处理

        # 🔧 按区域分组并应用空间排序
        cards_by_region = self._group_and_sort_cards_by_region(cards)

        assigned_cards = []

        # 按区域处理卡牌
        for region_id, region_cards in cards_by_region.items():
            # 🔧 区域16特殊处理：列一致性验证
            if region_id == 16:
                region_cards = self._apply_column_consistency_fix(region_cards, region_id)

            # 🔧 按标签分组，实现批量连续分配
            cards_by_label = {}
            for card in region_cards:
                # 跳过已有ID的卡牌（这些应该是继承来的）
                if 'twin_id' in card and card['twin_id']:
                    assigned_cards.append(card)
                    logger.debug(f"跳过已有ID的卡牌: {card['twin_id']}")
                    continue

                label = card.get('label', '')
                if label not in cards_by_label:
                    cards_by_label[label] = []
                cards_by_label[label].append(card)

            # 为每个标签的卡牌批量分配连续ID
            for label, label_cards in cards_by_label.items():
                if len(label_cards) == 1:
                    # 单张卡牌，使用原有逻辑
                    assigned_card = self._assign_single_id(label_cards[0])
                    assigned_cards.append(assigned_card)
                else:
                    # 多张相同标签卡牌，批量连续分配
                    batch_assigned = self._assign_batch_consecutive_ids(label_cards, region_id)
                    assigned_cards.extend(batch_assigned)

        # 生成统计信息
        statistics = self._generate_statistics()

        logger.info(f"ID分配完成: 新分配{self.assignment_stats['total_assigned']}张，物理{self.assignment_stats['physical_assigned']}张，虚拟{self.assignment_stats['virtual_assigned']}张")

        return IDAssignmentResult(
            assigned_cards=assigned_cards,
            statistics=statistics
        )



    def _group_and_sort_cards_by_region(self, cards: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        按区域分组卡牌并应用空间排序规则

        根据GAME_RULES.md：
        - 吃碰区(6,16)：从下到上分配ID
        - 其他区域：保持原顺序
        """
        cards_by_region = {}

        # 按区域分组
        for card in cards:
            region_id = card.get('group_id', 0)
            if region_id not in cards_by_region:
                cards_by_region[region_id] = []
            cards_by_region[region_id].append(card)

        # 对每个区域应用排序规则
        for region_id, region_cards in cards_by_region.items():
            if region_id in [6, 16]:  # 吃碰区
                # 从下到上排序：按Y坐标从大到小
                cards_by_region[region_id] = self._sort_cards_spatial(region_cards, "bottom_to_top")
                logger.debug(f"吃碰区{region_id}应用从下到上排序: {len(region_cards)}张卡牌")
            # 其他区域保持原顺序

        return cards_by_region

    def _sort_cards_spatial(self, cards: List[Dict[str, Any]], sort_type: str) -> List[Dict[str, Any]]:
        """
        按空间位置排序卡牌

        Args:
            cards: 卡牌列表
            sort_type: 排序类型 ("bottom_to_top" 等)
        """
        def get_y_coordinate(card):
            if 'bbox' in card and len(card['bbox']) >= 2:
                return card['bbox'][1]  # bbox[1]是y1坐标
            elif 'points' in card and card['points']:
                # 取points中所有Y坐标的最小值
                return min(point[1] for point in card['points'])
            return 0

        if sort_type == "bottom_to_top":
            # 从下到上：Y坐标从大到小
            return sorted(cards, key=get_y_coordinate, reverse=True)
        else:
            return cards

    def _assign_single_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为单张卡牌分配ID - 使用全局ID管理器"""
        label = card['label']

        # 处理暗牌
        if label == '暗':
            return self._assign_dark_card_id(card)

        # 处理明牌
        return self._assign_bright_card_id(card)
    
    def _assign_bright_card_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为明牌分配ID - 使用全局ID管理器确保ID唯一性"""
        label = card['label']
        target_region = card.get('group_id')

        # 🔧 修复：传递target_region参数
        available_id = self.global_id_manager.get_next_available_id(label, target_region=target_region)

        card_copy = card.copy()

        if available_id:
            # 分配物理ID：格式为 {序号}{牌面}（不包含区域信息）
            card_copy['twin_id'] = available_id
            card_copy['is_virtual'] = False
            card_copy['sequence_number'] = int(available_id[0])  # 提取序号

            # 注册到全局ID管理器
            self.global_id_manager.register_id(available_id, card_copy)

            self.assignment_stats["total_assigned"] += 1
            self.assignment_stats["physical_assigned"] += 1

            logger.info(f"明牌{label}分配物理ID: {available_id} (区域{card.get('group_id', 'unknown')})")
        else:
            # 物理ID已用完，分配虚拟ID：格式为 虚拟{牌面}（不包含区域信息）
            virtual_id = f"虚拟{label}"
            card_copy['twin_id'] = virtual_id
            card_copy['is_virtual'] = True
            card_copy['virtual_reason'] = f"{label}已达{self.global_id_manager.max_cards_per_type}张上限"

            self.assignment_stats["total_assigned"] += 1
            self.assignment_stats["virtual_assigned"] += 1

            logger.warning(f"明牌{label}物理ID已用完，分配虚拟ID: {virtual_id} (区域{card.get('group_id', 'unknown')})")

        return card_copy
    
    def _assign_dark_card_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为暗牌分配临时ID（后续会被暗牌处理器关联）"""
        # 暗牌分配临时ID，后续由暗牌处理器进行关联
        # 格式：临时暗_{唯一标识}（不包含区域信息）

        # 生成临时暗牌ID（使用对象ID确保唯一性）
        temp_id = f"临时暗_{id(card)}"

        card_copy = card.copy()
        card_copy['twin_id'] = temp_id
        card_copy['is_virtual'] = False
        card_copy['is_dark'] = True
        card_copy['needs_association'] = True  # 标记需要关联

        self.assignment_stats["total_assigned"] += 1

        logger.info(f"暗牌分配临时ID: {temp_id} (区域{card.get('group_id', 'unknown')}, 等待关联)")
        return card_copy
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            "total_assigned": self.assignment_stats["total_assigned"],
            "physical_assigned": self.assignment_stats["physical_assigned"],
            "virtual_assigned": self.assignment_stats["virtual_assigned"],
            "global_id_count": len(self.global_id_manager.global_id_registry),
            "id_counters": dict(self.global_id_manager.id_counters),
            "max_cards_per_type": self.global_id_manager.max_cards_per_type
        }

    def reset_counters(self):
        """重置计数器（用于新局开始）"""
        self.global_id_manager.global_id_registry.clear()
        self.global_id_manager.id_counters.clear()
        self.assignment_stats = {
            "total_assigned": 0,
            "physical_assigned": 0,
            "virtual_assigned": 0
        }
        logger.info("全局ID状态已重置")

    def get_current_count(self, label: str) -> int:
        """获取指定标签的当前计数"""
        return self.global_id_manager.id_counters.get(label, 0)

    def can_assign_physical_id(self, label: str, target_region: int = None) -> bool:
        """检查是否可以分配物理ID"""
        if label == '暗':
            return True  # 暗牌由暗牌处理器处理
        return self.global_id_manager.get_next_available_id(label, target_region=target_region) is not None

    def _process_id_releases(self, cards: List[Dict[str, Any]]):
        """处理需要释放源ID的卡牌"""
        released_ids = set()  # 避免重复释放同一个ID

        for card in cards:
            if card.get('release_source_id') and card.get('source_card_id'):
                source_id = card['source_card_id']
                if source_id not in released_ids:
                    self.global_id_manager.release_id(source_id)
                    released_ids.add(source_id)
                    logger.info(f"🔄 释放源ID以供重新分配: {source_id} (区域{card.get('transition_source', '未知')}流转)")

    def _assign_batch_consecutive_ids(self, cards: List[Dict[str, Any]], region_id: int) -> List[Dict[str, Any]]:
        """为同一区域相同标签的多张卡牌批量分配连续ID"""
        if not cards:
            return []

        label = cards[0].get('label', '')

        # 🔧 检查是否有流转卡牌需要完全重新分配
        has_transition_cards = any(
            card.get('from_region_4') or
            card.get('from_region_3') or
            card.get('from_region_7') or
            card.get('transition_source') == '4→16' or
            card.get('transition_source') == '3→16' or
            card.get('transition_source') == '7→16'
            for card in cards
        )

        if has_transition_cards:
            logger.info(f"🔧 检测到流转场景，对区域{region_id}的{len(cards)}张'{label}'牌执行完全重新分配")
            return self._assign_complete_reassignment(cards, region_id, label)
        else:
            logger.info(f"🔧 批量连续分配: 区域{region_id}的{len(cards)}张'{label}'牌")

        assigned_cards = []

        for i, card in enumerate(cards):
            # 🔧 最小化修复：传递target_region参数
            next_id = self.global_id_manager.get_next_available_id(label, target_region=region_id)

            if next_id:
                # 分配物理ID
                updated_card = card.copy()
                updated_card['twin_id'] = next_id
                updated_card['digital_twin_id'] = next_id

                # 注册ID（修复缺少参数的问题）
                self.global_id_manager.register_id(next_id, updated_card)

                logger.info(f"明牌{label}分配物理ID: {next_id} (区域{region_id}, 批量第{i+1}张)")
                assigned_cards.append(updated_card)
            else:
                # 分配虚拟ID
                virtual_id = f"虚拟{label}"
                updated_card = card.copy()
                updated_card['twin_id'] = virtual_id
                updated_card['digital_twin_id'] = virtual_id

                logger.info(f"明牌{label}物理ID已用完，分配虚拟ID: {virtual_id} (区域{region_id}, 批量第{i+1}张)")
                assigned_cards.append(updated_card)

        return assigned_cards

    def _assign_complete_reassignment(self, cards: List[Dict[str, Any]], region_id: int, label: str) -> List[Dict[str, Any]]:
        """
        完全重新分配机制（类似Frame 60）
        当检测到流转场景时，对目标区域的同类型卡牌进行完全重新分配
        """
        logger.info(f"🔄 执行完全重新分配: 区域{region_id}的{len(cards)}张'{label}'牌")

        # 按空间位置排序（从下到上）
        sorted_cards = self._sort_cards_spatial(cards, "bottom_to_top")

        # 🔧 查找继承的源ID，确定起始分配ID
        source_id_num = 1  # 默认从1开始
        for card in sorted_cards:
            source_card_id = card.get('source_card_id')
            if source_card_id:
                # 提取源ID的数字部分（如"2六" -> 2）
                try:
                    extracted_num = int(''.join(filter(str.isdigit, source_card_id)))
                    source_id_num = extracted_num
                    logger.info(f"🔧 检测到源ID {source_card_id}，从{source_id_num}开始重新分配")
                    break
                except (ValueError, TypeError):
                    logger.warning(f"⚠️ 无法解析源ID数字: {source_card_id}")

        assigned_cards = []

        # 从源ID开始重新分配
        for i, card in enumerate(sorted_cards):
            # 计算目标ID：从源ID开始递增
            target_id_num = source_id_num + i
            target_id = f"{target_id_num}{label}"

            # 🔧 修复：检查ID是否可用，考虑跨区域共存
            if self.global_id_manager.is_id_used(target_id):
                logger.info(f"🔄 释放被占用的ID以供重新分配: {target_id}")
                self.global_id_manager.release_id(target_id)

            # 分配目标ID
            updated_card = card.copy()
            updated_card['twin_id'] = target_id
            updated_card['digital_twin_id'] = target_id

            # 注册ID
            self.global_id_manager.register_id(target_id, updated_card)

            # 记录分配信息
            transition_source = card.get('transition_source', '')
            if transition_source:
                logger.info(f"明牌{label}完全重新分配ID: {target_id} (区域{region_id}, 位置{i+1}, 来源:{transition_source})")
            else:
                logger.info(f"明牌{label}完全重新分配ID: {target_id} (区域{region_id}, 位置{i+1})")

            assigned_cards.append(updated_card)

        end_id_num = source_id_num + len(cards) - 1
        logger.info(f"✅ 完全重新分配完成: 区域{region_id}的{len(cards)}张'{label}'牌，ID范围: {source_id_num}{label}-{end_id_num}{label}")
        return assigned_cards

    def _apply_column_consistency_fix(self, cards: List[Dict[str, Any]], region_id: int) -> List[Dict[str, Any]]:
        """
        区域16列一致性修复：确保同一列内的卡牌属于同一类别

        根据GAME_RULES.md：
        - 区域16（对战方吃碰区）从下到上分配ID
        - 同一列应该是同一类型的牌（吃碰组合）
        """
        if not cards:
            return cards

        logger.info(f"🔧 区域{region_id}列一致性检查: {len(cards)}张卡牌")

        # 按X坐标分列
        tolerance = 8.0
        columns = self._group_cards_by_column(cards, tolerance)

        # 检查每列的类别一致性
        needs_fix = False
        for x_key, column_cards in columns.items():
            if self._column_has_category_confusion(column_cards):
                needs_fix = True
                logger.warning(f"检测到列混淆 (X≈{x_key:.1f}): {[card.get('label', '') for card in column_cards]}")

        if not needs_fix:
            logger.info(f"✅ 区域{region_id}列一致性正常，无需修复")
            return cards

        # 执行修复：按类别重新分组和分配
        logger.info(f"🔧 执行区域{region_id}列一致性修复")
        return self._redistribute_cards_by_category(cards, region_id)

    def _group_cards_by_column(self, cards: List[Dict[str, Any]], tolerance: float) -> Dict[float, List[Dict[str, Any]]]:
        """按X坐标将卡牌分组到列中"""
        from collections import defaultdict

        columns = defaultdict(list)

        for card in cards:
            x_center = self._get_card_x_center(card)

            # 寻找合适的列
            assigned = False
            for x_key in columns.keys():
                if abs(x_center - x_key) <= tolerance:
                    columns[x_key].append(card)
                    assigned = True
                    break

            if not assigned:
                columns[x_center].append(card)

        return dict(columns)

    def _get_card_x_center(self, card: Dict[str, Any]) -> float:
        """获取卡牌的X中心坐标"""
        points = card.get('points', [])
        if points and len(points) >= 4:
            x_coords = [point[0] for point in points]
            return sum(x_coords) / len(x_coords)
        return 0.0

    def _column_has_category_confusion(self, column_cards: List[Dict[str, Any]]) -> bool:
        """检查列是否存在类别混淆"""
        if len(column_cards) <= 1:
            return False

        # 提取基础标签（去掉数字前缀）
        base_labels = set()
        for card in column_cards:
            label = card.get('label', '')
            base_label = self._extract_base_label(label)
            base_labels.add(base_label)

        # 检查是否为合法组合
        if len(base_labels) <= 1:
            return False  # 单一类别，正常

        # 检查是否为合法的吃碰组合（如陆+六）
        if self._is_legal_eating_combination(base_labels):
            return False  # 合法组合，正常

        # 检查是否为暗牌转明牌（如拾暗+拾）
        if self._is_dark_to_bright_transition(base_labels):
            return False  # 暗牌转明牌，正常

        # 其他情况视为类别混淆
        return True

    def _extract_base_label(self, label: str) -> str:
        """提取基础标签（去掉数字前缀）"""
        if len(label) >= 2 and label[0].isdigit():
            return label[1:]
        return label

    def _is_legal_eating_combination(self, base_labels: set) -> bool:
        """判断是否为合法的吃碰组合"""
        # 已知的合法吃碰组合
        legal_combinations = [
            {'陆', '六'},  # 陆和六可以组合
            # 可以根据需要添加其他合法组合
        ]

        for combo in legal_combinations:
            if base_labels == combo:
                return True

        return False

    def _is_dark_to_bright_transition(self, base_labels: set) -> bool:
        """判断是否为暗牌转明牌的正常过渡"""
        # 检查是否包含暗牌和对应的明牌
        for label in base_labels:
            if label.endswith('暗'):
                # 提取对应的明牌标签
                bright_label = label[:-1]  # 去掉"暗"字
                if bright_label in base_labels:
                    # 如果同时包含暗牌和对应明牌，且只有这两种标签，则为正常转换
                    if base_labels == {label, bright_label}:
                        return True

        return False

    def _redistribute_cards_by_category(self, cards: List[Dict[str, Any]], region_id: int) -> List[Dict[str, Any]]:
        """按类别重新分配卡牌到列中"""
        # 按基础标签分组
        cards_by_category = {}
        for card in cards:
            label = card.get('label', '')
            base_label = self._extract_base_label(label)

            if base_label not in cards_by_category:
                cards_by_category[base_label] = []
            cards_by_category[base_label].append(card)

        # 为每个类别分配新的列位置
        redistributed_cards = []
        x_start = 80.0  # 起始X坐标
        x_spacing = 20.0  # 列间距

        for i, (category, category_cards) in enumerate(sorted(cards_by_category.items())):
            # 计算该类别的列X坐标
            column_x = x_start + i * x_spacing

            # 按Y坐标排序（从下到上）
            category_cards.sort(key=lambda c: -self._get_card_y_bottom(c))

            # 重新分配位置
            for j, card in enumerate(category_cards):
                updated_card = card.copy()

                # 更新位置（保持Y坐标，调整X坐标）
                points = updated_card.get('points', [])
                if points and len(points) >= 4:
                    # 计算原始宽度
                    x_coords = [point[0] for point in points]
                    original_width = max(x_coords) - min(x_coords)

                    # 更新X坐标，保持Y坐标不变
                    new_points = []
                    for point in points:
                        new_x = column_x + (point[0] - min(x_coords)) - original_width / 2
                        new_points.append([new_x, point[1]])

                    updated_card['points'] = new_points

                redistributed_cards.append(updated_card)
                logger.debug(f"重新分配 {category} 类别卡牌: {updated_card.get('label', '')} -> 列{i+1}")

        logger.info(f"✅ 区域{region_id}列一致性修复完成: {len(cards_by_category)}个类别，{len(redistributed_cards)}张卡牌")
        return redistributed_cards

    def _get_card_y_bottom(self, card: Dict[str, Any]) -> float:
        """获取卡牌的Y底部坐标"""
        points = card.get('points', [])
        if points and len(points) >= 4:
            y_coords = [point[1] for point in points]
            return max(y_coords)
        return 0.0

def create_basic_id_assigner(global_id_manager: GlobalIDManager = None):
    """创建基础ID分配器"""
    if global_id_manager is None:
        global_id_manager = GlobalIDManager()
    return BasicIDAssigner(global_id_manager)

"""
第二阶段集成器 (Phase2Integrator)
集成第一阶段模块 + 第二阶段模块，实现完整的数字孪生功能

处理流程：
1. 数据验证 (第一阶段)
2. 简单继承 (第一阶段)  
3. 区域流转 (第二阶段)
4. 暗牌处理 (第二阶段)
5. ID分配 (第一阶段)
6. 遮挡补偿 (第二阶段)
"""

from typing import List, Dict, Any
from dataclasses import dataclass
import logging

from .data_validator import create_data_validator
from .basic_id_assigner import create_basic_id_assigner, GlobalIDManager
from .simple_inheritor import create_simple_inheritor
from .region2_processor import create_region2_processor
from .region_transitioner import create_region_transitioner
from .dark_card_processor import create_dark_card_processor
from .occlusion_compensator import create_occlusion_compensator
from .virtual_region_processor import create_virtual_region_processor
from .card_21_tracker import create_card21_tracker
from .spatial_sorter import create_spatial_sorter
from .game_boundary_detector import GameBoundaryDetector

logger = logging.getLogger(__name__)

@dataclass
class Phase2Result:
    """第二阶段处理结果"""
    success: bool
    processed_cards: List[Dict[str, Any]]
    validation_errors: List[str]
    validation_warnings: List[str]
    statistics: Dict[str, Any]

class Phase2Integrator:
    """第二阶段集成器 - 完整功能集成，实现真正的n+1帧设计"""

    def __init__(self):
        # 创建全局ID管理器（整个牌局共享）
        self.global_id_manager = GlobalIDManager()

        # 初始化所有模块
        self.data_validator = create_data_validator()
        self.inheritor = create_simple_inheritor()  # 基于区域状态的继承，不需要IOU阈值
        self.virtual_region_processor = create_virtual_region_processor()  # 🆕 虚拟区域处理器
        self.region2_processor = create_region2_processor()
        self.region_transitioner = create_region_transitioner()
        self.dark_card_processor = create_dark_card_processor()
        self.id_assigner = create_basic_id_assigner(self.global_id_manager)
        self.spatial_sorter = create_spatial_sorter()  # 🆕 空间排序器
        self.occlusion_compensator = create_occlusion_compensator()
        self.card21_tracker = create_card21_tracker()  # 🆕 第21张牌跟踪器
        self.boundary_detector = GameBoundaryDetector()  # 🆕 游戏边界检测器

        # 处理统计
        self.frame_count = 0
        
        logger.info("第二阶段集成器初始化完成")
    
    def process_frame(self, detections: List[Dict[str, Any]]) -> Phase2Result:
        """处理一帧数据 - 第二阶段完整流程"""
        self.frame_count += 1
        logger.info(f"开始处理第{self.frame_count}帧，输入{len(detections)}条检测数据")
        
        try:
            # 步骤0：游戏边界检测（检测单局边界）
            boundary_result = self.boundary_detector.detect_boundary(detections)
            if boundary_result.should_reset:
                logger.info(f"🔄 检测到单局边界: {boundary_result.trigger_labels}，重置系统状态")
                self.reset_system()

            # 步骤1：数据验证
            validation_result = self.data_validator.validate(detections)
            if not validation_result.is_valid:
                logger.error(f"数据验证失败: {validation_result.errors}")
                return Phase2Result(
                    success=False,
                    processed_cards=[],
                    validation_errors=validation_result.errors,
                    validation_warnings=validation_result.warnings,
                    statistics={}
                )

            logger.info(f"数据验证通过: {len(validation_result.cleaned_data)}条有效数据")
            
            # 步骤2：虚拟区域处理 🆕
            virtual_region_result = self.virtual_region_processor.process_virtual_regions(validation_result.cleaned_data)
            logger.info(f"虚拟区域处理完成: 物理{len(virtual_region_result.physical_cards)}张, "
                       f"虚拟{len(virtual_region_result.virtual_cards)}张")

            # 步骤3：区域2互斥处理（基于原始检测数据）
            region2_result = self.region2_processor.process_region2_exclusive(virtual_region_result.physical_cards)
            logger.info(f"区域2互斥完成: 处理{len(region2_result.processed_cards)}张, "
                       f"区域1删除{len(region2_result.region1_removed_cards)}张, "
                       f"区域2继承{len(region2_result.region2_inherited_cards)}张")

            # 步骤4：简单继承处理（处理区域2互斥后的卡牌）
            inheritance_result = self.inheritor.process_inheritance(region2_result.processed_cards)
            logger.info(f"继承处理完成: 继承{len(inheritance_result.inherited_cards)}张, "
                       f"新增{len(inheritance_result.new_cards)}张")

            # 🔧 调试继承处理后的区域4数据
            all_cards_after_inheritance = inheritance_result.inherited_cards + inheritance_result.new_cards
            region_4_after_inheritance = [card for card in all_cards_after_inheritance if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_INHERITANCE] 继承处理后: 总共{len(all_cards_after_inheritance)}张，区域4有{len(region_4_after_inheritance)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_after_inheritance):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 步骤5：区域流转处理
            # 传递前一帧数据以支持跨帧区域流转（如7→16）
            transition_result = self.region_transitioner.process_transitions(
                all_cards_after_inheritance,
                getattr(self, '_previous_frame_cards', None)
            )
            logger.info(f"区域流转完成: 流转{len(transition_result.transitioned_cards)}张, "
                       f"新增{len(transition_result.new_cards)}张")

            # 🔧 调试区域流转后的区域4数据
            all_cards_after_transition = transition_result.transitioned_cards + transition_result.new_cards
            region_4_after_transition = [card for card in all_cards_after_transition if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_TRANSITION] 区域流转后: 总共{len(all_cards_after_transition)}张，区域4有{len(region_4_after_transition)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_after_transition):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 步骤6：暗牌处理
            all_cards_after_transition = transition_result.transitioned_cards + transition_result.new_cards
            dark_card_result = self.dark_card_processor.process_dark_cards(all_cards_after_transition)
            logger.info(f"暗牌处理完成: 处理{len(dark_card_result.processed_cards)}张卡牌")

            # 🔧 调试暗牌处理后的区域4数据
            region_4_after_dark = [card for card in dark_card_result.processed_cards if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_DARK_CARD] 暗牌处理后: 总共{len(dark_card_result.processed_cards)}张，区域4有{len(region_4_after_dark)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_after_dark):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 步骤7：为新卡牌分配ID（先空间排序，再分配）
            cards_without_ids = [card for card in dark_card_result.processed_cards if not card.get('twin_id')]
            print(f"🚨🚨🚨 [ID_ASSIGNMENT] 需要分配ID的卡牌: {len(cards_without_ids)}张 🚨🚨🚨")

            # 🔧 检查区域4是否在需要分配ID的列表中
            region_4_without_ids = [card for card in cards_without_ids if card.get('group_id') == 4]
            if region_4_without_ids:
                print(f"🚨🚨🚨 [REGION_4_ID_ISSUE] 警告：区域4有{len(region_4_without_ids)}张卡牌需要重新分配ID！🚨🚨🚨")
                for card in region_4_without_ids:
                    label = card.get('label', 'None')
                    print(f"  需要重新分配ID的区域4卡牌: 标签'{label}'")

            if cards_without_ids:
                # 🆕 空间排序：按卡牌类型和区域进行空间排序
                spatially_sorted_cards = self._assign_ids_with_spatial_order(cards_without_ids)
                new_cards_with_ids = spatially_sorted_cards
                assignment_stats = {"spatial_sorting_applied": True, "cards_processed": len(spatially_sorted_cards)}

                # 注册新卡牌到区域流转器
                for card in new_cards_with_ids:
                    self.region_transitioner.register_new_card(card)
            else:
                new_cards_with_ids = []
                assignment_stats = {}

            # 合并所有已有ID的卡牌和新分配ID的卡牌
            cards_with_ids = [card for card in dark_card_result.processed_cards if card.get('twin_id')]
            all_processed_cards = cards_with_ids + new_cards_with_ids

            # 🔧 调试ID分配后的区域4数据
            region_4_final = [card for card in all_processed_cards if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_ID_ASSIGNMENT] ID分配后: 总共{len(all_processed_cards)}张，区域4有{len(region_4_final)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_final):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 步骤8：遮挡补偿
            compensation_result = self.occlusion_compensator.process_compensation(all_processed_cards)
            physical_cards_final = all_processed_cards + compensation_result.compensated_cards
            logger.info(f"遮挡补偿完成: 补偿{len(compensation_result.compensated_cards)}张卡牌")

            # 🔧 调试遮挡补偿后的区域4数据
            region_4_after_compensation = [card for card in physical_cards_final if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_COMPENSATION] 遮挡补偿后: 总共{len(physical_cards_final)}张，区域4有{len(region_4_after_compensation)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_after_compensation):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 步骤9：第21张牌跟踪 🆕
            card21_result = self.card21_tracker.process_card21_tracking(
                physical_cards_final,
                previous_cards=getattr(self, '_previous_frame_cards', None)
            )
            logger.info(f"第21张牌跟踪完成: 当前跟踪{len(card21_result.tracked_cards)}张, "
                       f"重现{len(card21_result.reappeared_cards)}张")

            # 🔧 调试第21张牌跟踪后的区域4数据
            region_4_after_card21 = [card for card in physical_cards_final if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [AFTER_CARD21] 第21张牌跟踪后: 总共{len(physical_cards_final)}张，区域4有{len(region_4_after_card21)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_after_card21):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 合并物理卡牌和虚拟卡牌作为最终结果
            final_cards = physical_cards_final + virtual_region_result.virtual_cards

            # 🔧 调试最终结果的区域4数据
            region_4_final_result = [card for card in final_cards if card.get('group_id') == 4]
            print(f"🚨🚨🚨 [FINAL_RESULT] Phase2Integrator最终结果: 总共{len(final_cards)}张，区域4有{len(region_4_final_result)}张 🚨🚨🚨")
            for i, card in enumerate(region_4_final_result):
                label = card.get('label', 'None')
                twin_id = card.get('twin_id', 'None')
                print(f"  区域4[{i}]: 标签'{label}', ID'{twin_id}'")

            # 保存当前帧数据供下一帧使用
            self._previous_frame_cards = physical_cards_final

            # 步骤10：更新继承器的前一帧数据（只使用物理卡牌）
            self.inheritor._update_previous_frame(physical_cards_final)
            logger.debug(f"更新继承器前一帧数据: {len(physical_cards_final)}张物理卡牌")

            # 生成综合统计
            comprehensive_stats = self._generate_comprehensive_statistics(
                virtual_region_result.statistics,
                inheritance_result.statistics,
                region2_result.statistics,
                transition_result.statistics,
                dark_card_result.statistics,
                assignment_stats,
                compensation_result.statistics,
                card21_result.statistics,
                validation_result.warnings
            )
            
            logger.info(f"第{self.frame_count}帧处理完成: 总计{len(final_cards)}张卡牌")
            
            return Phase2Result(
                success=True,
                processed_cards=final_cards,
                validation_errors=[],
                validation_warnings=validation_result.warnings,
                statistics=comprehensive_stats
            )
            
        except Exception as e:
            logger.error(f"第{self.frame_count}帧处理异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return Phase2Result(
                success=False,
                processed_cards=[],
                validation_errors=[f"处理异常: {str(e)}"],
                validation_warnings=[],
                statistics={}
            )
    
    def _generate_comprehensive_statistics(self,
                                         virtual_region_stats: Dict[str, Any],
                                         inheritance_stats: Dict[str, Any],
                                         region2_stats: Dict[str, Any],
                                         transition_stats: Dict[str, Any],
                                         dark_card_stats: Dict[str, Any],
                                         assignment_stats: Dict[str, Any],
                                         compensation_stats: Dict[str, Any],
                                         card21_stats: Dict[str, Any],
                                         warnings: List[str]) -> Dict[str, Any]:
        """生成综合统计信息"""
        return {
            "frame_info": {
                "frame_number": self.frame_count,
                "processing_success": True,
                "processing_stage": "第二阶段完整流程（含虚拟区域和第21张牌跟踪）"
            },
            "virtual_region_processing": virtual_region_stats,
            "inheritance": inheritance_stats,
            "region2_exclusive": region2_stats,
            "region_transition": transition_stats,
            "dark_card_processing": dark_card_stats,
            "id_assignment": assignment_stats,
            "occlusion_compensation": compensation_stats,
            "card21_tracking": card21_stats,
            "validation": {
                "warnings_count": len(warnings),
                "warnings": warnings
            },
            "summary": {
                "total_cards": (
                    inheritance_stats.get("current_frame", {}).get("total", 0)
                ),
                "inheritance_rate": (
                    inheritance_stats.get("current_frame", {}).get("inheritance_rate", 0)
                ),
                "transition_rate": (
                    transition_stats.get("current_frame", {}).get("transition_rate", 0)
                ),
                "dark_card_success_rate": (
                    dark_card_stats.get("processing_summary", {}).get("success_rate", 0)
                ),
                "compensation_rate": (
                    compensation_stats.get("current_frame", {}).get("compensation_rate", 0)
                ),
                "new_assignments": (
                    assignment_stats.get("total_assigned", 0)
                )
            }
        }
    
    def reset_system(self):
        """重置整个系统（用于新局开始）"""
        self.id_assigner.reset_counters()
        self.inheritor.reset_inheritance_history()
        self.region_transitioner.reset_transition_history()
        self.dark_card_processor.reset_processing_stats()
        self.occlusion_compensator.reset_compensation_history()
        self.frame_count = 0
        logger.info("第二阶段系统已重置")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "frame_count": self.frame_count,
            "virtual_region_rate": self.virtual_region_processor.get_virtual_rate(),
            "inheritance_rate": self.inheritor.get_inheritance_rate(),
            "transition_rate": self.region_transitioner.get_transition_rate(),
            "dark_card_success_rate": self.dark_card_processor.get_success_rate(),
            "compensation_rate": self.occlusion_compensator.get_compensation_rate(),
            "card21_tracking_efficiency": self.card21_tracker._calculate_tracking_efficiency(),
            "has_previous_frame": self.inheritor.has_previous_frame(),
            "id_counters": dict(self.global_id_manager.id_counters),
            "active_transitions": len(self.region_transitioner.card_transition_history),
            "active_compensations": len(self.occlusion_compensator.compensation_history),
            "currently_tracking_card21": len(self.card21_tracker.tracked_cards)
        }
    
    def get_detailed_summary(self) -> Dict[str, Any]:
        """获取详细摘要"""
        return {
            "system_info": {
                "stage": "第二阶段（完整版）",
                "modules": [
                    "数据验证器", "虚拟区域处理器", "简单继承器", "区域2处理器",
                    "区域流转器", "暗牌处理器", "基础ID分配器", "遮挡补偿器", "第21张牌跟踪器"
                ],
                "frame_count": self.frame_count
            },
            "performance_metrics": {
                "virtual_region_rate": f"{self.virtual_region_processor.get_virtual_rate():.1%}",
                "inheritance_rate": f"{self.inheritor.get_inheritance_rate():.1%}",
                "transition_rate": f"{self.region_transitioner.get_transition_rate():.1%}",
                "dark_card_success_rate": f"{self.dark_card_processor.get_success_rate():.1%}",
                "compensation_rate": f"{self.occlusion_compensator.get_compensation_rate():.1%}",
                "card21_tracking_efficiency": f"{self.card21_tracker._calculate_tracking_efficiency():.1%}"
            },
            "compensation_summary": self.occlusion_compensator.get_compensation_summary(),
            "card21_tracking_summary": self.card21_tracker.get_ai_inference_support()
        }

    def _assign_ids_with_spatial_order(self, cards_without_ids: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为新卡牌分配ID，先进行空间排序

        核心逻辑：
        1. 按区域和卡牌类型分组
        2. 对每组进行空间排序（根据GAME_RULES.md原则）
        3. 按排序后的顺序依次分配ID
        """
        logger.info(f"开始为{len(cards_without_ids)}张新卡牌进行空间排序和ID分配")

        from collections import defaultdict

        # 按区域分组
        cards_by_region = defaultdict(list)
        for card in cards_without_ids:
            region_id = card.get('group_id', 1)
            cards_by_region[region_id].append(card)

        all_assigned_cards = []

        for region_id, region_cards in cards_by_region.items():
            logger.debug(f"处理区域{region_id}的{len(region_cards)}张卡牌")

            # 按卡牌类型分组
            cards_by_type = defaultdict(list)
            for card in region_cards:
                card_type = card.get('label', 'unknown')
                cards_by_type[card_type].append(card)

            # 对每种类型的卡牌进行空间排序和ID分配
            for card_type, type_cards in cards_by_type.items():
                if len(type_cards) > 1:
                    # 多张同类型卡牌：先空间排序，再按顺序分配ID
                    logger.info(f"区域{region_id}的{len(type_cards)}张'{card_type}'牌需要空间排序")

                    # 使用空间排序器排序
                    sorting_result = self.spatial_sorter.sort_cards_by_spatial_order(type_cards, region_id)
                    sorted_cards = sorting_result.sorted_cards

                    logger.info(f"空间排序完成，使用规则: {sorting_result.sorting_rule}")

                    # 🔧 检查是否有流转卡牌，如果有则批量处理以触发完全重新分配
                    has_transition_cards = any(
                        card.get('from_region_4') or
                        card.get('from_region_3') or
                        card.get('from_region_7') or
                        card.get('transition_source') in ['4→16', '3→16', '7→16']
                        for card in sorted_cards
                    )

                    if has_transition_cards:
                        # 流转场景：批量分配以触发完全重新分配逻辑
                        logger.info(f"检测到流转场景，对{len(sorted_cards)}张'{card_type}'牌执行批量分配")
                        assignment_result = self.id_assigner.assign_ids(sorted_cards)
                        all_assigned_cards.extend(assignment_result.assigned_cards)
                    else:
                        # 非流转场景：按排序后的顺序逐个分配ID
                        for i, card in enumerate(sorted_cards):
                            assignment_result = self.id_assigner.assign_ids([card])
                            if assignment_result.assigned_cards:
                                assigned_card = assignment_result.assigned_cards[0]
                                all_assigned_cards.append(assigned_card)
                                logger.debug(f"第{i+1}张'{card_type}'牌分配ID: {assigned_card.get('twin_id', 'unknown')}")
                            else:
                                logger.warning(f"第{i+1}张'{card_type}'牌ID分配失败")
                                all_assigned_cards.append(card)
                else:
                    # 单张卡牌：直接分配ID
                    assignment_result = self.id_assigner.assign_ids(type_cards)
                    all_assigned_cards.extend(assignment_result.assigned_cards)
                    logger.debug(f"区域{region_id}的单张'{card_type}'牌已分配ID")

        logger.info(f"空间排序和ID分配完成，共处理{len(all_assigned_cards)}张卡牌")
        return all_assigned_cards

    def _assign_ids_with_spatial_order(self, cards_without_ids: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为新卡牌分配ID，先进行空间排序

        核心逻辑：
        1. 按区域和卡牌类型分组
        2. 对每组进行空间排序（根据GAME_RULES.md原则）
        3. 按排序后的顺序依次分配ID
        """
        logger.info(f"开始为{len(cards_without_ids)}张新卡牌进行空间排序和ID分配")

        from collections import defaultdict

        # 按区域分组
        cards_by_region = defaultdict(list)
        for card in cards_without_ids:
            region_id = card.get('group_id', 1)
            cards_by_region[region_id].append(card)

        all_assigned_cards = []

        for region_id, region_cards in cards_by_region.items():
            logger.debug(f"处理区域{region_id}的{len(region_cards)}张卡牌")

            # 按卡牌类型分组
            cards_by_type = defaultdict(list)
            for card in region_cards:
                card_type = card.get('label', 'unknown')
                cards_by_type[card_type].append(card)

            # 对每种类型的卡牌进行空间排序和ID分配
            for card_type, type_cards in cards_by_type.items():
                if len(type_cards) > 1:
                    # 多张同类型卡牌：先空间排序，再按顺序分配ID
                    logger.info(f"区域{region_id}的{len(type_cards)}张'{card_type}'牌需要空间排序")

                    # 使用空间排序器排序
                    sorting_result = self.spatial_sorter.sort_cards_by_spatial_order(type_cards, region_id)
                    sorted_cards = sorting_result.sorted_cards

                    logger.info(f"空间排序完成，使用规则: {sorting_result.sorting_rule}")

                    # 🔧 检查是否有流转卡牌，如果有则批量处理以触发完全重新分配
                    has_transition_cards = any(
                        card.get('from_region_4') or
                        card.get('from_region_3') or
                        card.get('from_region_7') or
                        card.get('transition_source') in ['4→16', '3→16', '7→16']
                        for card in sorted_cards
                    )

                    if has_transition_cards:
                        # 流转场景：批量分配以触发完全重新分配逻辑
                        logger.info(f"检测到流转场景，对{len(sorted_cards)}张'{card_type}'牌执行批量分配")
                        assignment_result = self.id_assigner.assign_ids(sorted_cards)
                        all_assigned_cards.extend(assignment_result.assigned_cards)
                    else:
                        # 非流转场景：按排序后的顺序逐个分配ID
                        for i, card in enumerate(sorted_cards):
                            assignment_result = self.id_assigner.assign_ids([card])
                            if assignment_result.assigned_cards:
                                assigned_card = assignment_result.assigned_cards[0]
                                all_assigned_cards.append(assigned_card)
                                logger.debug(f"第{i+1}张'{card_type}'牌分配ID: {assigned_card.get('twin_id', 'unknown')}")
                            else:
                                logger.warning(f"第{i+1}张'{card_type}'牌ID分配失败")
                                all_assigned_cards.append(card)
                else:
                    # 单张卡牌：直接分配ID
                    assignment_result = self.id_assigner.assign_ids(type_cards)
                    all_assigned_cards.extend(assignment_result.assigned_cards)
                    logger.debug(f"区域{region_id}的单张'{card_type}'牌已分配ID")

        logger.info(f"空间排序和ID分配完成，共处理{len(all_assigned_cards)}张卡牌")
        return all_assigned_cards

def create_phase2_integrator():
    """创建第二阶段集成器"""
    return Phase2Integrator()

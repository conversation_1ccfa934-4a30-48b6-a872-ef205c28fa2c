# 跑胡子卡牌检测系统安装指南

本文档提供了跑胡子卡牌检测系统的安装和使用指南。

## 系统要求

- Windows 10/11 64位操作系统
- Python 3.10或更高版本
- NVIDIA GPU（推荐，支持CUDA 11.8或更高版本）
- 至少8GB内存
- 至少10GB磁盘空间

## 安装步骤

### 1. 安装Python环境

如果您尚未安装Python，请从[Python官网](https://www.python.org/downloads/)下载并安装Python 3.10或更高版本。

### 2. 克隆或下载项目

```bash
git clone https://github.com/your-username/phz-ai-simple.git
cd phz-ai-simple
```

### 3. 创建虚拟环境（推荐）

```bash
python -m venv env
```

### 4. 激活虚拟环境

Windows:
```bash
.\env\Scripts\activate
```

### 5. 安装依赖

```bash
pip install -r requirements.txt
```

### 6. 下载预训练模型

如果您没有自己训练的模型，可以使用我们提供的预训练模型：

```bash
# 预训练模型已包含在仓库中：yolo11n.pt
# 如果需要重新下载，请联系项目维护者
```

## 使用指南

### 1. 卡牌检测

#### 1.1 检测单张图像

```bash
python -m src.detect --source path/to/your/image.jpg --visualize --save-json
```

#### 1.2 检测视频

```bash
python -m src.detect --source path/to/your/video.mp4 --output output/result.mp4 --visualize --save-json
```

#### 1.3 实时屏幕检测

```bash
python -m src.main --config src/config.yaml
```

### 2. 测试和评估

#### 2.1 测试单张图像

```bash
python -m src.test_detector --mode image --input path/to/your/image.jpg --output output
```

#### 2.2 批量测试图像

```bash
python -m src.test_detector --mode batch --input path/to/your/image_folder --output output
```

#### 2.3 测试视频

```bash
python -m src.test_detector --mode video --input path/to/your/video.mp4 --output output
```

#### 2.4 性能评估

```bash
python -m src.test_detector --mode evaluate --input path/to/your/test_images --output output
```

## 配置文件

系统的主要配置文件位于`src/config.yaml`，您可以根据需要修改以下参数：

- 模型路径
- 置信度阈值
- IOU阈值
- 批处理大小
- 输出配置

## 常见问题

### 1. 无法导入mss模块

如果遇到`ImportError: No module named 'mss'`错误，请运行：

```bash
pip install mss
```

### 2. CUDA相关错误

确保您已安装与PyTorch兼容的CUDA版本。如果遇到CUDA相关错误，可以尝试使用CPU模式：

在`config.yaml`中将`device`设置为`"cpu"`。

### 3. 检测效果不佳

- 确保图像清晰，光线充足
- 尝试调整`confidence_threshold`参数（默认为0.25）
- 考虑使用更大的模型（如yolov8l.pt）

## 支持与反馈

如有任何问题或建议，请通过以下方式联系我们：

- 提交GitHub Issue
- 发送邮件至：<EMAIL> 
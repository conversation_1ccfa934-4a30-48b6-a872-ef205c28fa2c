#!/usr/bin/env python3
"""
7→16区域流转深度分析脚本

专门分析frame_00033→frame_00034中7→16区域流转失效的原因，
重点跟踪1五卡牌的处理过程。
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目核心模块
from calibration_gt_final_processor import CalibrationGTFinalProcessor, FinalProcessorConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_frame_data(frame_name: str, config: FinalProcessorConfig) -> Dict[str, Any]:
    """分析单帧的原始数据和处理结果"""
    frame_path = Path(config.source_dir) / "labels" / f"{frame_name}.json"
    
    if not frame_path.exists():
        return {"error": f"文件不存在: {frame_path}"}
    
    # 读取原始标注数据
    with open(frame_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    # 分析原始数据中的卡牌
    analysis = {
        "frame_name": frame_name,
        "original_shapes": original_data.get("shapes", []),
        "region_7_cards": [],
        "region_16_cards": [],
        "wu_cards": []
    }
    
    # 分析原始标注中的卡牌分布
    for shape in original_data.get("shapes", []):
        if shape.get("group_id") == 7:
            analysis["region_7_cards"].append({
                "label": shape.get("label"),
                "bbox": shape.get("points", []),
                "group_id": shape.get("group_id")
            })
            if shape.get("label") == "五":
                analysis["wu_cards"].append({
                    "label": shape.get("label"),
                    "region": 7,
                    "bbox": shape.get("points", [])
                })
        elif shape.get("group_id") == 16:
            analysis["region_16_cards"].append({
                "label": shape.get("label"),
                "bbox": shape.get("points", []),
                "group_id": shape.get("group_id")
            })
            if shape.get("label") == "五":
                analysis["wu_cards"].append({
                    "label": shape.get("label"),
                    "region": 16,
                    "bbox": shape.get("points", [])
                })
    
    return analysis

def process_frame_and_track(frame_name: str, processor: CalibrationGTFinalProcessor, 
                           previous_frame_data: Optional[Dict] = None) -> Dict[str, Any]:
    """处理帧并跟踪结果"""
    frame_path = Path(processor.config.source_dir) / "labels" / f"{frame_name}.json"
    
    try:
        # 读取并处理帧
        with open(frame_path, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 转换为检测格式
        all_detections = processor._convert_all_shapes_to_detections(original_data.get("shapes", []))
        
        # 处理帧
        dt_result = processor.digital_twin_controller.process_frame(all_detections)
        
        # 分析处理结果
        result_analysis = {
            "frame_name": frame_name,
            "processing_success": dt_result.success,
            "total_cards": len(dt_result.processed_cards) if dt_result.success else 0,
            "region_7_cards": [],
            "region_16_cards": [],
            "wu_cards_final": []
        }
        
        if dt_result.success:
            for card in dt_result.processed_cards:
                if card.get("group_id") == 7:
                    result_analysis["region_7_cards"].append(card)
                    if card.get("label") == "五" or "五" in str(card.get("twin_id", "")):
                        result_analysis["wu_cards_final"].append({
                            "twin_id": card.get("twin_id"),
                            "label": card.get("label"),
                            "region": 7,
                            "inherited": card.get("inherited", False)
                        })
                elif card.get("group_id") == 16:
                    result_analysis["region_16_cards"].append(card)
                    if card.get("label") == "五" or "五" in str(card.get("twin_id", "")):
                        result_analysis["wu_cards_final"].append({
                            "twin_id": card.get("twin_id"),
                            "label": card.get("label"),
                            "region": 16,
                            "inherited": card.get("inherited", False)
                        })
        
        return result_analysis
        
    except Exception as e:
        logger.error(f"处理帧 {frame_name} 失败: {e}")
        return {
            "frame_name": frame_name,
            "processing_success": False,
            "error": str(e)
        }

def analyze_transition_issue() -> Dict[str, Any]:
    """分析7→16流转问题"""
    config = FinalProcessorConfig(
        source_dir='legacy_assets/ceshi/calibration_gt',
        output_dir='output/transition_analysis'
    )
    
    processor = CalibrationGTFinalProcessor(config)
    
    print("🔍 分析7→16区域流转问题")
    print("=" * 50)
    
    # 1. 分析原始数据
    print("\n📊 步骤1: 分析原始标注数据")
    frame_33_raw = analyze_frame_data("frame_00033", config)
    frame_34_raw = analyze_frame_data("frame_00034", config)
    
    print(f"Frame_33原始数据:")
    print(f"  区域7卡牌: {len(frame_33_raw['region_7_cards'])}张")
    print(f"  区域16卡牌: {len(frame_33_raw['region_16_cards'])}张")
    print(f"  五卡牌: {len(frame_33_raw['wu_cards'])}张")
    for wu in frame_33_raw['wu_cards']:
        print(f"    - 标签:{wu['label']}, 区域:{wu['region']}")
    
    print(f"\nFrame_34原始数据:")
    print(f"  区域7卡牌: {len(frame_34_raw['region_7_cards'])}张")
    print(f"  区域16卡牌: {len(frame_34_raw['region_16_cards'])}张")
    print(f"  五卡牌: {len(frame_34_raw['wu_cards'])}张")
    for wu in frame_34_raw['wu_cards']:
        print(f"    - 标签:{wu['label']}, 区域:{wu['region']}")
    
    # 2. 处理frame_33
    print("\n📊 步骤2: 处理frame_00033")
    frame_33_processed = process_frame_and_track("frame_00033", processor)
    
    print(f"Frame_33处理结果:")
    print(f"  处理成功: {frame_33_processed['processing_success']}")
    print(f"  总卡牌: {frame_33_processed['total_cards']}")
    print(f"  区域7卡牌: {len(frame_33_processed['region_7_cards'])}张")
    print(f"  区域16卡牌: {len(frame_33_processed['region_16_cards'])}张")
    print(f"  五卡牌最终状态: {len(frame_33_processed['wu_cards_final'])}张")
    for wu in frame_33_processed['wu_cards_final']:
        print(f"    - ID:{wu['twin_id']}, 标签:{wu['label']}, 区域:{wu['region']}")
    
    # 3. 处理frame_34
    print("\n📊 步骤3: 处理frame_00034")
    frame_34_processed = process_frame_and_track("frame_00034", processor, frame_33_processed)
    
    print(f"Frame_34处理结果:")
    print(f"  处理成功: {frame_34_processed['processing_success']}")
    print(f"  总卡牌: {frame_34_processed['total_cards']}")
    print(f"  区域7卡牌: {len(frame_34_processed['region_7_cards'])}张")
    print(f"  区域16卡牌: {len(frame_34_processed['region_16_cards'])}张")
    print(f"  五卡牌最终状态: {len(frame_34_processed['wu_cards_final'])}张")
    for wu in frame_34_processed['wu_cards_final']:
        print(f"    - ID:{wu['twin_id']}, 标签:{wu['label']}, 区域:{wu['region']}, 继承:{wu['inherited']}")
    
    # 4. 分析流转问题
    print("\n🔍 步骤4: 流转问题分析")
    
    # 检查原始数据中的流转
    frame_33_wu_in_7 = [wu for wu in frame_33_raw['wu_cards'] if wu['region'] == 7]
    frame_34_wu_in_16 = [wu for wu in frame_34_raw['wu_cards'] if wu['region'] == 16]
    
    print(f"原始数据流转分析:")
    print(f"  Frame_33区域7中的五: {len(frame_33_wu_in_7)}张")
    print(f"  Frame_34区域16中的五: {len(frame_34_wu_in_16)}张")
    
    if frame_33_wu_in_7 and frame_34_wu_in_16:
        print("  ✅ 原始数据显示存在7→16流转")
    else:
        print("  ❌ 原始数据中未检测到7→16流转")
    
    # 检查处理结果中的流转
    frame_33_processed_wu_in_7 = [wu for wu in frame_33_processed['wu_cards_final'] if wu['region'] == 7]
    frame_34_processed_wu_in_16 = [wu for wu in frame_34_processed['wu_cards_final'] if wu['region'] == 16]
    
    print(f"处理结果流转分析:")
    print(f"  Frame_33处理后区域7中的五: {len(frame_33_processed_wu_in_7)}张")
    print(f"  Frame_34处理后区域16中的五: {len(frame_34_processed_wu_in_16)}张")
    
    # 5. 根本原因分析
    print("\n🔍 步骤5: 根本原因分析")
    
    issues = []
    
    if not frame_33_wu_in_7:
        issues.append("Frame_33原始数据中区域7没有五卡牌")
    elif not frame_33_processed_wu_in_7:
        issues.append("Frame_33处理过程中区域7的五卡牌丢失")
    
    if not frame_34_wu_in_16:
        issues.append("Frame_34原始数据中区域16没有五卡牌")
    elif not frame_34_processed_wu_in_16:
        issues.append("Frame_34处理过程中区域16的五卡牌丢失")
    
    if frame_33_processed_wu_in_7 and not frame_34_processed_wu_in_16:
        issues.append("7→16流转机制失效：前一帧有五卡牌但当前帧区域16中没有")
    
    print("发现的问题:")
    for i, issue in enumerate(issues, 1):
        print(f"  {i}. {issue}")
    
    # 6. 解决方案建议
    print("\n💡 解决方案建议:")
    
    recommendations = [
        "检查RegionTransitioner模块的7→16流转实现",
        "验证SimpleInheritor的跨区域继承逻辑",
        "确认Phase2Integrator中模块调用顺序",
        "添加详细的流转过程日志记录",
        "检查ID分配器是否覆盖了继承的ID"
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    # 返回分析结果
    return {
        "frame_33_raw": frame_33_raw,
        "frame_34_raw": frame_34_raw,
        "frame_33_processed": frame_33_processed,
        "frame_34_processed": frame_34_processed,
        "issues": issues,
        "recommendations": recommendations
    }

def main():
    """主函数"""
    try:
        result = analyze_transition_issue()
        
        # 保存分析结果
        output_path = Path("output/transition_analysis/7_to_16_analysis.json")
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 详细分析结果已保存到: {output_path}")
        
    except Exception as e:
        logger.exception("分析过程中发生错误")
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()

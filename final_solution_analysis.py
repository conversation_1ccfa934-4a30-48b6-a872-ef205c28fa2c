#!/usr/bin/env python3
"""
最终解决方案分析：
重新定义"真正的错误"，区分暗牌转明牌（正常）和类别混淆（错误）
"""

import json
import sys
from collections import defaultdict

def is_dark_to_bright_transition(base_labels):
    """判断是否为暗牌转明牌的正常过渡"""
    
    # 暗牌转明牌的正常组合模式
    normal_transitions = [
        {'拾暗', '拾'},
        {'肆暗', '肆'},
        {'三暗', '三'},
        {'二暗', '二'},
        {'一暗', '一'},
        {'五暗', '五'},
        {'六暗', '六'},
        {'七暗', '七'},
        {'八暗', '八'},
        {'九暗', '九'},
        {'十暗', '十'},
        {'壹暗', '壹'},
        {'贰暗', '贰'},
        {'叁暗', '叁'},
        {'伍暗', '伍'},
        {'陆暗', '陆'},
        {'柒暗', '柒'},
        {'捌暗', '捌'},
        {'玖暗', '玖'},
        {'拾暗', '拾'}
    ]
    
    base_labels_set = set(base_labels)
    
    # 检查是否匹配任何正常转换模式
    for transition in normal_transitions:
        if base_labels_set == transition:
            return True
    
    return False

def is_legal_eating_combination(base_labels):
    """判断是否为合法的吃碰组合"""
    
    # 已知的合法吃碰组合
    legal_combinations = [
        {'陆', '六'},  # 陆和六可以组合
        # 可以根据需要添加其他合法组合
    ]
    
    base_labels_set = set(base_labels)
    
    for combo in legal_combinations:
        if base_labels_set == combo:
            return True
    
    return False

def analyze_column_with_game_rules(labels_in_column):
    """基于游戏规则分析列的状态"""
    
    # 提取基础标签
    base_labels = []
    for label in labels_in_column:
        base_label = label[1:] if len(label) >= 2 and label[0].isdigit() else label
        base_labels.append(base_label)
    
    unique_base_labels = list(set(base_labels))
    
    if len(unique_base_labels) == 1:
        return {
            'status': 'normal',
            'type': 'single_type',
            'description': '单一类别',
            'needs_fix': False
        }
    
    # 检查是否为暗牌转明牌
    if is_dark_to_bright_transition(unique_base_labels):
        return {
            'status': 'normal',
            'type': 'dark_to_bright',
            'description': '暗牌转明牌（正常游戏过程）',
            'needs_fix': False
        }
    
    # 检查是否为合法吃碰组合
    if is_legal_eating_combination(unique_base_labels):
        return {
            'status': 'normal',
            'type': 'legal_combination',
            'description': '合法吃碰组合',
            'needs_fix': False
        }
    
    # 其他情况视为真正的类别混淆
    return {
        'status': 'error',
        'type': 'category_confusion',
        'description': f'类别混淆: {unique_base_labels}',
        'needs_fix': True,
        'mixed_types': unique_base_labels
    }

def reanalyze_all_frames():
    """基于游戏规则重新分析所有帧"""
    
    frames = ['frame_00018', 'frame_00028', 'frame_00034', 'frame_00060', 'frame_00230']
    
    print(f"🎮 基于游戏规则的重新分析")
    print(f"=" * 80)
    
    for frame_name in frames:
        frame_path = f'output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json'
        
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except FileNotFoundError:
            print(f"\n❌ {frame_name}: 文件不存在")
            continue
        
        region16_cards = []
        for shape in data.get('shapes', []):
            if shape.get('group_id') == 16:
                points = shape.get('points', [])
                if points:
                    x_center = sum([p[0] for p in points]) / len(points)
                    y_bottom = max([p[1] for p in points])
                else:
                    x_center = y_bottom = 0
                    
                card_info = {
                    'label': shape.get('label', ''),
                    'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'x_center': x_center,
                    'y_bottom': y_bottom
                }
                region16_cards.append(card_info)
        
        print(f"\n🔍 {frame_name}")
        print(f"-" * 50)
        
        if not region16_cards:
            print(f"  无区域16卡牌")
            continue
        
        print(f"  区域16卡牌数: {len(region16_cards)}")
        
        # 按X坐标分列
        tolerance = 8.0
        columns = defaultdict(list)
        
        for card in region16_cards:
            x_center = card['x_center']
            
            assigned = False
            for x_key in columns.keys():
                if abs(x_center - x_key) <= tolerance:
                    columns[x_key].append(card)
                    assigned = True
                    break
            
            if not assigned:
                columns[x_center].append(card)
        
        real_errors = []
        
        for i, (x_key, column_cards) in enumerate(sorted(columns.items())):
            column_cards.sort(key=lambda c: -c['y_bottom'])
            
            labels_in_column = [card['label'] for card in column_cards]
            analysis = analyze_column_with_game_rules(labels_in_column)
            
            status_icon = "❌" if analysis['needs_fix'] else "✅"
            print(f"    列{i+1}: {labels_in_column}")
            print(f"      {status_icon} {analysis['description']}")
            
            if analysis['needs_fix']:
                real_errors.append({
                    'column': i+1,
                    'labels': labels_in_column,
                    'mixed_types': analysis.get('mixed_types', [])
                })
        
        if real_errors:
            print(f"  🎯 需要修复的真正错误: {len(real_errors)}个")
        else:
            print(f"  🎉 无需修复（所有列都是正常游戏状态）")

def propose_final_solution():
    """提出最终解决方案"""
    
    print(f"\n" + "="*80)
    print(f"最终解决方案")
    print(f"="*80)
    
    print(f"\n🎯 重新定义的'真正错误':")
    print(f"1. ✅ 暗牌转明牌（如'拾暗'+'拾'）- 正常游戏过程，不需修复")
    print(f"2. ✅ 合法吃碰组合（如'陆'+'六'）- 正常游戏规则，不需修复")
    print(f"3. ❌ 类别混淆（如'三'+'一'）- 真正错误，需要修复")
    
    print(f"\n🔧 实施策略:")
    print(f"1. 在basic_id_assigner.py中添加智能列分析函数")
    print(f"2. 只对真正的类别混淆进行修复")
    print(f"3. 保持所有正常游戏状态不变")
    
    print(f"\n📝 具体实施步骤:")
    print(f"步骤1: 添加游戏规则判断函数")
    print(f"  - is_dark_to_bright_transition()")
    print(f"  - is_legal_eating_combination()")
    print(f"  - analyze_column_with_game_rules()")
    
    print(f"\n步骤2: 在批量ID分配前进行智能检查")
    print(f"  - 分析每列的游戏状态")
    print(f"  - 只对真正错误触发修复")
    print(f"  - 保持正常状态不变")
    
    print(f"\n步骤3: 实现列重新分配逻辑")
    print(f"  - 检测到真正错误时，按类别重新分组")
    print(f"  - 确保同类卡牌分配到同一列")
    print(f"  - 保持空间排序规则不变")
    
    print(f"\n✅ 这种方案的优势:")
    print(f"1. 基于游戏规则，逻辑清晰")
    print(f"2. 不会误修复正常的游戏状态")
    print(f"3. 关键帧的正常状态得到保持")
    print(f"4. 只修复真正的错误")
    
    print(f"\n🎮 预期效果:")
    print(f"- frame_00018/00028/00034: 保持现状（暗牌转明牌是正常的）")
    print(f"- frame_00060: 保持现状（已经正常）")
    print(f"- frame_00230: 修复列3和列4的'三'+'一'混淆")

def generate_implementation_code():
    """生成实施代码示例"""
    
    print(f"\n" + "="*80)
    print(f"实施代码示例")
    print(f"="*80)
    
    print(f"\n```python")
    print(f"# 在basic_id_assigner.py中添加")
    print(f"")
    print(f"def _is_dark_to_bright_transition(self, base_labels):")
    print(f"    \"\"\"判断是否为暗牌转明牌的正常过渡\"\"\"")
    print(f"    normal_transitions = [")
    print(f"        {{'拾暗', '拾'}}, {{'肆暗', '肆'}}, {{'三暗', '三'}},")
    print(f"        # ... 其他暗牌转明牌组合")
    print(f"    ]")
    print(f"    return set(base_labels) in normal_transitions")
    print(f"")
    print(f"def _is_legal_eating_combination(self, base_labels):")
    print(f"    \"\"\"判断是否为合法的吃碰组合\"\"\"")
    print(f"    legal_combinations = [{{'陆', '六'}}]")
    print(f"    return set(base_labels) in legal_combinations")
    print(f"")
    print(f"def _analyze_column_consistency(self, cards, region_id):")
    print(f"    \"\"\"分析列一致性，只修复真正的错误\"\"\"")
    print(f"    # 按X坐标分列")
    print(f"    # 分析每列的游戏状态")
    print(f"    # 只对真正的类别混淆进行重新分配")
    print(f"    pass")
    print(f"")
    print(f"# 在_assign_batch_consecutive_ids中调用")
    print(f"if region_id == 16:")
    print(f"    cards = self._analyze_column_consistency(cards, region_id)")
    print(f"```")

if __name__ == "__main__":
    print("🎮 基于游戏规则的最终解决方案分析")
    
    # 重新分析所有帧
    reanalyze_all_frames()
    
    # 提出最终解决方案
    propose_final_solution()
    
    # 生成实施代码示例
    generate_implementation_code()
    
    print(f"\n🎉 分析完成！")
    print(f"基于游戏规则的智能修复方案可以完美解决问题")

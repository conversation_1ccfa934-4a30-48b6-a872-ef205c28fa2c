#!/usr/bin/env python3
"""
测试删除16→16内部继承后的效果

验证要求：
1. 确认16→16内部继承逻辑已被完全移除
2. 验证区域16的继承现在完全由SimpleInheritor处理
3. 检查frame_00061是否仍然存在问题
4. 分析删除16→16内部继承对系统的影响

作者：AI助手
日期：2025-07-26
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

def load_frame_data(frame_num: int) -> Optional[Dict[str, Any]]:
    """加载帧数据"""
    output_dir = Path("output")
    digital_twin_dir = output_dir / "calibration_gt_final_with_digital_twin" / "labels"
    frame_file = digital_twin_dir / f"frame_{frame_num:05d}.json"
    
    if not frame_file.exists():
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载frame_{frame_num:05d}.json失败: {e}")
        return None

def extract_region_cards(frame_data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not frame_data or 'shapes' not in frame_data:
        return []
    
    return [card for card in frame_data['shapes'] if card.get('group_id') == region_id]

def analyze_region_16_inheritance():
    """分析区域16的继承情况"""
    print("🔍 分析区域16继承情况...")
    
    # 测试关键帧
    test_frames = [60, 61, 35]
    results = {}
    
    for frame_num in test_frames:
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            results[frame_num] = {"error": "无法加载数据"}
            continue
        
        region_16_cards = extract_region_cards(frame_data, 16)
        
        # 分析ID分配
        ids = []
        for card in region_16_cards:
            twin_id = card.get('attributes', {}).get('digital_twin_id')
            if twin_id:
                ids.append(twin_id)
        
        # 检查继承标记
        inheritance_markers = []
        for card in region_16_cards:
            if card.get('inherited'):
                inheritance_markers.append({
                    'label': card.get('label'),
                    'twin_id': card.get('attributes', {}).get('digital_twin_id'),
                    'inherited_from_region_16': card.get('inherited_from_region_16', False),
                    'transition_source': card.get('transition_source'),
                    'from_region_7': card.get('from_region_7', False),
                    'from_region_3': card.get('from_region_3', False),
                    'from_region_4': card.get('from_region_4', False)
                })
        
        results[frame_num] = {
            "total_cards": len(region_16_cards),
            "ids": ids,
            "unique_ids": list(set(ids)),
            "id_distribution": {id_val: ids.count(id_val) for id_val in set(ids)},
            "inheritance_markers": inheritance_markers,
            "has_16_16_inheritance": any(card.get('inherited_from_region_16') for card in region_16_cards)
        }
    
    return results

def check_code_for_16_16_references():
    """检查代码中是否还有16→16内部继承的引用"""
    print("🔍 检查代码中的16→16引用...")
    
    # 检查RegionTransitioner
    transitioner_file = Path("src/modules/region_transitioner.py")
    if transitioner_file.exists():
        with open(transitioner_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有16→16相关的代码
        has_16_16_method = "_find_matching_card_in_region_16" in content
        has_16_16_inheritance = "inherited_from_region_16" in content
        has_16_16_comment = "16→16" in content or "区域16内部继承" in content
        
        print(f"RegionTransitioner检查结果:")
        print(f"  - 是否还有_find_matching_card_in_region_16方法: {has_16_16_method}")
        print(f"  - 是否还有inherited_from_region_16标记: {has_16_16_inheritance}")
        print(f"  - 是否还有16→16相关注释: {has_16_16_comment}")
        
        return {
            "has_16_16_method": has_16_16_method,
            "has_16_16_inheritance": has_16_16_inheritance,
            "has_16_16_comment": has_16_16_comment
        }
    
    return {"error": "无法找到RegionTransitioner文件"}

def analyze_frame_61_problem():
    """深度分析frame_00061的问题"""
    print("🔍 深度分析frame_00061问题...")
    
    frame_61_data = load_frame_data(61)
    frame_60_data = load_frame_data(60)
    
    if not frame_61_data or not frame_60_data:
        return {"error": "无法加载frame_60或frame_61数据"}
    
    # 分析frame_60区域16
    region_16_60 = extract_region_cards(frame_60_data, 16)
    ids_60 = [card.get('attributes', {}).get('digital_twin_id') for card in region_16_60]
    
    # 分析frame_61区域16
    region_16_61 = extract_region_cards(frame_61_data, 16)
    ids_61 = [card.get('attributes', {}).get('digital_twin_id') for card in region_16_61]
    
    return {
        "frame_60": {
            "total_cards": len(region_16_60),
            "ids": ids_60,
            "id_distribution": {id_val: ids_60.count(id_val) for id_val in set(ids_60) if id_val}
        },
        "frame_61": {
            "total_cards": len(region_16_61),
            "ids": ids_61,
            "id_distribution": {id_val: ids_61.count(id_val) for id_val in set(ids_61) if id_val}
        },
        "inheritance_analysis": {
            "should_inherit_from_60": ids_60,
            "actually_inherited_61": ids_61,
            "inheritance_success": ids_60 == ids_61 if ids_60 and ids_61 else False
        }
    }

def main():
    """主函数"""
    print("🔍 测试删除16→16内部继承后的效果")
    print("="*60)
    
    # 1. 检查代码清理情况
    print("\n1. 检查代码清理情况")
    code_check = check_code_for_16_16_references()
    print(f"代码清理结果: {code_check}")
    
    # 2. 分析区域16继承情况
    print("\n2. 分析区域16继承情况")
    inheritance_results = analyze_region_16_inheritance()
    
    for frame_num, result in inheritance_results.items():
        print(f"\nFrame {frame_num:05d}:")
        if "error" in result:
            print(f"  ❌ {result['error']}")
            continue
        
        print(f"  - 卡牌数量: {result['total_cards']}")
        print(f"  - ID分布: {result['id_distribution']}")
        print(f"  - 是否有16→16继承标记: {result['has_16_16_inheritance']}")
        
        if result['inheritance_markers']:
            print(f"  - 继承标记: {len(result['inheritance_markers'])}个")
            for marker in result['inheritance_markers']:
                print(f"    * {marker['label']}: {marker['twin_id']} (来源: {marker.get('transition_source', 'unknown')})")
    
    # 3. 深度分析frame_61问题
    print("\n3. 深度分析frame_61问题")
    frame_61_analysis = analyze_frame_61_problem()
    
    if "error" in frame_61_analysis:
        print(f"❌ {frame_61_analysis['error']}")
    else:
        print(f"Frame 60 → Frame 61 继承分析:")
        print(f"  - Frame 60 ID分布: {frame_61_analysis['frame_60']['id_distribution']}")
        print(f"  - Frame 61 ID分布: {frame_61_analysis['frame_61']['id_distribution']}")
        print(f"  - 继承是否成功: {frame_61_analysis['inheritance_analysis']['inheritance_success']}")
        
        if not frame_61_analysis['inheritance_analysis']['inheritance_success']:
            print("  ❌ 继承失败！frame_61仍然存在问题")
        else:
            print("  ✅ 继承成功")
    
    # 4. 总结
    print("\n4. 总结")
    print("删除16→16内部继承的影响:")
    
    # 检查是否还有16→16相关代码
    if not code_check.get("has_16_16_method", True) and not code_check.get("has_16_16_inheritance", True):
        print("  ✅ 16→16内部继承代码已成功删除")
    else:
        print("  ❌ 仍有16→16内部继承相关代码残留")
    
    # 检查frame_61问题是否解决
    if frame_61_analysis.get('inheritance_analysis', {}).get('inheritance_success'):
        print("  ✅ frame_61问题已解决")
    else:
        print("  ❌ frame_61问题仍然存在，需要进一步分析")
    
    print("\n建议:")
    print("1. 确认所有16→16内部继承相关代码已完全删除")
    print("2. 验证SimpleInheritor是否正确处理区域16的继承")
    print("3. 如果frame_61仍有问题，需要分析SimpleInheritor的继承逻辑")

if __name__ == "__main__":
    main()

# 🚀 Cursor Context 使用速查表

## 📋 常用Context命令模板

### 🎯 项目理解
```bash
# 快速了解项目
@Files docs/AI_PROJECT_GUIDE.md
@Files docs/CODE_ARCHITECTURE_MAP.md
@Files README.md

# 分析核心架构
@Folders src/core
@Files src/main.py
@Folders src/utils
```

### 🔧 开发调试
```bash
# 查看测试状态
@Folders tests
@Files *_verification.py
@Files *_test.py

# 性能分析
@Files tools/performance_*.py
@Folders output/analysis
@Files analysis/*.json
```

### 🐛 问题诊断
```bash
# 查看日志和错误
@Files *.log
@Folders output
@Files tools/debug_*.py

# 验证和修复
@Files *_validation.py
@Files tools/fixes/*.py
@Folders tests/integration
```

### 📊 数据分析
```bash
# 数据处理
@Folders data
@Files tools/data_*.py
@Files *_analyzer.py

# 结果分析
@Folders output/analysis
@Files analysis/*.md
@Files *_report.json
```

## 🎨 高级Context技巧

### 1. 组合使用
```bash
@Files src/main.py @Files src/core/detect.py 
分析主程序和检测模块的关系
```

### 2. 特定功能分析
```bash
@Files src/core/digital_twin_v2.py @Files tests/test_digital_twin_v2.py
理解数字孪生系统的实现和测试
```

### 3. 配置和文档
```bash
@Files .cursorrules @Files docs/AI_PROJECT_GUIDE.md
了解AI助手配置和项目指南
```

## 📚 任务特定模板

### 添加新功能
1. `@Files docs/CODE_ARCHITECTURE_MAP.md` - 理解架构
2. `@Folders src/core` - 查看核心模块
3. `@Files src/core/interfaces.py` - 了解接口规范
4. `@Folders tests` - 参考测试模式

### 性能优化
1. `@Files tools/performance_*.py` - 性能分析工具
2. `@Files analysis/performance_*.json` - 基准数据
3. `@Folders models` - 模型文件
4. `@Files tools/enhanced_model_validator.py` - 模型验证

### 修复Bug
1. `@Files *_verification.py` - 验证脚本
2. `@Folders tests` - 测试套件
3. `@Files tools/fixes/*.py` - 修复工具
4. `@Files *.log` - 日志文件

## 💡 最佳实践

### DO ✅
- 先查看项目指南和架构文档
- 使用组合Context获取完整信息
- 关注核心模块和接口定义
- 参考测试文件理解预期行为

### DON'T ❌
- 不要一次加载过多文件
- 不要忽略配置和文档文件
- 不要跳过测试和验证步骤
- 不要忘记查看最新的分析结果

## 🔍 快速诊断命令

```bash
# 项目健康检查
@Files docs/AI_PROJECT_GUIDE.md 项目概览
@Folders tests 测试状态
@Folders output 最新结果

# 代码质量检查
@Files docs/CODE_REFACTORING_PLAN.md 重构建议
@Files tools/analysis/*.py 分析工具
@Files analysis/*.md 分析报告

# 性能状态检查
@Files tools/test_yolov8l_performance.py 性能测试
@Files analysis/performance_*.json 性能数据
@Files models/*.pt 模型文件
```

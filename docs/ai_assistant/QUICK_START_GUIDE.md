# 🚀 AI助手快速开始指南

## 🎯 5分钟快速上手

### 第1分钟：了解项目
```bash
@Files docs/AI_PROJECT_GUIDE.md
```
**目标**: 快速理解项目是什么，做什么用的

### 第2分钟：理解架构
```bash
@Files docs/CODE_ARCHITECTURE_MAP.md
@Files ARCHITECTURE.md
```
**目标**: 了解系统设计和模块关系

### 第3分钟：查看核心代码
```bash
@Files src/main.py
@Folders src/core
```
**目标**: 理解主要程序流程和核心功能

### 第4分钟：了解配置和规则
```bash
@Files .cursorrules
@Files src/config/config.json
```
**目标**: 理解AI助手行为规则和系统配置

### 第5分钟：查看最新状态
```bash
@Folders tests
@Folders output
```
**目标**: 了解测试状态和最新结果

## 📋 常见任务快速模板

### 🔍 理解某个功能
```bash
# 例：理解检测功能
@Files src/core/detect.py
@Files src/core/enhanced_detector.py
@Files tests/test_*detect*.py
```

### 🐛 调试问题
```bash
# 查看相关日志和测试
@Files *.log
@Files *_verification.py
@Folders output
```

### ⚡ 性能分析
```bash
@Files tools/test_yolov8l_performance.py
@Files analysis/performance_*.json
@Folders models
```

### 📊 数据分析
```bash
@Folders data
@Files tools/data_*.py
@Files *_analyzer.py
```

### 🧪 测试相关
```bash
@Folders tests
@Files comprehensive_validation.py
@Files final_verification.py
```

## 🎨 高级使用技巧

### 1. 组合Context
```bash
@Files src/main.py @Files src/core/detect.py 
分析主程序如何调用检测器
```

### 2. 特定模块深入
```bash
@Files src/core/digital_twin_v2.py @Files tests/test_digital_twin_v2.py
理解数字孪生系统的实现和测试
```

### 3. 配置和文档结合
```bash
@Files .cursorrules @Files docs/AI_PROJECT_GUIDE.md
了解AI助手配置和项目指南
```

## 🔧 问题解决流程

### 步骤1：理解问题
```bash
@Files docs/AI_PROJECT_GUIDE.md  # 了解项目背景
@Files 相关的错误日志或文件      # 查看具体问题
```

### 步骤2：定位代码
```bash
@Folders src/core              # 查看核心代码
@Files 相关的模块文件           # 深入具体实现
```

### 步骤3：查看测试
```bash
@Folders tests                 # 查看测试用例
@Files 相关的验证脚本           # 了解预期行为
```

### 步骤4：分析结果
```bash
@Folders output                # 查看输出结果
@Files analysis/*.json         # 分析数据
```

## 💡 最佳实践

### ✅ 推荐做法
1. **先读指南**: 总是从AI_PROJECT_GUIDE.md开始
2. **理解架构**: 查看架构文档了解整体设计
3. **关注核心**: 重点分析核心模块
4. **结合测试**: 通过测试理解预期行为
5. **查看最新**: 关注最新的输出和分析结果

### ❌ 避免做法
1. 不要一次加载过多文件
2. 不要忽略配置和文档
3. 不要跳过测试验证
4. 不要忘记查看日志

## 🎯 任务特定指南

### 新功能开发
1. `@Files docs/CODE_ARCHITECTURE_MAP.md` - 理解架构
2. `@Folders src/core` - 查看核心模块
3. `@Files 相关接口文件` - 了解接口规范
4. `@Folders tests` - 参考测试模式

### Bug修复
1. `@Files 错误日志` - 了解问题
2. `@Files 相关代码文件` - 定位问题代码
3. `@Files 相关测试文件` - 理解预期行为
4. `@Files tools/fixes/*.py` - 查看修复工具

### 性能优化
1. `@Files tools/performance_*.py` - 性能分析工具
2. `@Files analysis/performance_*.json` - 基准数据
3. `@Folders models` - 模型文件
4. `@Files tools/enhanced_model_validator.py` - 模型验证

### 代码重构
1. `@Files docs/CODE_REFACTORING_PLAN.md` - 重构计划
2. `@Folders src` - 源代码结构
3. `@Files 目标模块` - 重构目标
4. `@Folders tests` - 确保测试覆盖

## 🔍 快速诊断命令

### 项目健康检查
```bash
@Files docs/AI_PROJECT_GUIDE.md    # 项目概览
@Folders tests                     # 测试状态  
@Folders output                    # 最新结果
@Files comprehensive_validation.py # 综合验证
```

### 代码质量检查
```bash
@Files docs/CODE_REFACTORING_PLAN.md  # 重构建议
@Files tools/analysis/*.py            # 分析工具
@Files analysis/*.md                  # 分析报告
```

### 性能状态检查
```bash
@Files tools/test_yolov8l_performance.py  # 性能测试
@Files analysis/performance_*.json        # 性能数据
@Files models/*.pt                        # 模型文件
```

## 📚 学习路径

### 初学者 (第1天)
1. 阅读 `@Files docs/AI_PROJECT_GUIDE.md`
2. 运行 `@Files simple_test.py`
3. 查看 `@Files examples/` 目录

### 进阶者 (第2-3天)
1. 分析 `@Folders src/core`
2. 运行完整测试套件
3. 理解双轨输出机制

### 专家级 (第4-7天)
1. 性能优化和调试
2. 新功能开发
3. 系统架构改进

## 🆘 遇到问题时

### 常见问题解决
1. **找不到文件**: 检查路径是否正确，使用相对路径
2. **理解困难**: 先读项目指南，再看具体代码
3. **测试失败**: 查看验证脚本和输出结果
4. **性能问题**: 使用性能分析工具

### 获取帮助
1. 查看 `@Files docs/` 目录下的文档
2. 运行 `@Files tools/` 目录下的分析工具
3. 查看 `@Files tests/` 目录下的测试用例
4. 参考 `@Files output/` 目录下的最新结果

## 🎉 成功标志

当您能够：
1. 快速理解项目的核心功能
2. 准确定位相关代码文件
3. 有效使用Context命令
4. 独立解决常见问题

恭喜您已经掌握了AI助手的使用技巧！

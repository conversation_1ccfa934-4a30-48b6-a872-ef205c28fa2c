# 数字孪生模块化设计结构目录

## 📋 文档信息
- **版本**：v2.0
- **创建日期**：2025-07-20
- **适用范围**：数字孪生统一主控器系统
- **状态**：正式发布

## 🎯 概述

本文档详细说明数字孪生系统的模块化结构，包括统一主控器、集成器、功能模块等各个组件的职责与主要功能，为开发人员提供清晰的架构指导。

## 🏗️ 系统架构层次

```
数字孪生统一主控器系统
├── 1. 统一主控层 (Controller Layer)
├── 2. 策略选择层 (Strategy Layer)  
├── 3. 集成器层 (Integrator Layer)
└── 4. 功能模块层 (Module Layer)
```

## 📁 目录结构与模块职责

### 1. 统一主控层 (Controller Layer)

#### 📂 `src/core/digital_twin_controller.py`
**主要职责：**
- 提供数字孪生功能的统一入口接口
- 管理不同处理策略的切换
- 统一配置管理和性能监控
- 错误处理和日志管理

**核心功能：**
- `process_frame()`: 统一的帧处理入口
- `switch_strategy()`: 动态策略切换
- `get_system_status()`: 系统状态查询
- `get_performance_stats()`: 性能统计获取

**使用场景：**
- calibration_gt_final_processor.py 调用
- 未来主流程调用
- 测试和验证脚本调用

---

### 2. 策略选择层 (Strategy Layer)

#### 📂 `src/core/digital_twin_controller.py` (ProcessingStrategy)
**主要职责：**
- 定义不同的处理策略
- 支持策略间的动态切换
- 适应不同场景的需求

**策略类型：**
- `PHASE1_BASIC`: 基础功能策略（3个模块）
- `PHASE2_COMPLETE`: 完整功能策略（6个模块）
- `CUSTOM`: 自定义策略（预留扩展）

**配置管理：**
- `DigitalTwinConfig`: 统一配置类
- 模块开关控制
- 性能参数设置

---

### 3. 集成器层 (Integrator Layer)

#### 📂 `src/modules/phase1_integrator.py`
**主要职责：**
- 集成第一阶段的3个基础模块
- 提供基础的数字孪生功能
- 适用于简单场景和性能要求高的场景

**集成模块：**
1. DataValidator (数据验证器)
2. SimpleInheritor (简单继承器)  
3. BasicIDAssigner (基础ID分配器)

**处理流程：**
```
输入检测数据 → 数据验证 → 简单继承 → ID分配 → 输出结果
```

#### 📂 `src/modules/phase2_integrator.py`
**主要职责：**
- 集成第二阶段的6个完整模块
- 提供完整的数字孪生功能
- 适用于生产环境和复杂场景

**集成模块：**
1. DataValidator (数据验证器)
2. SimpleInheritor (简单继承器)
3. VirtualRegionProcessor (虚拟区域处理器) 🆕
4. Region2Processor (区域2处理器)
5. RegionTransitioner (区域流转器)
6. DarkCardProcessor (暗牌处理器)
7. BasicIDAssigner (基础ID分配器)
8. OcclusionCompensator (遮挡补偿器)
9. Card21Tracker (第21张牌跟踪器) 🆕

**处理流程：**
```
输入检测数据 → 数据验证 → 虚拟区域处理 → 简单继承 → 区域2互斥处理 → 区域流转 → 暗牌处理 → ID分配 → 遮挡补偿 → 第21张牌跟踪 → 输出结果
```

---

### 4. 功能模块层 (Module Layer)

#### 📂 `src/modules/data_validator.py`
**主要职责：**
- 验证输入数据的完整性和格式正确性
- 清理无效或异常的检测数据
- 确保后续模块接收到标准化的数据

**核心功能：**
- 边界框格式验证
- 置信度范围检查
- 标签有效性验证
- 区域ID合法性检查

**输入/输出：**
- 输入：原始检测数据列表
- 输出：验证清理后的数据列表

#### 📂 `src/modules/simple_inheritor.py`
**主要职责：**
- 实现帧间ID继承机制
- 基于区域+标签的智能匹配
- 维护ID的连续性和稳定性

**核心功能：**
- 区域+标签匹配算法
- 继承历史记录维护
- 新卡牌识别和标记

**输入/输出：**
- 输入：验证后的检测数据
- 输出：标记继承状态的卡牌数据

#### 📂 `src/modules/region_transitioner.py`
**主要职责：**
- 处理卡牌在不同区域间的流转
- 维护流转历史和路径记录
- 确保ID在流转过程中的稳定性

**核心功能：**
- 流转路径定义和验证
- 跨区域ID追踪
- 流转历史记录

**流转路径：**
- 观战方：手牌(1) ↔ 调整(2) → 打出(4) → 弃牌(5)
- 对战方：手牌(7) ↔ 调整(8) → 打出(10) → 弃牌(11)
- 吃碰区：观战方(6) ↔ 对战方(16)

#### 📂 `src/modules/dark_card_processor.py`
**主要职责：**
- 处理暗牌的身份推断和关联
- 基于明牌推断暗牌身份
- 生成关联后的暗牌ID

**核心功能：**
- 同区域明牌推断
- 跨区域推断备用策略
- 偎牌(1明2暗)和提牌(1明3暗)模式识别

**ID格式：**
- 关联暗牌：1二暗、2三暗
- 未关联暗牌：1暗、2暗

#### 📂 `src/modules/basic_id_assigner.py`
**主要职责：**
- 为新检测到的卡牌分配数字孪生ID
- 严格遵循游戏规则的物理约束
- 管理全局ID的唯一性

**核心功能：**
- GlobalIDManager：全局ID管理
- 物理ID优先分配（1二、2二、3二、4二）
- 虚拟ID备用机制（虚拟二）
- 80张牌总量控制

**ID分配策略：**
1. 优先分配物理ID
2. 物理ID耗尽时分配虚拟ID
3. 严格遵循每种牌最多4个物理ID的限制

#### 📂 `src/modules/occlusion_compensator.py`
**主要职责：**
- 检测和补偿被遮挡或暂时消失的卡牌
- 创建虚拟卡牌维持ID连续性
- 控制补偿策略避免过度补偿

**核心功能：**
- 消失卡牌检测
- 智能补偿策略
- 补偿次数和总量控制

**补偿配置：**
- 每张牌最多补偿3次

#### 📂 `src/modules/virtual_region_processor.py` 🆕
**主要职责：**
- 处理虚拟区域（区域10、11、12）的完全虚拟化
- 确保虚拟区域不参与物理ID分配和RLCard状态转换
- 提供统一的虚拟化标准和监控

**核心功能：**
- 识别虚拟区域的卡牌（区域10、11、12）
- 分配虚拟ID格式：`虚拟_{标签}_{区域ID}`
- 标记虚拟状态，不参与物理ID分配
- 虚拟化统计和监控

**虚拟化规则：**
- 区域10、11、12：完全虚拟化，UI提示用途
- 虚拟ID格式：虚拟_二_10、虚拟_三_11、虚拟_四_12
- 不参与RLCard状态转换
- 不占用80张物理牌限额

**输入/输出：**
- 输入：包含虚拟区域的卡牌列表
- 输出：虚拟化处理后的卡牌列表和统计信息

#### 📂 `src/modules/region2_processor.py`
**主要职责：**
- 处理区域2与区域1的互斥逻辑
- 实现区域2继承区域1最大ID的机制
- 确保物理卡牌的唯一性

**核心功能：**
- 区域2卡牌与区域1相同标签卡牌的匹配
- 选择最大ID数值进行继承（如1二、2二、3二选3二）
- 删除区域1中对应的卡牌，确保互斥
- 记录继承关系和状态转换

**互斥规则：**
- 区域2出现时，继承区域1中相同标签的最大ID
- 区域1中被继承的卡牌必须删除
- 确保同一物理卡牌不会同时存在于两个区域
- 支持状态回转（区域2消失时区域1恢复）

#### 📂 `src/modules/card_21_tracker.py` 🆕
**主要职责：**
- 跟踪对战方庄家的第21张牌
- 为AI推理提供已知信息，减少不确定性
- 实现消失-重现的完整跟踪机制

**核心功能：**
- 检测中央区域（对战方抓牌区）卡牌消失事件
- 跟踪消失卡牌的标签和信息
- 检测打牌区域（对战方打牌区）的重现事件
- 为AI推理提供已知卡牌信息

**跟踪机制：**
- 消失检测：监控区域7（对战方抓牌区）的卡牌变化
- 重现检测：监控区域8（对战方打牌区）的卡牌出现
- AI支持：提供最多20%的推理置信度提升
- 不确定性降低：从80张未知减少到已知数量

**业务价值：**
- 直接支持AI决策优化
- 减少游戏状态的不确定性
- 提供对战方已知信息
- 增强AI推理能力

---

## 🔧 工厂函数与便捷接口

#### 📂 `src/modules/__init__.py`
**主要职责：**
- 提供模块的统一导入接口
- 定义工厂函数创建集成器实例

**工厂函数：**
```python
def create_phase1_integrator() -> Phase1Integrator
def create_phase2_integrator() -> Phase2Integrator
```

#### 📂 `src/core/digital_twin_controller.py` (工厂函数)
**便捷创建函数：**
```python
def create_digital_twin_controller(config=None) -> DigitalTwinController
def create_default_controller() -> DigitalTwinController
def create_basic_controller() -> DigitalTwinController  
def create_complete_controller() -> DigitalTwinController
```

---

## 📊 模块依赖关系

### 依赖图
```
DigitalTwinController
├── Phase1Integrator
│   ├── DataValidator
│   ├── SimpleInheritor
│   └── BasicIDAssigner
└── Phase2Integrator
    ├── DataValidator
    ├── SimpleInheritor
    ├── VirtualRegionProcessor 🆕
    ├── Region2Processor
    ├── RegionTransitioner
    ├── DarkCardProcessor
    ├── BasicIDAssigner
    ├── OcclusionCompensator
    └── Card21Tracker 🆕
```

### 数据流向
```
检测数据 → 主控器 → 策略选择 → 集成器 → 功能模块链 → 处理结果

Phase2完整流程：
检测数据 → 数据验证 → 虚拟区域处理 → 帧间继承 → 区域2互斥 → 区域流转 → 暗牌处理 → ID分配 → 遮挡补偿 → 第21张牌跟踪 → 最终结果
```

---

## 🎯 使用指南

### 开发人员快速上手

1. **理解架构层次**：从主控器到功能模块的4层架构
2. **选择合适策略**：根据场景选择基础或完整功能
3. **模块化开发**：每个模块独立开发和测试
4. **统一接口**：通过主控器统一访问所有功能

### 扩展开发指南

1. **新增功能模块**：实现标准接口，在集成器中注册
2. **新增处理策略**：在策略枚举中添加，在主控器中实现
3. **配置扩展**：在DigitalTwinConfig中添加新配置项
4. **性能优化**：通过性能监控识别瓶颈，针对性优化

---

## 📚 相关文档索引

### 核心设计文档
- [数字孪生主控器设计方案](./数字孪生主控器设计方案.md)
- [模块化数字孪生系统架构设计](./模块化数字孪生系统架构设计.md)
- [数字孪生ID系统技术规范](../technical/数字孪生ID系统技术规范.md)

### 实现文档
- [数字孪生ID模块化系统完整设计文档](../development/数字孪生ID模块化系统完整设计文档.md)
- [数字孪生V2完成总结](../development/数字孪生V2完成总结.md)
- [模块化重构技术实施指南](../technical/模块化重构技术实施指南.md)

### 使用指南
- [模块化系统快速开始指南](../user_guide/模块化系统快速开始指南.md)
- [主流程演示](../../examples/main_pipeline_demo.py)

### 测试文档
- [数字孪生系统V2.0性能突破报告](../testing/数字孪生系统V2.0性能突破报告.md)
- [测试策略](../testing/test_strategy.md)

---

## 🔄 版本历史

### v2.0 (2025-07-20)
- ✅ 添加统一主控器架构
- ✅ 完善策略选择机制
- ✅ 更新模块职责说明
- ✅ 添加使用指南和扩展指南

### v1.0 (2025-07-19)
- ✅ 初始版本
- ✅ 基础模块化架构设计
- ✅ 6个功能模块定义

---

**文档维护**：请在修改模块功能时同步更新此文档
**版本控制**：重大架构变更时更新版本号
**联系方式**：如有疑问请参考相关技术文档或联系开发团队

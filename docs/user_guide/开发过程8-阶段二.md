关键发现：SimpleInheritor成功完成3→6继承后，RegionTransitioner覆盖了结果
核心问题：frame_00360→frame_00361中，区域6的柒应该从区域3继承2柒的ID，但实际继承了1柒。
def _handle_special_transitions_to_6(self, target_cards, all_source_cards):
    for target_card in target_cards:
        # 🔧 新增：检查卡牌是否已经被SimpleInheritor处理过
        already_inherited = target_card.get('twin_id') is not None and target_card.get('inherited', False)
        
        if already_inherited:
            # 保持SimpleInheritor的继承结果
            continue

系统性思考：不仅要修复问题，还要考虑模块间的协调
分层调试：从简单到复杂，逐步定位问题
完整验证：既要验证修复效果，也要确保不破坏其他功能
详细记录：完整的调试日志对问题定位至关重要
# 项目配置指南

**更新日期**: 2025-07-17  
**版本**: v2.0（YOLOv8l升级版）

## 📋 核心配置

### 1. 模型配置

#### 当前推荐配置（2025-07-17更新）
```json
{
  "model_path": "data/processed/train/weights/best.onnx",
  "confidence_threshold": 0.25,
  "iou_threshold": 0.45,
  "device": "cuda"
}
```

#### 配置说明
- **model_path**: YOLOv8l ONNX模型路径
- **confidence_threshold**: 0.25（与AnyLabeling一致）
- **iou_threshold**: 0.45（与AnyLabeling一致）
- **device**: 推荐使用CUDA加速

### 2. 模型性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| **精确率** | 98.1% | 检测准确性 |
| **召回率** | 97.2% | 检测完整性 |
| **F1分数** | 97.7% | 综合性能 |
| **推理速度** | 3.6 FPS | 单张图像处理速度 |
| **模型大小** | 166.7 MB | ONNX格式 |

## 🔧 环境配置

### 1. 硬件要求
- **GPU**: RTX 5060或更高（推荐）
- **内存**: 8GB以上
- **存储**: 2GB可用空间

### 2. 软件依赖
```bash
# 核心依赖
pip install ultralytics>=8.0.0
pip install onnxruntime-gpu  # GPU版本
# 或
pip install onnxruntime      # CPU版本

# 其他依赖
pip install opencv-python
pip install numpy
pip install torch torchvision
```

## 📊 性能调优

### 1. 推理参数调优

#### 高精度模式（推荐）
```python
CONFIDENCE_THRESHOLD = 0.25
IOU_THRESHOLD = 0.45
```
- **适用场景**: 标注、验证、高质量检测
- **特点**: 高精度、低漏检率

#### 高速模式
```python
CONFIDENCE_THRESHOLD = 0.4
IOU_THRESHOLD = 0.5
```
- **适用场景**: 实时应用、快速筛选
- **特点**: 更快速度、略低精度

#### 高召回模式
```python
CONFIDENCE_THRESHOLD = 0.1
IOU_THRESHOLD = 0.3
```
- **适用场景**: 不能漏检的关键应用
- **特点**: 最低漏检率、可能有误检

### 2. 硬件优化

#### GPU优化
```python
# 使用GPU加速
device = "cuda" if torch.cuda.is_available() else "cpu"

# 批处理优化（如果处理多张图像）
batch_size = 4  # 根据GPU内存调整
```

#### CPU优化
```python
# 多线程处理
import torch
torch.set_num_threads(4)  # 根据CPU核心数调整
```

## 🎯 AnyLabeling集成

### 1. 模型导入
1. 将`data/processed/train/weights/best.onnx`复制到AnyLabeling模型目录
2. 在AnyLabeling中选择ONNX模型
3. 设置推理参数：conf=0.25, iou=0.45

### 2. 验证步骤
1. 加载测试图像
2. 运行推理
3. 检查检测结果
4. 验证性能指标

## 🔍 故障排除

### 1. 常见问题

#### 模型加载失败
```bash
# 检查模型文件
ls -la data/processed/train/weights/best.onnx

# 验证模型完整性
python -c "import onnxruntime; ort.InferenceSession('data/processed/train/weights/best.onnx')"
```

#### 推理速度慢
1. 检查GPU是否可用
2. 调整批处理大小
3. 考虑模型量化

#### 检测效果差
1. 检查输入图像质量
2. 调整置信度阈值
3. 验证模型版本

### 2. 性能监控

#### 关键指标监控
```python
# 推理时间监控
import time
start_time = time.time()
results = model(image)
inference_time = time.time() - start_time

# 内存使用监控
import psutil
memory_usage = psutil.virtual_memory().percent
```

#### 质量指标监控
- 检测数量异常（过多或过少）
- 置信度分布异常
- 边界框质量异常

## 📈 版本升级指南

### 从旧版本升级到YOLOv8l

#### 1. 备份当前配置
```bash
cp src/config/config.json src/config/config.json.backup
```

#### 2. 更新模型文件
```bash
# 确保新模型文件存在
ls data/processed/train/weights/best.onnx
```

#### 3. 更新配置文件
```json
{
  "model_path": "data/processed/train/weights/best.onnx",
  "confidence_threshold": 0.25,
  "iou_threshold": 0.45
}
```

#### 4. 验证升级
```bash
# 运行性能测试
python tools/test_yolov8l_performance.py
```

## 🚀 最佳实践

### 1. 生产部署
- 使用ONNX模型确保兼容性
- 设置合适的置信度阈值
- 实施性能监控
- 定期验证模型效果

### 2. 开发测试
- 使用大规模测试数据验证
- 对比不同参数配置效果
- 记录性能基准
- 建立回归测试

### 3. 维护更新
- 定期检查模型性能
- 收集边缘案例数据
- 考虑模型重训练
- 保持文档更新

---

**配置负责人**: AI Assistant  
**最后更新**: 2025-07-17  
**状态**: ✅ 当前版本

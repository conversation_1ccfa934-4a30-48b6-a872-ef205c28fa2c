1校准小结算画面，是否有卡牌识别错误，如有错误，只生成提示日志，记录对应的文件夹，图片与json文件，不修改标注内容，需要双模型，5.0识别小结算只识别标签（不识别卡牌，因为识别的卡牌不准），3.0识别卡牌（因为版本低识别不到小结算的相关类别，但是识别卡牌准确）
2校准指定文件夹内的检测框位置是否标准，如人工画的框位置有稍许偏差，偏差小的进行修改json文件进行纠正，偏差大的只生成提示日志，记录对应的文件夹，图片与json文件，不修改标注内容。不添加未识别的卡牌（现有yolo可能有过拟合会识别出多出的卡牌，其实并没有该卡牌）目的是修正人工标注画的框不精准（大小不精准，位置不精准），不增减人工标注的卡牌，用3.0版模型
4明显漏标的，很明显的，不包含遮挡的，与其它卡牌有共同区域的，生成日志
5可能需要全面打散训练集，将训练集进行类别均衡，将所有文件夹内的文件集中到1个文件夹（有同名需要处理），要保障图片与标注的json文件关联性
6找出不均衡的类别，需要脚本对特别少的类别进行投射伪标签类别，并修改json文件，进行同步，实现即投射同时进行添加投射标注。我现在又觉得这种方式可能会污染原有训练集，改其它方案，脚本自动生成新图片640宽 320高与原图大小一致。将这10个类别按上述方法投射到脚本生成的图片中。这些训练集单独建1个文件夹，你觉得这个方案是否可行？

以上内容全部完成。

训练集情况介绍：
D:\phz-ai-simple\data\xunlianjiyolo\images\train 这个路径下1-61 共计49个文件夹内，共计约1800张图片
D:\phz-ai-simple\data\xunlianjiyolo\labels\train 49个文件夹内与图片配套的json文件
打散后的训练集路径D:\phz-ai-simple\data\xunlianjiyolo\consolidated_shuffled

其中1-47 按顺序 48为脚本生成稀有类别数据集文 61文件夹单独
1-14 文件夹内为纯人工画框进行的标注，之后的是通过模型推理画框标注并人工多次审核。
1-14 包含"group_id"包含唯一ID的数字孪生系统 1二 2二 3二 4二 但是人工计算的可能有误差，准确度约80%

3.0模型路径D:\phz-ai-simple\models\train3.0\weights
5.0模型路径D:\phz-ai-simple\models\train5.0\weights

10个模板路径D:\phz-ai-simple\data\muban  包含报告中较少的的10个类别


官方下载的yolo模型路径 D:\phz-ai-simple\models\yolov8l.pt

我发现train_yolo.py中还有几个错误需要修复：
logger.success方法不存在，需要改为logger.info
sklearn.metrics导入错误，需要安装scikit-learn
在evaluate方法中，返回值可能为None导致的迭代错误
让我修复这些问题：











# 数字孪生ID模块化系统快速开始指南

## 📋 概述

本指南将帮助您快速上手新的模块化数字孪生ID系统。该系统采用分阶段设计，目前第二阶段已完成，系统功能完整且生产就绪。

## 🎯 系统功能概览

### 第一阶段功能（基础功能）
- ✅ **数据验证**：验证输入数据的完整性和格式
- ✅ **ID分配**：为新卡牌分配基础ID（支持物理ID和虚拟ID）
- ✅ **继承机制**：基于区域+标签的简单继承
- ✅ **模块集成**：将三个模块组合成可工作的系统

### 第二阶段功能（扩展功能，推荐使用）
- ✅ **区域流转**：处理跨区域的ID流转，保持ID稳定性
- ✅ **暗牌处理**：智能关联暗牌到明牌（1暗 → 1二暗）
- ✅ **遮挡补偿**：补偿被遮挡的卡牌，维持ID连续性
- ✅ **完整集成**：六个模块协同工作的完整系统

## 🚀 快速开始

### 1. 推荐使用方式（第二阶段系统）

```python
from src.modules import create_phase2_integrator

# 创建第二阶段系统（推荐使用，功能完整）
system = create_phase2_integrator()

# 准备测试数据
detections = [
    {
        'label': '二',
        'bbox': [100, 100, 150, 150],
        'confidence': 0.9,
        'group_id': 1
    },
    {
        'label': '三',
        'bbox': [200, 100, 250, 150],
        'confidence': 0.8,
        'group_id': 1
    },
    {
        'label': '暗',
        'bbox': [300, 100, 350, 150],
        'confidence': 0.85,
        'group_id': 1
    }
]

# 处理第一帧
result = system.process_frame(detections)

print(f"处理成功: {result.success}")
print(f"处理卡牌: {len(result.processed_cards)}张")

# 查看分配的ID
for card in result.processed_cards:
    print(f"  {card['twin_id']} (区域{card['group_id']}, 标签{card['label']})")
```

### 2. 继承功能测试

```python
# 第二帧：测试继承功能
detections_2 = [
    {
        'label': '二',
        'bbox': [105, 105, 155, 155],  # 位置稍有变化
        'confidence': 0.9,
        'group_id': 1  # 相同区域+标签，应该继承
    },
    {
        'label': '四',
        'bbox': [300, 100, 350, 150],
        'confidence': 0.85,
        'group_id': 1  # 新标签，应该分配新ID
    }
]

result_2 = system.process_frame(detections_2)

print("\n第二帧处理结果:")
for card in result_2.processed_cards:
    inherited = "继承" if card.get('inherited', False) else "新增"
    print(f"  {card['twin_id']} (区域{card['group_id']}, 标签{card['label']}, {inherited})")

# 显示继承率
if 'inheritance' in result_2.statistics:
    inheritance_rate = result_2.statistics['inheritance'].get('current_frame', {}).get('inheritance_rate', 0)
    print(f"\n继承率: {inheritance_rate:.1%}")
```

### 3. 系统状态监控

```python
# 获取系统状态
status = system.get_system_status()

print(f"\n系统状态:")
print(f"  处理帧数: {status['frame_count']}")
print(f"  继承率: {status['inheritance_rate']:.2%}")
print(f"  ID计数器: {status['id_counters']}")
```

## 🧪 完整示例

### 多帧处理示例

```python
from src.modules import create_phase1_integrator

def test_multi_frame_processing():
    """测试多帧处理"""
    system = create_phase1_integrator()
    
    # 模拟多帧游戏场景
    scenarios = [
        {
            "name": "第1帧: 初始手牌",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},
                {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.85, 'group_id': 1},
            ]
        },
        {
            "name": "第2帧: 部分卡牌移动到调整区",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 继承
                {'label': '三', 'bbox': [200, 200, 250, 250], 'confidence': 0.8, 'group_id': 2},  # 移动到调整区
                {'label': '四', 'bbox': [300, 100, 350, 150], 'confidence': 0.85, 'group_id': 1}, # 继承
                {'label': '五', 'bbox': [400, 100, 450, 150], 'confidence': 0.9, 'group_id': 1},  # 新摸的牌
            ]
        },
        {
            "name": "第3帧: 出现暗牌",
            "detections": [
                {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 继承
                {'label': '暗', 'bbox': [500, 100, 550, 150], 'confidence': 0.7, 'group_id': 6},  # 吃碰区暗牌
                {'label': '暗', 'bbox': [500, 200, 550, 250], 'confidence': 0.7, 'group_id': 6},  # 吃碰区暗牌
                {'label': '三', 'bbox': [500, 300, 550, 350], 'confidence': 0.8, 'group_id': 6},  # 吃碰区明牌
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        result = system.process_frame(scenario['detections'])
        
        print(f"  处理结果: {'✅ 成功' if result.success else '❌ 失败'}")
        print(f"  卡牌数量: {len(result.processed_cards)}")
        
        for card in result.processed_cards:
            inherited = "继承" if card.get('inherited', False) else "新增"
            is_virtual = "虚拟" if card.get('is_virtual', False) else "物理"
            print(f"    {card['twin_id']} (区域{card['group_id']}, {card['label']}, {inherited}, {is_virtual})")
        
        # 显示继承率
        if 'inheritance' in result.statistics:
            inheritance_rate = result.statistics['inheritance'].get('current_frame', {}).get('inheritance_rate', 0)
            print(f"  继承率: {inheritance_rate:.2%}")

if __name__ == "__main__":
    test_multi_frame_processing()
```

## 🔧 高级功能

### 1. 数据验证

```python
from src.modules.data_validator import create_data_validator

# 单独使用数据验证器
validator = create_data_validator()

# 包含错误数据的测试
invalid_detections = [
    {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 正常
    {'label': '未知牌', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},  # 未知标签
    {'bbox': [300, 100, 350, 150], 'confidence': 0.7, 'group_id': 1},  # 缺少label
]

result = validator.validate(invalid_detections)
print(f"验证结果: {'通过' if result.is_valid else '失败'}")
print(f"错误数量: {len(result.errors)}")
print(f"警告数量: {len(result.warnings)}")
print(f"有效数据: {len(result.cleaned_data)}")
```

### 2. ID分配限制测试

```python
from src.modules.basic_id_assigner import create_basic_id_assigner

# 单独使用ID分配器
assigner = create_basic_id_assigner()

# 创建超过4张的相同牌
many_cards = []
for i in range(6):  # 创建6张"二"
    many_cards.append({
        'label': '二',
        'bbox': [100 + i*50, 100, 150 + i*50, 150],
        'confidence': 0.9,
        'group_id': 1
    })

result = assigner.assign_ids(many_cards)
print(f"输入6张'二'，处理结果:")

physical_count = 0
virtual_count = 0

for card in result.assigned_cards:
    is_virtual = card.get('is_virtual', False)
    if is_virtual:
        virtual_count += 1
        print(f"  {card['twin_id']} (虚拟牌)")
    else:
        physical_count += 1
        print(f"  {card['twin_id']} (物理牌)")

print(f"统计: 物理牌{physical_count}张, 虚拟牌{virtual_count}张")
```

## 📊 性能监控

### 系统统计信息

```python
# 获取详细统计信息
result = system.process_frame(detections)

if result.success:
    stats = result.statistics
    
    print("详细统计信息:")
    print(f"  帧信息: {stats.get('frame_info', {})}")
    print(f"  继承统计: {stats.get('inheritance', {})}")
    print(f"  分配统计: {stats.get('assignment', {})}")
    print(f"  验证统计: {stats.get('validation', {})}")
```

## 🔄 系统重置

```python
# 重置系统（用于新局开始）
system.reset_system()
print("系统已重置，可以开始新局")
```

## 📝 注意事项

### 数据格式要求
- **必需字段**：label, bbox, confidence, group_id
- **bbox格式**：[x, y, width, height]
- **confidence范围**：0.0 - 1.0
- **group_id**：有效的区域ID（1-16）

### 性能建议
- 第一阶段系统适合处理基础场景（80%的常见情况）
- 对于复杂场景，建议等待第二、三阶段模块完成
- 系统设计为实时处理，单帧处理时间通常在毫秒级别

### 故障排除
- 如果处理失败，检查`result.validation_errors`获取详细错误信息
- 如果继承率过低，检查输入数据的区域ID和标签是否一致
- 如果出现过多虚拟牌，可能是同一种牌超过了4张限制

---

**下一步**：等待第二阶段模块完成后，系统将支持区域流转、暗牌处理和遮挡补偿等高级功能。

# AI项目理解指南

## 🎯 项目概述
PHZ AI Simple是一个基于YOLOv8的计算机视觉项目，专注于卡牌检测和识别。

## 📁 核心目录结构
```
phz-ai-simple/
├── src/                    # 核心源代码
│   ├── core/              # 核心算法模块
│   ├── config/            # 配置管理
│   └── utils/             # 工具函数
├── models/                # 训练好的模型文件
├── data/                  # 数据集和标注
├── tests/                 # 测试和验证
├── tools/                 # 分析和处理工具
├── output/                # 输出结果
└── docs/                  # 项目文档
```

## 🔧 核心功能模块

### 1. 模型推理引擎 (src/core/)
- **主要功能**: YOLOv8模型加载和推理
- **关键文件**: 
  - 模型加载器
  - 推理引擎
  - 后处理模块

### 2. 数据处理管道 (data/)
- **主要功能**: 数据预处理、标注转换、数据增强
- **关键组件**:
  - 标注格式转换器
  - 数据清洗工具
  - 合成数据生成器

### 3. 验证和测试系统 (tests/)
- **主要功能**: 模型验证、性能测试、端到端测试
- **测试类型**:
  - 单元测试
  - 集成测试
  - 性能基准测试

## 🎮 业务逻辑核心

### 卡牌检测流程
1. **输入处理**: 图像预处理和标准化
2. **模型推理**: YOLOv8目标检测
3. **后处理**: NMS、置信度过滤
4. **结果输出**: 边界框、类别、置信度

### 关键算法
- **双轨输出系统**: 同时支持多种输出格式
- **记忆机制**: 跨帧信息保持
- **状态区域管理**: 游戏状态识别

## 🔍 常见开发任务

### 模型相关
- 模型训练和验证
- 性能优化
- 格式转换 (PyTorch → ONNX)

### 数据相关
- 标注质量检查
- 数据集扩充
- 格式转换

### 测试相关
- 端到端验证
- 性能基准测试
- 回归测试

## 📊 项目状态指标
- **模型精度**: mAP@0.5 > 0.95
- **推理速度**: < 50ms per frame
- **内存使用**: < 2GB
- **测试覆盖率**: > 80%

## 🚨 重要注意事项
1. **模型版本管理**: 严格控制模型版本，避免兼容性问题
2. **数据质量**: 标注质量直接影响模型性能
3. **测试先行**: 任何修改都需要通过完整测试验证
4. **性能监控**: 持续监控推理性能和内存使用

## 🔗 相关文档链接
- [架构设计](./ARCHITECTURE.md)
- [API参考](./API_REFERENCE.md)
- [开发指南](./CONTRIBUTING.md)
- [测试指南](./tests/README_TESTS.md)

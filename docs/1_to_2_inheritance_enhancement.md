# 1-2转换继承功能增强

## 概述

本文档描述了对`SimpleInheritor`类的增强，新增了1-2转换继承功能，支持区域2的卡牌从区域1继承相同基础标签的最大ID。

## 功能描述

### 核心功能
当区域2出现新卡牌，但在前一帧中没有相同区域+标签的匹配时，系统会：

1. **提取基础标签**：去除数字前缀，如"2伍" → "伍"
2. **查找区域1匹配**：在前一帧区域1中查找相同基础标签的卡牌
3. **选择最大ID**：从匹配的卡牌中选择ID数字最大的卡牌
4. **继承ID**：区域2的卡牌继承该最大ID

### 应用场景
- **卡牌流转**：手牌区(区域1) → 调整手牌区(区域2)
- **区域转换**：玩家操作导致卡牌从一个区域移动到另一个区域
- **ID连续性**：确保卡牌在不同区域间移动时保持ID的连续性

## 技术实现

### 新增方法

#### `_try_1_to_2_inheritance`
```python
def _try_1_to_2_inheritance(self, current_cards_list: List[Dict[str, Any]], 
                           original_label: str, 
                           inherited_cards: List[Dict[str, Any]]) -> bool
```

**功能**：尝试为区域2的卡牌从区域1继承最大ID

**参数**：
- `current_cards_list`: 当前区域2的卡牌列表
- `original_label`: 原始标签
- `inherited_cards`: 继承卡牌列表（输出）

**返回值**：是否成功进行了1-2转换继承

#### `_extract_base_label`
```python
def _extract_base_label(self, label: str) -> str
```

**功能**：提取基础标签（去除数字前缀）

**示例**：
- "1伍" → "伍"
- "2二" → "二"
- "伍" → "伍"（无变化）

#### `_find_max_id_card`
```python
def _find_max_id_card(self, cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]
```

**功能**：从卡牌列表中找到ID数字最大的卡牌

**逻辑**：
- 提取ID中的数字部分（如"3伍"中的"3"）
- 返回数字最大的卡牌
- 虚拟卡牌ID优先级最低

### 集成点

在`process_inheritance`方法中，当没有找到直接匹配时：

```python
else:
    # 没有找到前一帧数据，尝试1-2转换继承
    inherited_from_1_to_2 = False
    
    # 如果当前是区域2，尝试从区域1继承
    if group_id == 2:
        inherited_from_1_to_2 = self._try_1_to_2_inheritance(
            current_cards_list, original_label, inherited_cards
        )
    
    # 如果没有通过1-2转换继承，则作为新卡牌
    if not inherited_from_1_to_2:
        new_cards.extend(current_cards_list)
```

## 测试验证

### 基础功能测试
- ✅ 区域2卡牌能从区域1继承相同基础标签的最大ID
- ✅ 支持带数字前缀和不带前缀的标签匹配
- ✅ 正确处理没有匹配标签的情况
- ✅ 多张区域2卡牌继承相同的最大ID

### 真实场景测试
- ✅ 支持手牌区到调整手牌区的卡牌流转
- ✅ 正确处理多种卡牌类型的同时转换
- ✅ 每种基础标签继承对应的最大ID
- ✅ 保持区域信息和其他属性的正确性
- ✅ 无匹配标签的卡牌正确作为新卡牌处理

## 使用示例

### 场景1：单一卡牌类型转换
```
帧1: 区域1有 ["1伍", "2伍", "3伍"]
帧2: 区域2出现 ["伍", "2伍"]

结果: 区域2的两张卡牌都继承ID "3伍"（最大ID）
```

### 场景2：多种卡牌类型转换
```
帧1: 区域1有 ["1伍", "2伍", "1二", "2二", "3二", "1三"]
帧2: 区域2出现 ["伍", "二", "1二", "三", "四"]

结果:
- "伍" → 继承 "2伍"
- "二" → 继承 "3二"
- "1二" → 继承 "3二"
- "三" → 继承 "1三"
- "四" → 新卡牌（无匹配）
```

## 日志输出

系统会输出详细的日志信息：

```
INFO - 1-2转换继承: 3伍 位置(unknown) (区域2, 标签伍 <- 区域1, 基础标签伍)
INFO - 1-2转换继承完成: 1张区域2卡牌继承了区域1的'伍'最大ID
```

## 配置和扩展

### 扩展到其他区域
当前实现专门针对区域2，但可以轻松扩展到其他区域：

```python
# 扩展示例：支持区域3
if group_id in [2, 3]:  # 支持区域2和区域3
    inherited_from_1_to_x = self._try_1_to_x_inheritance(
        current_cards_list, original_label, inherited_cards, group_id
    )
```

### 自定义匹配规则
可以扩展`_extract_base_label`方法以支持更复杂的标签匹配规则。

## 性能考虑

- **时间复杂度**：O(n*m)，其中n是当前帧卡牌数，m是前一帧卡牌数
- **空间复杂度**：O(1)，不增加额外的存储开销
- **优化建议**：对于大量卡牌的场景，可以考虑建立基础标签索引

## 兼容性

- ✅ **向后兼容**：不影响现有的直接继承逻辑
- ✅ **可选功能**：只在特定条件下触发（区域2且无直接匹配）
- ✅ **无副作用**：不修改现有数据结构和接口

## 总结

1-2转换继承功能成功增强了`SimpleInheritor`的能力，使其能够处理更复杂的卡牌流转场景。该功能在保持系统稳定性的同时，提供了更智能的ID继承机制，特别适用于游戏中卡牌在不同区域间移动的场景。

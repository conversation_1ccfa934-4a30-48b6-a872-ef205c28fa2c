#!/usr/bin/env python3
"""
frame_00034.jpg 图像处理结果分析脚本

根据测试文档描述，该帧应该显示：
1. 吃牌操作：上一帧7区域的卡牌流转到16区域（对战方吃碰区）
2. 数字孪生ID分配规则：从下到上依次为"1三 3四 1五"
3. 继承规则：其中"1五"应该继承上一帧7区域卡牌的数字孪生ID

分析任务：
1. 使用脚本工具分析当前frame_00034.jpg的实际输出结果
2. 对比实际输出与预期设计的差异
3. 重点检查数字孪生ID的流转继承机制是否正确实现
4. 分析可能的根本原因（算法逻辑、数据流转、ID分配等）
5. 提出具体的解决方案建议
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目核心模块
from calibration_gt_final_processor import CalibrationGTFinalProcessor, FinalProcessorConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Frame00034Analyzer:
    """frame_00034.jpg 专用分析器"""
    
    def __init__(self):
        self.config = FinalProcessorConfig(
            source_dir='legacy_assets/ceshi/calibration_gt',
            output_dir='output/frame_00034_analysis'
        )
        self.processor = CalibrationGTFinalProcessor(self.config)
        
        # 预期结果（根据测试文档）
        self.expected_results = {
            "frame_00034": {
                "description": "吃牌操作：7区域→16区域流转",
                "expected_ids": ["1三", "3四", "1五"],
                "expected_inheritance": {
                    "1五": "应继承上一帧7区域卡牌的数字孪生ID"
                },
                "expected_region": 16,  # 对战方吃碰区
                "spatial_order": "从下到上依次为：1三 3四 1五"
            }
        }
    
    def analyze_frame_00034(self) -> Dict[str, Any]:
        """分析frame_00034.jpg的处理结果"""
        logger.info("🔍 开始分析frame_00034.jpg...")
        
        # 1. 检查输入文件是否存在
        frame_33_path = Path(self.config.source_dir) / "labels" / "frame_00033.json"
        frame_34_path = Path(self.config.source_dir) / "labels" / "frame_00034.json"
        
        if not frame_33_path.exists():
            logger.error(f"❌ 前一帧文件不存在: {frame_33_path}")
            return {"error": "前一帧文件不存在"}
            
        if not frame_34_path.exists():
            logger.error(f"❌ 目标帧文件不存在: {frame_34_path}")
            return {"error": "目标帧文件不存在"}
        
        # 2. 分析前一帧（frame_00033）的状态
        logger.info("📊 分析前一帧 frame_00033...")
        frame_33_analysis = self._analyze_single_frame(frame_33_path, "frame_00033")
        
        # 3. 分析当前帧（frame_00034）的状态
        logger.info("📊 分析当前帧 frame_00034...")
        frame_34_analysis = self._analyze_single_frame(frame_34_path, "frame_00034")
        
        # 4. 对比分析
        logger.info("🔄 进行对比分析...")
        comparison_result = self._compare_frames(frame_33_analysis, frame_34_analysis)
        
        # 5. 检查继承机制
        logger.info("🔗 检查继承机制...")
        inheritance_analysis = self._analyze_inheritance(frame_33_analysis, frame_34_analysis)
        
        # 6. 生成诊断报告
        logger.info("📋 生成诊断报告...")
        diagnostic_report = self._generate_diagnostic_report(
            frame_33_analysis, frame_34_analysis, comparison_result, inheritance_analysis
        )
        
        return diagnostic_report
    
    def _analyze_single_frame(self, frame_path: Path, frame_name: str) -> Dict[str, Any]:
        """分析单个帧的详细信息"""
        try:
            # 读取原始标注数据
            with open(frame_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            # 提取卡牌信息
            card_shapes = []
            for shape in original_data.get("shapes", []):
                if shape.get("label") in self.config.valid_card_labels:
                    card_shapes.append(shape)
            
            # 转换为检测格式
            card_detections = self.processor._convert_shapes_to_detections(card_shapes)
            all_detections = self.processor._convert_all_shapes_to_detections(original_data.get("shapes", []))
            
            # 使用数字孪生系统处理
            dt_result = self.processor.digital_twin_controller.process_frame(all_detections)
            
            # 分析结果
            analysis = {
                "frame_name": frame_name,
                "original_shapes_count": len(original_data.get("shapes", [])),
                "card_shapes_count": len(card_shapes),
                "processing_success": dt_result.success,
                "processed_cards": dt_result.processed_cards if dt_result.success else [],
                "statistics": dt_result.statistics if dt_result.success else {},
                "validation_errors": dt_result.validation_errors if not dt_result.success else [],
                "cards_by_region": self._group_cards_by_region(dt_result.processed_cards if dt_result.success else []),
                "region_7_cards": [],  # 对战方抓牌区
                "region_16_cards": []  # 对战方吃碰区
            }
            
            # 特别关注区域7和16的卡牌
            for card in analysis["processed_cards"]:
                if card.get("group_id") == 7:
                    analysis["region_7_cards"].append(card)
                elif card.get("group_id") == 16:
                    analysis["region_16_cards"].append(card)
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析帧 {frame_name} 失败: {e}")
            return {
                "frame_name": frame_name,
                "error": str(e),
                "processing_success": False
            }
    
    def _group_cards_by_region(self, cards: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """按区域分组卡牌"""
        grouped = {}
        for card in cards:
            region_id = card.get("group_id", 0)
            if region_id not in grouped:
                grouped[region_id] = []
            grouped[region_id].append(card)
        return grouped
    
    def _compare_frames(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any]) -> Dict[str, Any]:
        """对比两帧的差异"""
        comparison = {
            "region_7_changes": {
                "frame_33_count": len(frame_33.get("region_7_cards", [])),
                "frame_34_count": len(frame_34.get("region_7_cards", [])),
                "cards_disappeared": [],
                "cards_appeared": []
            },
            "region_16_changes": {
                "frame_33_count": len(frame_33.get("region_16_cards", [])),
                "frame_34_count": len(frame_34.get("region_16_cards", [])),
                "cards_disappeared": [],
                "cards_appeared": []
            },
            "total_cards_change": len(frame_34.get("processed_cards", [])) - len(frame_33.get("processed_cards", []))
        }
        
        # 分析区域7的变化（应该有卡牌消失）
        frame_33_region_7_ids = [card.get("twin_id") for card in frame_33.get("region_7_cards", [])]
        frame_34_region_7_ids = [card.get("twin_id") for card in frame_34.get("region_7_cards", [])]
        
        comparison["region_7_changes"]["cards_disappeared"] = [
            id for id in frame_33_region_7_ids if id not in frame_34_region_7_ids
        ]
        comparison["region_7_changes"]["cards_appeared"] = [
            id for id in frame_34_region_7_ids if id not in frame_33_region_7_ids
        ]
        
        # 分析区域16的变化（应该有卡牌出现）
        frame_33_region_16_ids = [card.get("twin_id") for card in frame_33.get("region_16_cards", [])]
        frame_34_region_16_ids = [card.get("twin_id") for card in frame_34.get("region_16_cards", [])]
        
        comparison["region_16_changes"]["cards_disappeared"] = [
            id for id in frame_33_region_16_ids if id not in frame_34_region_16_ids
        ]
        comparison["region_16_changes"]["cards_appeared"] = [
            id for id in frame_34_region_16_ids if id not in frame_33_region_16_ids
        ]
        
        return comparison
    
    def _analyze_inheritance(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any]) -> Dict[str, Any]:
        """分析继承机制是否正确工作"""
        inheritance_analysis = {
            "expected_inheritance": self.expected_results["frame_00034"]["expected_inheritance"],
            "actual_inheritance": {},
            "inheritance_success": False,
            "issues_found": []
        }
        
        # 检查区域7→16的流转继承
        frame_33_region_7_cards = frame_33.get("region_7_cards", [])
        frame_34_region_16_cards = frame_34.get("region_16_cards", [])
        
        # 查找可能的继承关系
        for card_33 in frame_33_region_7_cards:
            card_33_id = card_33.get("twin_id")
            card_33_label = card_33.get("label")
            
            # 在frame_34的区域16中查找相同ID或相同标签的卡牌
            for card_34 in frame_34_region_16_cards:
                card_34_id = card_34.get("twin_id")
                card_34_label = card_34.get("label")
                
                if card_33_id == card_34_id:
                    inheritance_analysis["actual_inheritance"][card_34_id] = {
                        "source_region": 7,
                        "target_region": 16,
                        "id_preserved": True,
                        "label_match": card_33_label == card_34_label
                    }
        
        # 检查是否符合预期
        expected_inherited_id = "1五"
        if expected_inherited_id in inheritance_analysis["actual_inheritance"]:
            inheritance_analysis["inheritance_success"] = True
        else:
            inheritance_analysis["issues_found"].append(f"预期继承的ID '{expected_inherited_id}' 未找到")
        
        return inheritance_analysis

    def _generate_diagnostic_report(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any],
                                   comparison: Dict[str, Any], inheritance: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细的诊断报告"""
        report = {
            "analysis_summary": {
                "frame_33_status": "成功" if frame_33.get("processing_success") else "失败",
                "frame_34_status": "成功" if frame_34.get("processing_success") else "失败",
                "inheritance_status": "成功" if inheritance.get("inheritance_success") else "失败"
            },
            "expected_vs_actual": {
                "expected": self.expected_results["frame_00034"],
                "actual_frame_34_region_16": [
                    {
                        "twin_id": card.get("twin_id"),
                        "label": card.get("label"),
                        "bbox": card.get("bbox"),
                        "inherited": card.get("inherited", False)
                    }
                    for card in frame_34.get("region_16_cards", [])
                ]
            },
            "flow_analysis": {
                "region_7_to_16_flow": {
                    "cards_left_region_7": comparison["region_7_changes"]["cards_disappeared"],
                    "cards_entered_region_16": comparison["region_16_changes"]["cards_appeared"],
                    "flow_detected": len(comparison["region_7_changes"]["cards_disappeared"]) > 0 and
                                   len(comparison["region_16_changes"]["cards_appeared"]) > 0
                }
            },
            "inheritance_analysis": inheritance,
            "issues_identified": [],
            "root_cause_analysis": {},
            "recommendations": []
        }

        # 识别问题
        if not frame_33.get("processing_success"):
            report["issues_identified"].append("前一帧处理失败")

        if not frame_34.get("processing_success"):
            report["issues_identified"].append("当前帧处理失败")

        if not inheritance.get("inheritance_success"):
            report["issues_identified"].append("继承机制未正确工作")

        # 检查预期ID是否存在
        expected_ids = self.expected_results["frame_00034"]["expected_ids"]
        actual_ids = [card.get("twin_id") for card in frame_34.get("region_16_cards", [])]

        missing_ids = [id for id in expected_ids if id not in actual_ids]
        unexpected_ids = [id for id in actual_ids if id not in expected_ids]

        if missing_ids:
            report["issues_identified"].append(f"缺少预期ID: {missing_ids}")

        if unexpected_ids:
            report["issues_identified"].append(f"出现意外ID: {unexpected_ids}")

        # 根本原因分析
        report["root_cause_analysis"] = self._analyze_root_causes(frame_33, frame_34, comparison, inheritance)

        # 生成建议
        report["recommendations"] = self._generate_recommendations(report)

        return report

    def _analyze_root_causes(self, frame_33: Dict[str, Any], frame_34: Dict[str, Any],
                           comparison: Dict[str, Any], inheritance: Dict[str, Any]) -> Dict[str, Any]:
        """分析根本原因"""
        root_causes = {
            "data_flow_issues": [],
            "inheritance_issues": [],
            "id_assignment_issues": [],
            "region_transition_issues": []
        }

        # 检查数据流问题
        flow_analysis = comparison.get("flow_analysis", {}).get("region_7_to_16_flow", {})
        if not flow_analysis.get("flow_detected", False):
            root_causes["data_flow_issues"].append("未检测到7→16区域的卡牌流转")

        # 检查继承问题
        if not inheritance.get("inheritance_success"):
            root_causes["inheritance_issues"].extend(inheritance.get("issues_found", []))

        # 检查ID分配问题
        frame_34_region_16_cards = frame_34.get("region_16_cards", [])
        if len(frame_34_region_16_cards) != 3:
            root_causes["id_assignment_issues"].append(
                f"区域16卡牌数量不符合预期：实际{len(frame_34_region_16_cards)}张，预期3张"
            )

        # 检查空间排序问题
        if len(frame_34_region_16_cards) > 0:
            sorted_cards = sorted(frame_34_region_16_cards, key=lambda x: x.get("bbox", [0,0,0,0])[1])  # 按Y坐标排序
            actual_order = [card.get("twin_id") for card in sorted_cards]
            expected_order = self.expected_results["frame_00034"]["expected_ids"]

            if actual_order != expected_order:
                root_causes["id_assignment_issues"].append(
                    f"空间排序不符合预期：实际{actual_order}，预期{expected_order}"
                )

        return root_causes

    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """生成解决方案建议"""
        recommendations = []

        root_causes = report.get("root_cause_analysis", {})

        # 针对数据流问题的建议
        if root_causes.get("data_flow_issues"):
            recommendations.append("检查RegionTransitioner模块的7→16流转路径配置")
            recommendations.append("验证区域流转器的_handle_special_7_to_16_transition方法")

        # 针对继承问题的建议
        if root_causes.get("inheritance_issues"):
            recommendations.append("检查SimpleInheritor模块的跨区域继承逻辑")
            recommendations.append("验证_try_cross_region_inheritance_for_eating_region方法")
            recommendations.append("确认前一帧数据是否正确传递给继承器")

        # 针对ID分配问题的建议
        if root_causes.get("id_assignment_issues"):
            recommendations.append("检查BasicIDAssigner的空间排序逻辑")
            recommendations.append("验证SpatialSorter模块的区域16排序规则")
            recommendations.append("确认ID分配的优先级策略（继承优先vs新分配）")

        # 针对区域流转问题的建议
        if root_causes.get("region_transition_issues"):
            recommendations.append("检查区域流转的触发条件和时机")
            recommendations.append("验证流转历史记录的维护机制")

        # 通用建议
        recommendations.append("启用详细日志记录，跟踪每个模块的处理过程")
        recommendations.append("使用单步调试模式分析frame_00033→frame_00034的完整处理流程")

        return recommendations

def main():
    """主函数"""
    print("🔍 frame_00034.jpg 图像处理结果分析")
    print("=" * 60)

    analyzer = Frame00034Analyzer()

    try:
        # 执行分析
        result = analyzer.analyze_frame_00034()

        if "error" in result:
            print(f"❌ 分析失败: {result['error']}")
            return

        # 输出分析结果
        print("\n📊 分析结果摘要:")
        print(f"   前一帧状态: {result['analysis_summary']['frame_33_status']}")
        print(f"   当前帧状态: {result['analysis_summary']['frame_34_status']}")
        print(f"   继承机制状态: {result['analysis_summary']['inheritance_status']}")

        print("\n🎯 预期 vs 实际结果:")
        expected = result['expected_vs_actual']['expected']
        actual = result['expected_vs_actual']['actual_frame_34_region_16']

        print(f"   预期ID: {expected['expected_ids']}")
        print(f"   预期继承: {expected['expected_inheritance']}")
        print(f"   实际区域16卡牌: {len(actual)}张")

        for i, card in enumerate(actual):
            print(f"     {i+1}. ID: {card['twin_id']}, 标签: {card['label']}, 继承: {card['inherited']}")

        print("\n🔄 流转分析:")
        flow = result['flow_analysis']['region_7_to_16_flow']
        print(f"   区域7消失卡牌: {flow['cards_left_region_7']}")
        print(f"   区域16新增卡牌: {flow['cards_entered_region_16']}")
        print(f"   流转检测: {'✅ 成功' if flow['flow_detected'] else '❌ 失败'}")

        print("\n🔗 继承分析:")
        inheritance = result['inheritance_analysis']
        print(f"   预期继承: {inheritance['expected_inheritance']}")
        print(f"   实际继承: {inheritance['actual_inheritance']}")
        print(f"   继承成功: {'✅ 是' if inheritance['inheritance_success'] else '❌ 否'}")

        if inheritance['issues_found']:
            print("   发现问题:")
            for issue in inheritance['issues_found']:
                print(f"     - {issue}")

        print("\n❗ 识别的问题:")
        for issue in result['issues_identified']:
            print(f"   - {issue}")

        print("\n🔍 根本原因分析:")
        root_causes = result['root_cause_analysis']
        for category, issues in root_causes.items():
            if issues:
                print(f"   {category}:")
                for issue in issues:
                    print(f"     - {issue}")

        print("\n💡 解决方案建议:")
        for i, recommendation in enumerate(result['recommendations'], 1):
            print(f"   {i}. {recommendation}")

        # 保存详细报告
        output_path = Path(analyzer.config.output_dir) / "frame_00034_analysis_report.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n📁 详细报告已保存到: {output_path}")

    except Exception as e:
        logger.exception("分析过程中发生错误")
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()

"""
剩余区域错误深度分析脚本

分析V2.0仍然存在的8.6%区域错误，为多算法融合提供精确改进方向。

分析内容：
1. 剩余错误的具体模式
2. 错误位置的空间分布
3. 算法改进的具体方向
4. 多算法融合的策略建议
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict

class RemainingErrorAnalyzer:
    """剩余错误分析器"""
    
    def __init__(self, validation_report_path: str):
        self.report_path = Path(validation_report_path)
        self.validation_data = self._load_validation_data()
        
    def _load_validation_data(self) -> Dict[str, Any]:
        """加载验证数据"""
        with open(self.report_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_remaining_errors(self) -> Dict[str, Any]:
        """分析剩余的区域错误"""
        print("🔍 分析V2.0剩余的区域错误...")
        
        all_region_errors = []
        total_cards = 0
        total_correct = 0
        
        # 收集所有错误
        for seq_result in self.validation_data["sequence_results"]:
            all_region_errors.extend(seq_result["region_errors"])
            total_cards += seq_result["total_cards"]
            total_correct += seq_result["region_correct"]
        
        error_rate = (len(all_region_errors) / total_cards) * 100 if total_cards > 0 else 0
        print(f"总错误数: {len(all_region_errors)}")
        print(f"总卡牌数: {total_cards}")
        print(f"错误率: {error_rate:.1f}%")
        
        # 1. 错误模式分析
        error_patterns = Counter()
        for error in all_region_errors:
            pattern = f"{error['expected_region']} -> {error['actual_region']}"
            error_patterns[pattern] += 1
        
        print("\n📊 剩余错误模式:")
        for pattern, count in error_patterns.most_common(10):
            print(f"  {pattern}: {count}次")
        
        # 2. 高IoU错误分析（检测准确但分类错误）
        high_iou_errors = [e for e in all_region_errors if e.get('iou', 0) >= 0.9]
        print(f"\n🎯 高IoU错误: {len(high_iou_errors)}次 ({len(high_iou_errors)/len(all_region_errors)*100:.1f}%)")
        
        # 3. 特定区域对的混淆分析
        confusion_matrix = defaultdict(lambda: defaultdict(int))
        for error in all_region_errors:
            confusion_matrix[error['expected_region']][error['actual_region']] += 1
        
        print("\n🔄 主要区域混淆:")
        for expected_region in sorted(confusion_matrix.keys()):
            for actual_region in sorted(confusion_matrix[expected_region].keys()):
                count = confusion_matrix[expected_region][actual_region]
                if count > 1:  # 只显示出现多次的混淆
                    print(f"  区域{expected_region} -> 区域{actual_region}: {count}次")
        
        # 4. 按卡牌类型分析
        card_errors = Counter(error['card'] for error in all_region_errors)
        print("\n🎯 错误最多的卡牌类型:")
        for card, count in card_errors.most_common(10):
            print(f"  {card}: {count}次")
        
        return {
            "total_errors": len(all_region_errors),
            "error_rate": error_rate,
            "error_patterns": dict(error_patterns.most_common()),
            "high_iou_errors": len(high_iou_errors),
            "confusion_matrix": dict(confusion_matrix),
            "card_errors": dict(card_errors.most_common())
        }
    
    def analyze_spatial_distribution(self) -> Dict[str, Any]:
        """分析错误的空间分布"""
        print("\n📏 分析错误的空间分布...")
        
        # 从原始数据中提取错误位置信息
        # 这里需要结合原始的zhuangtaiquyu数据来获取精确位置
        
        # 分析不同区域对之间的空间关系
        spatial_analysis = {
            "boundary_errors": [],
            "center_errors": [],
            "overlap_errors": []
        }
        
        # 基于已知的区域规则分析
        region_centers = {
            1: (0.485, 0.808),  # 手牌区_观战方
            2: (0.536, 0.602),  # 区域2
            3: (0.506, 0.248),  # 区域3
            5: (0.927, 0.967),  # 弃牌区
            6: (0.451, 0.330),  # 吃碰区_观战方
            7: (0.266, 0.244),  # 区域7
            8: (0.234, 0.297),  # 区域8
            9: (0.159, 0.350),  # 对战方区域
            10: (0.808, 0.263), # 区域10
            16: (0.142, 0.196)  # 吃碰区_对战方
        }
        
        # 计算区域间距离
        region_distances = {}
        for r1, center1 in region_centers.items():
            for r2, center2 in region_centers.items():
                if r1 != r2:
                    distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
                    region_distances[f"{r1}-{r2}"] = distance
        
        print("🔍 最容易混淆的区域对（距离最近）:")
        sorted_distances = sorted(region_distances.items(), key=lambda x: x[1])
        for pair, distance in sorted_distances[:5]:
            print(f"  {pair}: 距离 {distance:.3f}")
        
        return {
            "region_centers": region_centers,
            "region_distances": region_distances,
            "closest_pairs": sorted_distances[:10]
        }
    
    def identify_algorithm_improvement_directions(self, error_analysis: Dict, spatial_analysis: Dict) -> List[str]:
        """识别算法改进方向"""
        print("\n💡 识别算法改进方向...")
        
        directions = []
        
        # 1. 基于错误模式的改进
        top_error_patterns = list(error_analysis["error_patterns"].items())[:3]
        for pattern, count in top_error_patterns:
            expected, actual = pattern.split(" -> ")
            directions.append(f"优化区域{expected}和区域{actual}的边界区分算法（{count}次错误）")
        
        # 2. 基于高IoU错误的改进
        high_iou_ratio = error_analysis["high_iou_errors"] / error_analysis["total_errors"]
        if high_iou_ratio > 0.8:
            directions.append(f"引入更多特征进行分类（{high_iou_ratio:.1%}的错误是高IoU错误）")
        
        # 3. 基于空间分布的改进
        closest_pairs = spatial_analysis["closest_pairs"][:3]
        for pair, distance in closest_pairs:
            if distance < 0.2:  # 距离很近的区域对
                directions.append(f"使用机器学习方法区分相近区域{pair}（距离{distance:.3f}）")
        
        # 4. 基于卡牌类型的改进
        top_error_cards = list(error_analysis["card_errors"].items())[:3]
        for card, count in top_error_cards:
            if count > 3:
                directions.append(f"针对'{card}'卡牌优化分类算法（{count}次错误）")
        
        return directions
    
    def suggest_multi_algorithm_fusion(self, improvement_directions: List[str]) -> Dict[str, Any]:
        """建议多算法融合策略"""
        print("\n🚀 建议多算法融合策略...")
        
        fusion_strategy = {
            "rule_based": {
                "description": "基于规则的分类器（当前V2.0）",
                "strengths": ["快速", "可解释", "覆盖主要区域"],
                "weaknesses": ["边界模糊", "难以处理复杂情况"],
                "improvement": "优化边界定义和容忍度"
            },
            "machine_learning": {
                "description": "机器学习分类器",
                "algorithm": "随机森林或SVM",
                "features": ["位置坐标", "卡牌大小", "邻近关系", "密度特征"],
                "advantages": ["处理复杂边界", "自动特征学习"],
                "implementation": "使用现有标注数据训练"
            },
            "spatial_clustering": {
                "description": "空间聚类算法",
                "algorithm": "DBSCAN或K-means",
                "purpose": "识别卡牌密集区域",
                "advantages": ["发现隐藏模式", "处理不规则形状"],
                "use_case": "辅助区域边界优化"
            },
            "ensemble_learning": {
                "description": "集成学习方法",
                "strategy": "投票机制或加权平均",
                "components": ["规则分类器", "ML分类器", "聚类结果"],
                "advantages": ["提高准确率", "增强鲁棒性"],
                "target": "95%+准确率"
            }
        }
        
        print("📋 多算法融合策略:")
        for name, strategy in fusion_strategy.items():
            print(f"  {name}: {strategy['description']}")
        
        return fusion_strategy
    
    def run_complete_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        print("🚀 剩余区域错误深度分析")
        print("=" * 50)
        
        # 1. 错误分析
        error_analysis = self.analyze_remaining_errors()
        
        # 2. 空间分布分析
        spatial_analysis = self.analyze_spatial_distribution()
        
        # 3. 改进方向识别
        improvement_directions = self.identify_algorithm_improvement_directions(error_analysis, spatial_analysis)
        
        print("\n💡 算法改进方向:")
        for i, direction in enumerate(improvement_directions, 1):
            print(f"{i}. {direction}")
        
        # 4. 多算法融合建议
        fusion_strategy = self.suggest_multi_algorithm_fusion(improvement_directions)
        
        # 保存分析结果
        analysis_result = {
            "analysis_timestamp": "2025-01-17 08:15:00",
            "error_analysis": error_analysis,
            "spatial_analysis": spatial_analysis,
            "improvement_directions": improvement_directions,
            "fusion_strategy": fusion_strategy
        }
        
        output_path = "analysis/remaining_error_analysis_results.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存: {output_path}")
        
        return analysis_result

def main():
    """主分析函数"""
    report_path = "tests/enhanced_system_validation_20250717_075841.json"
    
    if not Path(report_path).exists():
        print(f"❌ 验证报告未找到: {report_path}")
        return False
    
    analyzer = RemainingErrorAnalyzer(report_path)
    results = analyzer.run_complete_analysis()
    
    print("\n🎉 剩余错误分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

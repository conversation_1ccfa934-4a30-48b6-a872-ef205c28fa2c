# 🤖 PHZ AI Simple - 智能编程助手规则

## 📋 项目核心信息
**项目类型**: 计算机视觉 - YOLOv8卡牌检测系统
**技术栈**: Python 3.8+, PyTorch, OpenCV, YOLOv8
**主要功能**: 实时卡牌检测、双轨输出、记忆机制

## 🏗️ 架构理解要点
### 核心模块层次
1. **推理引擎层** (src/core/) - YOLOv8模型加载和推理
2. **业务逻辑层** (src/) - 双轨输出、记忆机制、状态管理
3. **工具验证层** (tools/, tests/) - 分析工具、测试验证

### 关键数据流
```
输入图像 → 预处理 → YOLOv8推理 → 后处理 → 双轨输出
         ↓
    记忆机制 → 状态融合 → 结果增强
```

## 🎯 AI助手行为准则

### 代码分析时
- **必须先查看**: `docs/AI_PROJECT_GUIDE.md` 了解项目概况
- **架构理解**: 参考 `docs/CODE_ARCHITECTURE_MAP.md`
- **依赖关系**: 检查模块间的调用关系
- **性能考虑**: 关注推理速度和内存使用

### 修改代码时
- **测试先行**: 任何修改都需要对应的测试
- **向后兼容**: 保持与现有模型格式的兼容性
- **性能监控**: 确保不降低推理性能
- **文档同步**: 更新相关文档和注释

### 问题诊断时
- **日志优先**: 先查看相关日志文件
- **测试验证**: 运行相关测试脚本
- **性能分析**: 使用tools/下的分析工具
- **版本对比**: 检查模型版本和配置变化

## 🔍 智能Context使用指南

### 分析项目结构
```
@Files docs/AI_PROJECT_GUIDE.md 快速了解项目
@Files docs/CODE_ARCHITECTURE_MAP.md 理解架构
@Folders src 查看核心代码结构
```

### 模型相关任务
```
@Folders models 检查模型文件
@Files tools/model_*.py 模型分析工具
@Files src/core 核心推理代码
```

### 数据处理任务
```
@Folders data 数据集状态
@Files tools/data_*.py 数据处理工具
@Files *_gt_*.py 标注处理脚本
```

### 测试和验证
```
@Folders tests 测试套件
@Files *_verification.py 验证脚本
@Folders output 查看测试结果
```

## 📊 性能基准和约束
- **推理速度**: < 50ms per frame
- **内存使用**: < 2GB
- **模型精度**: mAP@0.5 > 0.95
- **测试覆盖率**: > 80%

## 🚨 关键注意事项
1. **模型版本**: 严格控制，避免兼容性问题
2. **数据质量**: 标注质量直接影响性能
3. **双轨输出**: 保持多格式输出的一致性
4. **记忆机制**: 注意跨帧状态的正确性

## 🔧 常见开发模式

### 新功能开发流程
1. 需求分析 → 设计方案
2. 编码实现 → 单元测试
3. 集成测试 → 性能验证
4. 文档更新 → 代码审查

### 性能优化流程
1. 性能分析 → 瓶颈识别
2. 优化实现 → 基准测试
3. 回归验证 → 部署上线

### 问题修复流程
1. 问题复现 → 根因分析
2. 修复实现 → 测试验证
3. 回归测试 → 文档更新

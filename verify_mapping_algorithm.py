#!/usr/bin/env python3
"""
验证脚本2：模拟正确的映射算法

分析当前映射算法的问题，并模拟正确的空间位置映射，
验证是否能解决frame_00060.jpg的ID分配问题。

重点分析：
1. 当前映射算法的具体逻辑
2. 空间位置匹配的准确性
3. 一对一精确匹配的实现
4. 正确映射算法的效果验证
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import math

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.core.digital_twin_controller import DigitalTwinController
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_frame_data(frame_number: int) -> Optional[Dict[str, Any]]:
    """加载指定帧的标注数据"""
    frame_path = f"legacy_assets/ceshi/calibration_gt/labels/frame_{frame_number:05d}.json"
    
    if not os.path.exists(frame_path):
        logger.warning(f"帧文件不存在: {frame_path}")
        return None
        
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载帧{frame_number}失败: {e}")
        return None

def convert_to_detection_format(shapes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """将AnyLabeling格式转换为检测格式"""
    detections = []
    
    for shape in shapes:
        if shape.get('shape_type') == 'rectangle' and 'points' in shape:
            points = shape['points']
            if len(points) >= 4:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [min(x_coords), min(y_coords), max(x_coords), max(y_coords)],
                    'confidence': shape.get('score', 1.0),
                    'group_id': shape.get('group_id', 1),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', '')
                }
                detections.append(detection)
    
    return detections

def calculate_distance(bbox1: List[float], bbox2: List[float]) -> float:
    """计算两个边界框的中心点距离"""
    center1_x = (bbox1[0] + bbox1[2]) / 2
    center1_y = (bbox1[1] + bbox1[3]) / 2
    center2_x = (bbox2[0] + bbox2[2]) / 2
    center2_y = (bbox2[1] + bbox2[3]) / 2
    
    dx = center1_x - center2_x
    dy = center1_y - center2_y
    return math.sqrt(dx * dx + dy * dy)

def simulate_current_mapping_algorithm():
    """模拟当前的映射算法"""
    print("🔍 验证脚本2：模拟当前映射算法")
    print("="*60)
    
    # 准备数据
    frame59_data = load_frame_data(59)
    frame60_data = load_frame_data(60)
    
    frame59_detections = convert_to_detection_format(frame59_data.get('shapes', []))
    frame60_detections = convert_to_detection_format(frame60_data.get('shapes', []))
    
    # 处理frame_00059和frame_00060
    controller = DigitalTwinController()
    controller.process_frame(frame59_detections)  # 建立前置状态
    result60 = controller.process_frame(frame60_detections)
    
    print("步骤1: 获取集成器处理结果")
    print("-" * 40)
    
    # 提取区域16的处理结果
    region16_processed = []
    if hasattr(result60, 'processed_cards'):
        for card in result60.processed_cards:
            if isinstance(card, dict):
                group_id = card.get('group_id')
                twin_id = card.get('twin_id') or card.get('digital_twin_id')
                label = card.get('label')
                bbox = card.get('bbox', [0, 0, 0, 0])
            else:
                group_id = getattr(card, 'group_id', None)
                twin_id = getattr(card, 'twin_id', None) or getattr(card, 'digital_twin_id', None)
                label = getattr(card, 'label', None)
                bbox = getattr(card, 'bbox', [0, 0, 0, 0])
            
            if group_id == 16 and label == '二':
                region16_processed.append({
                    'label': label,
                    'twin_id': twin_id,
                    'group_id': group_id,
                    'bbox': bbox,
                    'bottom_y': bbox[3] if len(bbox) > 3 else 0
                })
    
    # 按bottom_y排序
    region16_processed.sort(key=lambda c: -c['bottom_y'])
    
    print(f"集成器输出区域16'二'牌: {len(region16_processed)}张")
    for i, card in enumerate(region16_processed):
        print(f"  位置{i+1}: {card['label']} -> {card['twin_id']} (bottom_y: {card['bottom_y']:.1f})")
    
    print("\n步骤2: 获取原始shapes数据")
    print("-" * 40)
    
    # 获取原始shapes
    original_shapes = frame60_data.get('shapes', [])
    region16_shapes = []
    
    for shape in original_shapes:
        if shape.get('group_id') == 16 and shape.get('label') == '二':
            points = shape.get('points', [])
            if len(points) >= 4:
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                region16_shapes.append({
                    'label': shape.get('label'),
                    'bbox': bbox,
                    'bottom_y': bbox[3],
                    'points': points,
                    'original_shape': shape
                })
    
    # 按bottom_y排序
    region16_shapes.sort(key=lambda s: -s['bottom_y'])
    
    print(f"原始shapes区域16'二'牌: {len(region16_shapes)}张")
    for i, shape in enumerate(region16_shapes):
        print(f"  位置{i+1}: {shape['label']} (bottom_y: {shape['bottom_y']:.1f})")
    
    return region16_processed, region16_shapes

def test_current_mapping_logic(processed_cards, shapes):
    """测试当前映射逻辑的问题"""
    print("\n步骤3: 模拟当前映射逻辑")
    print("-" * 40)
    
    # 模拟简单标签匹配（可能的当前逻辑）
    print("🧪 测试方案A: 简单标签匹配")
    simple_mapping = []
    for shape in shapes:
        # 找第一个匹配的processed_card
        for card in processed_cards:
            if card['label'] == shape['label']:
                simple_mapping.append({
                    'shape_bottom_y': shape['bottom_y'],
                    'matched_twin_id': card['twin_id'],
                    'method': '简单标签匹配'
                })
                break
    
    print("简单标签匹配结果:")
    for i, mapping in enumerate(simple_mapping):
        print(f"  位置{i+1}: bottom_y={mapping['shape_bottom_y']:.1f} -> {mapping['matched_twin_id']}")
    
    # 模拟距离匹配（可能的当前逻辑）
    print("\n🧪 测试方案B: 最近距离匹配")
    distance_mapping = []
    used_cards = set()
    
    for shape in shapes:
        best_match = None
        min_distance = float('inf')
        
        for j, card in enumerate(processed_cards):
            if j in used_cards:
                continue
                
            distance = calculate_distance(shape['bbox'], card['bbox'])
            if distance < min_distance:
                min_distance = distance
                best_match = (j, card, distance)
        
        if best_match:
            used_cards.add(best_match[0])
            distance_mapping.append({
                'shape_bottom_y': shape['bottom_y'],
                'matched_twin_id': best_match[1]['twin_id'],
                'distance': best_match[2],
                'method': '最近距离匹配'
            })
    
    print("最近距离匹配结果:")
    for i, mapping in enumerate(distance_mapping):
        print(f"  位置{i+1}: bottom_y={mapping['shape_bottom_y']:.1f} -> {mapping['matched_twin_id']} (距离: {mapping['distance']:.2f})")
    
    return simple_mapping, distance_mapping

def test_correct_mapping_logic(processed_cards, shapes):
    """测试正确的映射逻辑"""
    print("\n步骤4: 测试正确的映射逻辑")
    print("-" * 40)
    
    print("🎯 正确方案: 空间位置一对一匹配")
    
    # 确保两个列表都按bottom_y排序
    processed_sorted = sorted(processed_cards, key=lambda c: -c['bottom_y'])
    shapes_sorted = sorted(shapes, key=lambda s: -s['bottom_y'])
    
    print("排序后的数据:")
    print("集成器结果 (按bottom_y从大到小):")
    for i, card in enumerate(processed_sorted):
        print(f"  {i+1}. {card['twin_id']} (bottom_y: {card['bottom_y']:.1f})")
    
    print("原始shapes (按bottom_y从大到小):")
    for i, shape in enumerate(shapes_sorted):
        print(f"  {i+1}. {shape['label']} (bottom_y: {shape['bottom_y']:.1f})")
    
    # 一对一匹配
    correct_mapping = []
    for i, (shape, card) in enumerate(zip(shapes_sorted, processed_sorted)):
        correct_mapping.append({
            'shape_bottom_y': shape['bottom_y'],
            'card_bottom_y': card['bottom_y'],
            'matched_twin_id': card['twin_id'],
            'position_diff': abs(shape['bottom_y'] - card['bottom_y']),
            'method': '空间位置一对一匹配'
        })
    
    print("\n正确映射结果:")
    for i, mapping in enumerate(correct_mapping):
        print(f"  位置{i+1}: shape_y={mapping['shape_bottom_y']:.1f} -> {mapping['matched_twin_id']} (card_y={mapping['card_bottom_y']:.1f}, 差异: {mapping['position_diff']:.2f})")
    
    return correct_mapping

def compare_mapping_results(simple_mapping, distance_mapping, correct_mapping):
    """比较不同映射方法的结果"""
    print("\n" + "="*60)
    print("📊 映射方法比较分析")
    print("="*60)
    
    print("🔍 结果对比:")
    print(f"{'位置':<6} {'简单匹配':<12} {'距离匹配':<12} {'正确匹配':<12}")
    print("-" * 50)
    
    for i in range(len(simple_mapping)):
        simple_id = simple_mapping[i]['matched_twin_id'] if i < len(simple_mapping) else 'N/A'
        distance_id = distance_mapping[i]['matched_twin_id'] if i < len(distance_mapping) else 'N/A'
        correct_id = correct_mapping[i]['matched_twin_id'] if i < len(correct_mapping) else 'N/A'
        
        print(f"{i+1:<6} {simple_id:<12} {distance_id:<12} {correct_id:<12}")
    
    print("\n🎯 问题分析:")
    
    # 检查简单匹配的问题
    simple_ids = [m['matched_twin_id'] for m in simple_mapping]
    if len(set(simple_ids)) == 1:
        print(f"❌ 简单标签匹配: 所有卡牌都匹配到 '{simple_ids[0]}'")
        print("   原因: 总是匹配第一个找到的卡牌，没有考虑空间位置")
    else:
        print("✅ 简单标签匹配: ID分配有差异")
    
    # 检查距离匹配的问题
    distance_ids = [m['matched_twin_id'] for m in distance_mapping]
    expected_ids = ['1二', '2二', '3二', '4二']
    
    if distance_ids == expected_ids:
        print("✅ 距离匹配: 结果正确")
    else:
        print(f"❌ 距离匹配: 期望{expected_ids}, 实际{distance_ids}")
    
    # 检查正确匹配的效果
    correct_ids = [m['matched_twin_id'] for m in correct_mapping]
    if correct_ids == expected_ids:
        print("✅ 空间位置一对一匹配: 结果正确")
    else:
        print(f"❌ 空间位置一对一匹配: 期望{expected_ids}, 实际{correct_ids}")
    
    return {
        'simple_ids': simple_ids,
        'distance_ids': distance_ids,
        'correct_ids': correct_ids,
        'expected_ids': expected_ids
    }

def main():
    """主函数"""
    print("🔍 验证脚本2：映射算法问题诊断")
    print("="*70)
    print("目标: 分析当前映射算法的问题并验证正确的映射方法")
    print("测试: 简单匹配 vs 距离匹配 vs 空间位置一对一匹配")
    print()
    
    # 获取处理数据
    processed_cards, shapes = simulate_current_mapping_algorithm()
    
    # 测试不同映射方法
    simple_mapping, distance_mapping = test_current_mapping_logic(processed_cards, shapes)
    correct_mapping = test_correct_mapping_logic(processed_cards, shapes)
    
    # 比较结果
    comparison = compare_mapping_results(simple_mapping, distance_mapping, correct_mapping)
    
    print("\n" + "="*70)
    print("📋 验证脚本2总结")
    print("="*70)
    print("✅ 映射算法问题诊断完成")
    print()
    print("🎯 关键发现:")
    if comparison['simple_ids'] == ['1二', '1二', '1二', '1二']:
        print("1. ❌ 当前使用简单标签匹配，导致所有卡牌都匹配到'1二'")
    if comparison['correct_ids'] == comparison['expected_ids']:
        print("2. ✅ 空间位置一对一匹配能解决问题")
    print("3. 🎯 解决方案: 实现基于空间排序的一对一精确匹配")
    print()
    print("📝 下一步: 运行验证脚本3检查数据流架构")

if __name__ == "__main__":
    main()

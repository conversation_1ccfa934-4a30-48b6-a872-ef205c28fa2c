#!/usr/bin/env python3
"""
测试region_transitioner是否被正确调用
"""

import json
import sys
from pathlib import Path

def test_region_6_cards():
    """测试区域6卡牌是否存在"""
    print("🔍 测试区域6卡牌是否存在")
    print("=" * 50)
    
    # 文件路径
    output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    
    test_frames = ["frame_00361", "frame_00362", "frame_00340", "frame_00341"]
    
    for frame_name in test_frames:
        frame_path = output_dir / f"{frame_name}.json"
        
        if not frame_path.exists():
            print(f"❌ 文件不存在: {frame_path}")
            continue
        
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                frame_data = json.load(f)
        except Exception as e:
            print(f"❌ 加载失败: {frame_path} - {e}")
            continue
        
        # 统计区域6卡牌
        region_6_cards = []
        if 'shapes' in frame_data:
            for shape in frame_data['shapes']:
                if shape.get('group_id') == 6:
                    region_6_cards.append(shape)
        
        print(f"📋 {frame_name}: 区域6有{len(region_6_cards)}张卡牌")
        
        if region_6_cards:
            for i, card in enumerate(region_6_cards):
                label = card.get('label', '')
                twin_id = card.get('attributes', {}).get('digital_twin_id', '')
                print(f"  {i+1}. {label} (ID: {twin_id})")
        else:
            print("  ❌ 没有区域6卡牌")
        print()

def main():
    """主函数"""
    print("🔍 Region Transitioner 调用测试")
    print("=" * 60)
    
    test_region_6_cards()
    
    print("💡 分析:")
    print("如果所有帧都有区域6卡牌，但处理日志中没有看到'🔧 区域6流转源分析'，")
    print("说明_handle_special_transitions_to_6方法没有被调用。")
    print()
    print("可能的原因:")
    print("1. process_transitions方法中的调用被跳过")
    print("2. previous_cards为None")
    print("3. 方法调用有异常")

if __name__ == "__main__":
    main()
    sys.exit(0)

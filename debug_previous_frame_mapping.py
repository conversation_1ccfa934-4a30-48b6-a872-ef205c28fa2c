#!/usr/bin/env python3
"""
调试previous_frame_mapping的内容，检查为什么3→6继承没有工作
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def simulate_previous_frame_mapping(frame_data: Dict[str, Any]) -> Dict[tuple, List[Dict[str, Any]]]:
    """模拟SimpleInheritor创建previous_frame_mapping的过程"""
    mapping = {}
    
    if not frame_data or 'shapes' not in frame_data:
        return mapping
    
    for shape in frame_data['shapes']:
        region_id = shape.get('group_id')
        label = shape.get('label', '')
        twin_id = get_digital_twin_id(shape)
        
        if region_id is not None and label and twin_id != 'None':
            key = (region_id, label)
            if key not in mapping:
                mapping[key] = []
            mapping[key].append(shape)
    
    return mapping

def analyze_frame_360_mapping():
    """分析frame_00360的映射情况"""
    print("🔍 分析frame_00360的previous_frame_mapping")
    print("="*80)
    
    frame_360_data = load_frame_data(360)
    if not frame_360_data:
        return
    
    # 模拟创建previous_frame_mapping
    mapping = simulate_previous_frame_mapping(frame_360_data)
    
    print(f"📋 Frame_00360映射总数: {len(mapping)}个")
    
    # 查找所有柒类卡牌
    qi_mappings = []
    for key, cards in mapping.items():
        region_id, label = key
        if '柒' in label:
            qi_mappings.append((key, cards))
    
    print(f"\n🎯 柒类卡牌映射: {len(qi_mappings)}个")
    for key, cards in qi_mappings:
        region_id, label = key
        print(f"  键: {key} (区域{region_id}, 标签'{label}')")
        for i, card in enumerate(cards):
            twin_id = get_digital_twin_id(card)
            print(f"    卡牌{i+1}: ID={twin_id}")
    
    # 特别检查区域3
    region_3_mappings = []
    for key, cards in mapping.items():
        region_id, label = key
        if region_id == 3:
            region_3_mappings.append((key, cards))
    
    print(f"\n📋 区域3映射: {len(region_3_mappings)}个")
    for key, cards in region_3_mappings:
        region_id, label = key
        print(f"  键: {key} (区域{region_id}, 标签'{label}')")
        for i, card in enumerate(cards):
            twin_id = get_digital_twin_id(card)
            print(f"    卡牌{i+1}: ID={twin_id}")
    
    # 检查(3, '2柒')键是否存在
    target_key = (3, '2柒')
    if target_key in mapping:
        print(f"\n✅ 找到目标键: {target_key}")
        cards = mapping[target_key]
        for i, card in enumerate(cards):
            twin_id = get_digital_twin_id(card)
            print(f"  卡牌{i+1}: ID={twin_id}")
    else:
        print(f"\n❌ 未找到目标键: {target_key}")
        
        # 检查可能的替代键
        possible_keys = []
        for key in mapping.keys():
            region_id, label = key
            if region_id == 3 and '柒' in label:
                possible_keys.append(key)
        
        if possible_keys:
            print(f"🔍 区域3中的柒类替代键: {possible_keys}")
        else:
            print("🔍 区域3中没有柒类卡牌")

def analyze_frame_361_current():
    """分析frame_00361的当前卡牌"""
    print("\n🔍 分析frame_00361的当前卡牌")
    print("="*80)
    
    frame_361_data = load_frame_data(361)
    if not frame_361_data:
        return
    
    # 查找区域6的柒类卡牌
    region_6_cards = extract_region_cards(frame_361_data, 6)
    qi_cards = [card for card in region_6_cards if '柒' in card.get('label', '')]
    
    print(f"📋 Frame_00361区域6中的柒类卡牌: {len(qi_cards)}张")
    for i, card in enumerate(qi_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  卡牌{i+1}: 标签={label}, ID={twin_id}")
    
    # 查找所有区域的柒类卡牌
    all_qi_cards = []
    for shape in frame_361_data.get('shapes', []):
        if '柒' in shape.get('label', ''):
            all_qi_cards.append(shape)
    
    print(f"\n📋 Frame_00361所有区域的柒类卡牌: {len(all_qi_cards)}张")
    for i, card in enumerate(all_qi_cards):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        region_id = card.get('group_id', 'None')
        print(f"  卡牌{i+1}: 标签={label}, ID={twin_id}, 区域={region_id}")

def check_base_label_extraction():
    """检查基础标签提取逻辑"""
    print("\n🔍 检查基础标签提取逻辑")
    print("="*80)
    
    test_labels = ['2柒', '1柒', '柒', '3柒']
    
    def extract_base_label(label: str) -> str:
        """模拟SimpleInheritor的_extract_base_label方法"""
        if not label:
            return ''
        
        # 移除数字前缀
        import re
        base_label = re.sub(r'^\d+', '', label)
        return base_label
    
    print("📋 基础标签提取测试:")
    for label in test_labels:
        base_label = extract_base_label(label)
        print(f"  '{label}' → '{base_label}'")
    
    # 检查匹配逻辑
    print("\n📋 匹配逻辑测试:")
    current_label = '1柒'
    current_base = extract_base_label(current_label)
    
    for prev_label in ['2柒', '1柒', '3柒']:
        prev_base = extract_base_label(prev_label)
        match = (current_base == prev_base)
        print(f"  当前'{current_label}'({current_base}) vs 前一帧'{prev_label}'({prev_base}) → {'✅匹配' if match else '❌不匹配'}")

def main():
    """主函数"""
    print("🔍 调试previous_frame_mapping内容")
    print("="*80)
    print("检查为什么3→6继承没有工作")
    print("="*80)
    
    # 分析frame_00360的映射
    analyze_frame_360_mapping()
    
    # 分析frame_00361的当前状态
    analyze_frame_361_current()
    
    # 检查基础标签提取逻辑
    check_base_label_extraction()
    
    print("\n📊 总结")
    print("="*80)
    print("通过以上分析，我们可以确定3→6继承失败的具体原因")

if __name__ == "__main__":
    main()

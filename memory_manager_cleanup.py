#!/usr/bin/env python3
"""
Memory Manager相关文件清理脚本
删除与老版本数字孪生功能相关的memory_manager依赖文件
确保项目符合当前模块化数字孪生ID设计
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict
from datetime import datetime

class MemoryManagerCleanup:
    """Memory Manager相关文件清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "archive" / "memory_manager_cleanup"
        self.cleanup_log = []
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def analyze_memory_manager_dependencies(self):
        """分析memory_manager相关的依赖文件"""
        print("🔍 分析memory_manager相关依赖文件")
        print("-" * 50)
        
        # 需要删除的文件列表
        files_to_remove = [
            # 1. 直接依赖memory_manager的核心文件
            "src/core/enhanced_detector.py",  # 第22行导入memory_manager
            
            # 2. 记忆机制相关的测试文件
            "archive/development_tests/test_memory_mechanism.py",
            "tests/validate_memory_improvement.py",
            
            # 3. 记忆机制相关的分析工具
            "tools/analysis/memory_impact_analyzer.py",
            
            # 4. 与老版本数字孪生系统相关的文档和API引用
            # API_REFERENCE.md 中的记忆机制部分需要手动清理
        ]
        
        # 需要修改的文件（移除memory_manager引用）
        files_to_modify = [
            "API_REFERENCE.md",  # 删除记忆机制管理器部分
            "core_cleanup_manager.py",  # 删除memory_manager.py的期望
        ]
        
        # 检查文件存在性
        existing_files = []
        missing_files = []
        
        for file_path in files_to_remove:
            full_path = self.project_root / file_path
            if full_path.exists():
                existing_files.append(full_path)
                print(f"  ✅ 找到: {file_path}")
            else:
                missing_files.append(file_path)
                print(f"  ⚠️ 不存在: {file_path}")
        
        print(f"\n📊 分析结果:")
        print(f"  需要删除的文件: {len(files_to_remove)}")
        print(f"  实际存在: {len(existing_files)}")
        print(f"  已不存在: {len(missing_files)}")
        print(f"  需要修改: {len(files_to_modify)}")
        
        return {
            "existing_files": existing_files,
            "missing_files": missing_files,
            "files_to_modify": files_to_modify
        }
    
    def backup_and_remove_files(self, files_to_remove: List[Path]):
        """备份并删除文件"""
        print("\n🗑️ 备份并删除memory_manager相关文件")
        
        for file_path in files_to_remove:
            try:
                # 创建备份
                relative_path = file_path.relative_to(self.project_root)
                backup_path = self.backup_dir / relative_path
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制到备份目录
                shutil.copy2(str(file_path), str(backup_path))
                
                # 删除原文件
                file_path.unlink()
                
                print(f"  ✅ 已删除: {relative_path}")
                
                self.cleanup_log.append({
                    "action": "removed",
                    "source": str(relative_path),
                    "backup": str(backup_path),
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                print(f"  ❌ 删除失败: {file_path} - {e}")
                self.cleanup_log.append({
                    "action": "failed",
                    "source": str(file_path),
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
    
    def clean_api_reference(self):
        """清理API_REFERENCE.md中的记忆机制部分"""
        print("\n📝 清理API_REFERENCE.md中的记忆机制引用")
        
        api_ref_path = self.project_root / "API_REFERENCE.md"
        if not api_ref_path.exists():
            print("  ⚠️ API_REFERENCE.md 不存在")
            return
        
        try:
            with open(api_ref_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原文件
            backup_path = self.backup_dir / "API_REFERENCE.md.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 删除记忆机制管理器部分 (第101-127行)
            lines = content.split('\n')
            
            # 找到记忆机制部分的开始和结束
            start_idx = None
            end_idx = None
            
            for i, line in enumerate(lines):
                if "### 🔄 记忆机制管理器" in line:
                    start_idx = i
                elif start_idx is not None and line.startswith("### ") and "记忆机制" not in line:
                    end_idx = i
                    break
            
            if start_idx is not None:
                if end_idx is None:
                    # 如果是最后一个部分，删除到文件末尾
                    end_idx = len(lines)
                
                # 删除记忆机制部分
                new_lines = lines[:start_idx] + lines[end_idx:]
                new_content = '\n'.join(new_lines)
                
                with open(api_ref_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  ✅ 已删除记忆机制管理器部分 (行 {start_idx+1}-{end_idx})")
                
                self.cleanup_log.append({
                    "action": "modified",
                    "file": "API_REFERENCE.md",
                    "change": f"删除记忆机制管理器部分 (行 {start_idx+1}-{end_idx})",
                    "backup": str(backup_path),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                print("  ⚠️ 未找到记忆机制管理器部分")
                
        except Exception as e:
            print(f"  ❌ 修改失败: {e}")
    
    def clean_core_cleanup_manager(self):
        """清理core_cleanup_manager.py中的memory_manager期望"""
        print("\n📝 清理core_cleanup_manager.py中的memory_manager引用")
        
        cleanup_manager_path = self.project_root / "core_cleanup_manager.py"
        if not cleanup_manager_path.exists():
            print("  ⚠️ core_cleanup_manager.py 不存在")
            return
        
        try:
            with open(cleanup_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原文件
            backup_path = self.backup_dir / "core_cleanup_manager.py.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 删除memory_manager.py的期望
            new_content = content.replace('"memory_manager.py",', '')
            new_content = new_content.replace('"memory_manager.py"', '')
            
            with open(cleanup_manager_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("  ✅ 已删除memory_manager.py的期望")
            
            self.cleanup_log.append({
                "action": "modified",
                "file": "core_cleanup_manager.py",
                "change": "删除memory_manager.py期望",
                "backup": str(backup_path),
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            print(f"  ❌ 修改失败: {e}")
    
    def verify_modular_compatibility(self):
        """验证与模块化设计的兼容性"""
        print("\n🔍 验证与模块化数字孪生ID设计的兼容性")
        
        # 检查模块化架构文件
        modular_files = [
            "src/modules/data_validator.py",
            "src/modules/basic_id_assigner.py", 
            "src/modules/simple_inheritor.py",
            "src/modules/region2_processor.py",
            "src/modules/region_transitioner.py",
            "src/modules/dark_card_processor.py",
            "src/modules/occlusion_compensator.py",
            "src/modules/phase1_integrator.py",
            "src/modules/phase2_integrator.py",
        ]
        
        all_present = True
        for module_file in modular_files:
            module_path = self.project_root / module_file
            if module_path.exists():
                print(f"  ✅ {module_file}")
            else:
                print(f"  ❌ 缺失: {module_file}")
                all_present = False
        
        if all_present:
            print("  🎉 模块化架构完整！")
        else:
            print("  ⚠️ 模块化架构不完整")
        
        return all_present
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = self.backup_dir / "memory_manager_cleanup_report.json"
        
        report = {
            "cleanup_date": datetime.now().isoformat(),
            "cleanup_reason": "删除与老版本数字孪生功能相关的memory_manager依赖",
            "modular_design_compliance": "确保项目符合当前模块化数字孪生ID设计",
            "backup_location": str(self.backup_dir),
            "actions": self.cleanup_log,
            "summary": {
                "total_actions": len(self.cleanup_log),
                "files_removed": len([log for log in self.cleanup_log if log["action"] == "removed"]),
                "files_modified": len([log for log in self.cleanup_log if log["action"] == "modified"]),
                "failed_operations": len([log for log in self.cleanup_log if log["action"] == "failed"])
            }
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            import json
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已生成: {report_path}")
        return report

def main():
    """主函数"""
    print("🚀 Memory Manager相关文件清理开始")
    print("=" * 60)
    print("目标：删除与老版本数字孪生功能相关的memory_manager依赖")
    print("确保项目符合当前模块化数字孪生ID设计")
    print("=" * 60)
    
    cleanup = MemoryManagerCleanup()
    
    # 分析依赖
    analysis = cleanup.analyze_memory_manager_dependencies()
    
    # 确认清理
    if analysis["existing_files"]:
        print(f"\n⚠️ 将要删除 {len(analysis['existing_files'])} 个文件")
        for file_path in analysis["existing_files"]:
            print(f"  - {file_path.relative_to(cleanup.project_root)}")
        
        response = input("\n是否继续清理? (y/N): ")
        if response.lower() != 'y':
            print("清理已取消")
            return
    
    # 执行清理
    cleanup.backup_and_remove_files(analysis["existing_files"])
    cleanup.clean_api_reference()
    cleanup.clean_core_cleanup_manager()
    
    # 验证兼容性
    cleanup.verify_modular_compatibility()
    
    # 生成报告
    report = cleanup.generate_cleanup_report()
    
    print("\n" + "=" * 60)
    print("🎉 Memory Manager清理完成！")
    print(f"📊 删除文件: {report['summary']['files_removed']}")
    print(f"📝 修改文件: {report['summary']['files_modified']}")
    print(f"❌ 失败操作: {report['summary']['failed_operations']}")
    print(f"📁 备份位置: {cleanup.backup_dir}")
    print("\n✅ 项目现在完全符合模块化数字孪生ID设计！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
阈值优化器

通过测试不同的置信度和IoU阈值，找到与AnyLabeling推理效果最接近的最佳阈值。
"""

import os
import sys
import cv2
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
import numpy as np

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.core.detect import CardDetector
from src.core.state_builder import format_detections_for_state_builder


class ThresholdOptimizer:
    """阈值优化器"""
    
    def __init__(self, 
                 model_path: str = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.pt",
                 test_images_dir: str = "legacy_assets/ceshi/calibration_gt/images",
                 reference_labels_dir: str = "legacy_assets/ceshi/calibration_gt/labels"):
        """
        初始化优化器
        
        Args:
            model_path: YOLO模型路径
            test_images_dir: 测试图像目录
            reference_labels_dir: 参考标注目录
        """
        self.model_path = model_path
        self.test_images_dir = test_images_dir
        self.reference_labels_dir = reference_labels_dir
        
        # 测试的阈值范围
        self.conf_thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4]
        self.iou_thresholds = [0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6]
        
        print(f"🔧 阈值优化器初始化完成")
        print(f"   - 模型: {model_path}")
        print(f"   - 测试图像: {test_images_dir}")
        print(f"   - 参考标注: {reference_labels_dir}")
        print(f"   - 置信度阈值范围: {self.conf_thresholds}")
        print(f"   - IoU阈值范围: {self.iou_thresholds}")
    
    def optimize_thresholds(self, max_test_images: int = 20) -> Dict[str, Any]:
        """
        优化阈值设置
        
        Args:
            max_test_images: 最大测试图像数
            
        Returns:
            优化结果
        """
        print(f"🚀 开始阈值优化...")
        
        # 收集测试图像
        test_images = self._collect_test_images(max_test_images)
        print(f"📊 使用 {len(test_images)} 张图像进行测试")
        
        # 加载参考标注
        reference_annotations = self._load_reference_annotations(test_images)
        
        # 测试所有阈值组合
        optimization_results = []
        total_combinations = len(self.conf_thresholds) * len(self.iou_thresholds)
        
        print(f"🔬 测试 {total_combinations} 种阈值组合...")
        
        for i, conf_thresh in enumerate(self.conf_thresholds):
            for j, iou_thresh in enumerate(self.iou_thresholds):
                combination_idx = i * len(self.iou_thresholds) + j + 1
                
                print(f"   测试组合 {combination_idx}/{total_combinations}: conf={conf_thresh}, iou={iou_thresh}")
                
                # 测试当前阈值组合
                result = self._test_threshold_combination(
                    conf_thresh, iou_thresh, test_images, reference_annotations
                )
                
                optimization_results.append(result)
        
        # 分析结果并找到最佳阈值
        best_result = self._analyze_optimization_results(optimization_results)
        
        # 生成优化报告
        report = self._generate_optimization_report(optimization_results, best_result)
        
        print(f"✅ 阈值优化完成")
        return report
    
    def _collect_test_images(self, max_images: int) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        for filename in sorted(os.listdir(self.test_images_dir))[:max_images]:
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                test_images.append(filename)
        
        return test_images
    
    def _load_reference_annotations(self, test_images: List[str]) -> Dict[str, List[Dict]]:
        """加载参考标注"""
        reference_annotations = {}
        
        for image_filename in test_images:
            json_filename = Path(image_filename).stem + '.json'
            json_path = os.path.join(self.reference_labels_dir, json_filename)
            
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                detections = []
                for shape in data.get('shapes', []):
                    points = shape.get('points', [])
                    if len(points) == 4:
                        xs = [p[0] for p in points]
                        ys = [p[1] for p in points]
                        x, y = min(xs), min(ys)
                        w, h = max(xs) - x, max(ys) - y
                        
                        detection = {
                            'label': shape.get('label', ''),
                            'bbox': [x, y, w, h],
                            'confidence': shape.get('score', 1.0)
                        }
                        detections.append(detection)
                
                reference_annotations[image_filename] = detections
        
        return reference_annotations
    
    def _test_threshold_combination(self, conf_thresh: float, iou_thresh: float,
                                  test_images: List[str], 
                                  reference_annotations: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """测试特定阈值组合"""
        
        # 创建检测器
        detector = CardDetector(
            model_path=self.model_path,
            conf_threshold=conf_thresh,
            iou_threshold=iou_thresh
        )
        
        # 统计结果
        total_detections = 0
        total_reference = 0
        total_matches = 0
        total_missing = 0
        total_false = 0
        
        for image_filename in test_images:
            if image_filename not in reference_annotations:
                continue
            
            # 读取图像
            image_path = os.path.join(self.test_images_dir, image_filename)
            image = cv2.imread(image_path)
            
            if image is None:
                continue
            
            # 检测
            detections = detector.detect_image(image)
            filtered_detections = [d for d in detections if d.get('label', '') != 'background']
            
            # 获取参考标注
            reference = reference_annotations[image_filename]
            
            # 计算匹配情况
            matches, missing, false = self._calculate_matches(filtered_detections, reference)
            
            total_detections += len(filtered_detections)
            total_reference += len(reference)
            total_matches += matches
            total_missing += missing
            total_false += false
        
        # 计算指标
        recall = total_matches / total_reference if total_reference > 0 else 0
        precision = total_matches / total_detections if total_detections > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'conf_threshold': conf_thresh,
            'iou_threshold': iou_thresh,
            'total_detections': total_detections,
            'total_reference': total_reference,
            'total_matches': total_matches,
            'total_missing': total_missing,
            'total_false': total_false,
            'recall': recall,
            'precision': precision,
            'f1_score': f1_score
        }
    
    def _calculate_matches(self, detections: List[Dict], reference: List[Dict]) -> Tuple[int, int, int]:
        """计算匹配、漏检、误检数量"""
        matches = 0
        used_reference = set()
        used_detections = set()
        
        # 找匹配
        for i, detection in enumerate(detections):
            best_match_idx = -1
            best_iou = 0
            
            for j, ref in enumerate(reference):
                if j in used_reference:
                    continue
                
                iou = self._calculate_iou(detection['bbox'], ref['bbox'])
                if iou > best_iou and iou > 0.3:
                    best_iou = iou
                    best_match_idx = j
            
            if best_match_idx >= 0:
                used_reference.add(best_match_idx)
                used_detections.add(i)
                matches += 1
        
        missing = len(reference) - len(used_reference)
        false = len(detections) - len(used_detections)
        
        return matches, missing, false
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _analyze_optimization_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析优化结果"""
        
        # 按不同指标排序
        best_f1 = max(results, key=lambda x: x['f1_score'])
        best_recall = max(results, key=lambda x: x['recall'])
        best_precision = max(results, key=lambda x: x['precision'])
        
        # 找到平衡点（F1分数最高且召回率>0.8）
        balanced_candidates = [r for r in results if r['recall'] > 0.8]
        best_balanced = max(balanced_candidates, key=lambda x: x['f1_score']) if balanced_candidates else best_f1
        
        return {
            'best_f1': best_f1,
            'best_recall': best_recall,
            'best_precision': best_precision,
            'best_balanced': best_balanced,
            'recommended': best_balanced  # 推荐使用平衡点
        }
    
    def _generate_optimization_report(self, results: List[Dict[str, Any]], 
                                    best_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成优化报告"""
        
        # 按F1分数排序，显示前10个结果
        sorted_results = sorted(results, key=lambda x: x['f1_score'], reverse=True)
        
        report = {
            'optimization_summary': {
                'total_combinations_tested': len(results),
                'best_f1_score': best_result['best_f1']['f1_score'],
                'best_recall': best_result['best_recall']['recall'],
                'best_precision': best_result['best_precision']['precision']
            },
            'recommended_thresholds': {
                'conf_threshold': best_result['recommended']['conf_threshold'],
                'iou_threshold': best_result['recommended']['iou_threshold'],
                'expected_recall': best_result['recommended']['recall'],
                'expected_precision': best_result['recommended']['precision'],
                'expected_f1_score': best_result['recommended']['f1_score']
            },
            'top_10_results': sorted_results[:10],
            'threshold_analysis': {
                'conf_threshold_impact': self._analyze_threshold_impact(results, 'conf_threshold'),
                'iou_threshold_impact': self._analyze_threshold_impact(results, 'iou_threshold')
            }
        }
        
        # 保存报告
        report_path = "output/threshold_optimization_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _analyze_threshold_impact(self, results: List[Dict[str, Any]], threshold_key: str) -> Dict[str, Any]:
        """分析阈值影响"""
        threshold_groups = {}
        
        for result in results:
            threshold_value = result[threshold_key]
            if threshold_value not in threshold_groups:
                threshold_groups[threshold_value] = []
            threshold_groups[threshold_value].append(result)
        
        impact_analysis = {}
        for threshold_value, group_results in threshold_groups.items():
            avg_recall = np.mean([r['recall'] for r in group_results])
            avg_precision = np.mean([r['precision'] for r in group_results])
            avg_f1 = np.mean([r['f1_score'] for r in group_results])
            
            impact_analysis[threshold_value] = {
                'avg_recall': avg_recall,
                'avg_precision': avg_precision,
                'avg_f1_score': avg_f1,
                'count': len(group_results)
            }
        
        return impact_analysis


def main():
    """主函数"""
    print("🔧 阈值优化器")
    print("=" * 50)
    
    # 创建优化器
    optimizer = ThresholdOptimizer()
    
    # 优化阈值
    report = optimizer.optimize_thresholds(max_test_images=20)
    
    # 打印结果
    print("\n📊 优化结果:")
    print(f"   测试组合数: {report['optimization_summary']['total_combinations_tested']}")
    print(f"   最佳F1分数: {report['optimization_summary']['best_f1_score']:.3f}")
    print(f"   最佳召回率: {report['optimization_summary']['best_recall']:.3f}")
    print(f"   最佳精确率: {report['optimization_summary']['best_precision']:.3f}")
    
    print("\n🎯 推荐阈值:")
    recommended = report['recommended_thresholds']
    print(f"   置信度阈值: {recommended['conf_threshold']}")
    print(f"   IoU阈值: {recommended['iou_threshold']}")
    print(f"   预期召回率: {recommended['expected_recall']:.1%}")
    print(f"   预期精确率: {recommended['expected_precision']:.1%}")
    print(f"   预期F1分数: {recommended['expected_f1_score']:.3f}")
    
    print("\n🏆 前5个最佳组合:")
    for i, result in enumerate(report['top_10_results'][:5]):
        print(f"   {i+1}. conf={result['conf_threshold']}, iou={result['iou_threshold']}: "
              f"F1={result['f1_score']:.3f}, 召回={result['recall']:.1%}, 精确={result['precision']:.1%}")
    
    print(f"\n📁 详细报告已保存: output/threshold_optimization_report.json")


if __name__ == "__main__":
    main()

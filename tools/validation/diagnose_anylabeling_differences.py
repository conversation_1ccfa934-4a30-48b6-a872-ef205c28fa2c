#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AnyLabeling差异诊断器

诊断我们的脚本与AnyLabeling推理差异的可能原因：
1. ONNX vs PyTorch模型
2. FP16半精度影响
3. 数据清洗过滤
4. 设备和精度设置
"""

import os
import sys
import cv2
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
import numpy as np
import torch

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from ultralytics import YOLO
from src.core.detect import CardDetector


class AnyLabelingDifferenceDiagnoser:
    """AnyLabeling差异诊断器"""
    
    def __init__(self):
        """初始化诊断器"""
        self.pt_model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.pt"
        self.onnx_model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.onnx"
        self.test_images_dir = "legacy_assets/ceshi/calibration_gt/images"
        
        print(f"🔍 AnyLabeling差异诊断器初始化")
        print(f"   - PyTorch模型: {self.pt_model_path}")
        print(f"   - ONNX模型: {self.onnx_model_path}")
        print(f"   - 测试图像: {self.test_images_dir}")
    
    def diagnose_differences(self, max_test_images: int = 5) -> Dict[str, Any]:
        """诊断差异"""
        print(f"🚀 开始诊断AnyLabeling差异...")
        
        # 收集测试图像
        test_images = self._collect_test_images(max_test_images)
        print(f"📊 使用 {len(test_images)} 张图像进行诊断")
        
        diagnosis_results = {}
        
        # 1. 测试不同模型格式
        print(f"\n🔬 测试1: 模型格式差异 (PyTorch vs ONNX)")
        diagnosis_results['model_format'] = self._test_model_formats(test_images)
        
        # 2. 测试精度设置
        print(f"\n🔬 测试2: 精度设置差异 (FP32 vs FP16)")
        diagnosis_results['precision'] = self._test_precision_settings(test_images)
        
        # 3. 测试数据清洗影响
        print(f"\n🔬 测试3: 数据清洗影响")
        diagnosis_results['data_cleaning'] = self._test_data_cleaning_impact(test_images)
        
        # 4. 测试设备和优化设置
        print(f"\n🔬 测试4: 设备和优化设置")
        diagnosis_results['device_optimization'] = self._test_device_optimization(test_images)
        
        # 5. 测试极低阈值
        print(f"\n🔬 测试5: 极低阈值设置")
        diagnosis_results['ultra_low_thresholds'] = self._test_ultra_low_thresholds(test_images)
        
        # 生成诊断报告
        report = self._generate_diagnosis_report(diagnosis_results)
        
        print(f"✅ AnyLabeling差异诊断完成")
        return report
    
    def _collect_test_images(self, max_images: int) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        for filename in sorted(os.listdir(self.test_images_dir))[:max_images]:
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                test_images.append(os.path.join(self.test_images_dir, filename))
        
        return test_images
    
    def _test_model_formats(self, test_images: List[str]) -> Dict[str, Any]:
        """测试不同模型格式"""
        print(f"   测试PyTorch vs ONNX模型...")
        
        results = {
            'pytorch_results': [],
            'onnx_results': [],
            'comparison': {}
        }
        
        # 测试PyTorch模型
        if os.path.exists(self.pt_model_path):
            pt_model = YOLO(self.pt_model_path)
            for image_path in test_images:
                image = cv2.imread(image_path)
                pt_result = pt_model(image, conf=0.1, iou=0.3, verbose=False)[0]
                detection_count = len(pt_result.boxes) if pt_result.boxes is not None else 0
                results['pytorch_results'].append({
                    'image': os.path.basename(image_path),
                    'detection_count': detection_count,
                    'confidences': pt_result.boxes.conf.tolist() if pt_result.boxes is not None else []
                })
        
        # 测试ONNX模型
        if os.path.exists(self.onnx_model_path):
            try:
                onnx_model = YOLO(self.onnx_model_path)
                for image_path in test_images:
                    image = cv2.imread(image_path)
                    onnx_result = onnx_model(image, conf=0.1, iou=0.3, verbose=False)[0]
                    detection_count = len(onnx_result.boxes) if onnx_result.boxes is not None else 0
                    results['onnx_results'].append({
                        'image': os.path.basename(image_path),
                        'detection_count': detection_count,
                        'confidences': onnx_result.boxes.conf.tolist() if onnx_result.boxes is not None else []
                    })
            except Exception as e:
                print(f"   ⚠️ ONNX模型测试失败: {e}")
                results['onnx_error'] = str(e)
        
        # 对比结果
        if results['pytorch_results'] and results['onnx_results']:
            pt_total = sum(r['detection_count'] for r in results['pytorch_results'])
            onnx_total = sum(r['detection_count'] for r in results['onnx_results'])
            results['comparison'] = {
                'pytorch_total_detections': pt_total,
                'onnx_total_detections': onnx_total,
                'difference': onnx_total - pt_total,
                'difference_percentage': (onnx_total - pt_total) / pt_total * 100 if pt_total > 0 else 0
            }
        
        return results
    
    def _test_precision_settings(self, test_images: List[str]) -> Dict[str, Any]:
        """测试精度设置"""
        print(f"   测试FP32 vs FP16精度...")
        
        results = {
            'fp32_results': [],
            'fp16_results': [],
            'comparison': {}
        }
        
        model = YOLO(self.pt_model_path)
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            
            # FP32测试
            with torch.cuda.amp.autocast(enabled=False):
                fp32_result = model(image, conf=0.1, iou=0.3, verbose=False)[0]
                fp32_count = len(fp32_result.boxes) if fp32_result.boxes is not None else 0
                results['fp32_results'].append({
                    'image': os.path.basename(image_path),
                    'detection_count': fp32_count,
                    'confidences': fp32_result.boxes.conf.tolist() if fp32_result.boxes is not None else []
                })
            
            # FP16测试
            if torch.cuda.is_available():
                try:
                    with torch.cuda.amp.autocast(enabled=True):
                        fp16_result = model(image, conf=0.1, iou=0.3, verbose=False)[0]
                        fp16_count = len(fp16_result.boxes) if fp16_result.boxes is not None else 0
                        results['fp16_results'].append({
                            'image': os.path.basename(image_path),
                            'detection_count': fp16_count,
                            'confidences': fp16_result.boxes.conf.tolist() if fp16_result.boxes is not None else []
                        })
                except Exception as e:
                    print(f"   ⚠️ FP16测试失败: {e}")
                    results['fp16_error'] = str(e)
        
        # 对比结果
        if results['fp32_results'] and results['fp16_results']:
            fp32_total = sum(r['detection_count'] for r in results['fp32_results'])
            fp16_total = sum(r['detection_count'] for r in results['fp16_results'])
            results['comparison'] = {
                'fp32_total_detections': fp32_total,
                'fp16_total_detections': fp16_total,
                'difference': fp16_total - fp32_total,
                'difference_percentage': (fp16_total - fp32_total) / fp32_total * 100 if fp32_total > 0 else 0
            }
        
        return results
    
    def _test_data_cleaning_impact(self, test_images: List[str]) -> Dict[str, Any]:
        """测试数据清洗影响"""
        print(f"   测试数据清洗前后差异...")
        
        results = {
            'without_cleaning': [],
            'with_cleaning': [],
            'comparison': {}
        }
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            
            # 不使用数据清洗
            detector_no_clean = CardDetector(
                model_path=self.pt_model_path,
                conf_threshold=0.1,
                iou_threshold=0.3,
                enable_validation=False  # 关闭数据清洗
            )
            no_clean_detections = detector_no_clean.detect_image(image)
            results['without_cleaning'].append({
                'image': os.path.basename(image_path),
                'detection_count': len(no_clean_detections),
                'confidences': [d['confidence'] for d in no_clean_detections]
            })
            
            # 使用数据清洗
            detector_with_clean = CardDetector(
                model_path=self.pt_model_path,
                conf_threshold=0.1,
                iou_threshold=0.3,
                enable_validation=True  # 启用数据清洗
            )
            with_clean_detections = detector_with_clean.detect_image(image)
            results['with_cleaning'].append({
                'image': os.path.basename(image_path),
                'detection_count': len(with_clean_detections),
                'confidences': [d['confidence'] for d in with_clean_detections]
            })
        
        # 对比结果
        no_clean_total = sum(r['detection_count'] for r in results['without_cleaning'])
        with_clean_total = sum(r['detection_count'] for r in results['with_cleaning'])
        results['comparison'] = {
            'without_cleaning_total': no_clean_total,
            'with_cleaning_total': with_clean_total,
            'filtered_out': no_clean_total - with_clean_total,
            'filter_rate': (no_clean_total - with_clean_total) / no_clean_total * 100 if no_clean_total > 0 else 0
        }
        
        return results
    
    def _test_device_optimization(self, test_images: List[str]) -> Dict[str, Any]:
        """测试设备和优化设置"""
        print(f"   测试设备和优化设置...")
        
        results = {
            'cpu_results': [],
            'gpu_results': [],
            'comparison': {}
        }
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            
            # CPU测试
            model_cpu = YOLO(self.pt_model_path)
            cpu_result = model_cpu(image, conf=0.1, iou=0.3, device='cpu', verbose=False)[0]
            cpu_count = len(cpu_result.boxes) if cpu_result.boxes is not None else 0
            results['cpu_results'].append({
                'image': os.path.basename(image_path),
                'detection_count': cpu_count
            })
            
            # GPU测试
            if torch.cuda.is_available():
                model_gpu = YOLO(self.pt_model_path)
                gpu_result = model_gpu(image, conf=0.1, iou=0.3, device='cuda', verbose=False)[0]
                gpu_count = len(gpu_result.boxes) if gpu_result.boxes is not None else 0
                results['gpu_results'].append({
                    'image': os.path.basename(image_path),
                    'detection_count': gpu_count
                })
        
        # 对比结果
        cpu_total = sum(r['detection_count'] for r in results['cpu_results'])
        gpu_total = sum(r['detection_count'] for r in results['gpu_results']) if results['gpu_results'] else 0
        results['comparison'] = {
            'cpu_total_detections': cpu_total,
            'gpu_total_detections': gpu_total,
            'difference': gpu_total - cpu_total,
            'gpu_available': torch.cuda.is_available()
        }
        
        return results
    
    def _test_ultra_low_thresholds(self, test_images: List[str]) -> Dict[str, Any]:
        """测试极低阈值设置"""
        print(f"   测试极低阈值设置...")
        
        threshold_configs = [
            {'conf': 0.05, 'iou': 0.2},
            {'conf': 0.01, 'iou': 0.1},
            {'conf': 0.001, 'iou': 0.05}
        ]
        
        results = {}
        
        for config in threshold_configs:
            conf_thresh = config['conf']
            iou_thresh = config['iou']
            config_name = f"conf_{conf_thresh}_iou_{iou_thresh}"
            
            config_results = []
            model = YOLO(self.pt_model_path)
            
            for image_path in test_images:
                image = cv2.imread(image_path)
                result = model(image, conf=conf_thresh, iou=iou_thresh, verbose=False)[0]
                detection_count = len(result.boxes) if result.boxes is not None else 0
                config_results.append({
                    'image': os.path.basename(image_path),
                    'detection_count': detection_count
                })
            
            total_detections = sum(r['detection_count'] for r in config_results)
            results[config_name] = {
                'config': config,
                'results': config_results,
                'total_detections': total_detections
            }
        
        return results
    
    def _generate_diagnosis_report(self, diagnosis_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成诊断报告"""
        
        # 分析最可能的原因
        likely_causes = []
        
        # 检查模型格式差异
        if 'model_format' in diagnosis_results and 'comparison' in diagnosis_results['model_format']:
            comp = diagnosis_results['model_format']['comparison']
            if abs(comp.get('difference_percentage', 0)) > 10:
                likely_causes.append({
                    'cause': 'ONNX vs PyTorch模型差异',
                    'evidence': f"检测数量差异: {comp.get('difference_percentage', 0):.1f}%",
                    'recommendation': '使用ONNX模型进行推理'
                })
        
        # 检查数据清洗影响
        if 'data_cleaning' in diagnosis_results and 'comparison' in diagnosis_results['data_cleaning']:
            comp = diagnosis_results['data_cleaning']['comparison']
            if comp.get('filter_rate', 0) > 20:
                likely_causes.append({
                    'cause': '数据清洗过滤过多',
                    'evidence': f"过滤率: {comp.get('filter_rate', 0):.1f}%",
                    'recommendation': '关闭数据验证和清洗 (enable_validation=False)'
                })
        
        # 检查精度影响
        if 'precision' in diagnosis_results and 'comparison' in diagnosis_results['precision']:
            comp = diagnosis_results['precision']['comparison']
            if abs(comp.get('difference_percentage', 0)) > 5:
                likely_causes.append({
                    'cause': 'FP16精度影响',
                    'evidence': f"精度差异: {comp.get('difference_percentage', 0):.1f}%",
                    'recommendation': '使用FP32精度或调整阈值'
                })
        
        report = {
            'diagnosis_summary': {
                'total_tests_performed': len(diagnosis_results),
                'likely_causes_found': len(likely_causes),
                'most_likely_cause': likely_causes[0] if likely_causes else None
            },
            'detailed_results': diagnosis_results,
            'likely_causes': likely_causes,
            'recommendations': self._generate_recommendations(likely_causes, diagnosis_results)
        }
        
        # 保存报告
        report_path = "output/anylabeling_difference_diagnosis.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _generate_recommendations(self, likely_causes: List[Dict], 
                                diagnosis_results: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        if likely_causes:
            for cause in likely_causes:
                recommendations.append(cause['recommendation'])
        
        # 基于极低阈值测试的建议
        if 'ultra_low_thresholds' in diagnosis_results:
            ultra_results = diagnosis_results['ultra_low_thresholds']
            max_detections = 0
            best_config = None
            
            for config_name, config_data in ultra_results.items():
                if config_data['total_detections'] > max_detections:
                    max_detections = config_data['total_detections']
                    best_config = config_data['config']
            
            if best_config:
                recommendations.append(
                    f"尝试极低阈值: conf={best_config['conf']}, iou={best_config['iou']}"
                )
        
        return recommendations


def main():
    """主函数"""
    print("🔍 AnyLabeling差异诊断器")
    print("=" * 50)
    
    # 创建诊断器
    diagnoser = AnyLabelingDifferenceDiagnoser()
    
    # 执行诊断
    report = diagnoser.diagnose_differences(max_test_images=5)
    
    # 打印结果
    print("\n📊 诊断结果汇总:")
    summary = report['diagnosis_summary']
    print(f"   执行测试数: {summary['total_tests_performed']}")
    print(f"   发现可能原因: {summary['likely_causes_found']}")
    
    if summary['most_likely_cause']:
        cause = summary['most_likely_cause']
        print(f"\n🎯 最可能的原因:")
        print(f"   原因: {cause['cause']}")
        print(f"   证据: {cause['evidence']}")
        print(f"   建议: {cause['recommendation']}")
    
    print(f"\n💡 修复建议:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"   {i}. {rec}")
    
    print(f"\n📁 详细诊断报告: output/anylabeling_difference_diagnosis.json")


if __name__ == "__main__":
    main()

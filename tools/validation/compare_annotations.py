#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
标注对比分析器

对比原始calibration_gt标注和增强标注的差异，
提供快速的准确性评估和误差分析。
"""

import os
import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import matplotlib.pyplot as plt


class AnnotationComparator:
    """标注对比分析器"""
    
    def __init__(self, 
                 original_path: str = "legacy_assets/ceshi/calibration_gt/labels",
                 enhanced_path: str = "legacy_assets/ceshi/calibration_gt_enhanced/labels"):
        """
        初始化对比器
        
        Args:
            original_path: 原始标注路径
            enhanced_path: 增强标注路径
        """
        self.original_path = original_path
        self.enhanced_path = enhanced_path
        
        self.comparison_results = {
            'total_files': 0,
            'compared_files': 0,
            'detection_accuracy': 0,
            'label_accuracy': 0,
            'position_accuracy': 0,
            'detailed_results': []
        }
        
        print(f"📊 标注对比分析器初始化")
        print(f"   - 原始标注: {original_path}")
        print(f"   - 增强标注: {enhanced_path}")
    
    def compare_all_annotations(self, sample_size: int = None) -> Dict[str, Any]:
        """对比所有标注文件"""
        print(f"🔍 开始标注对比分析...")
        
        # 获取所有JSON文件
        original_files = set(f for f in os.listdir(self.original_path) if f.endswith('.json'))
        enhanced_files = set(f for f in os.listdir(self.enhanced_path) if f.endswith('.json'))
        
        # 找到共同文件
        common_files = list(original_files & enhanced_files)
        
        if sample_size:
            common_files = common_files[:sample_size]
        
        self.comparison_results['total_files'] = len(common_files)
        
        print(f"📋 找到 {len(common_files)} 个共同文件")
        
        # 对比每个文件
        for i, filename in enumerate(common_files):
            try:
                result = self._compare_single_file(filename)
                self.comparison_results['detailed_results'].append(result)
                self.comparison_results['compared_files'] += 1
                
                # 显示进度
                if (i + 1) % 20 == 0:
                    progress = (i + 1) / len(common_files) * 100
                    print(f"   进度: {i+1}/{len(common_files)} ({progress:.1f}%)")
                    
            except Exception as e:
                print(f"❌ 对比失败: {filename} - {e}")
        
        # 计算总体指标
        self._calculate_overall_metrics()
        
        # 生成对比报告
        report = self._generate_comparison_report()
        
        print(f"✅ 对比分析完成")
        return report
    
    def _compare_single_file(self, filename: str) -> Dict[str, Any]:
        """对比单个文件"""
        # 读取原始标注
        original_path = os.path.join(self.original_path, filename)
        with open(original_path, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 读取增强标注
        enhanced_path = os.path.join(self.enhanced_path, filename)
        with open(enhanced_path, 'r', encoding='utf-8') as f:
            enhanced_data = json.load(f)
        
        # 提取检测结果
        original_detections = self._extract_detections(original_data)
        enhanced_detections = self._extract_detections(enhanced_data)
        
        # 计算匹配结果
        matches = self._match_detections(original_detections, enhanced_detections)
        
        # 计算指标
        detection_accuracy = len(matches) / len(original_detections) if original_detections else 0
        label_accuracy = sum(1 for m in matches if m['label_match']) / len(matches) if matches else 0
        position_accuracy = np.mean([m['iou'] for m in matches]) if matches else 0
        
        return {
            'filename': filename,
            'original_count': len(original_detections),
            'enhanced_count': len(enhanced_detections),
            'matched_count': len(matches),
            'detection_accuracy': detection_accuracy,
            'label_accuracy': label_accuracy,
            'position_accuracy': position_accuracy,
            'matches': matches,
            'memory_info': enhanced_data.get('enhanced_metadata', {})
        }
    
    def _extract_detections(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取检测结果"""
        detections = []
        
        for shape in data.get('shapes', []):
            # 提取边界框
            points = shape.get('points', [])
            if len(points) == 4:
                # 转换为[x, y, w, h]格式
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                x, y = min(xs), min(ys)
                w, h = max(xs) - x, max(ys) - y
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [x, y, w, h],
                    'confidence': shape.get('score', 1.0),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', ''),
                    'attributes': shape.get('attributes', {})
                }
                detections.append(detection)
        
        return detections
    
    def _match_detections(self, original: List[Dict], enhanced: List[Dict]) -> List[Dict]:
        """匹配检测结果"""
        matches = []
        used_enhanced = set()
        
        for orig in original:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, enh in enumerate(enhanced):
                if i in used_enhanced:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], enh['bbox'])
                if iou > best_iou and iou > 0.5:  # IoU阈值
                    best_iou = iou
                    best_match = enh
                    best_idx = i
            
            if best_match:
                used_enhanced.add(best_idx)
                matches.append({
                    'original': orig,
                    'enhanced': best_match,
                    'iou': best_iou,
                    'label_match': orig['label'] == best_match['label'],
                    'confidence_diff': best_match['confidence'] - orig.get('confidence', 1.0)
                })
        
        return matches
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _calculate_overall_metrics(self) -> None:
        """计算总体指标"""
        if not self.comparison_results['detailed_results']:
            return
        
        results = self.comparison_results['detailed_results']
        
        # 检测准确率
        total_original = sum(r['original_count'] for r in results)
        total_matched = sum(r['matched_count'] for r in results)
        self.comparison_results['detection_accuracy'] = total_matched / total_original if total_original > 0 else 0
        
        # 标签准确率
        all_matches = []
        for r in results:
            all_matches.extend(r['matches'])
        
        if all_matches:
            self.comparison_results['label_accuracy'] = sum(1 for m in all_matches if m['label_match']) / len(all_matches)
            self.comparison_results['position_accuracy'] = np.mean([m['iou'] for m in all_matches])
        else:
            self.comparison_results['label_accuracy'] = 0
            self.comparison_results['position_accuracy'] = 0
    
    def _generate_comparison_report(self) -> Dict[str, Any]:
        """生成对比报告"""
        results = self.comparison_results['detailed_results']
        
        # 统计信息
        total_original = sum(r['original_count'] for r in results)
        total_enhanced = sum(r['enhanced_count'] for r in results)
        total_matched = sum(r['matched_count'] for r in results)
        
        # 记忆机制统计
        memory_recoveries = sum(1 for r in results 
                              if r['memory_info'].get('statistics', {}).get('recovered_count', 0) > 0)
        
        # 错误分析
        label_errors = []
        position_errors = []
        
        for r in results:
            for match in r['matches']:
                if not match['label_match']:
                    label_errors.append({
                        'file': r['filename'],
                        'original': match['original']['label'],
                        'enhanced': match['enhanced']['label']
                    })
                
                if match['iou'] < 0.8:  # 位置误差阈值
                    position_errors.append({
                        'file': r['filename'],
                        'iou': match['iou']
                    })
        
        report = {
            'summary': {
                'compared_files': self.comparison_results['compared_files'],
                'detection_accuracy': self.comparison_results['detection_accuracy'],
                'label_accuracy': self.comparison_results['label_accuracy'],
                'position_accuracy': self.comparison_results['position_accuracy'],
                'overall_score': (self.comparison_results['detection_accuracy'] + 
                                self.comparison_results['label_accuracy'] + 
                                self.comparison_results['position_accuracy']) / 3
            },
            'statistics': {
                'total_original_detections': total_original,
                'total_enhanced_detections': total_enhanced,
                'total_matched_detections': total_matched,
                'detection_difference': total_enhanced - total_original,
                'files_with_memory_recovery': memory_recoveries
            },
            'error_analysis': {
                'label_errors': len(label_errors),
                'position_errors': len(position_errors),
                'label_error_rate': len(label_errors) / total_matched if total_matched > 0 else 0,
                'position_error_rate': len(position_errors) / total_matched if total_matched > 0 else 0
            },
            'detailed_errors': {
                'label_errors': label_errors[:10],  # 只保存前10个
                'position_errors': position_errors[:10]
            },
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_path = "output/annotation_comparison_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        detection_acc = self.comparison_results['detection_accuracy']
        label_acc = self.comparison_results['label_accuracy']
        position_acc = self.comparison_results['position_accuracy']
        
        if detection_acc > 0.9:
            recommendations.append("检测准确率优秀(>90%)，系统检测能力可靠")
        elif detection_acc > 0.8:
            recommendations.append("检测准确率良好(>80%)，可考虑优化检测阈值")
        else:
            recommendations.append("检测准确率需要改进(<80%)，建议检查模型和参数")
        
        if label_acc > 0.95:
            recommendations.append("标签识别准确率优秀(>95%)，标签分类效果很好")
        elif label_acc > 0.9:
            recommendations.append("标签识别准确率良好(>90%)，可进一步优化相似标签区分")
        else:
            recommendations.append("标签识别准确率需要改进(<90%)，建议增强标签分类训练")
        
        if position_acc > 0.8:
            recommendations.append("位置准确率优秀(>80% IoU)，边界框定位精确")
        elif position_acc > 0.7:
            recommendations.append("位置准确率良好(>70% IoU)，可优化边界框回归")
        else:
            recommendations.append("位置准确率需要改进(<70% IoU)，建议检查边界框预测")
        
        return recommendations
    
    def generate_quick_summary(self, sample_size: int = 50) -> str:
        """生成快速摘要"""
        print(f"🚀 生成快速对比摘要 (样本数: {sample_size})...")
        
        report = self.compare_all_annotations(sample_size)
        
        summary = f"""
📊 标注对比分析摘要 (样本: {sample_size}张)

🎯 总体准确性:
   检测准确率: {report['summary']['detection_accuracy']:.1%}
   标签准确率: {report['summary']['label_accuracy']:.1%}
   位置准确率: {report['summary']['position_accuracy']:.1%}
   综合得分: {report['summary']['overall_score']:.1%}

📈 统计信息:
   原始检测数: {report['statistics']['total_original_detections']}
   增强检测数: {report['statistics']['total_enhanced_detections']}
   匹配检测数: {report['statistics']['total_matched_detections']}
   检测差异: {report['statistics']['detection_difference']:+d}

❌ 错误分析:
   标签错误: {report['error_analysis']['label_errors']}个 ({report['error_analysis']['label_error_rate']:.1%})
   位置错误: {report['error_analysis']['position_errors']}个 ({report['error_analysis']['position_error_rate']:.1%})

💡 建议:
"""
        
        for rec in report['recommendations']:
            summary += f"   • {rec}\n"
        
        return summary


def main():
    """主函数"""
    print("📊 标注对比分析器")
    print("=" * 50)
    
    # 创建对比器
    comparator = AnnotationComparator()
    
    # 生成快速摘要
    summary = comparator.generate_quick_summary(sample_size=50)
    print(summary)
    
    # 询问是否进行完整对比
    print("\n❓ 是否进行完整对比分析? (需要更长时间)")
    print("   输入 'y' 进行完整分析，其他键跳过")
    
    # 这里简化处理，直接进行完整分析
    print("🔍 进行完整对比分析...")
    full_report = comparator.compare_all_annotations()
    
    print(f"\n📋 完整分析结果:")
    print(f"   对比文件数: {full_report['summary']['compared_files']}")
    print(f"   检测准确率: {full_report['summary']['detection_accuracy']:.1%}")
    print(f"   标签准确率: {full_report['summary']['label_accuracy']:.1%}")
    print(f"   位置准确率: {full_report['summary']['position_accuracy']:.1%}")
    print(f"   综合得分: {full_report['summary']['overall_score']:.1%}")
    
    print(f"\n📁 详细报告已保存: output/annotation_comparison_report.json")


if __name__ == "__main__":
    main()

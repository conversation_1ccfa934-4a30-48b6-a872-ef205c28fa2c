"""
修正数字孪生系统的卡牌过滤逻辑

问题：当前系统对所有输入检测都进行处理，包括非卡牌类别
解决方案：在数字孪生系统的process_frame方法中添加卡牌类别过滤逻辑

修正内容：
1. 在DigitalTwinCoordinator.process_frame中添加输入过滤
2. 只处理21个有效卡牌类别
3. 过滤掉"吃"、"碰"、"胡"、"打鸟选择"等非卡牌类别
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class CardFilteringFixer:
    """卡牌过滤逻辑修正器"""
    
    def __init__(self):
        self.digital_twin_file = Path("src/core/digital_twin_v2.py")
        
        # 定义21个有效卡牌类别
        self.valid_card_categories = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "暗",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        }
    
    def is_valid_card_label(self, label: str) -> bool:
        """判断是否是有效的卡牌标签"""
        if not label:
            return False
        
        # 处理带数字前缀的标签（如"1八"、"2四"）
        import re
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            card_name = match.group(2)
        else:
            card_name = label
        
        return card_name in self.valid_card_categories
    
    def add_card_filtering_logic(self) -> bool:
        """在数字孪生系统中添加卡牌过滤逻辑"""
        print("🔧 在数字孪生系统中添加卡牌过滤逻辑...")
        
        if not self.digital_twin_file.exists():
            print(f"❌ 文件未找到: {self.digital_twin_file}")
            return False
        
        try:
            # 读取文件内容
            with open(self.digital_twin_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经添加了过滤逻辑
            if "_filter_valid_card_detections" in content:
                print("✅ 卡牌过滤逻辑已存在")
                return True
            
            # 添加过滤方法
            filter_method = '''
    def _filter_valid_card_detections(self, detections: List[CardDetection]) -> List[CardDetection]:
        """过滤出有效的卡牌检测，排除非卡牌类别"""
        valid_card_categories = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "暗",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        }
        
        def is_valid_card_label(label: str) -> bool:
            if not label:
                return False
            
            # 处理带数字前缀的标签（如"1八"、"2四"）
            import re
            match = re.match(r'^(\\d+)(.+)$', label)
            if match:
                card_name = match.group(2)
            else:
                card_name = label
            
            return card_name in valid_card_categories
        
        filtered_detections = []
        filtered_count = 0
        
        for detection in detections:
            if is_valid_card_label(detection.label):
                filtered_detections.append(detection)
            else:
                filtered_count += 1
                logger.debug(f"过滤非卡牌类别: {detection.label}")
        
        if filtered_count > 0:
            logger.info(f"过滤了{filtered_count}个非卡牌类别，保留{len(filtered_detections)}个有效卡牌")
        
        return filtered_detections
'''
            
            # 在process_frame方法开始处添加过滤逻辑
            process_frame_start = 'def process_frame(self, detections: List[CardDetection]) -> Dict[str, Any]:'
            
            if process_frame_start in content:
                # 找到process_frame方法的位置
                start_pos = content.find(process_frame_start)
                if start_pos != -1:
                    # 找到方法体开始的位置
                    method_body_start = content.find('"""', start_pos)
                    if method_body_start != -1:
                        method_body_start = content.find('"""', method_body_start + 3) + 3
                        
                        # 在方法体开始处添加过滤逻辑
                        filter_call = '''
        
        # 过滤出有效的卡牌检测
        detections = self._filter_valid_card_detections(detections)
        
        if not detections:
            logger.info("没有有效的卡牌检测，跳过处理")
            return {
                "digital_twin_cards": [],
                "consensus_score": 1.0,
                "frame_statistics": {"total_cards": 0, "new_cards": 0, "updated_cards": 0},
                "system_status": "no_valid_cards"
            }
'''
                        
                        # 插入过滤调用
                        content = content[:method_body_start] + filter_call + content[method_body_start:]
                        
                        # 在类的末尾添加过滤方法
                        class_end = content.rfind("def get_session_statistics")
                        if class_end != -1:
                            content = content[:class_end] + filter_method + "\n    " + content[class_end:]
                        
                        # 保存修改后的文件
                        with open(self.digital_twin_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        print("✅ 卡牌过滤逻辑添加成功")
                        return True
            
            print("❌ 未找到process_frame方法")
            return False
            
        except Exception as e:
            print(f"❌ 添加过滤逻辑时出错: {e}")
            return False
    
    def test_filtering_logic(self) -> bool:
        """测试过滤逻辑是否正常工作"""
        print("\n🧪 测试卡牌过滤逻辑...")
        
        try:
            from src.core.digital_twin_v2 import CardDetection, create_digital_twin_system
            
            # 创建测试数据
            test_detections = [
                CardDetection("一", [100, 100, 150, 150], 0.9, 0, "unknown", "test"),
                CardDetection("二", [200, 100, 250, 150], 0.9, 0, "unknown", "test"),
                CardDetection("吃", [100, 300, 150, 350], 0.9, 0, "unknown", "test"),
                CardDetection("碰", [200, 300, 250, 350], 0.9, 0, "unknown", "test"),
                CardDetection("打鸟选择", [100, 400, 200, 450], 0.9, 0, "unknown", "test"),
            ]
            
            # 创建系统并测试
            dt_system = create_digital_twin_system()
            result = dt_system.process_frame(test_detections)
            
            # 检查结果
            output_cards = result["digital_twin_cards"]
            
            print(f"📊 测试结果:")
            print(f"  输入检测数: {len(test_detections)}")
            print(f"  输出卡牌数: {len(output_cards)}")
            
            # 检查是否只有有效卡牌被处理
            valid_labels = {"一", "二"}
            invalid_labels = {"吃", "碰", "打鸟选择"}
            
            output_labels = {card.label for card in output_cards}
            
            has_valid = any(label in output_labels for label in valid_labels)
            has_invalid = any(label in output_labels for label in invalid_labels)
            
            if has_valid and not has_invalid:
                print("✅ 过滤逻辑工作正常：只处理有效卡牌，过滤非卡牌类别")
                return True
            elif has_invalid:
                print("❌ 过滤逻辑失效：仍在处理非卡牌类别")
                print(f"  发现的非卡牌类别: {output_labels & invalid_labels}")
                return False
            else:
                print("⚠️ 没有检测到任何输出，可能存在其他问题")
                return False
                
        except Exception as e:
            print(f"❌ 测试过滤逻辑时出错: {e}")
            return False
    
    def run_complete_fix(self) -> bool:
        """运行完整的修正流程"""
        print("🚀 修正数字孪生系统的卡牌过滤逻辑")
        print("=" * 50)
        
        # 1. 添加过滤逻辑
        add_success = self.add_card_filtering_logic()
        
        if not add_success:
            print("❌ 添加过滤逻辑失败")
            return False
        
        # 2. 测试过滤逻辑
        test_success = self.test_filtering_logic()
        
        if test_success:
            print("\n🎉 卡牌过滤逻辑修正成功！")
            print("✅ 系统现在只会处理21个有效卡牌类别")
            print("✅ 非卡牌类别（如'吃'、'碰'、'胡'、'打鸟选择'等）将被过滤")
            return True
        else:
            print("\n⚠️ 过滤逻辑添加完成，但测试未通过，需要进一步检查")
            return False

def main():
    """主修正函数"""
    fixer = CardFilteringFixer()
    success = fixer.run_complete_fix()
    
    if success:
        print("\n📋 后续建议:")
        print("1. 重新运行区域分配逻辑检查脚本验证修正效果")
        print("2. 使用修正后的系统重新训练和验证")
        print("3. 确保所有测试都通过")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

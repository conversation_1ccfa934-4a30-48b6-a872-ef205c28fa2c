"""
使用增强calibration_gt数据集重新训练多算法融合分类器

利用包含1408张卡牌（97.4%有区域信息）的增强数据集重新训练分类器，
目标将区域分配准确率从90%提升到95%+。

训练策略：
1. 使用calibration_gt_enhanced作为主要训练数据
2. 使用zhuangtaiquyu作为验证数据
3. 优化融合权重和算法参数
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import Counter
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.multi_algorithm_region_classifier import MultiAlgorithmRegionClassifier

class EnhancedDatasetTrainer:
    """增强数据集训练器"""
    
    def __init__(self):
        self.enhanced_dataset_path = Path("legacy_assets/ceshi/calibration_gt_enhanced/labels")
        self.validation_dataset_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        self.classifier = MultiAlgorithmRegionClassifier()
        
    def collect_training_data_from_enhanced_dataset(self, max_samples: int = 1200) -> int:
        """从增强数据集收集训练数据"""
        print("📊 从calibration_gt_enhanced收集训练数据...")
        
        if not self.enhanced_dataset_path.exists():
            print("❌ 增强数据集未找到")
            return 0
        
        json_files = list(self.enhanced_dataset_path.glob("*.json"))
        print(f"📁 发现 {len(json_files)} 个JSON文件")
        
        collected_samples = 0
        region_distribution = Counter()
        
        for json_file in json_files:
            if collected_samples >= max_samples:
                break
                
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_width = data.get("imageWidth", 640)
                image_height = data.get("imageHeight", 320)
                
                for shape in data.get("shapes", []):
                    if collected_samples >= max_samples:
                        break
                        
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        bbox = [x1, y1, x2, y2]
                        
                        group_id = shape.get("group_id")
                        if group_id is not None and group_id != 0:
                            self.classifier.add_training_data(bbox, group_id, image_width, image_height)
                            collected_samples += 1
                            region_distribution[group_id] += 1
                            
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        print(f"✅ 收集完成，共{collected_samples}个训练样本")
        print("📊 区域分布:")
        for region_id, count in region_distribution.most_common():
            print(f"  区域{region_id}: {count}张卡牌")
        
        return collected_samples
    
    def train_enhanced_classifier(self) -> bool:
        """训练增强分类器"""
        print("\n🚀 训练增强多算法融合分类器...")
        
        # 收集训练数据
        samples_collected = self.collect_training_data_from_enhanced_dataset()
        
        if samples_collected < 100:
            print("❌ 训练数据不足，无法训练")
            return False
        
        # 训练分类器
        try:
            self.classifier.train_ml_classifier()
            print("✅ 分类器训练完成")
            return True
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            return False
    
    def validate_on_zhuangtaiquyu(self) -> Dict[str, Any]:
        """在zhuangtaiquyu数据集上验证"""
        print("\n🔍 在zhuangtaiquyu数据集上验证...")
        
        if not self.validation_dataset_path.exists():
            print("❌ 验证数据集未找到")
            return {"error": "validation_dataset_not_found"}
        
        sequence_dirs = [d for d in self.validation_dataset_path.iterdir() if d.is_dir()]
        sequence_dirs = sorted(sequence_dirs, key=lambda x: int(x.name) if x.name.isdigit() else 0)
        
        total_cards = 0
        correct_predictions = 0
        region_errors = []
        
        # 验证前3个序列
        for seq_dir in sequence_dirs[:3]:
            print(f"  📁 验证序列: {seq_dir.name}")
            
            json_files = sorted(seq_dir.glob("*.json"))[:5]  # 每序列前5帧
            
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    image_width = data.get("imageWidth", 640)
                    image_height = data.get("imageHeight", 320)
                    
                    for shape in data.get("shapes", []):
                        if len(shape.get("points", [])) >= 4:
                            points = shape["points"]
                            x1, y1 = points[0]
                            x2, y2 = points[2]
                            bbox = [x1, y1, x2, y2]
                            
                            true_region = shape.get("group_id")
                            if true_region is None:
                                continue
                            
                            # 使用分类器预测
                            predicted_region, confidence = self.classifier.classify_region(
                                bbox, image_width, image_height
                            )
                            
                            total_cards += 1
                            
                            if predicted_region == true_region:
                                correct_predictions += 1
                            else:
                                region_errors.append({
                                    "file": json_file.name,
                                    "sequence": seq_dir.name,
                                    "true_region": true_region,
                                    "predicted_region": predicted_region,
                                    "confidence": confidence,
                                    "label": shape.get("label", "")
                                })
                
                except Exception as e:
                    print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        # 计算准确率
        accuracy = correct_predictions / total_cards if total_cards > 0 else 0
        
        results = {
            "total_cards": total_cards,
            "correct_predictions": correct_predictions,
            "accuracy": accuracy,
            "region_errors": region_errors[:20],  # 只保存前20个错误
            "error_count": len(region_errors)
        }
        
        print(f"📊 验证结果:")
        print(f"  总卡牌数: {total_cards}")
        print(f"  正确预测: {correct_predictions}")
        print(f"  准确率: {accuracy:.1%}")
        print(f"  错误数: {len(region_errors)}")
        
        return results
    
    def analyze_improvement(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析改进效果"""
        print("\n📈 分析改进效果...")
        
        current_accuracy = validation_results.get("accuracy", 0)
        
        # 与之前的结果对比
        previous_results = {
            "v2_accuracy": 0.914,  # V2.0版本
            "multi_algorithm_accuracy": 0.900  # 多算法融合版本
        }
        
        improvement_analysis = {
            "current_accuracy": current_accuracy,
            "vs_v2_improvement": current_accuracy - previous_results["v2_accuracy"],
            "vs_multi_algorithm_improvement": current_accuracy - previous_results["multi_algorithm_accuracy"],
            "target_95_gap": 0.95 - current_accuracy,
            "performance_level": self._get_performance_level(current_accuracy)
        }
        
        print(f"📊 改进分析:")
        print(f"  当前准确率: {current_accuracy:.1%}")
        print(f"  相比V2.0: {improvement_analysis['vs_v2_improvement']:+.1%}")
        print(f"  相比多算法融合: {improvement_analysis['vs_multi_algorithm_improvement']:+.1%}")
        print(f"  距离95%目标: {improvement_analysis['target_95_gap']:.1%}")
        print(f"  性能等级: {improvement_analysis['performance_level']}")
        
        return improvement_analysis
    
    def _get_performance_level(self, accuracy: float) -> str:
        """获取性能等级"""
        if accuracy >= 0.95:
            return "优秀 (95%+)"
        elif accuracy >= 0.92:
            return "良好 (92%+)"
        elif accuracy >= 0.88:
            return "可接受 (88%+)"
        else:
            return "需要改进 (<88%)"
    
    def generate_optimization_recommendations(self, validation_results: Dict, improvement_analysis: Dict) -> List[str]:
        """生成优化建议"""
        print("\n💡 生成优化建议...")
        
        recommendations = []
        current_accuracy = improvement_analysis["current_accuracy"]
        
        # 基于准确率水平的建议
        if current_accuracy >= 0.95:
            recommendations.append("🎉 已达到95%目标！可以考虑进一步优化ID分配算法")
        elif current_accuracy >= 0.92:
            recommendations.append("🔧 接近目标，建议微调融合权重和特征工程")
        else:
            recommendations.append("⚠️ 需要更多改进，考虑增加训练数据或改进算法")
        
        # 基于错误分析的建议
        error_count = validation_results.get("error_count", 0)
        total_cards = validation_results.get("total_cards", 1)
        error_rate = error_count / total_cards
        
        if error_rate > 0.1:
            recommendations.append("📊 错误率较高，建议分析具体错误模式并针对性优化")
        
        # 基于改进幅度的建议
        vs_previous = improvement_analysis.get("vs_multi_algorithm_improvement", 0)
        if vs_previous > 0.02:
            recommendations.append("✅ 显著改进！建议保存当前模型配置")
        elif vs_previous < -0.01:
            recommendations.append("❌ 性能下降，建议检查训练数据质量")
        
        # 通用建议
        recommendations.extend([
            "🔄 使用AnyLabeling审核calibration_gt_enhanced中的区域分配",
            "📈 收集更多边界模糊情况的训练数据",
            "🧪 尝试不同的融合权重配置"
        ])
        
        print("建议的优化方向:")
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        return recommendations
    
    def run_complete_retraining(self) -> Dict[str, Any]:
        """运行完整的重新训练流程"""
        print("🚀 使用增强数据集重新训练多算法融合分类器")
        print("=" * 60)
        
        # 1. 训练分类器
        training_success = self.train_enhanced_classifier()
        
        if not training_success:
            return {"error": "training_failed"}
        
        # 2. 验证性能
        validation_results = self.validate_on_zhuangtaiquyu()
        
        if "error" in validation_results:
            return validation_results
        
        # 3. 分析改进效果
        improvement_analysis = self.analyze_improvement(validation_results)
        
        # 4. 生成优化建议
        recommendations = self.generate_optimization_recommendations(
            validation_results, improvement_analysis
        )
        
        # 保存结果
        results = {
            "retraining_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "training_success": training_success,
            "validation_results": validation_results,
            "improvement_analysis": improvement_analysis,
            "optimization_recommendations": recommendations,
            "algorithm_stats": self.classifier.get_algorithm_statistics()
        }
        
        output_path = "analysis/enhanced_retraining_results.json"
        os.makedirs("analysis", exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 重新训练结果已保存: {output_path}")
        
        return results

def main():
    """主训练函数"""
    trainer = EnhancedDatasetTrainer()
    results = trainer.run_complete_retraining()
    
    if "error" in results:
        print(f"❌ 重新训练失败: {results['error']}")
        return False
    
    # 判断成功程度
    accuracy = results["improvement_analysis"]["current_accuracy"]
    if accuracy >= 0.95:
        print("\n🎉 重新训练成功！达到95%+目标！")
        return True
    elif accuracy >= 0.92:
        print("\n✅ 重新训练成功！性能显著提升！")
        return True
    else:
        print("\n⚠️ 重新训练完成，但仍需进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

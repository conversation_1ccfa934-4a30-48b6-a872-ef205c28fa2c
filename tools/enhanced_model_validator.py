#!/usr/bin/env python3
"""
增强模型验证器

解决路径问题，增加验证深度，模拟AnyLabeling环境下的对比测试
专门验证用户在AnyLabeling中观察到的现象：
"相同置信度下，老版本模型表现更好"
"""

import sys
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import logging
from datetime import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedModelValidator:
    """增强模型验证器"""
    
    def __init__(self):
        self.current_model_path = "best.pt"
        self.old_model_path = "data/processed/train3.0/weights/best.pt"
        
        # 扩展测试配置 - 模拟AnyLabeling的参数范围
        self.test_configs = [
            {'conf': 0.1, 'iou': 0.45, 'name': '低置信度'},
            {'conf': 0.2, 'iou': 0.45, 'name': '中低置信度'},
            {'conf': 0.25, 'iou': 0.45, 'name': 'AnyLabeling默认'},
            {'conf': 0.3, 'iou': 0.45, 'name': '中等置信度'},
            {'conf': 0.4, 'iou': 0.45, 'name': '中高置信度'},
            {'conf': 0.5, 'iou': 0.45, 'name': '高置信度'},
            {'conf': 0.25, 'iou': 0.3, 'name': '宽松IoU'},
            {'conf': 0.25, 'iou': 0.6, 'name': '严格IoU'},
        ]
        
        self.validation_results = {
            'test_summary': {},
            'model_comparison': {},
            'anylabeling_simulation': {},
            'detailed_analysis': {},
            'fusion_recommendations': {}
        }
        
    def load_models(self) -> bool:
        """加载模型"""
        try:
            from ultralytics import YOLO
            
            # 加载当前模型
            if os.path.exists(self.current_model_path):
                self.current_model = YOLO(self.current_model_path)
                logger.info(f"当前模型加载成功: {len(self.current_model.names)} 个类别")
            else:
                logger.error(f"当前模型文件不存在: {self.current_model_path}")
                return False
                
            # 加载老版本模型
            if os.path.exists(self.old_model_path):
                self.old_model = YOLO(self.old_model_path)
                logger.info(f"老版本模型加载成功: {len(self.old_model.names)} 个类别")
            else:
                logger.error(f"老版本模型文件不存在: {self.old_model_path}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
            
    def collect_comprehensive_test_data(self) -> List[Tuple[str, str]]:
        """收集全面的测试数据"""
        test_pairs = []
        
        # 1. calibration_gt数据（高质量人工审核数据）
        calibration_path = Path("legacy_assets/ceshi/calibration_gt")
        if calibration_path.exists():
            images_dir = calibration_path / "images"
            labels_dir = calibration_path / "labels"
            
            if images_dir.exists() and labels_dir.exists():
                for img_file in images_dir.glob("*.jpg"):
                    json_file = labels_dir / f"{img_file.stem}.json"
                    if json_file.exists():
                        test_pairs.append((str(img_file), str(json_file)))
                        
        logger.info(f"从calibration_gt收集到 {len(test_pairs)} 个高质量样本")
        
        # 2. zhuangtaiquyu数据（大规模数据）
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
        if zhuangtaiquyu_path.exists():
            labels_train_path = zhuangtaiquyu_path / "labels" / "train"
            images_train_path = zhuangtaiquyu_path / "images" / "train"
            
            if labels_train_path.exists() and images_train_path.exists():
                zhuangtaiquyu_pairs = []
                for region_dir in labels_train_path.iterdir():
                    if region_dir.is_dir():
                        for json_file in region_dir.glob("*.json"):
                            img_file = images_train_path / region_dir.name / f"{json_file.stem}.jpg"
                            if img_file.exists():
                                zhuangtaiquyu_pairs.append((str(img_file), str(json_file)))
                                
                # 随机选择一部分zhuangtaiquyu数据
                import random
                random.shuffle(zhuangtaiquyu_pairs)
                selected_zhuangtaiquyu = zhuangtaiquyu_pairs[:100]  # 选择100个样本
                test_pairs.extend(selected_zhuangtaiquyu)
                
        logger.info(f"总共收集到 {len(test_pairs)} 个测试样本")
        return test_pairs
        
    def run_model_inference_with_timing(self, model, image_path: str, config: Dict) -> Tuple[List[Dict], float]:
        """运行模型推理并计时"""
        start_time = time.time()
        
        try:
            results = model(image_path, conf=config['conf'], iou=config['iou'], verbose=False)
            
            detections = []
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    classes = result.boxes.cls.cpu().numpy()
                    
                    for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                        class_name = model.names[int(cls)]
                        
                        detection = {
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls),
                            'class_name': class_name,
                            'area': (box[2] - box[0]) * (box[3] - box[1])
                        }
                        detections.append(detection)
                        
            inference_time = time.time() - start_time
            return detections, inference_time
            
        except Exception as e:
            logger.error(f"模型推理失败: {e}")
            return [], 0.0
            
    def load_ground_truth_robust(self, json_file: str) -> List[Dict]:
        """鲁棒的真实标注加载"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            ground_truth = []
            for shape in data.get('shapes', []):
                if len(shape.get('points', [])) >= 4:
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[2]
                    
                    # 确保坐标顺序正确
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    gt = {
                        'bbox': [x1, y1, x2, y2],
                        'label': shape.get('label', ''),
                        'area': (x2 - x1) * (y2 - y1),
                        'group_id': shape.get('group_id', 1)
                    }
                    ground_truth.append(gt)
                    
            return ground_truth
            
        except Exception as e:
            logger.error(f"加载真实标注失败 {json_file}: {e}")
            return []
            
    def calculate_comprehensive_metrics(self, detections: List[Dict], ground_truth: List[Dict], iou_threshold: float = 0.5) -> Dict:
        """计算全面的评估指标"""
        if not ground_truth:
            return {
                'precision': 0, 'recall': 0, 'f1': 0, 
                'detection_count': len(detections),
                'ap': 0, 'map': 0
            }
            
        # 匹配检测结果与真实标注
        matched_detections = []
        matched_gt = set()
        
        for i, detection in enumerate(detections):
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt in enumerate(ground_truth):
                iou = self.calculate_iou(detection['bbox'], gt['bbox'])
                if iou > best_iou and iou > iou_threshold:
                    best_iou = iou
                    best_gt_idx = j
                    
            if best_gt_idx >= 0:
                matched_detections.append({
                    'detection_idx': i,
                    'gt_idx': best_gt_idx,
                    'iou': best_iou,
                    'confidence': detection['confidence']
                })
                matched_gt.add(best_gt_idx)
                
        # 计算基础指标
        precision = len(matched_detections) / len(detections) if detections else 0
        recall = len(matched_gt) / len(ground_truth) if ground_truth else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        # 计算AP（平均精度）
        ap = self.calculate_ap(matched_detections, len(ground_truth))
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'detection_count': len(detections),
            'matched_count': len(matched_detections),
            'missed_count': len(ground_truth) - len(matched_gt),
            'false_positive_count': len(detections) - len(matched_detections),
            'ap': ap,
            'avg_iou': np.mean([m['iou'] for m in matched_detections]) if matched_detections else 0
        }
        
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
            
        inter_area = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
        
    def calculate_ap(self, matched_detections: List[Dict], total_gt: int) -> float:
        """计算平均精度（AP）"""
        if not matched_detections:
            return 0.0
            
        # 按置信度排序
        sorted_matches = sorted(matched_detections, key=lambda x: x['confidence'], reverse=True)
        
        # 计算precision-recall曲线
        precisions = []
        recalls = []
        
        for i in range(len(sorted_matches)):
            tp = i + 1
            fp = 0  # 这里简化，假设所有匹配都是真正例
            precision = tp / (tp + fp)
            recall = tp / total_gt
            
            precisions.append(precision)
            recalls.append(recall)
            
        # 计算AP（简化版本）
        if len(precisions) > 1:
            ap = np.trapz(precisions, recalls)
        else:
            ap = precisions[0] if precisions else 0.0
            
        return ap
        
    def run_comprehensive_validation(self) -> Dict:
        """运行全面验证"""
        logger.info("开始增强模型验证...")
        
        # 1. 加载模型
        if not self.load_models():
            return {}
            
        # 2. 收集测试数据
        test_pairs = self.collect_comprehensive_test_data()
        if not test_pairs:
            logger.error("未找到测试数据")
            return {}
            
        self.validation_results['test_summary'] = {
            'total_test_pairs': len(test_pairs),
            'test_configs': len(self.test_configs)
        }
        
        # 3. 对每个配置进行测试
        all_config_results = {}
        
        for config in self.test_configs:
            logger.info(f"测试配置: {config['name']} (conf={config['conf']}, iou={config['iou']})")
            
            config_results = {
                'config': config,
                'current_model_results': [],
                'old_model_results': [],
                'comparison_metrics': {}
            }
            
            # 测试所有图像
            for i, (image_path, json_path) in enumerate(test_pairs):
                if i % 20 == 0:
                    logger.info(f"  处理图像 {i+1}/{len(test_pairs)}")
                    
                # 加载真实标注
                ground_truth = self.load_ground_truth_robust(json_path)
                if not ground_truth:
                    continue
                    
                # 当前模型推理
                current_detections, current_time = self.run_model_inference_with_timing(
                    self.current_model, image_path, config
                )
                current_metrics = self.calculate_comprehensive_metrics(current_detections, ground_truth)
                current_metrics['inference_time'] = current_time
                config_results['current_model_results'].append(current_metrics)
                
                # 老版本模型推理
                old_detections, old_time = self.run_model_inference_with_timing(
                    self.old_model, image_path, config
                )
                old_metrics = self.calculate_comprehensive_metrics(old_detections, ground_truth)
                old_metrics['inference_time'] = old_time
                config_results['old_model_results'].append(old_metrics)
                
            # 计算配置级别的汇总指标
            config_results['comparison_metrics'] = self.calculate_config_summary(config_results)
            all_config_results[f"{config['name']}_{config['conf']}_{config['iou']}"] = config_results
            
        self.validation_results['model_comparison'] = all_config_results
        
        # 4. 特别分析AnyLabeling默认配置
        self.analyze_anylabeling_default()
        
        # 5. 生成融合建议
        self.generate_enhanced_fusion_recommendations()
        
        # 6. 保存结果
        self.save_enhanced_results()
        
        return self.validation_results
        
    def calculate_config_summary(self, config_results: Dict) -> Dict:
        """计算配置级别的汇总指标"""
        current_results = config_results['current_model_results']
        old_results = config_results['old_model_results']
        
        def avg_metric(results, metric_name):
            values = [r[metric_name] for r in results if metric_name in r]
            return np.mean(values) if values else 0
            
        summary = {
            'current_model': {
                'avg_precision': avg_metric(current_results, 'precision'),
                'avg_recall': avg_metric(current_results, 'recall'),
                'avg_f1': avg_metric(current_results, 'f1'),
                'avg_ap': avg_metric(current_results, 'ap'),
                'avg_detection_count': avg_metric(current_results, 'detection_count'),
                'avg_inference_time': avg_metric(current_results, 'inference_time')
            },
            'old_model': {
                'avg_precision': avg_metric(old_results, 'precision'),
                'avg_recall': avg_metric(old_results, 'recall'),
                'avg_f1': avg_metric(old_results, 'f1'),
                'avg_ap': avg_metric(old_results, 'ap'),
                'avg_detection_count': avg_metric(old_results, 'detection_count'),
                'avg_inference_time': avg_metric(old_results, 'inference_time')
            }
        }
        
        # 计算对比指标
        summary['comparison'] = {
            'recall_improvement': summary['old_model']['avg_recall'] - summary['current_model']['avg_recall'],
            'precision_difference': summary['old_model']['avg_precision'] - summary['current_model']['avg_precision'],
            'f1_improvement': summary['old_model']['avg_f1'] - summary['current_model']['avg_f1'],
            'detection_count_ratio': summary['old_model']['avg_detection_count'] / summary['current_model']['avg_detection_count'] if summary['current_model']['avg_detection_count'] > 0 else 0,
            'old_model_better_recall': summary['old_model']['avg_recall'] > summary['current_model']['avg_recall'],
            'old_model_better_f1': summary['old_model']['avg_f1'] > summary['current_model']['avg_f1']
        }
        
        return summary
        
    def analyze_anylabeling_default(self):
        """分析AnyLabeling默认配置下的表现"""
        # 查找AnyLabeling默认配置的结果
        anylabeling_key = None
        for key in self.validation_results['model_comparison'].keys():
            if 'AnyLabeling默认' in key:
                anylabeling_key = key
                break
                
        if anylabeling_key:
            anylabeling_results = self.validation_results['model_comparison'][anylabeling_key]
            comparison = anylabeling_results['comparison_metrics']['comparison']
            
            self.validation_results['anylabeling_simulation'] = {
                'config_used': anylabeling_results['config'],
                'user_claim_verification': {
                    'old_model_better_recall': comparison['old_model_better_recall'],
                    'recall_improvement': comparison['recall_improvement'],
                    'detection_count_ratio': comparison['detection_count_ratio'],
                    'claim_verified': comparison['old_model_better_recall'] and comparison['recall_improvement'] > 0.05
                },
                'detailed_metrics': anylabeling_results['comparison_metrics']
            }
            
    def generate_enhanced_fusion_recommendations(self):
        """生成增强的融合建议"""
        # 基于所有配置的结果生成建议
        best_config = None
        best_combined_score = 0
        
        for key, results in self.validation_results['model_comparison'].items():
            comparison = results['comparison_metrics']['comparison']
            # 综合评分：召回率改进 + F1改进
            combined_score = comparison['recall_improvement'] + comparison['f1_improvement']
            
            if combined_score > best_combined_score:
                best_combined_score = combined_score
                best_config = key
                
        recommendations = {
            'best_config_for_fusion': best_config,
            'fusion_strategy': 'cascade_old_primary',
            'reasoning': [],
            'implementation_details': {},
            'expected_improvements': {}
        }
        
        if best_config and best_config in self.validation_results['model_comparison']:
            best_results = self.validation_results['model_comparison'][best_config]
            comparison = best_results['comparison_metrics']['comparison']
            
            if comparison['old_model_better_recall']:
                recommendations['reasoning'].append("老版本模型召回率显著更高")
                
            if comparison['detection_count_ratio'] > 1.2:
                recommendations['reasoning'].append("老版本模型检测数量明显更多")
                
            recommendations['implementation_details'] = {
                'primary_detector': 'old_model',
                'primary_conf_threshold': best_results['config']['conf'],
                'secondary_classifier': 'current_model',
                'secondary_conf_threshold': best_results['config']['conf'] + 0.1,
                'iou_threshold': best_results['config']['iou']
            }
            
        self.validation_results['fusion_recommendations'] = recommendations
        
    def save_enhanced_results(self):
        """保存增强的验证结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细JSON
        json_file = f"analysis/enhanced_model_validation_{timestamp}.json"
        os.makedirs(os.path.dirname(json_file), exist_ok=True)
        
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        converted_results = convert_numpy_types(self.validation_results)

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, ensure_ascii=False, indent=2)
            
        # 生成详细报告
        report = self.generate_enhanced_report()
        report_file = f"analysis/enhanced_model_validation_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"增强验证结果已保存:")
        logger.info(f"  - 详细数据: {json_file}")
        logger.info(f"  - 详细报告: {report_file}")

    def generate_enhanced_report(self) -> str:
        """生成增强的验证报告"""
        report = []
        report.append("# 增强模型验证报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 测试概览
        if 'test_summary' in self.validation_results:
            summary = self.validation_results['test_summary']
            report.append("## 📊 测试概览")
            report.append(f"- 测试样本数: {summary['total_test_pairs']}")
            report.append(f"- 测试配置数: {summary['test_configs']}")
            report.append("")

        # AnyLabeling模拟结果
        if 'anylabeling_simulation' in self.validation_results:
            anylabeling = self.validation_results['anylabeling_simulation']
            report.append("## 🎯 AnyLabeling环境模拟")

            user_claim = anylabeling['user_claim_verification']
            status = "✅ 验证通过" if user_claim['claim_verified'] else "❌ 未验证"

            report.append(f"**用户观察验证**: {status}")
            report.append(f"- 老版本模型召回率更高: {user_claim['old_model_better_recall']}")
            report.append(f"- 召回率改进: {user_claim['recall_improvement']:.3f}")
            report.append(f"- 检测数量比例: {user_claim['detection_count_ratio']:.2f}")
            report.append("")

        # 详细配置对比
        if 'model_comparison' in self.validation_results:
            report.append("## 📈 详细配置对比")
            report.append("| 配置 | 置信度 | IoU | 当前模型召回率 | 老版本召回率 | 召回率改进 | F1改进 |")
            report.append("|------|--------|-----|---------------|-------------|-----------|--------|")

            for config_name, results in self.validation_results['model_comparison'].items():
                config = results['config']
                comparison = results['comparison_metrics']['comparison']
                current = results['comparison_metrics']['current_model']
                old = results['comparison_metrics']['old_model']

                report.append(f"| {config['name']} | {config['conf']} | {config['iou']} | {current['avg_recall']:.3f} | {old['avg_recall']:.3f} | {comparison['recall_improvement']:+.3f} | {comparison['f1_improvement']:+.3f} |")

            report.append("")

        # 融合建议
        if 'fusion_recommendations' in self.validation_results:
            fusion = self.validation_results['fusion_recommendations']
            report.append("## 💡 融合策略建议")
            report.append(f"**推荐策略**: {fusion['fusion_strategy']}")
            report.append(f"**最佳配置**: {fusion['best_config_for_fusion']}")

            if 'reasoning' in fusion:
                report.append("**理由**:")
                for reason in fusion['reasoning']:
                    report.append(f"- {reason}")

            if 'implementation_details' in fusion:
                impl = fusion['implementation_details']
                report.append("**实施细节**:")
                report.append(f"- 主检测器: {impl.get('primary_detector', 'N/A')}")
                report.append(f"- 主检测器置信度: {impl.get('primary_conf_threshold', 'N/A')}")
                report.append(f"- 辅助分类器: {impl.get('secondary_classifier', 'N/A')}")
                report.append(f"- 辅助分类器置信度: {impl.get('secondary_conf_threshold', 'N/A')}")

            report.append("")

        # 关键发现
        report.append("## 🔍 关键发现")

        # 分析最显著的改进
        best_improvement = 0
        best_config = None

        if 'model_comparison' in self.validation_results:
            for config_name, results in self.validation_results['model_comparison'].items():
                improvement = results['comparison_metrics']['comparison']['recall_improvement']
                if improvement > best_improvement:
                    best_improvement = improvement
                    best_config = config_name

        if best_config:
            report.append(f"1. **最大召回率改进**: {best_config} 配置下改进 {best_improvement:.3f}")

        # 验证用户观察
        if 'anylabeling_simulation' in self.validation_results:
            anylabeling = self.validation_results['anylabeling_simulation']
            if anylabeling['user_claim_verification']['claim_verified']:
                report.append("2. **用户观察得到验证**: 在AnyLabeling默认配置下，老版本模型确实表现更好")
            else:
                report.append("2. **用户观察需要进一步验证**: 当前测试未完全验证用户的观察")

        report.append("")

        # 实施建议
        report.append("## 🚀 实施建议")
        report.append("### 立即行动")
        report.append("1. 基于验证结果实施推荐的融合策略")
        report.append("2. 使用最佳配置参数进行融合")
        report.append("3. 在小规模数据上验证融合效果")
        report.append("")
        report.append("### 中期优化")
        report.append("1. 收集更多高质量标注数据")
        report.append("2. 优化融合算法的细节参数")
        report.append("3. 建立自动化的性能监控")
        report.append("")

        return "\n".join(report)

def main():
    """主函数"""
    print("🚀 增强模型验证器")
    print("=" * 50)
    
    validator = EnhancedModelValidator()
    
    # 运行全面验证
    results = validator.run_comprehensive_validation()
    
    if results:
        print("\n📊 增强验证完成！")
        
        # 显示关键发现
        if 'anylabeling_simulation' in results:
            anylabeling = results['anylabeling_simulation']
            user_claim = anylabeling['user_claim_verification']
            
            status = "✅ 验证通过" if user_claim['claim_verified'] else "❌ 未验证"
            print(f"   用户AnyLabeling观察: {status}")
            print(f"   召回率改进: {user_claim['recall_improvement']:.3f}")
            print(f"   检测数量比例: {user_claim['detection_count_ratio']:.2f}")
            
        if 'fusion_recommendations' in results:
            fusion = results['fusion_recommendations']
            print(f"   推荐融合策略: {fusion['fusion_strategy']}")
            print(f"   最佳配置: {fusion['best_config_for_fusion']}")
            
        print(f"\n✅ 详细验证报告已生成，请查看analysis目录")
    else:
        print("❌ 验证失败，请检查模型文件和数据路径")

if __name__ == "__main__":
    main()

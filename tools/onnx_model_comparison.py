#!/usr/bin/env python3
"""
ONNX模型对比测试器

专门测试PT vs ONNX版本的性能差异，验证用户假设：
"老版本ONNX导出方式更好，新版本ONNX导出可能有问题"

测试内容：
1. PT模型 vs ONNX模型性能对比
2. 不同ONNX导出参数的影响
3. AnyLabeling环境模拟
4. 导出方式差异分析
"""

import sys
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
from datetime import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ONNXModelComparator:
    """ONNX模型对比器"""
    
    def __init__(self):
        self.current_pt_path = "best.pt"
        self.old_pt_path = "data/processed/train3.0/weights/best.pt"
        
        # ONNX文件路径
        self.current_onnx_path = "best.onnx"
        self.old_onnx_path = "data/processed/train3.0/weights/best.onnx"
        
        # 测试配置
        self.test_configs = [
            {'conf': 0.25, 'iou': 0.45, 'name': 'AnyLabeling默认'},
            {'conf': 0.2, 'iou': 0.45, 'name': '低置信度'},
            {'conf': 0.3, 'iou': 0.45, 'name': '高置信度'},
        ]
        
        self.comparison_results = {
            'model_availability': {},
            'pt_vs_onnx_comparison': {},
            'export_analysis': {},
            'anylabeling_simulation': {},
            'recommendations': {}
        }
        
    def check_model_availability(self) -> Dict:
        """检查模型文件可用性"""
        availability = {
            'current_pt': os.path.exists(self.current_pt_path),
            'old_pt': os.path.exists(self.old_pt_path),
            'current_onnx': os.path.exists(self.current_onnx_path),
            'old_onnx': os.path.exists(self.old_onnx_path)
        }
        
        logger.info("模型文件可用性检查:")
        for model_name, available in availability.items():
            status = "✅" if available else "❌"
            logger.info(f"  {model_name}: {status}")
            
        self.comparison_results['model_availability'] = availability
        return availability
        
    def export_onnx_models(self):
        """导出ONNX模型（如果不存在）"""
        try:
            from ultralytics import YOLO
            
            # 导出当前模型的ONNX
            if not os.path.exists(self.current_onnx_path) and os.path.exists(self.current_pt_path):
                logger.info("导出当前模型的ONNX版本...")
                current_model = YOLO(self.current_pt_path)
                current_model.export(format='onnx', imgsz=640, optimize=True, half=False)
                logger.info(f"当前模型ONNX导出完成: {self.current_onnx_path}")
                
            # 导出老版本模型的ONNX
            if not os.path.exists(self.old_onnx_path) and os.path.exists(self.old_pt_path):
                logger.info("导出老版本模型的ONNX版本...")
                old_model = YOLO(self.old_pt_path)
                
                # 尝试不同的导出参数（模拟可能的差异）
                export_configs = [
                    {'half': False, 'optimize': True, 'name': 'standard'},
                    {'half': True, 'optimize': True, 'name': 'half_precision'},
                    {'half': False, 'optimize': False, 'name': 'no_optimize'},
                ]
                
                for i, config in enumerate(export_configs):
                    try:
                        onnx_path = self.old_onnx_path.replace('.onnx', f'_{config["name"]}.onnx')
                        old_model.export(format='onnx', imgsz=640, **{k:v for k,v in config.items() if k != 'name'})
                        
                        # 重命名为标准名称（第一个成功的）
                        if i == 0 and os.path.exists(self.old_pt_path.replace('.pt', '.onnx')):
                            import shutil
                            shutil.move(self.old_pt_path.replace('.pt', '.onnx'), onnx_path)
                            
                        logger.info(f"老版本模型ONNX导出完成 ({config['name']}): {onnx_path}")
                        break
                    except Exception as e:
                        logger.warning(f"导出配置 {config['name']} 失败: {e}")
                        
        except Exception as e:
            logger.error(f"ONNX导出失败: {e}")
            
    def load_onnx_model(self, onnx_path: str):
        """加载ONNX模型"""
        try:
            import onnxruntime as ort
            
            # 创建推理会话
            providers = ['CPUExecutionProvider']
            if ort.get_device() == 'GPU':
                providers.insert(0, 'CUDAExecutionProvider')
                
            session = ort.InferenceSession(onnx_path, providers=providers)
            
            # 获取输入输出信息
            input_info = session.get_inputs()[0]
            output_info = session.get_outputs()[0]
            
            logger.info(f"ONNX模型加载成功: {onnx_path}")
            logger.info(f"  输入形状: {input_info.shape}")
            logger.info(f"  输出形状: {output_info.shape}")
            
            return session, input_info, output_info
            
        except ImportError:
            logger.error("onnxruntime未安装，请运行: pip install onnxruntime")
            return None, None, None
        except Exception as e:
            logger.error(f"ONNX模型加载失败 {onnx_path}: {e}")
            return None, None, None
            
    def preprocess_image_for_onnx(self, image_path: str, input_shape: Tuple) -> np.ndarray:
        """为ONNX模型预处理图像"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        # 获取目标尺寸
        target_h, target_w = input_shape[2], input_shape[3]
        
        # 调整尺寸（保持宽高比）
        h, w = image.shape[:2]
        scale = min(target_w / w, target_h / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # 缩放图像
        resized = cv2.resize(image, (new_w, new_h))
        
        # 创建填充图像
        padded = np.full((target_h, target_w, 3), 114, dtype=np.uint8)
        
        # 计算填充位置
        top = (target_h - new_h) // 2
        left = (target_w - new_w) // 2
        padded[top:top+new_h, left:left+new_w] = resized
        
        # 转换为模型输入格式
        input_tensor = padded.transpose(2, 0, 1).astype(np.float32) / 255.0
        input_tensor = np.expand_dims(input_tensor, axis=0)
        
        return input_tensor, scale, (left, top)
        
    def postprocess_onnx_output(self, output: np.ndarray, scale: float, offset: Tuple, 
                               conf_threshold: float = 0.25, iou_threshold: float = 0.45) -> List[Dict]:
        """后处理ONNX输出"""
        detections = []
        
        # 解析输出（假设是YOLO格式）
        if len(output.shape) == 3:
            output = output[0]  # 移除batch维度
            
        # 过滤低置信度检测
        scores = output[:, 4]  # 置信度分数
        valid_indices = scores > conf_threshold
        
        if not np.any(valid_indices):
            return detections
            
        valid_output = output[valid_indices]
        
        # 解析边界框和类别
        boxes = valid_output[:, :4]
        confidences = valid_output[:, 4]
        class_scores = valid_output[:, 5:]
        
        # 获取最高分类别
        class_ids = np.argmax(class_scores, axis=1)
        class_confidences = np.max(class_scores, axis=1)
        
        # 最终置信度
        final_confidences = confidences * class_confidences
        
        # 应用NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), 
            final_confidences.tolist(), 
            conf_threshold, 
            iou_threshold
        )
        
        if len(indices) > 0:
            for i in indices.flatten():
                box = boxes[i]
                confidence = final_confidences[i]
                class_id = class_ids[i]
                
                # 转换坐标（考虑缩放和偏移）
                x1 = (box[0] - offset[0]) / scale
                y1 = (box[1] - offset[1]) / scale
                x2 = (box[2] - offset[0]) / scale
                y2 = (box[3] - offset[1]) / scale
                
                detection = {
                    'bbox': [x1, y1, x2, y2],
                    'confidence': float(confidence),
                    'class_id': int(class_id),
                    'class_name': f'class_{class_id}',  # 需要类别映射
                    'area': (x2 - x1) * (y2 - y1)
                }
                detections.append(detection)
                
        return detections
        
    def run_onnx_inference(self, session, input_info, image_path: str, config: Dict) -> Tuple[List[Dict], float]:
        """运行ONNX推理"""
        start_time = time.time()
        
        try:
            # 预处理
            input_tensor, scale, offset = self.preprocess_image_for_onnx(
                image_path, input_info.shape
            )
            
            # 推理
            outputs = session.run(None, {input_info.name: input_tensor})
            
            # 后处理
            detections = self.postprocess_onnx_output(
                outputs[0], scale, offset, 
                config['conf'], config['iou']
            )
            
            inference_time = time.time() - start_time
            return detections, inference_time
            
        except Exception as e:
            logger.error(f"ONNX推理失败: {e}")
            return [], 0.0
            
    def compare_pt_vs_onnx(self, test_images: List[str]) -> Dict:
        """对比PT和ONNX模型性能"""
        logger.info("开始PT vs ONNX性能对比...")
        
        comparison = {
            'test_images_count': len(test_images),
            'config_comparisons': {}
        }
        
        try:
            from ultralytics import YOLO
            
            # 加载PT模型
            current_pt = YOLO(self.current_pt_path) if os.path.exists(self.current_pt_path) else None
            old_pt = YOLO(self.old_pt_path) if os.path.exists(self.old_pt_path) else None
            
            # 加载ONNX模型
            current_onnx_session, current_onnx_input, _ = self.load_onnx_model(self.current_onnx_path) if os.path.exists(self.current_onnx_path) else (None, None, None)
            old_onnx_session, old_onnx_input, _ = self.load_onnx_model(self.old_onnx_path) if os.path.exists(self.old_onnx_path) else (None, None, None)
            
            # 对每个配置进行测试
            for config in self.test_configs:
                logger.info(f"测试配置: {config['name']}")
                
                config_results = {
                    'config': config,
                    'current_pt_results': [],
                    'current_onnx_results': [],
                    'old_pt_results': [],
                    'old_onnx_results': []
                }
                
                # 测试图像子集（避免测试时间过长）
                test_subset = test_images[:20]  # 测试前20张图像
                
                for i, image_path in enumerate(test_subset):
                    if i % 5 == 0:
                        logger.info(f"  处理图像 {i+1}/{len(test_subset)}")
                        
                    # 当前模型PT版本
                    if current_pt:
                        try:
                            start_time = time.time()
                            results = current_pt(image_path, conf=config['conf'], iou=config['iou'], verbose=False)
                            pt_time = time.time() - start_time
                            
                            detection_count = 0
                            avg_confidence = 0
                            if results and results[0].boxes is not None:
                                detection_count = len(results[0].boxes)
                                if detection_count > 0:
                                    avg_confidence = float(results[0].boxes.conf.mean())
                                    
                            config_results['current_pt_results'].append({
                                'detection_count': detection_count,
                                'avg_confidence': avg_confidence,
                                'inference_time': pt_time
                            })
                        except Exception as e:
                            logger.warning(f"当前模型PT推理失败: {e}")
                            
                    # 当前模型ONNX版本
                    if current_onnx_session:
                        try:
                            detections, onnx_time = self.run_onnx_inference(
                                current_onnx_session, current_onnx_input, image_path, config
                            )
                            
                            avg_confidence = np.mean([d['confidence'] for d in detections]) if detections else 0
                            
                            config_results['current_onnx_results'].append({
                                'detection_count': len(detections),
                                'avg_confidence': float(avg_confidence),
                                'inference_time': onnx_time
                            })
                        except Exception as e:
                            logger.warning(f"当前模型ONNX推理失败: {e}")
                            
                    # 老版本模型PT版本
                    if old_pt:
                        try:
                            start_time = time.time()
                            results = old_pt(image_path, conf=config['conf'], iou=config['iou'], verbose=False)
                            pt_time = time.time() - start_time
                            
                            detection_count = 0
                            avg_confidence = 0
                            if results and results[0].boxes is not None:
                                detection_count = len(results[0].boxes)
                                if detection_count > 0:
                                    avg_confidence = float(results[0].boxes.conf.mean())
                                    
                            config_results['old_pt_results'].append({
                                'detection_count': detection_count,
                                'avg_confidence': avg_confidence,
                                'inference_time': pt_time
                            })
                        except Exception as e:
                            logger.warning(f"老版本模型PT推理失败: {e}")
                            
                    # 老版本模型ONNX版本
                    if old_onnx_session:
                        try:
                            detections, onnx_time = self.run_onnx_inference(
                                old_onnx_session, old_onnx_input, image_path, config
                            )
                            
                            avg_confidence = np.mean([d['confidence'] for d in detections]) if detections else 0
                            
                            config_results['old_onnx_results'].append({
                                'detection_count': len(detections),
                                'avg_confidence': float(avg_confidence),
                                'inference_time': onnx_time
                            })
                        except Exception as e:
                            logger.warning(f"老版本模型ONNX推理失败: {e}")
                            
                # 计算配置级别的统计
                config_results['statistics'] = self.calculate_config_statistics(config_results)
                comparison['config_comparisons'][config['name']] = config_results
                
        except Exception as e:
            logger.error(f"PT vs ONNX对比失败: {e}")
            
        return comparison
        
    def calculate_config_statistics(self, config_results: Dict) -> Dict:
        """计算配置级别的统计信息"""
        stats = {}
        
        for model_key in ['current_pt_results', 'current_onnx_results', 'old_pt_results', 'old_onnx_results']:
            results = config_results[model_key]
            if results:
                stats[model_key] = {
                    'avg_detection_count': np.mean([r['detection_count'] for r in results]),
                    'avg_confidence': np.mean([r['avg_confidence'] for r in results]),
                    'avg_inference_time': np.mean([r['inference_time'] for r in results]),
                    'total_detections': sum([r['detection_count'] for r in results])
                }
            else:
                stats[model_key] = {
                    'avg_detection_count': 0,
                    'avg_confidence': 0,
                    'avg_inference_time': 0,
                    'total_detections': 0
                }
                
        return stats
        
    def collect_test_images(self, max_images: int = 50) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        # 从calibration_gt收集
        calibration_path = Path("legacy_assets/ceshi/calibration_gt/images")
        if calibration_path.exists():
            for img_file in calibration_path.glob("*.jpg"):
                test_images.append(str(img_file))
                if len(test_images) >= max_images:
                    break
                    
        return test_images
        
    def run_comprehensive_comparison(self) -> Dict:
        """运行全面对比"""
        logger.info("开始ONNX模型全面对比...")
        
        # 1. 检查模型可用性
        availability = self.check_model_availability()
        
        # 2. 导出ONNX模型（如果需要）
        self.export_onnx_models()
        
        # 3. 重新检查可用性
        availability_after_export = self.check_model_availability()
        self.comparison_results['model_availability'] = availability_after_export
        
        # 4. 收集测试图像
        test_images = self.collect_test_images()
        logger.info(f"收集到 {len(test_images)} 张测试图像")
        
        if not test_images:
            logger.error("未找到测试图像")
            return self.comparison_results
            
        # 5. 运行PT vs ONNX对比
        pt_onnx_comparison = self.compare_pt_vs_onnx(test_images)
        self.comparison_results['pt_vs_onnx_comparison'] = pt_onnx_comparison
        
        # 6. 分析结果
        self.analyze_comparison_results()
        
        # 7. 保存结果
        self.save_comparison_results()
        
        return self.comparison_results
        
    def analyze_comparison_results(self):
        """分析对比结果"""
        analysis = {
            'key_findings': [],
            'performance_differences': {},
            'anylabeling_hypothesis_verification': {}
        }
        
        # 分析AnyLabeling默认配置下的表现
        anylabeling_config = self.comparison_results['pt_vs_onnx_comparison']['config_comparisons'].get('AnyLabeling默认')
        
        if anylabeling_config and 'statistics' in anylabeling_config:
            stats = anylabeling_config['statistics']
            
            # 对比老版本ONNX vs 当前版本ONNX
            old_onnx_detections = stats.get('old_onnx_results', {}).get('total_detections', 0)
            current_onnx_detections = stats.get('current_onnx_results', {}).get('total_detections', 0)
            
            # 对比老版本PT vs ONNX
            old_pt_detections = stats.get('old_pt_results', {}).get('total_detections', 0)
            old_onnx_vs_pt_ratio = old_onnx_detections / old_pt_detections if old_pt_detections > 0 else 0
            
            # 对比当前版本PT vs ONNX
            current_pt_detections = stats.get('current_pt_results', {}).get('total_detections', 0)
            current_onnx_vs_pt_ratio = current_onnx_detections / current_pt_detections if current_pt_detections > 0 else 0
            
            analysis['anylabeling_hypothesis_verification'] = {
                'old_onnx_better_than_current_onnx': old_onnx_detections > current_onnx_detections,
                'old_onnx_vs_current_onnx_ratio': old_onnx_detections / current_onnx_detections if current_onnx_detections > 0 else 0,
                'old_onnx_vs_pt_consistency': abs(old_onnx_vs_pt_ratio - 1.0),
                'current_onnx_vs_pt_consistency': abs(current_onnx_vs_pt_ratio - 1.0),
                'hypothesis_supported': old_onnx_detections > current_onnx_detections and current_onnx_vs_pt_ratio < old_onnx_vs_pt_ratio
            }
            
            if analysis['anylabeling_hypothesis_verification']['hypothesis_supported']:
                analysis['key_findings'].append("✅ 用户假设得到验证：老版本ONNX导出更好")
            else:
                analysis['key_findings'].append("❌ 用户假设未得到验证")
                
        self.comparison_results['export_analysis'] = analysis

    def save_comparison_results(self):
        """保存对比结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细JSON
        json_file = f"analysis/onnx_model_comparison_{timestamp}.json"
        os.makedirs(os.path.dirname(json_file), exist_ok=True)

        # 转换numpy类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            return obj

        converted_results = convert_numpy_types(self.comparison_results)

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, ensure_ascii=False, indent=2)

        # 生成报告
        report = self.generate_comparison_report()
        report_file = f"analysis/onnx_model_comparison_report_{timestamp}.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"ONNX对比结果已保存:")
        logger.info(f"  - 详细数据: {json_file}")
        logger.info(f"  - 对比报告: {report_file}")

    def generate_comparison_report(self) -> str:
        """生成对比报告"""
        report = []
        report.append("# ONNX模型对比测试报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 模型可用性
        if 'model_availability' in self.comparison_results:
            availability = self.comparison_results['model_availability']
            report.append("## 📋 模型文件可用性")
            for model_name, available in availability.items():
                status = "✅ 可用" if available else "❌ 不可用"
                report.append(f"- **{model_name}**: {status}")
            report.append("")

        # 用户假设验证
        if 'export_analysis' in self.comparison_results:
            analysis = self.comparison_results['export_analysis']
            report.append("## 🎯 用户假设验证")

            if 'anylabeling_hypothesis_verification' in analysis:
                hyp = analysis['anylabeling_hypothesis_verification']
                status = "✅ 支持" if hyp.get('hypothesis_supported', False) else "❌ 不支持"
                report.append(f"**假设验证结果**: {status}")
                report.append(f"- 老版本ONNX检测数量更多: {hyp.get('old_onnx_better_than_current_onnx', False)}")
                report.append(f"- 检测数量比例: {hyp.get('old_onnx_vs_current_onnx_ratio', 0):.3f}")
                report.append("")

            if 'key_findings' in analysis:
                report.append("**关键发现**:")
                for finding in analysis['key_findings']:
                    report.append(f"- {finding}")
                report.append("")

        # PT vs ONNX性能对比
        if 'pt_vs_onnx_comparison' in self.comparison_results:
            comparison = self.comparison_results['pt_vs_onnx_comparison']
            report.append("## 📊 PT vs ONNX性能对比")

            if 'config_comparisons' in comparison:
                for config_name, config_data in comparison['config_comparisons'].items():
                    report.append(f"### {config_name}")

                    if 'statistics' in config_data:
                        stats = config_data['statistics']

                        report.append("| 模型版本 | 平均检测数 | 平均置信度 | 平均推理时间(s) |")
                        report.append("|----------|------------|------------|----------------|")

                        for model_key in ['current_pt_results', 'current_onnx_results', 'old_pt_results', 'old_onnx_results']:
                            if model_key in stats:
                                model_stats = stats[model_key]
                                model_name = model_key.replace('_results', '').replace('_', ' ')
                                report.append(f"| {model_name} | {model_stats['avg_detection_count']:.1f} | {model_stats['avg_confidence']:.3f} | {model_stats['avg_inference_time']:.4f} |")

                        report.append("")

        # 建议和结论
        report.append("## 💡 建议和结论")

        if 'export_analysis' in self.comparison_results:
            analysis = self.comparison_results['export_analysis']
            if 'anylabeling_hypothesis_verification' in analysis:
                hyp = analysis['anylabeling_hypothesis_verification']

                if hyp.get('hypothesis_supported', False):
                    report.append("### ✅ 用户假设得到验证")
                    report.append("1. **问题确认**: 新版本ONNX导出确实存在问题")
                    report.append("2. **解决方案**: 使用老版本的ONNX导出方式")
                    report.append("3. **融合策略**: 优先使用老版本ONNX模型进行检测")
                else:
                    report.append("### ❌ 用户假设未得到验证")
                    report.append("1. **需要进一步调查**: ONNX导出参数差异")
                    report.append("2. **可能原因**: 推理环境差异、预处理差异")
                    report.append("3. **建议**: 直接在AnyLabeling中测试验证")

        report.append("")
        report.append("## 🚀 下一步行动")
        report.append("1. 基于测试结果调整ONNX导出参数")
        report.append("2. 在AnyLabeling中直接验证结果")
        report.append("3. 实施最优的模型融合策略")

        return "\n".join(report)

def main():
    """主函数"""
    print("🔍 ONNX模型对比测试器")
    print("=" * 50)
    
    comparator = ONNXModelComparator()
    
    # 运行全面对比
    results = comparator.run_comprehensive_comparison()
    
    if results:
        print("\n📊 ONNX对比测试完成！")
        
        # 显示关键发现
        if 'export_analysis' in results and 'key_findings' in results['export_analysis']:
            findings = results['export_analysis']['key_findings']
            for finding in findings:
                print(f"   {finding}")
                
        print(f"\n✅ 详细对比报告已生成，请查看analysis目录")
    else:
        print("❌ 对比测试失败")

if __name__ == "__main__":
    main()

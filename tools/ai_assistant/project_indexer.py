#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目智能索引器 - 为AI助手提供项目理解支持

功能：
1. 自动扫描和索引项目文件
2. 生成语义化的项目地图
3. 创建智能搜索索引
4. 提供AI上下文建议
"""

import os
import json
import ast
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import hashlib

@dataclass
class FileInfo:
    """文件信息"""
    path: str
    type: str  # 'python', 'config', 'doc', 'data', 'test'
    size: int
    modified: str
    functions: List[str]
    classes: List[str]
    imports: List[str]
    docstring: Optional[str]
    complexity_score: int
    dependencies: List[str]

@dataclass
class ProjectIndex:
    """项目索引"""
    project_name: str
    root_path: str
    generated_at: str
    total_files: int
    total_lines: int
    files: List[FileInfo]
    module_graph: Dict[str, List[str]]
    ai_suggestions: Dict[str, Any]

class ProjectIndexer:
    """项目智能索引器"""
    
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.ignore_patterns = {
            '__pycache__', '.git', '.vscode', '.idea', 'node_modules',
            '*.pyc', '*.pyo', '*.pyd', '.DS_Store', 'Thumbs.db',
            'env', 'venv', '.env'
        }
        
    def should_ignore(self, path: Path) -> bool:
        """检查是否应该忽略文件/目录"""
        for pattern in self.ignore_patterns:
            if pattern in str(path) or path.name.startswith('.'):
                return True
        return False
    
    def analyze_python_file(self, file_path: Path) -> FileInfo:
        """分析Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            docstring = None
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        imports.extend([alias.name for alias in node.names])
                    else:
                        imports.append(node.module or '')
            
            # 获取模块文档字符串
            if (tree.body and isinstance(tree.body[0], ast.Expr) 
                and isinstance(tree.body[0].value, ast.Str)):
                docstring = tree.body[0].value.s
            
            # 计算复杂度分数
            complexity_score = len(functions) + len(classes) * 2 + len(content.split('\n'))
            
            return FileInfo(
                path=str(file_path.relative_to(self.root_path)),
                type='python',
                size=len(content),
                modified=datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                functions=functions,
                classes=classes,
                imports=imports,
                docstring=docstring,
                complexity_score=complexity_score,
                dependencies=self._extract_dependencies(imports)
            )
            
        except Exception as e:
            print(f"分析文件失败 {file_path}: {e}")
            return self._create_basic_file_info(file_path, 'python')
    
    def _extract_dependencies(self, imports: List[str]) -> List[str]:
        """提取项目内部依赖"""
        dependencies = []
        for imp in imports:
            if imp and (imp.startswith('src.') or imp.startswith('.')):
                dependencies.append(imp)
        return dependencies
    
    def _create_basic_file_info(self, file_path: Path, file_type: str) -> FileInfo:
        """创建基础文件信息"""
        try:
            stat = file_path.stat()
            return FileInfo(
                path=str(file_path.relative_to(self.root_path)),
                type=file_type,
                size=stat.st_size,
                modified=datetime.fromtimestamp(stat.st_mtime).isoformat(),
                functions=[],
                classes=[],
                imports=[],
                docstring=None,
                complexity_score=0,
                dependencies=[]
            )
        except Exception:
            return FileInfo(
                path=str(file_path.relative_to(self.root_path)),
                type=file_type,
                size=0,
                modified='',
                functions=[],
                classes=[],
                imports=[],
                docstring=None,
                complexity_score=0,
                dependencies=[]
            )
    
    def determine_file_type(self, file_path: Path) -> str:
        """确定文件类型"""
        suffix = file_path.suffix.lower()
        name = file_path.name.lower()
        
        if suffix == '.py':
            if 'test' in name:
                return 'test'
            return 'python'
        elif suffix in ['.json', '.yaml', '.yml', '.toml', '.ini']:
            return 'config'
        elif suffix in ['.md', '.rst', '.txt']:
            return 'doc'
        elif suffix in ['.jpg', '.png', '.mp4', '.avi']:
            return 'data'
        else:
            return 'other'
    
    def build_module_graph(self, files: List[FileInfo]) -> Dict[str, List[str]]:
        """构建模块依赖图"""
        graph = {}
        
        for file_info in files:
            if file_info.type == 'python':
                module_name = file_info.path.replace('/', '.').replace('.py', '')
                graph[module_name] = file_info.dependencies
        
        return graph
    
    def generate_ai_suggestions(self, files: List[FileInfo]) -> Dict[str, Any]:
        """生成AI助手建议"""
        python_files = [f for f in files if f.type == 'python']
        test_files = [f for f in files if f.type == 'test']
        
        # 核心模块识别
        core_modules = []
        for f in python_files:
            if 'core' in f.path or len(f.classes) > 0 or len(f.functions) > 5:
                core_modules.append(f.path)
        
        # 复杂度分析
        complex_files = sorted(python_files, key=lambda x: x.complexity_score, reverse=True)[:5]
        
        # 测试覆盖率估算
        test_coverage = len(test_files) / max(len(python_files), 1) * 100
        
        return {
            "core_modules": core_modules,
            "most_complex_files": [f.path for f in complex_files],
            "test_coverage_estimate": f"{test_coverage:.1f}%",
            "recommended_context_files": core_modules[:3],
            "entry_points": [f.path for f in python_files if 'main' in f.path],
            "configuration_files": [f.path for f in files if f.type == 'config'],
            "documentation_files": [f.path for f in files if f.type == 'doc']
        }
    
    def scan_project(self) -> ProjectIndex:
        """扫描整个项目"""
        print(f"开始扫描项目: {self.root_path}")
        
        files = []
        total_lines = 0
        
        for file_path in self.root_path.rglob('*'):
            if file_path.is_file() and not self.should_ignore(file_path):
                file_type = self.determine_file_type(file_path)
                
                if file_type == 'python':
                    file_info = self.analyze_python_file(file_path)
                else:
                    file_info = self._create_basic_file_info(file_path, file_type)
                
                files.append(file_info)
                total_lines += file_info.size
        
        module_graph = self.build_module_graph(files)
        ai_suggestions = self.generate_ai_suggestions(files)
        
        return ProjectIndex(
            project_name=self.root_path.name,
            root_path=str(self.root_path),
            generated_at=datetime.now().isoformat(),
            total_files=len(files),
            total_lines=total_lines,
            files=files,
            module_graph=module_graph,
            ai_suggestions=ai_suggestions
        )
    
    def save_index(self, index: ProjectIndex, output_path: str):
        """保存索引到文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(index), f, indent=2, ensure_ascii=False)
        print(f"项目索引已保存到: {output_path}")

def main():
    """主函数"""
    import argparse
    import sys

    # 设置UTF-8编码输出
    if sys.platform == 'win32':
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

    parser = argparse.ArgumentParser(description='项目智能索引器')
    parser.add_argument('--root', default='.', help='项目根目录')
    parser.add_argument('--output', default='project_index.json', help='输出文件')

    args = parser.parse_args()

    indexer = ProjectIndexer(args.root)
    index = indexer.scan_project()
    indexer.save_index(index, args.output)

    # 使用ASCII字符避免编码问题
    print(f"\n[Project Statistics]")
    print(f"Total files: {index.total_files}")
    print(f"Total lines: {index.total_lines}")
    print(f"Core modules: {len(index.ai_suggestions['core_modules'])}")
    print(f"Test coverage estimate: {index.ai_suggestions['test_coverage_estimate']}")

if __name__ == "__main__":
    main()

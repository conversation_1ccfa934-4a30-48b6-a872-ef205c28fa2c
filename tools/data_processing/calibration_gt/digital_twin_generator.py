#!/usr/bin/env python3
"""
calibration_gt数字孪生ID生成器

功能：
1. 为calibration_gt数据集生成数字孪生ID
2. 转换现有标注为CardDetection格式
3. 生成完整的双轨输出
4. 质量验证和一致性检查

使用方法：
    python tools/data_processing/calibration_gt/digital_twin_generator.py
    python tools/data_processing/calibration_gt/digital_twin_generator.py --input_dir data/calibration_gt
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime
import argparse

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

# 导入项目核心模块
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class GeneratorConfig:
    """生成器配置"""
    input_dir: str = "data/calibration_gt"
    output_dir: str = "output/digital_twin_output"
    enable_validation: bool = True
    save_intermediate: bool = True
    overwrite_existing: bool = False

class DigitalTwinGenerator:
    """数字孪生ID生成器"""
    
    def __init__(self, config: GeneratorConfig):
        self.config = config
        self.dt_system = create_digital_twin_system()
        self.stats = {
            'total_files': 0,
            'processed': 0,
            'failed': 0,
            'total_cards': 0,
            'twin_ids_generated': 0
        }
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
    
    def convert_annotation_to_detections(self, annotation_data: Dict[str, Any]) -> List[CardDetection]:
        """将标注数据转换为CardDetection格式"""
        detections = []
        
        shapes = annotation_data.get('shapes', [])
        for i, shape in enumerate(shapes):
            try:
                # 提取标签和坐标
                label = shape.get('label', f'unknown_{i}')
                points = shape.get('points', [])
                
                if not points or len(points) < 4:
                    logger.warning(f"跳过无效形状: {label} (坐标点不足)")
                    continue
                
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                # 解析卡牌值 (假设标签格式为卡牌名称)
                card_value = self._extract_card_value(label)
                
                # 创建CardDetection
                detection = CardDetection(
                    card_value=card_value,
                    bbox=bbox,
                    confidence=0.95,  # 标注数据假设高置信度
                    class_id=i + 1,
                    region_name="手牌_观战方",  # 默认区域
                    perspective="spectator"
                )
                
                detections.append(detection)
                
            except Exception as e:
                logger.error(f"转换形状失败 {i}: {e}")
                continue
        
        return detections
    
    def _extract_card_value(self, label: str) -> str:
        """从标签中提取卡牌值"""
        # 简单的卡牌值提取逻辑
        # 实际使用时可能需要更复杂的解析
        
        # 移除数字前缀 (如 "1_二" -> "二")
        if '_' in label:
            parts = label.split('_')
            if len(parts) > 1:
                return parts[-1]
        
        # 移除虚拟前缀 (如 "虚拟_三" -> "三")
        if label.startswith('虚拟'):
            return label.replace('虚拟', '').replace('_', '')
        
        return label
    
    def process_single_file(self, annotation_path: str) -> Dict[str, Any]:
        """处理单个标注文件"""
        try:
            # 读取标注文件
            with open(annotation_path, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)
            
            # 转换为检测格式
            detections = self.convert_annotation_to_detections(annotation_data)
            
            if not detections:
                logger.warning(f"文件中没有有效检测: {annotation_path}")
                return {'success': False, 'error': '没有有效检测'}
            
            # 数字孪生处理
            result = self.dt_system.process_frame(detections)
            
            # 获取图像信息
            image_width = annotation_data.get('imageWidth', 640)
            image_height = annotation_data.get('imageHeight', 320)
            image_path = annotation_data.get('imagePath', 'unknown.jpg')
            
            # 双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                result, image_width, image_height, image_path
            )
            
            # 更新统计
            self.stats['processed'] += 1
            self.stats['total_cards'] += len(result.get('digital_twin_cards', []))
            self.stats['twin_ids_generated'] += len([
                card for card in result.get('digital_twin_cards', [])
                if not card.twin_id.startswith('虚拟')
            ])
            
            return {
                'success': True,
                'result': dual_result,
                'original_annotation': annotation_data,
                'stats': {
                    'detections_count': len(detections),
                    'twin_cards_count': len(result.get('digital_twin_cards', [])),
                    'consistency_score': dual_result['consistency_validation'].get('consistency_score', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"处理文件失败 {annotation_path}: {e}")
            self.stats['failed'] += 1
            return {
                'success': False,
                'error': str(e),
                'file_path': annotation_path
            }
    
    def process_batch(self) -> Dict[str, Any]:
        """批量处理"""
        logger.info(f"开始处理 calibration_gt 数字孪生ID生成")
        logger.info(f"输入目录: {self.config.input_dir}")
        logger.info(f"输出目录: {self.config.output_dir}")
        
        # 查找所有JSON标注文件
        annotation_files = []
        if os.path.exists(self.config.input_dir):
            for file_path in Path(self.config.input_dir).rglob('*.json'):
                annotation_files.append(str(file_path))
        
        if not annotation_files:
            logger.warning(f"在 {self.config.input_dir} 中未找到JSON标注文件")
            return {'success': False, 'error': '未找到标注文件'}
        
        logger.info(f"找到 {len(annotation_files)} 个标注文件")
        self.stats['total_files'] = len(annotation_files)
        
        # 处理结果存储
        results = []
        failed_files = []
        
        # 批量处理
        for i, annotation_path in enumerate(annotation_files):
            logger.info(f"处理进度: {i+1}/{len(annotation_files)} - {os.path.basename(annotation_path)}")
            
            # 检查是否跳过已存在的文件
            if not self.config.overwrite_existing:
                output_path = self._get_output_path(annotation_path)
                if os.path.exists(output_path):
                    logger.info(f"跳过已存在的文件: {output_path}")
                    continue
            
            # 处理单个文件
            result = self.process_single_file(annotation_path)
            
            if result['success']:
                results.append(result)
                
                # 保存结果文件
                self._save_result_files(annotation_path, result)
                
            else:
                failed_files.append(result)
        
        # 生成最终报告
        final_report = self._generate_final_report(results, failed_files)
        
        # 保存报告
        report_path = os.path.join(self.config.output_dir, "generation_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"生成完成！报告已保存到: {report_path}")
        
        return final_report
    
    def _get_output_path(self, annotation_path: str) -> str:
        """获取输出文件路径"""
        base_name = os.path.splitext(os.path.basename(annotation_path))[0]
        return os.path.join(self.config.output_dir, f"{base_name}_twin.json")
    
    def _save_result_files(self, annotation_path: str, result: Dict[str, Any]):
        """保存结果文件"""
        base_name = os.path.splitext(os.path.basename(annotation_path))[0]
        
        # 保存完整结果
        full_result_path = os.path.join(self.config.output_dir, f"{base_name}_twin_complete.json")
        with open(full_result_path, 'w', encoding='utf-8') as f:
            json.dump(result['result'], f, ensure_ascii=False, indent=2)
        
        # 保存RLCard格式
        rlcard_path = os.path.join(self.config.output_dir, f"{base_name}_rlcard.json")
        with open(rlcard_path, 'w', encoding='utf-8') as f:
            json.dump(result['result']['rlcard_format'], f, ensure_ascii=False, indent=2)
        
        # 保存AnyLabeling格式
        anylabeling_path = os.path.join(self.config.output_dir, f"{base_name}_anylabeling.json")
        with open(anylabeling_path, 'w', encoding='utf-8') as f:
            json.dump(result['result']['anylabeling_format'], f, ensure_ascii=False, indent=2)
    
    def _generate_final_report(self, results: List[Dict], failed_files: List[Dict]) -> Dict[str, Any]:
        """生成最终报告"""
        total_consistency = sum(
            r['stats']['consistency_score'] for r in results if r['stats']['consistency_score'] > 0
        )
        avg_consistency = total_consistency / len(results) if results else 0
        
        return {
            'generation_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_files': self.stats['total_files'],
                'processed': self.stats['processed'],
                'failed': self.stats['failed'],
                'success_rate': self.stats['processed'] / self.stats['total_files'] if self.stats['total_files'] > 0 else 0
            },
            'data_statistics': {
                'total_cards_processed': self.stats['total_cards'],
                'twin_ids_generated': self.stats['twin_ids_generated'],
                'average_consistency_score': avg_consistency
            },
            'configuration': {
                'input_dir': self.config.input_dir,
                'output_dir': self.config.output_dir,
                'enable_validation': self.config.enable_validation
            },
            'failed_files': [f['file_path'] for f in failed_files] if failed_files else []
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='calibration_gt数字孪生ID生成器')
    parser.add_argument('--input_dir', default='data/calibration_gt',
                       help='输入标注目录')
    parser.add_argument('--output_dir', default='output/digital_twin_output',
                       help='输出目录')
    parser.add_argument('--disable_validation', action='store_true',
                       help='禁用验证')
    parser.add_argument('--overwrite', action='store_true',
                       help='覆盖已存在的文件')
    
    args = parser.parse_args()
    
    # 创建配置
    config = GeneratorConfig(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        enable_validation=not args.disable_validation,
        overwrite_existing=args.overwrite
    )
    
    # 创建生成器并执行
    generator = DigitalTwinGenerator(config)
    result = generator.process_batch()
    
    # 输出结果
    if result.get('success', True):
        print(f"✅ 生成完成！")
        print(f"📊 成功率: {result['generation_summary']['success_rate']:.2%}")
        print(f"🎯 数字孪生ID: {result['data_statistics']['twin_ids_generated']}")
        print(f"📁 输出目录: {config.output_dir}")
    else:
        print(f"❌ 生成失败: {result.get('error', '未知错误')}")
        sys.exit(1)

if __name__ == "__main__":
    main()

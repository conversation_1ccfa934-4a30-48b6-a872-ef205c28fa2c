# 🔧 数据处理工具

本目录包含项目的数据处理相关工具和脚本。

## 📁 目录结构

### calibration_gt/
calibration_gt数据集的专用处理工具
- `final_processor.py` - 最终版本处理器 (推荐使用)
- `digital_twin_generator.py` - 数字孪生ID生成器
- `dual_format_generator.py` - 双格式输出生成器

### batch_processors/
批量数据处理工具
- `comprehensive_processor.py` - 综合批量处理器
- `verification_processor.py` - 批量验证处理器

### verification/
数据验证和格式检查工具
- `format_verifier.py` - 格式验证器
- `consistency_checker.py` - 一致性检查器

## 🚀 快速使用

### calibration_gt数据处理
```bash
# 使用最终版本处理器
python tools/data_processing/calibration_gt/final_processor.py

# 生成数字孪生ID
python tools/data_processing/calibration_gt/digital_twin_generator.py
```

### 批量处理
```bash
# 综合批量处理
python tools/data_processing/batch_processors/comprehensive_processor.py

# 格式验证
python tools/data_processing/verification/format_verifier.py
```

## 📋 工具说明

### 处理器选择指南
- **日常使用**: 推荐使用 `final_processor.py`
- **研究开发**: 可使用其他专用处理器
- **批量操作**: 使用 `batch_processors/` 下的工具
- **质量检查**: 使用 `verification/` 下的工具

### 输入输出格式
- **输入**: 支持图像文件、JSON标注、批量目录
- **输出**: RLCard格式、AnyLabeling格式、验证报告
- **配置**: 支持配置文件和命令行参数

## ⚠️ 注意事项

1. **数据备份**: 处理前请备份原始数据
2. **环境要求**: 确保已安装所需依赖
3. **GPU支持**: 部分处理器支持GPU加速
4. **内存使用**: 大批量处理时注意内存使用

---

**💡 提示**: 如有问题请参考各工具的详细文档或联系开发团队。

#!/usr/bin/env python3
"""
快速模型验证脚本

专门验证用户提到的两个模型特性：
1. 当前模型：识别31个类别，但精度低，经常漏检
2. 老版本模型：只识别21个卡牌类别，但精度高，框准确，不漏检，但会误检数字/文字

快速验证这些特性是否属实，为融合策略提供依据
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickModelVerifier:
    """快速模型验证器"""
    
    def __init__(self):
        self.current_model_path = "best.pt"
        self.old_model_path = "data/processed/train3.0/weights/best.pt"
        
        # 测试配置
        self.test_confidence = 0.25
        self.test_iou = 0.45
        
        # 预期的类别数量
        self.expected_current_categories = 31  # 21卡牌 + 10其他
        self.expected_old_categories = 21      # 仅卡牌
        
        self.verification_results = {
            'model_loading': {},
            'category_verification': {},
            'detection_comparison': {},
            'user_claims_verification': {}
        }
        
    def load_and_verify_models(self) -> bool:
        """加载并验证模型"""
        try:
            from ultralytics import YOLO
            
            # 加载当前模型
            if os.path.exists(self.current_model_path):
                self.current_model = YOLO(self.current_model_path)
                current_classes = len(self.current_model.names)
                logger.info(f"当前模型加载成功: {current_classes} 个类别")
                
                self.verification_results['model_loading']['current_model'] = {
                    'loaded': True,
                    'path': self.current_model_path,
                    'class_count': current_classes,
                    'class_names': list(self.current_model.names.values())
                }
            else:
                logger.error(f"当前模型文件不存在: {self.current_model_path}")
                return False
                
            # 加载老版本模型
            if os.path.exists(self.old_model_path):
                self.old_model = YOLO(self.old_model_path)
                old_classes = len(self.old_model.names)
                logger.info(f"老版本模型加载成功: {old_classes} 个类别")
                
                self.verification_results['model_loading']['old_model'] = {
                    'loaded': True,
                    'path': self.old_model_path,
                    'class_count': old_classes,
                    'class_names': list(self.old_model.names.values())
                }
            else:
                logger.error(f"老版本模型文件不存在: {self.old_model_path}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
            
    def verify_category_claims(self):
        """验证类别数量声明"""
        current_info = self.verification_results['model_loading']['current_model']
        old_info = self.verification_results['model_loading']['old_model']
        
        # 验证当前模型类别
        current_claim_correct = current_info['class_count'] == self.expected_current_categories
        
        # 验证老版本模型类别
        old_claim_correct = old_info['class_count'] == self.expected_old_categories
        
        self.verification_results['category_verification'] = {
            'current_model': {
                'expected': self.expected_current_categories,
                'actual': current_info['class_count'],
                'claim_correct': current_claim_correct,
                'categories': current_info['class_names']
            },
            'old_model': {
                'expected': self.expected_old_categories,
                'actual': old_info['class_count'],
                'claim_correct': old_claim_correct,
                'categories': old_info['class_names']
            }
        }
        
        logger.info(f"类别验证 - 当前模型: {current_claim_correct}, 老版本: {old_claim_correct}")
        
    def quick_detection_test(self, test_images: List[str]) -> Dict:
        """快速检测测试"""
        detection_results = {
            'test_images': len(test_images),
            'current_model_stats': {'total_detections': 0, 'avg_confidence': 0, 'detection_counts': []},
            'old_model_stats': {'total_detections': 0, 'avg_confidence': 0, 'detection_counts': []},
            'comparison': {}
        }
        
        current_all_detections = []
        old_all_detections = []
        
        for i, image_path in enumerate(test_images):
            logger.info(f"测试图像 {i+1}/{len(test_images)}: {Path(image_path).name}")
            
            # 当前模型检测
            current_results = self.current_model(image_path, conf=self.test_confidence, iou=self.test_iou, verbose=False)
            current_count = 0
            current_confidences = []
            
            for result in current_results:
                if result.boxes is not None:
                    current_count = len(result.boxes)
                    current_confidences = result.boxes.conf.cpu().numpy().tolist()
                    
            current_all_detections.extend(current_confidences)
            detection_results['current_model_stats']['detection_counts'].append(current_count)
            
            # 老版本模型检测
            old_results = self.old_model(image_path, conf=self.test_confidence, iou=self.test_iou, verbose=False)
            old_count = 0
            old_confidences = []
            
            for result in old_results:
                if result.boxes is not None:
                    old_count = len(result.boxes)
                    old_confidences = result.boxes.conf.cpu().numpy().tolist()
                    
            old_all_detections.extend(old_confidences)
            detection_results['old_model_stats']['detection_counts'].append(old_count)
            
        # 计算统计信息
        detection_results['current_model_stats']['total_detections'] = sum(detection_results['current_model_stats']['detection_counts'])
        detection_results['current_model_stats']['avg_confidence'] = np.mean(current_all_detections) if current_all_detections else 0
        detection_results['current_model_stats']['avg_detections_per_image'] = np.mean(detection_results['current_model_stats']['detection_counts'])
        
        detection_results['old_model_stats']['total_detections'] = sum(detection_results['old_model_stats']['detection_counts'])
        detection_results['old_model_stats']['avg_confidence'] = np.mean(old_all_detections) if old_all_detections else 0
        detection_results['old_model_stats']['avg_detections_per_image'] = np.mean(detection_results['old_model_stats']['detection_counts'])
        
        # 对比分析
        detection_results['comparison'] = {
            'old_detects_more': detection_results['old_model_stats']['avg_detections_per_image'] > detection_results['current_model_stats']['avg_detections_per_image'],
            'detection_ratio': detection_results['old_model_stats']['avg_detections_per_image'] / detection_results['current_model_stats']['avg_detections_per_image'] if detection_results['current_model_stats']['avg_detections_per_image'] > 0 else 0,
            'confidence_difference': detection_results['old_model_stats']['avg_confidence'] - detection_results['current_model_stats']['avg_confidence']
        }
        
        return detection_results
        
    def verify_user_claims(self, detection_results: Dict):
        """验证用户的声明"""
        claims_verification = {
            'claim_1_current_model_low_accuracy': {
                'claim': '当前模型识别精度较低，经常漏掉',
                'evidence': [],
                'verified': False
            },
            'claim_2_old_model_high_accuracy': {
                'claim': '老版本模型识别精度高，框精准，不存在漏标，只会多标记',
                'evidence': [],
                'verified': False
            },
            'claim_3_old_model_limited_categories': {
                'claim': '老版本模型不能识别卡牌以外的类别',
                'evidence': [],
                'verified': False
            }
        }
        
        # 验证声明1：当前模型精度低
        current_avg_conf = detection_results['current_model_stats']['avg_confidence']
        if current_avg_conf < 0.7:  # 平均置信度较低
            claims_verification['claim_1_current_model_low_accuracy']['evidence'].append(f"平均置信度较低: {current_avg_conf:.3f}")
            
        current_avg_detections = detection_results['current_model_stats']['avg_detections_per_image']
        old_avg_detections = detection_results['old_model_stats']['avg_detections_per_image']
        
        if old_avg_detections > current_avg_detections * 1.2:  # 老模型检测数量明显更多
            claims_verification['claim_1_current_model_low_accuracy']['evidence'].append(f"检测数量较少: 当前{current_avg_detections:.1f} vs 老版本{old_avg_detections:.1f}")
            claims_verification['claim_1_current_model_low_accuracy']['verified'] = True
            
        # 验证声明2：老版本模型精度高
        old_avg_conf = detection_results['old_model_stats']['avg_confidence']
        if old_avg_conf > current_avg_conf:
            claims_verification['claim_2_old_model_high_accuracy']['evidence'].append(f"置信度更高: {old_avg_conf:.3f} vs {current_avg_conf:.3f}")
            
        if old_avg_detections > current_avg_detections:
            claims_verification['claim_2_old_model_high_accuracy']['evidence'].append(f"检测数量更多: {old_avg_detections:.1f} vs {current_avg_detections:.1f}")
            claims_verification['claim_2_old_model_high_accuracy']['verified'] = True
            
        # 验证声明3：老版本模型类别限制
        old_categories = self.verification_results['category_verification']['old_model']['actual']
        current_categories = self.verification_results['category_verification']['current_model']['actual']
        
        if old_categories < current_categories:
            claims_verification['claim_3_old_model_limited_categories']['evidence'].append(f"类别数量更少: {old_categories} vs {current_categories}")
            claims_verification['claim_3_old_model_limited_categories']['verified'] = True
            
        self.verification_results['user_claims_verification'] = claims_verification
        
    def collect_test_images(self, max_images: int = 10) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        # 从calibration_gt收集
        calibration_path = Path("legacy_assets/ceshi/calibration_gt/images")
        if calibration_path.exists():
            for img_file in calibration_path.glob("*.jpg"):
                test_images.append(str(img_file))
                if len(test_images) >= max_images:
                    break
                    
        # 如果不够，从其他目录收集
        if len(test_images) < max_images:
            tupian_path = Path("legacy_assets/ceshi/tupian")
            if tupian_path.exists():
                for img_file in tupian_path.glob("*.jpg"):
                    test_images.append(str(img_file))
                    if len(test_images) >= max_images:
                        break
                        
        return test_images
        
    def generate_verification_report(self) -> str:
        """生成验证报告"""
        report = []
        report.append("# 双模型快速验证报告\n")
        
        # 模型加载验证
        report.append("## 📋 模型加载验证")
        current_info = self.verification_results['model_loading']['current_model']
        old_info = self.verification_results['model_loading']['old_model']
        
        report.append(f"- **当前模型**: {current_info['class_count']} 个类别")
        report.append(f"- **老版本模型**: {old_info['class_count']} 个类别")
        report.append("")
        
        # 类别验证
        report.append("## 🎯 类别数量验证")
        cat_verify = self.verification_results['category_verification']
        
        current_result = "✅ 正确" if cat_verify['current_model']['claim_correct'] else "❌ 不符"
        old_result = "✅ 正确" if cat_verify['old_model']['claim_correct'] else "❌ 不符"
        
        report.append(f"- **当前模型类别声明**: {current_result} (期望31个，实际{cat_verify['current_model']['actual']}个)")
        report.append(f"- **老版本模型类别声明**: {old_result} (期望21个，实际{cat_verify['old_model']['actual']}个)")
        report.append("")
        
        # 用户声明验证
        report.append("## 🔍 用户声明验证")
        claims = self.verification_results['user_claims_verification']
        
        for claim_key, claim_data in claims.items():
            status = "✅ 验证通过" if claim_data['verified'] else "❌ 未验证"
            report.append(f"### {claim_data['claim']}")
            report.append(f"**验证结果**: {status}")
            
            if claim_data['evidence']:
                report.append("**证据**:")
                for evidence in claim_data['evidence']:
                    report.append(f"- {evidence}")
            report.append("")
            
        # 融合建议
        report.append("## 💡 融合策略建议")
        
        # 基于验证结果生成建议
        old_better_detection = claims['claim_2_old_model_high_accuracy']['verified']
        current_more_categories = claims['claim_3_old_model_limited_categories']['verified']
        
        if old_better_detection and current_more_categories:
            report.append("**推荐策略**: 级联融合 - 老模型主导检测")
            report.append("**理由**: 老版本模型检测能力更强，当前模型类别更全面")
            report.append("**实施方案**:")
            report.append("1. 使用老版本模型进行卡牌检测（高召回率）")
            report.append("2. 使用当前模型进行类别分类和非卡牌检测")
            report.append("3. 智能融合两个模型的结果")
        else:
            report.append("**需要更多验证**: 当前测试数据不足以确定最优策略")
            
        return "\n".join(report)
        
    def run_quick_verification(self) -> Dict:
        """运行快速验证"""
        logger.info("开始快速模型验证...")
        
        # 1. 加载模型
        if not self.load_and_verify_models():
            return {}
            
        # 2. 验证类别声明
        self.verify_category_claims()
        
        # 3. 收集测试图像
        test_images = self.collect_test_images(max_images=10)
        logger.info(f"收集到 {len(test_images)} 张测试图像")
        
        if not test_images:
            logger.warning("未找到测试图像，跳过检测测试")
            return self.verification_results
            
        # 4. 快速检测测试
        detection_results = self.quick_detection_test(test_images)
        self.verification_results['detection_comparison'] = detection_results
        
        # 5. 验证用户声明
        self.verify_user_claims(detection_results)
        
        # 6. 生成报告
        report = self.generate_verification_report()
        
        # 7. 保存结果
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report_file = f"analysis/quick_model_verification_{timestamp}.md"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"验证报告已保存: {report_file}")
        
        return self.verification_results

def main():
    """主函数"""
    print("⚡ 快速模型验证器")
    print("=" * 50)
    
    verifier = QuickModelVerifier()
    
    # 运行验证
    results = verifier.run_quick_verification()
    
    if results:
        print("\n📊 快速验证结果:")
        
        # 显示关键发现
        if 'category_verification' in results:
            cat_verify = results['category_verification']
            print(f"   当前模型类别: {cat_verify['current_model']['actual']} (期望31)")
            print(f"   老版本模型类别: {cat_verify['old_model']['actual']} (期望21)")
            
        if 'user_claims_verification' in results:
            claims = results['user_claims_verification']
            verified_claims = sum(1 for claim in claims.values() if claim['verified'])
            print(f"   用户声明验证: {verified_claims}/3 个声明得到验证")
            
        print(f"\n✅ 详细验证报告已生成，请查看analysis目录")
    else:
        print("❌ 验证失败，请检查模型文件路径")

if __name__ == "__main__":
    main()

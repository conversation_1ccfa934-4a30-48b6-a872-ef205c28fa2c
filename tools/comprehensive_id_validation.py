#!/usr/bin/env python3
"""
全面ID分配验证脚本 - 利用大数据优势

使用494张zhuangtaiquyu图像进行全面验证，分析ID分配算法的问题
"""

import sys
import os
import json
import re
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.digital_twin_v2 import DigitalTwinCoordinator, CardDetection

# 配置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出
logger = logging.getLogger(__name__)

class ComprehensiveIDValidator:
    """全面ID分配验证器"""
    
    def __init__(self):
        self.coordinator = DigitalTwinCoordinator()
        self.validation_results = {
            "total_frames": 0,
            "total_cards": 0,
            "correct_assignments": 0,
            "error_patterns": defaultdict(int),
            "region_analysis": defaultdict(lambda: {"total": 0, "correct": 0}),
            "card_type_analysis": defaultdict(lambda: {"total": 0, "correct": 0}),
            "detailed_errors": []
        }
        
        # 卡牌名称映射
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_ground_truth_label(self, label: str) -> Tuple[str, str]:
        """解析人工标注标签"""
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
            
    def load_frame_data(self, json_file: Path) -> Tuple[List[CardDetection], List[Dict]]:
        """加载单帧数据"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            detections = []
            ground_truth = []
            
            for shape in data.get('shapes', []):
                if len(shape.get('points', [])) >= 4:
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[2]
                    
                    label = shape.get('label', '')
                    twin_id, card_name = self.parse_ground_truth_label(label)
                    
                    # 只处理有效的卡牌标注
                    if twin_id and card_name and card_name not in ['吃', '过', '胡', '碰']:
                        # 创建检测对象
                        detection = CardDetection(
                            label=card_name,
                            bbox=[x1, y1, x2, y2],
                            confidence=0.9,
                            group_id=shape.get('group_id', 1),
                            region_name=f"region_{shape.get('group_id', 1)}"
                        )
                        detections.append(detection)
                        
                        # 创建真实标注
                        gt = {
                            "twin_id": twin_id,
                            "card_name": card_name,
                            "bbox": [x1, y1, x2, y2],
                            "group_id": shape.get('group_id', 1),
                            "expected_full_id": f"{twin_id}_{card_name}"
                        }
                        ground_truth.append(gt)
                        
            return detections, ground_truth
            
        except Exception as e:
            logger.error(f"加载文件 {json_file} 失败: {e}")
            return [], []
            
    def calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
            
        inter_area = (x2 - x1) * (y2 - y1)
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
        
    def validate_frame(self, detections: List[CardDetection], ground_truth: List[Dict], frame_name: str):
        """验证单帧"""
        if not detections or not ground_truth:
            return
            
        # 处理检测结果
        result = self.coordinator.process_frame(detections)
        predicted_cards = result['digital_twin_cards']
        
        self.validation_results["total_frames"] += 1
        
        # 匹配预测结果与真实标注
        for gt in ground_truth:
            self.validation_results["total_cards"] += 1
            
            # 寻找最佳匹配
            best_match = None
            best_iou = 0.0
            
            for pred_card in predicted_cards:
                if pred_card.label == gt["card_name"]:
                    iou = self.calculate_iou(gt["bbox"], pred_card.bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_match = pred_card
                        
            if best_match and best_iou > 0.5:
                # 检查ID是否正确
                expected_id = gt["expected_full_id"]
                actual_id = best_match.twin_id
                
                # 更新统计
                region_id = gt["group_id"]
                card_type = gt["card_name"]
                
                self.validation_results["region_analysis"][region_id]["total"] += 1
                self.validation_results["card_type_analysis"][card_type]["total"] += 1
                
                if expected_id == actual_id:
                    self.validation_results["correct_assignments"] += 1
                    self.validation_results["region_analysis"][region_id]["correct"] += 1
                    self.validation_results["card_type_analysis"][card_type]["correct"] += 1
                else:
                    # 记录错误模式
                    error_pattern = f"{expected_id} -> {actual_id}"
                    self.validation_results["error_patterns"][error_pattern] += 1
                    
                    # 记录详细错误
                    self.validation_results["detailed_errors"].append({
                        "frame": frame_name,
                        "region": region_id,
                        "card_type": card_type,
                        "expected": expected_id,
                        "actual": actual_id,
                        "iou": best_iou
                    })
                    
    def run_comprehensive_validation(self):
        """运行全面验证"""
        print("🚀 开始全面ID分配验证（利用大数据优势）")
        print("=" * 60)
        
        # 加载所有zhuangtaiquyu数据
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        
        if not zhuangtaiquyu_path.exists():
            print("❌ zhuangtaiquyu数据集未找到")
            return
            
        # 收集所有JSON文件
        json_files = []
        for region_dir in zhuangtaiquyu_path.iterdir():
            if region_dir.is_dir():
                for json_file in region_dir.glob("*.json"):
                    json_files.append(json_file)
                    
        print(f"📊 发现 {len(json_files)} 个标注文件")
        
        # 处理所有文件
        processed = 0
        for json_file in json_files:
            detections, ground_truth = self.load_frame_data(json_file)
            if detections and ground_truth:
                self.validate_frame(detections, ground_truth, json_file.name)
                processed += 1
                
                if processed % 50 == 0:
                    print(f"📈 已处理 {processed}/{len(json_files)} 个文件...")
                    
        print(f"✅ 验证完成，共处理 {processed} 个有效文件")
        
    def analyze_results(self):
        """分析验证结果"""
        results = self.validation_results
        
        if results["total_cards"] == 0:
            print("❌ 没有有效的验证数据")
            return
            
        # 总体准确率
        overall_accuracy = results["correct_assignments"] / results["total_cards"] * 100
        
        print(f"\n📊 总体验证结果:")
        print(f"   - 总帧数: {results['total_frames']}")
        print(f"   - 总卡牌数: {results['total_cards']}")
        print(f"   - 正确分配: {results['correct_assignments']}")
        print(f"   - 总体准确率: {overall_accuracy:.1f}%")
        
        # 区域分析
        print(f"\n📍 区域准确率分析:")
        for region_id, stats in sorted(results["region_analysis"].items(), key=lambda x: x[0] or 0):
            if stats["total"] > 0:
                accuracy = stats["correct"] / stats["total"] * 100
                print(f"   - 区域{region_id}: {accuracy:.1f}% ({stats['correct']}/{stats['total']})")
                
        # 卡牌类型分析
        print(f"\n🎴 卡牌类型准确率分析:")
        card_accuracies = []
        for card_type, stats in sorted(results["card_type_analysis"].items()):
            if stats["total"] > 0:
                accuracy = stats["correct"] / stats["total"] * 100
                card_accuracies.append((card_type, accuracy, stats["total"]))
                
        # 按准确率排序
        card_accuracies.sort(key=lambda x: x[1])
        
        print("   最差的卡牌类型:")
        for card_type, accuracy, total in card_accuracies[:5]:
            print(f"   - {card_type}: {accuracy:.1f}% ({total}张)")
            
        print("   最好的卡牌类型:")
        for card_type, accuracy, total in card_accuracies[-5:]:
            print(f"   - {card_type}: {accuracy:.1f}% ({total}张)")
            
        # 错误模式分析
        print(f"\n🔍 最常见错误模式:")
        top_errors = Counter(results["error_patterns"]).most_common(10)
        for pattern, count in top_errors:
            percentage = count / (results["total_cards"] - results["correct_assignments"]) * 100
            print(f"   - {pattern}: {count}次 ({percentage:.1f}%)")
            
        # 偏移分析
        self.analyze_offset_patterns()
        
    def analyze_offset_patterns(self):
        """分析偏移模式"""
        print(f"\n📈 偏移模式分析:")
        
        offsets = []
        for error in self.validation_results["detailed_errors"]:
            expected = error["expected"]
            actual = error["actual"]
            
            # 提取数字ID
            try:
                expected_num = int(expected.split('_')[0])
                actual_num = int(actual.split('_')[0]) if not actual.startswith('虚拟_') else 0
                
                if actual_num > 0:
                    offset = actual_num - expected_num
                    offsets.append(offset)
            except:
                continue
                
        if offsets:
            offset_counter = Counter(offsets)
            total_offsets = len(offsets)
            
            print("   偏移分布:")
            for offset, count in sorted(offset_counter.items()):
                percentage = count / total_offsets * 100
                print(f"   - {offset:+d}: {count}次 ({percentage:.1f}%)")
                
            # 系统性偏移检测
            plus_one = offset_counter.get(1, 0)
            minus_one = offset_counter.get(-1, 0)
            
            if plus_one > total_offsets * 0.3:
                print(f"   🔴 检测到系统性+1偏移: {plus_one}/{total_offsets} ({plus_one/total_offsets*100:.1f}%)")
            if minus_one > total_offsets * 0.3:
                print(f"   🔴 检测到系统性-1偏移: {minus_one}/{total_offsets} ({minus_one/total_offsets*100:.1f}%)")
                
    def save_results(self):
        """保存验证结果"""
        output_path = "analysis/comprehensive_id_validation_report.json"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 计算总体准确率
        if self.validation_results["total_cards"] > 0:
            overall_accuracy = self.validation_results["correct_assignments"] / self.validation_results["total_cards"]
        else:
            overall_accuracy = 0.0
            
        report = {
            "timestamp": str(datetime.now()),
            "overall_accuracy": overall_accuracy,
            "summary": {
                "total_frames": self.validation_results["total_frames"],
                "total_cards": self.validation_results["total_cards"],
                "correct_assignments": self.validation_results["correct_assignments"],
                "error_count": self.validation_results["total_cards"] - self.validation_results["correct_assignments"]
            },
            "region_analysis": dict(self.validation_results["region_analysis"]),
            "card_type_analysis": dict(self.validation_results["card_type_analysis"]),
            "error_patterns": dict(self.validation_results["error_patterns"]),
            "detailed_errors": self.validation_results["detailed_errors"][:100]  # 限制详细错误数量
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n📄 详细报告已保存: {output_path}")

def main():
    """主函数"""
    validator = ComprehensiveIDValidator()
    
    # 运行全面验证
    validator.run_comprehensive_validation()
    
    # 分析结果
    validator.analyze_results()
    
    # 保存结果
    validator.save_results()
    
    print("\n🎯 验证完成！")

if __name__ == "__main__":
    main()

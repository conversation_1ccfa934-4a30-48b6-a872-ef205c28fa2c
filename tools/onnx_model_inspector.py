#!/usr/bin/env python3
"""
ONNX模型检查器

深度分析ONNX模型的结构和输出格式，找出推理问题的根源
"""

import sys
import os
import numpy as np
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ONNXModelInspector:
    """ONNX模型检查器"""
    
    def __init__(self):
        self.current_onnx_path = "best.onnx"
        self.old_onnx_path = "data/processed/train3.0/weights/best.onnx"
        
    def inspect_onnx_model(self, onnx_path: str):
        """检查ONNX模型结构"""
        try:
            import onnx
            import onnxruntime as ort
            
            logger.info(f"检查ONNX模型: {onnx_path}")
            
            # 加载ONNX模型
            model = onnx.load(onnx_path)
            
            # 检查模型信息
            logger.info(f"模型版本: {model.ir_version}")
            logger.info(f"生产者: {model.producer_name} {model.producer_version}")
            
            # 检查输入输出
            session = ort.InferenceSession(onnx_path)
            
            logger.info("输入信息:")
            for input_info in session.get_inputs():
                logger.info(f"  名称: {input_info.name}")
                logger.info(f"  形状: {input_info.shape}")
                logger.info(f"  类型: {input_info.type}")
                
            logger.info("输出信息:")
            for output_info in session.get_outputs():
                logger.info(f"  名称: {output_info.name}")
                logger.info(f"  形状: {output_info.shape}")
                logger.info(f"  类型: {output_info.type}")
                
            return session
            
        except ImportError:
            logger.error("需要安装: pip install onnx onnxruntime")
            return None
        except Exception as e:
            logger.error(f"检查ONNX模型失败: {e}")
            return None
            
    def test_onnx_output_format(self, session, test_image_path: str):
        """测试ONNX输出格式"""
        try:
            import cv2
            
            # 读取测试图像
            image = cv2.imread(test_image_path)
            if image is None:
                logger.error(f"无法读取图像: {test_image_path}")
                return
                
            # 预处理（简单版本）
            input_size = 640
            resized = cv2.resize(image, (input_size, input_size))
            input_tensor = resized.transpose(2, 0, 1).astype(np.float32) / 255.0
            input_tensor = np.expand_dims(input_tensor, axis=0)
            
            # 获取输入名称
            input_name = session.get_inputs()[0].name
            
            # 推理
            outputs = session.run(None, {input_name: input_tensor})
            
            logger.info(f"输出数量: {len(outputs)}")
            for i, output in enumerate(outputs):
                logger.info(f"输出 {i}:")
                logger.info(f"  形状: {output.shape}")
                logger.info(f"  数据类型: {output.dtype}")
                logger.info(f"  数值范围: [{output.min():.6f}, {output.max():.6f}]")
                logger.info(f"  前10个值: {output.flatten()[:10]}")
                
                # 分析可能的格式
                if len(output.shape) == 3 and output.shape[2] > 10:
                    logger.info(f"  可能格式: [batch, detections, attributes]")
                    logger.info(f"  属性数: {output.shape[2]}")
                    
                    # 检查前几个检测的置信度
                    if output.shape[2] >= 5:
                        confidences = output[0, :10, 4]  # 假设第5列是置信度
                        logger.info(f"  前10个置信度: {confidences}")
                        
                elif len(output.shape) == 2:
                    logger.info(f"  可能格式: [detections, attributes]")
                    
        except Exception as e:
            logger.error(f"测试ONNX输出失败: {e}")
            
    def compare_pt_onnx_outputs(self, test_image_path: str):
        """对比PT和ONNX的输出"""
        try:
            from ultralytics import YOLO
            
            logger.info("对比PT和ONNX输出...")
            
            # PT模型推理
            if os.path.exists("best.pt"):
                pt_model = YOLO("best.pt")
                pt_results = pt_model(test_image_path, conf=0.25, verbose=False)
                
                logger.info("PT模型输出:")
                if pt_results and pt_results[0].boxes is not None:
                    boxes = pt_results[0].boxes
                    logger.info(f"  检测数量: {len(boxes)}")
                    logger.info(f"  置信度范围: [{boxes.conf.min():.3f}, {boxes.conf.max():.3f}]")
                    logger.info(f"  类别数: {len(boxes.cls.unique())}")
                    
                    # 显示前几个检测
                    for i in range(min(5, len(boxes))):
                        box = boxes.xyxy[i].cpu().numpy()
                        conf = boxes.conf[i].cpu().numpy()
                        cls = boxes.cls[i].cpu().numpy()
                        logger.info(f"  检测 {i}: bbox={box}, conf={conf:.3f}, cls={int(cls)}")
                        
            # ONNX模型推理
            if os.path.exists("best.onnx"):
                onnx_session = self.inspect_onnx_model("best.onnx")
                if onnx_session:
                    logger.info("\nONNX模型输出:")
                    self.test_onnx_output_format(onnx_session, test_image_path)
                    
        except Exception as e:
            logger.error(f"对比输出失败: {e}")
            
    def run_inspection(self):
        """运行完整检查"""
        logger.info("开始ONNX模型深度检查...")
        
        # 检查当前模型
        if os.path.exists(self.current_onnx_path):
            logger.info("\n" + "="*50)
            logger.info("检查当前模型ONNX")
            self.inspect_onnx_model(self.current_onnx_path)
        else:
            logger.warning(f"当前模型ONNX不存在: {self.current_onnx_path}")
            
        # 检查老版本模型
        if os.path.exists(self.old_onnx_path):
            logger.info("\n" + "="*50)
            logger.info("检查老版本模型ONNX")
            self.inspect_onnx_model(self.old_onnx_path)
        else:
            logger.warning(f"老版本模型ONNX不存在: {self.old_onnx_path}")
            
        # 找一张测试图像
        test_image = None
        calibration_path = Path("legacy_assets/ceshi/calibration_gt/images")
        if calibration_path.exists():
            for img_file in calibration_path.glob("*.jpg"):
                test_image = str(img_file)
                break
                
        if test_image:
            logger.info("\n" + "="*50)
            logger.info(f"使用测试图像: {Path(test_image).name}")
            self.compare_pt_onnx_outputs(test_image)
        else:
            logger.warning("未找到测试图像")

def main():
    """主函数"""
    print("🔍 ONNX模型深度检查器")
    print("=" * 50)
    
    inspector = ONNXModelInspector()
    inspector.run_inspection()
    
    print("\n✅ ONNX模型检查完成")

if __name__ == "__main__":
    main()

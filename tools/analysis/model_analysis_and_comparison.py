#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型分析和对比脚本
深度分析"牌局结束"识别问题的原因，并测试不同模型
"""

import sys
import os
import cv2
import json
import numpy as np
from pathlib import Path
from collections import Counter

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector

class ModelAnalysisAndComparison:
    """模型分析和对比类"""
    
    def __init__(self):
        """初始化"""
        self.base_path = Path("legacy_assets/ceshi")
        self.models_to_test = [
            {
                "name": "当前模型",
                "path": "best.pt",
                "description": "项目根目录的当前模型"
            },
            {
                "name": "train5.0模型",
                "path": "models/train5.0/weights/best.pt",
                "description": "可能有不同标签映射的模型"
            },
            {
                "name": "train9.0模型", 
                "path": "data/processed/train9.0/weights/best.pt",
                "description": "与当前标签映射相同的模型"
            }
        ]
        self.analysis_results = {}
        
    def analyze_current_label_mapping(self):
        """分析当前标签映射问题"""
        print("🔍 分析当前标签映射问题...")
        
        # 检查detect.py中的映射
        try:
            from src.core.detect import LABEL_TO_ID, ID_TO_LABEL
            
            print(f"\n📋 当前LABEL_TO_ID映射:")
            for label, id in LABEL_TO_ID.items():
                print(f"   {label}: {id}")
            
            print(f"\n📋 当前ID_TO_LABEL映射:")
            for id, label in sorted(ID_TO_LABEL.items()):
                print(f"   {id}: {label}")
            
            # 检查重复问题
            print(f"\n🚨 检查映射问题:")
            
            # 检查LABEL_TO_ID中的重复键
            label_counts = Counter(LABEL_TO_ID.keys())
            duplicate_labels = [label for label, count in label_counts.items() if count > 1]
            
            if duplicate_labels:
                print(f"   发现重复标签: {duplicate_labels}")
            else:
                print(f"   未发现重复标签")
            
            # 检查ID_TO_LABEL中的重复值
            id_counts = Counter(LABEL_TO_ID.values())
            duplicate_ids = [id for id, count in id_counts.items() if count > 1]
            
            if duplicate_ids:
                print(f"   发现重复ID: {duplicate_ids}")
            else:
                print(f"   未发现重复ID")
            
            # 检查"牌局结束"的映射
            if "牌局结束" in LABEL_TO_ID:
                paiju_id = LABEL_TO_ID["牌局结束"]
                print(f"   '牌局结束'映射到ID: {paiju_id}")
                
                if paiju_id in ID_TO_LABEL:
                    reverse_label = ID_TO_LABEL[paiju_id]
                    print(f"   ID {paiju_id} 反向映射到: '{reverse_label}'")
                    
                    if reverse_label != "牌局结束":
                        print(f"   🚨 映射不一致！'{reverse_label}' != '牌局结束'")
                else:
                    print(f"   🚨 ID {paiju_id} 在ID_TO_LABEL中不存在！")
            else:
                print(f"   🚨 '牌局结束'在LABEL_TO_ID中不存在！")
                
        except Exception as e:
            print(f"   ❌ 分析映射时出错: {e}")
    
    def check_model_availability(self):
        """检查模型文件可用性"""
        print(f"\n📦 检查模型文件可用性...")
        
        available_models = []
        
        for model_info in self.models_to_test:
            model_path = model_info["path"]
            if os.path.exists(model_path):
                file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
                print(f"   ✅ {model_info['name']}: {model_path} ({file_size:.1f}MB)")
                available_models.append(model_info)
            else:
                print(f"   ❌ {model_info['name']}: {model_path} (不存在)")
        
        return available_models
    
    def test_model_on_frame_00371(self, model_path: str, model_name: str):
        """测试模型在frame_00371.jpg上的表现"""
        print(f"\n🖼️  测试 {model_name} 在frame_00371.jpg上的表现:")
        
        # 读取图片
        img_path = self.base_path / "calibration_gt" / "images" / "frame_00371.jpg"
        if not img_path.exists():
            print(f"   ❌ 图片不存在: {img_path}")
            return None
        
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"   ❌ 无法读取图片")
            return None
        
        try:
            # 初始化检测器
            detector = CardDetector(model_path, enable_validation=False)
            
            # 检测
            detections = detector.detect_image(image)
            
            # 分析结果
            labels = [det.get('label', 'unknown') for det in detections]
            confidences = [det.get('confidence', 0.0) for det in detections]
            
            print(f"   📊 检测结果 ({len(detections)}个):")
            for i, (label, conf) in enumerate(zip(labels, confidences)):
                print(f"      {i+1}. {label} (置信度: {conf:.3f})")
            
            # 检查是否包含"牌局结束"
            has_paiju = "牌局结束" in labels
            print(f"   🎯 是否检测到'牌局结束': {'✅ 是' if has_paiju else '❌ 否'}")
            
            # 检查unknown标签
            unknown_count = labels.count('unknown')
            if unknown_count > 0:
                print(f"   ⚠️  检测到 {unknown_count} 个unknown标签")
            
            return {
                'model_name': model_name,
                'model_path': model_path,
                'detections': detections,
                'labels': labels,
                'confidences': confidences,
                'has_paiju': has_paiju,
                'unknown_count': unknown_count
            }
            
        except Exception as e:
            print(f"   ❌ 测试模型时出错: {e}")
            return None
    
    def compare_models_on_key_frames(self, available_models):
        """对比不同模型在关键帧上的表现"""
        print(f"\n🔄 对比不同模型在关键帧上的表现...")
        
        key_frames = [
            "frame_00000.jpg",  # 打鸟选择画面
            "frame_00371.jpg"   # 牌局结束画面
        ]
        
        comparison_results = {}
        
        for frame_file in key_frames:
            print(f"\n📋 测试帧: {frame_file}")
            
            # 加载真实标注
            json_file = frame_file.replace('.jpg', '.json')
            json_path = self.base_path / "calibration_gt" / "labels" / json_file
            
            ground_truth = []
            if json_path.exists():
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    ground_truth = [shape.get('label', '') for shape in data.get('shapes', [])]
                except:
                    pass
            
            print(f"   📋 真实标注: {ground_truth}")
            
            frame_results = {}
            
            # 测试每个可用模型
            for model_info in available_models:
                result = self.test_model_on_frame(frame_file, model_info["path"], model_info["name"])
                if result:
                    frame_results[model_info["name"]] = result
            
            comparison_results[frame_file] = {
                'ground_truth': ground_truth,
                'model_results': frame_results
            }
        
        self.analysis_results['model_comparison'] = comparison_results
        return comparison_results
    
    def test_model_on_frame(self, frame_file: str, model_path: str, model_name: str):
        """测试模型在指定帧上的表现"""
        img_path = self.base_path / "calibration_gt" / "images" / frame_file
        if not img_path.exists():
            return None
        
        image = cv2.imread(str(img_path))
        if image is None:
            return None
        
        try:
            detector = CardDetector(model_path, enable_validation=False)
            detections = detector.detect_image(image)
            
            labels = [det.get('label', 'unknown') for det in detections]
            confidences = [det.get('confidence', 0.0) for det in detections]
            
            print(f"      {model_name}: {labels} (置信度: {[f'{c:.2f}' for c in confidences]})")
            
            return {
                'labels': labels,
                'confidences': confidences,
                'detection_count': len(detections)
            }
            
        except Exception as e:
            print(f"      {model_name}: ❌ 错误 - {e}")
            return None
    
    def analyze_paiju_detection_issue(self):
        """深度分析"牌局结束"检测问题"""
        print(f"\n🔬 深度分析'牌局结束'检测问题...")
        
        # 1. 检查当前映射表的问题
        print(f"\n1. 检查映射表问题:")
        self.analyze_current_label_mapping()
        
        # 2. 检查模型文件
        print(f"\n2. 检查可用模型:")
        available_models = self.check_model_availability()
        
        if not available_models:
            print(f"   ❌ 没有可用的模型文件")
            return
        
        # 3. 专门测试frame_00371.jpg
        print(f"\n3. 专门测试frame_00371.jpg:")
        for model_info in available_models:
            result = self.test_model_on_frame_00371(model_info["path"], model_info["name"])
            if result:
                self.analysis_results[f"frame_00371_{model_info['name']}"] = result
        
        # 4. 对比不同模型
        print(f"\n4. 对比不同模型:")
        self.compare_models_on_key_frames(available_models)
    
    def fix_label_mapping_issues(self):
        """修复标签映射问题"""
        print(f"\n🔧 修复标签映射问题...")
        
        # 检查train_yolo.py中的正确映射
        print(f"   检查train_yolo.py中的标准映射...")
        
        try:
            # 读取train_yolo.py中的映射
            with open("train_yolo.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找LABEL_TO_ID定义
            if "LABEL_TO_ID" in content:
                print(f"   找到train_yolo.py中的LABEL_TO_ID定义")
                
                # 提取映射内容
                start_idx = content.find("LABEL_TO_ID = {")
                if start_idx != -1:
                    brace_count = 0
                    end_idx = start_idx
                    for i, char in enumerate(content[start_idx:], start_idx):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_idx = i + 1
                                break
                    
                    mapping_str = content[start_idx:end_idx]
                    print(f"   标准映射定义:")
                    print(f"   {mapping_str}")
                    
                    # 生成修复脚本
                    self.generate_mapping_fix_script(mapping_str)
            
        except Exception as e:
            print(f"   ❌ 读取train_yolo.py时出错: {e}")
    
    def generate_mapping_fix_script(self, correct_mapping: str):
        """生成映射修复脚本"""
        print(f"\n📝 生成映射修复脚本...")
        
        fix_script = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
标签映射修复脚本 - 修复重复键问题
基于train_yolo.py中的标准映射修复detect.py中的映射错误
"""

def fix_detect_mapping():
    """修复detect.py中的标签映射"""
    
    # 从train_yolo.py中提取的正确映射
    correct_mapping = """{correct_mapping}"""
    
    print("🔧 修复detect.py中的标签映射...")
    
    detect_file = "src/core/detect.py"
    
    try:
        # 读取当前文件
        with open(detect_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = detect_file + ".backup2"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   原文件已备份至: {{backup_file}}")
        
        # 查找并替换LABEL_TO_ID定义
        start_marker = "LABEL_TO_ID = {{"
        end_marker = "}}"
        
        start_idx = content.find(start_marker)
        if start_idx != -1:
            # 找到结束位置
            brace_count = 0
            end_idx = start_idx
            for i, char in enumerate(content[start_idx:], start_idx):
                if char == '{{':
                    brace_count += 1
                elif char == '}}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break
            
            # 替换映射定义
            new_content = content[:start_idx] + correct_mapping + content[end_idx:]
            
            # 写入修复后的文件
            with open(detect_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"   ✅ 标签映射已修复")
            print(f"   📋 修复内容: 使用train_yolo.py中的标准映射")
            
        else:
            print(f"   ⚠️ 未找到LABEL_TO_ID定义")
            
    except Exception as e:
        print(f"   ❌ 修复过程中出错: {{e}}")

if __name__ == "__main__":
    fix_detect_mapping()
'''
        
        with open("fix_mapping_duplicates.py", 'w', encoding='utf-8') as f:
            f.write(fix_script)
        
        print(f"   ✅ 修复脚本已生成: fix_mapping_duplicates.py")
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print(f"\n📊 生成分析报告...")
        
        # 保存详细结果
        with open("model_analysis_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"   💾 详细结果已保存至: model_analysis_results.json")
        
        # 生成总结报告
        print(f"\n📋 分析总结:")
        
        # 检查是否有模型能正确检测"牌局结束"
        paiju_detection_success = False
        successful_models = []
        
        for key, result in self.analysis_results.items():
            if "frame_00371" in key and isinstance(result, dict):
                if result.get('has_paiju', False):
                    paiju_detection_success = True
                    model_name = result.get('model_name', 'unknown')
                    successful_models.append(model_name)
        
        if paiju_detection_success:
            print(f"   ✅ 找到能检测'牌局结束'的模型: {successful_models}")
        else:
            print(f"   ❌ 所有测试模型都无法检测'牌局结束'")
        
        # 分析可能的原因
        print(f"\n🔍 可能的原因分析:")
        print(f"   1. 标签映射重复键问题 - detect.py中存在重复的标签定义")
        print(f"   2. 模型训练问题 - 模型可能没有充分学习'牌局结束'类别")
        print(f"   3. 数据集问题 - 训练数据中'牌局结束'样本可能不足")
        print(f"   4. 类别ID冲突 - 不同标签映射到相同ID")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🚀 模型分析和对比")
        print("深度分析'牌局结束'识别问题的原因")
        
        try:
            # 分析"牌局结束"检测问题
            self.analyze_paiju_detection_issue()
            
            # 修复标签映射问题
            self.fix_label_mapping_issues()
            
            # 生成分析报告
            self.generate_analysis_report()
            
            print(f"\n🎉 分析完成！")
            
            print(f"\n📋 下一步建议:")
            print(f"   1. 运行: python fix_mapping_duplicates.py")
            print(f"   2. 重新测试: python cross_validation_test.py")
            print(f"   3. 如果问题仍存在，考虑重新训练模型")
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    analyzer = ModelAnalysisAndComparison()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析zhuangtaiquyu数据集中的实际区域分布
帮助理解真实的区域分配模式，优化区域定义
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector


def load_annotation(dataset_id: str, frame_name: str):
    """加载标注文件"""
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    json_file = base_path / "labels" / "train" / dataset_id / f"{frame_name}.json"
    
    if not json_file.exists():
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def extract_ground_truth_cards(annotation):
    """从标注中提取卡牌信息"""
    cards = []
    
    if 'shapes' not in annotation:
        return cards
    
    for shape in annotation['shapes']:
        if shape.get('shape_type') == 'rectangle':
            points = shape.get('points', [])
            if len(points) >= 4:
                # 4个点的矩形格式
                all_x = [p[0] for p in points]
                all_y = [p[1] for p in points]
                x1, x2 = min(all_x), max(all_x)
                y1, y2 = min(all_y), max(all_y)
                bbox = [x1, y1, x2, y2]
            elif len(points) >= 2:
                # 2个点格式
                bbox = [points[0][0], points[0][1], points[1][0], points[1][1]]
            else:
                continue
                
            card_info = {
                'label': shape.get('label', ''),
                'group_id': shape.get('group_id', 0),
                'bbox': bbox,
                'center_x': (bbox[0] + bbox[2]) / 2,
                'center_y': (bbox[1] + bbox[3]) / 2,
                'width': bbox[2] - bbox[0],
                'height': bbox[3] - bbox[1]
            }
            cards.append(card_info)
    
    return cards


def analyze_region_distribution():
    """分析区域分布"""
    print("🔍 分析zhuangtaiquyu数据集的实际区域分布")
    print("="*60)
    
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    
    # 区域ID映射
    region_mapping = {
        1: "手牌_观战方", 2: "调整手牌_观战方", 3: "抓牌_观战方",
        4: "打牌_观战方", 5: "弃牌_观战方", 6: "吃碰区_观战方",
        7: "抓牌_对战方", 8: "打牌_对战方", 9: "弃牌_对战方",
        10: "弹窗提示_观战方", 11: "透明提示_观战方", 12: "听牌区_观战方",
        13: "底牌区域", 14: "赢方区域", 15: "输方区域", 16: "吃碰区_对战方"
    }
    
    # 统计信息
    region_stats = defaultdict(lambda: {
        'count': 0, 
        'positions': [], 
        'labels': [],
        'x_range': [float('inf'), -float('inf')],
        'y_range': [float('inf'), -float('inf')]
    })
    
    total_cards = 0
    
    # 分析数据集1和11
    for dataset_id in ["1", "11"]:
        images_path = base_path / "images" / "train" / dataset_id
        if not images_path.exists():
            continue
            
        print(f"\n📁 分析数据集 {dataset_id}:")
        
        frame_files = sorted(list(images_path.glob("*.jpg")))[:5]  # 分析前5帧
        
        for frame_file in frame_files:
            frame_name = frame_file.stem
            
            # 加载图片获取尺寸
            image = cv2.imread(str(frame_file))
            if image is None:
                continue
            height, width = image.shape[:2]
            
            # 加载标注
            annotation = load_annotation(dataset_id, frame_name)
            if not annotation:
                continue
            
            cards = extract_ground_truth_cards(annotation)
            
            print(f"   帧 {frame_name}: {len(cards)} 张卡牌")
            
            for card in cards:
                region_id = card['group_id']
                if region_id is None:
                    continue
                    
                total_cards += 1
                
                # 统计区域信息
                region_stats[region_id]['count'] += 1
                region_stats[region_id]['positions'].append((card['center_x'], card['center_y']))
                region_stats[region_id]['labels'].append(card['label'])
                
                # 更新位置范围
                region_stats[region_id]['x_range'][0] = min(region_stats[region_id]['x_range'][0], card['center_x'])
                region_stats[region_id]['x_range'][1] = max(region_stats[region_id]['x_range'][1], card['center_x'])
                region_stats[region_id]['y_range'][0] = min(region_stats[region_id]['y_range'][0], card['center_y'])
                region_stats[region_id]['y_range'][1] = max(region_stats[region_id]['y_range'][1], card['center_y'])
                
                # 转换为相对坐标（百分比）
                rel_x = card['center_x'] / width
                rel_y = card['center_y'] / height
                
                print(f"      {card['label']} -> 区域{region_id}({region_mapping.get(region_id, 'unknown')}) 位置:({rel_x:.2f}, {rel_y:.2f})")
    
    # 生成分析报告
    print(f"\n📊 区域分布分析报告")
    print("="*60)
    print(f"总卡牌数: {total_cards}")
    
    for region_id in sorted(region_stats.keys()):
        if region_id is None:
            continue
            
        stats = region_stats[region_id]
        region_name = region_mapping.get(region_id, f"未知区域_{region_id}")
        
        print(f"\n🎯 区域 {region_id}: {region_name}")
        print(f"   卡牌数量: {stats['count']} ({stats['count']/total_cards*100:.1f}%)")
        
        if stats['positions']:
            # 计算平均位置
            avg_x = sum(pos[0] for pos in stats['positions']) / len(stats['positions'])
            avg_y = sum(pos[1] for pos in stats['positions']) / len(stats['positions'])
            
            # 假设图像尺寸为640x320（常见的YOLO输入尺寸）
            rel_avg_x = avg_x / 640
            rel_avg_y = avg_y / 320
            
            print(f"   平均位置: ({avg_x:.1f}, {avg_y:.1f}) 相对位置: ({rel_avg_x:.2f}, {rel_avg_y:.2f})")
            print(f"   X范围: {stats['x_range'][0]:.1f} - {stats['x_range'][1]:.1f}")
            print(f"   Y范围: {stats['y_range'][0]:.1f} - {stats['y_range'][1]:.1f}")
            
            # 统计标签分布
            label_counter = Counter(stats['labels'])
            top_labels = label_counter.most_common(5)
            print(f"   常见标签: {', '.join([f'{label}({count})' for label, count in top_labels])}")
    
    # 生成优化建议
    print(f"\n💡 区域定义优化建议")
    print("="*60)
    
    for region_id in sorted(region_stats.keys()):
        if region_id is None or region_stats[region_id]['count'] == 0:
            continue
            
        stats = region_stats[region_id]
        region_name = region_mapping.get(region_id, f"未知区域_{region_id}")
        
        if stats['positions']:
            # 计算相对位置范围
            positions = stats['positions']
            min_x = min(pos[0] for pos in positions) / 640
            max_x = max(pos[0] for pos in positions) / 640
            min_y = min(pos[1] for pos in positions) / 320
            max_y = max(pos[1] for pos in positions) / 320
            
            print(f"\n区域 {region_id} ({region_name}):")
            print(f"   建议定义: x_min={min_x:.2f}, x_max={max_x:.2f}, y_min={min_y:.2f}, y_max={max_y:.2f}")
            print(f"   像素范围: x({min_x*640:.0f}-{max_x*640:.0f}), y({min_y*320:.0f}-{max_y*320:.0f})")


if __name__ == "__main__":
    try:
        analyze_region_distribution()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
"""
分析frame_00123中区域6的数字孪生ID继承错误问题

根据测试素材详细介绍.md第115行：
- frame_00122.jpg: 8区域出现1七，观战方手牌1区域卡牌1八 2八
- frame_00123.jpg: 6区域从下到上依次应为 2八 1九 1七
  其中2八为从区域1流转（取最大值卡牌），1七从区域8流转

问题：当前继承的是另外一个1八而不是最大数值2八
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_cards_by_region(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in data.get("shapes", []):
        if shape.get("group_id") == region_id:
            card_info = {
                "label": shape.get("label", ""),
                "twin_id": shape.get("attributes", {}).get("digital_twin_id", ""),
                "bbox": shape.get("points", []),
                "y_center": 0
            }
            
            # 计算Y坐标中心点（用于排序）
            if len(card_info["bbox"]) == 4:
                y_coords = [point[1] for point in card_info["bbox"]]
                card_info["y_center"] = sum(y_coords) / len(y_coords)
            
            cards.append(card_info)
    
    return cards

def analyze_frame_122_123_inheritance():
    """分析frame_00122到frame_00123的继承问题"""
    print("🔍 分析frame_00122→frame_00123的区域6继承错误问题")
    print("=" * 70)
    
    # 加载两帧数据
    frame_122_data = load_frame_data(122)
    frame_123_data = load_frame_data(123)
    
    if not frame_122_data or not frame_123_data:
        print("❌ 无法加载帧数据")
        return
    
    print("\n📊 Frame_00122源数据分析")
    print("-" * 50)
    
    # 分析frame_00122的关键区域
    region_1_cards = extract_cards_by_region(frame_122_data, 1)  # 观战方手牌区
    region_8_cards = extract_cards_by_region(frame_122_data, 8)  # 对战方打牌区
    region_6_cards = extract_cards_by_region(frame_122_data, 6)  # 观战方吃碰区
    
    print(f"🎯 区域1（观战方手牌区）: {len(region_1_cards)}张卡牌")
    ba_cards_region_1 = []
    for i, card in enumerate(region_1_cards):
        if "八" in card['label']:
            ba_cards_region_1.append(card)
            print(f"  [{i+1}] ⭐ 八类卡牌: {card['label']} (ID: {card['twin_id']})")
        else:
            print(f"  [{i+1}] {card['label']} (ID: {card['twin_id']})")
    
    print(f"\n🎯 区域8（对战方打牌区）: {len(region_8_cards)}张卡牌")
    qi_cards_region_8 = []
    for i, card in enumerate(region_8_cards):
        if "七" in card['label']:
            qi_cards_region_8.append(card)
            print(f"  [{i+1}] ⭐ 七类卡牌: {card['label']} (ID: {card['twin_id']})")
        else:
            print(f"  [{i+1}] {card['label']} (ID: {card['twin_id']})")
    
    print(f"\n🎯 区域6（观战方吃碰区）: {len(region_6_cards)}张卡牌")
    for i, card in enumerate(region_6_cards):
        print(f"  [{i+1}] {card['label']} (ID: {card['twin_id']})")
    
    print("\n📊 Frame_00123目标数据分析")
    print("-" * 50)
    
    # 分析frame_00123的区域6
    region_6_cards_123 = extract_cards_by_region(frame_123_data, 6)
    region_1_cards_123 = extract_cards_by_region(frame_123_data, 1)
    
    print(f"🎯 区域6（观战方吃碰区）: {len(region_6_cards_123)}张卡牌")
    
    # 按Y坐标排序（从下到上）
    region_6_cards_123.sort(key=lambda x: -x['y_center'])
    
    ba_cards_region_6 = []
    qi_cards_region_6 = []
    jiu_cards_region_6 = []
    
    print("从下到上排序：")
    for i, card in enumerate(region_6_cards_123, 1):
        status = ""
        if "八" in card['label']:
            ba_cards_region_6.append(card)
            status = " ⭐ 八类卡牌"
        elif "七" in card['label']:
            qi_cards_region_6.append(card)
            status = " ⭐ 七类卡牌"
        elif "九" in card['label']:
            jiu_cards_region_6.append(card)
            status = " ⭐ 九类卡牌"
        
        print(f"  {i}. {card['label']} (ID: {card['twin_id']}, Y: {card['y_center']:.1f}){status}")
    
    print(f"\n🎯 区域1（观战方手牌区）: {len(region_1_cards_123)}张卡牌")
    ba_cards_region_1_123 = []
    for i, card in enumerate(region_1_cards_123):
        if "八" in card['label']:
            ba_cards_region_1_123.append(card)
            print(f"  [{i+1}] ⭐ 八类卡牌: {card['label']} (ID: {card['twin_id']})")
        else:
            print(f"  [{i+1}] {card['label']} (ID: {card['twin_id']})")
    
    print("\n🔍 继承优先级分析")
    print("-" * 50)
    
    # 分析八类卡牌的继承
    print("📋 八类卡牌继承分析：")
    print(f"  Frame_00122区域1中的八类卡牌: {len(ba_cards_region_1)}张")
    for card in ba_cards_region_1:
        print(f"    - {card['label']} (ID: {card['twin_id']})")
    
    print(f"  Frame_00123区域6中的八类卡牌: {len(ba_cards_region_6)}张")
    for card in ba_cards_region_6:
        print(f"    - {card['label']} (ID: {card['twin_id']})")
    
    print(f"  Frame_00123区域1中的八类卡牌: {len(ba_cards_region_1_123)}张")
    for card in ba_cards_region_1_123:
        print(f"    - {card['label']} (ID: {card['twin_id']})")
    
    # 分析七类卡牌的继承
    print("\n📋 七类卡牌继承分析：")
    print(f"  Frame_00122区域8中的七类卡牌: {len(qi_cards_region_8)}张")
    for card in qi_cards_region_8:
        print(f"    - {card['label']} (ID: {card['twin_id']})")
    
    print(f"  Frame_00123区域6中的七类卡牌: {len(qi_cards_region_6)}张")
    for card in qi_cards_region_6:
        print(f"    - {card['label']} (ID: {card['twin_id']})")
    
    print("\n💡 问题分析")
    print("-" * 50)
    
    # 检查预期vs实际
    expected_order = ["2八", "1九", "1七"]  # 从下到上的预期顺序
    actual_order = [card['twin_id'] for card in region_6_cards_123]
    
    print("📊 预期vs实际对比：")
    print(f"  预期顺序（从下到上）: {expected_order}")
    print(f"  实际顺序（从下到上）: {actual_order}")
    
    # 分析八类卡牌的问题
    if ba_cards_region_1:
        max_ba_id = max(ba_cards_region_1, key=lambda x: int(x['twin_id'][0]) if x['twin_id'] and x['twin_id'][0].isdigit() else 0)
        print(f"\n🔍 八类卡牌优先级分析：")
        print(f"  Frame_00122区域1中最大数值的八类卡牌: {max_ba_id['twin_id']}")
        
        if ba_cards_region_6:
            actual_ba_id = ba_cards_region_6[0]['twin_id']
            print(f"  Frame_00123区域6中实际继承的八类卡牌: {actual_ba_id}")
            
            if actual_ba_id == max_ba_id['twin_id']:
                print("  ✅ 八类卡牌继承正确：继承了最大数值")
            else:
                print("  ❌ 八类卡牌继承错误：没有继承最大数值")
                print(f"     期望: {max_ba_id['twin_id']}, 实际: {actual_ba_id}")
    
    print("\n🔧 可能的问题原因")
    print("-" * 50)
    print("1. 跨区域继承的优先级选择逻辑可能有问题")
    print("2. 当源区域有多张同类别卡牌时，可能没有正确选择最大数值")
    print("3. SimpleInheritor的_process_region_6_priority_inheritance方法可能需要改进")
    print("4. 可能存在模块间覆盖导致优先级失效")
    
    return {
        "frame_122_region_1_ba_cards": ba_cards_region_1,
        "frame_122_region_8_qi_cards": qi_cards_region_8,
        "frame_123_region_6_cards": region_6_cards_123,
        "frame_123_region_1_ba_cards": ba_cards_region_1_123,
        "expected_order": expected_order,
        "actual_order": actual_order
    }

def analyze_inheritance_priority_mechanism():
    """分析继承优先级机制"""
    print("\n🔧 继承优先级机制分析")
    print("=" * 70)
    
    print("📋 当前设计的优先级顺序：")
    print("  1. 优先级1: 本区域状态继承（6→6）")
    print("  2. 优先级2: 观战抓牌区继承（3→6）")
    print("  3. 优先级3: 对战抓牌区继承（7→6）")
    print("  4. 优先级4: 对战打牌区继承（8→6）")
    print("  5. 优先级5: 观战手牌区继承（1→6）")
    
    print("\n🎯 Frame_00123的具体流转需求：")
    print("  - 8→6流转: 1七（对战方打牌区→观战方吃碰区）")
    print("  - 1→6流转: 2八（观战方手牌区→观战方吃碰区，取最大值）")
    
    print("\n❓ 需要验证的问题：")
    print("  1. 当前的优先级顺序是否正确？")
    print("  2. 1→6流转时是否正确选择了最大数值的卡牌？")
    print("  3. 多个流转同时发生时的处理顺序是否正确？")

if __name__ == "__main__":
    result = analyze_frame_122_123_inheritance()
    analyze_inheritance_priority_mechanism()
    
    print("\n📋 分析总结")
    print("=" * 70)
    if result:
        print("✅ 数据分析完成，详细信息见上述报告")
        print("🔍 重点关注八类卡牌的继承优先级问题")
    else:
        print("❌ 数据分析失败，请检查文件路径")

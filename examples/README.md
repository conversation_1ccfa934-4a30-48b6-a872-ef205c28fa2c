# 📚 使用示例

本目录包含跑胡子AI系统的各种使用示例，帮助用户快速上手和学习高级功能。

## 📁 目录结构

### quick_start/
快速开始示例，适合新用户
- `simple_detection_demo.py` - 基础卡牌检测演示
- `dual_format_demo.py` - 双格式输出演示
- `basic_usage_demo.py` - 基本功能使用演示

### advanced/
高级功能示例，适合有经验的用户
- `batch_processing_demo.py` - 批量处理演示
- `real_data_demo.py` - 真实数据处理演示
- `memory_mechanism_demo.py` - 记忆机制演示
- `performance_optimization_demo.py` - 性能优化演示

### calibration_gt/
calibration_gt数据处理示例
- `processing_example.py` - 数据处理完整示例
- `digital_twin_example.py` - 数字孪生生成示例
- `validation_example.py` - 验证和检查示例

### integration/
系统集成示例
- `api_integration_demo.py` - API集成演示
- `pipeline_demo.py` - 完整流水线演示
- `custom_workflow_demo.py` - 自定义工作流演示

## 🚀 快速开始

### 1. 基础检测演示
```bash
# 运行简单检测演示
python examples/quick_start/simple_detection_demo.py

# 预期输出：检测结果可视化和JSON报告
```

### 2. 双格式输出演示
```bash
# 运行双格式演示
python examples/quick_start/dual_format_demo.py

# 预期输出：RLCard和AnyLabeling格式文件
```

### 3. 批量处理演示
```bash
# 运行批量处理演示
python examples/advanced/batch_processing_demo.py

# 预期输出：批量处理结果和性能报告
```

## 📋 示例说明

### 新用户推荐路径
1. **simple_detection_demo.py** - 了解基本检测功能
2. **dual_format_demo.py** - 学习双格式输出
3. **basic_usage_demo.py** - 掌握基本使用方法
4. **batch_processing_demo.py** - 学习批量处理

### 高级用户路径
1. **real_data_demo.py** - 真实数据处理技巧
2. **memory_mechanism_demo.py** - 记忆机制应用
3. **performance_optimization_demo.py** - 性能优化方法
4. **custom_workflow_demo.py** - 自定义工作流

### 开发者路径
1. **api_integration_demo.py** - API集成方法
2. **pipeline_demo.py** - 流水线设计
3. **calibration_gt/processing_example.py** - 数据处理最佳实践

## 🔧 运行要求

### 环境要求
- Python 3.10+
- 已安装项目依赖 (`pip install -r requirements.txt`)
- GPU推荐 (CPU也可运行，但速度较慢)

### 数据要求
- 部分示例需要测试数据 (data/calibration_gt/)
- 部分示例会自动生成测试数据
- 确保有足够的磁盘空间存储输出结果

### 权限要求
- 读取测试数据的权限
- 写入输出目录的权限
- GPU访问权限 (如果使用GPU)

## 📊 示例特点

### 教育性
- **渐进式学习**: 从简单到复杂
- **详细注释**: 每行代码都有说明
- **错误处理**: 展示正确的错误处理方法
- **最佳实践**: 体现项目的最佳实践

### 实用性
- **真实场景**: 基于实际使用场景
- **完整流程**: 展示完整的处理流程
- **性能考虑**: 包含性能优化建议
- **可扩展性**: 易于修改和扩展

### 可维护性
- **模块化设计**: 清晰的模块划分
- **配置分离**: 配置与代码分离
- **文档完整**: 每个示例都有详细文档
- **版本兼容**: 与项目版本保持同步

## 🎯 使用建议

### 学习顺序
1. **阅读README**: 了解示例结构和要求
2. **运行quick_start**: 从最简单的示例开始
3. **查看代码**: 仔细阅读代码和注释
4. **修改参数**: 尝试修改参数观察效果
5. **扩展功能**: 基于示例开发自己的功能

### 最佳实践
- **备份数据**: 运行前备份重要数据
- **检查环境**: 确保环境配置正确
- **阅读输出**: 仔细查看输出结果和日志
- **记录问题**: 遇到问题及时记录和反馈

## 🆘 获取帮助

### 问题排查
1. **检查依赖**: 确保所有依赖已正确安装
2. **查看日志**: 检查错误日志获取详细信息
3. **参考文档**: 查阅相关技术文档
4. **社区支持**: 在GitHub Issues中寻求帮助

### 贡献示例
- **发现问题**: 报告示例中的问题
- **改进建议**: 提出改进建议
- **新增示例**: 贡献新的使用示例
- **文档完善**: 帮助完善示例文档

---

**🎉 开始探索**: 选择适合您水平的示例开始学习吧！

"""
数字孪生系统V2.0 演示脚本

本脚本展示了数字孪生系统V2.0的核心功能，包括：
1. 基础使用方法
2. 物理约束验证
3. 帧间继承演示
4. 多帧共识验证
5. AnyLabeling格式导出
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator,
    CardDetection,
    create_digital_twin_system
)

def demo_basic_usage():
    """演示基础使用方法"""
    print("🎯 演示1: 基础使用方法")
    print("-" * 40)
    
    # 创建数字孪生系统
    dt_system = create_digital_twin_system()
    
    # 模拟检测结果
    detections = [
        CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
        CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
        CardDetection("四", [220, 100, 270, 150], 0.88, 1, "手牌_观战方", "spectator"),
    ]
    
    # 处理帧
    result = dt_system.process_frame(detections)
    
    print(f"✅ 处理结果:")
    print(f"   - 数字孪生卡牌: {len(result['digital_twin_cards'])}张")
    print(f"   - 共识分数: {result['consensus_score']:.3f}")
    print(f"   - 会话ID: {result['session_id'][:8]}...")
    
    # 显示分配的ID
    for card in result['digital_twin_cards']:
        print(f"   - {card.label} -> {card.twin_id} (区域: {card.region_name})")
    
    print()
    return dt_system

def demo_physical_constraints():
    """演示物理约束验证"""
    print("🔒 演示2: 物理约束验证")
    print("-" * 40)
    
    dt_system = create_digital_twin_system()
    
    # 尝试分配超过4张相同卡牌
    print("尝试分配6张'二'牌...")
    detections = []
    for i in range(6):
        detections.append(
            CardDetection("二", [100+i*10, 100, 150+i*10, 150], 0.9, 1, "手牌_观战方", "spectator")
        )
    
    result = dt_system.process_frame(detections)
    
    # 统计物理牌和虚拟牌
    physical_cards = [card for card in result['digital_twin_cards'] if not card.is_virtual]
    virtual_cards = [card for card in result['digital_twin_cards'] if card.is_virtual]
    
    print(f"✅ 约束验证结果:")
    print(f"   - 物理牌: {len(physical_cards)}张 (最多4张)")
    print(f"   - 虚拟牌: {len(virtual_cards)}张")
    print(f"   - 物理约束验证: {'✅ 通过' if dt_system.card_manager.validate_constraints() else '❌ 失败'}")
    
    # 显示分配的ID
    for card in result['digital_twin_cards']:
        card_type = "虚拟" if card.is_virtual else "物理"
        print(f"   - {card.label} -> {card.twin_id} ({card_type})")
    
    print()
    return dt_system

def demo_frame_inheritance():
    """演示帧间继承"""
    print("🔄 演示3: 帧间继承")
    print("-" * 40)
    
    dt_system = create_digital_twin_system()
    
    # 第一帧
    print("处理第1帧...")
    frame1_detections = [
        CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
        CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
    ]
    result1 = dt_system.process_frame(frame1_detections)
    frame1_ids = {card.twin_id for card in result1['digital_twin_cards']}
    print(f"   第1帧ID: {list(frame1_ids)}")
    
    # 第二帧（位置略有变化）
    print("处理第2帧（位置略有变化）...")
    frame2_detections = [
        CardDetection("二", [105, 105, 155, 155], 0.93, 1, "手牌_观战方", "spectator"),
        CardDetection("三", [165, 105, 215, 155], 0.90, 1, "手牌_观战方", "spectator"),
    ]
    result2 = dt_system.process_frame(frame2_detections)
    frame2_ids = {card.twin_id for card in result2['digital_twin_cards']}
    print(f"   第2帧ID: {list(frame2_ids)}")
    
    # 检查ID一致性
    consistency = len(frame1_ids & frame2_ids) / len(frame1_ids | frame2_ids)
    print(f"✅ ID一致性: {consistency:.1%}")
    
    # 第三帧（区域变化）
    print("处理第3帧（区域变化）...")
    frame3_detections = [
        CardDetection("二", [105, 105, 155, 155], 0.93, 6, "吃碰区_观战方", "spectator"),  # 区域变化
        CardDetection("三", [165, 105, 215, 155], 0.90, 1, "手牌_观战方", "spectator"),
    ]
    result3 = dt_system.process_frame(frame3_detections)
    
    print(f"✅ 区域流转事件: {len(result3['region_transitions'])}个")
    for transition in result3['region_transitions']:
        print(f"   - {transition['twin_id']}: {transition['from_region_name']} -> {transition['to_region_name']}")
    
    print()
    return dt_system

def demo_consensus_validation():
    """演示多帧共识验证"""
    print("🤝 演示4: 多帧共识验证")
    print("-" * 40)
    
    dt_system = create_digital_twin_system()
    
    # 处理多帧建立历史
    print("建立多帧历史...")
    for i in range(5):
        detections = [
            CardDetection("二", [100+i, 100+i, 150+i, 150+i], 0.9, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160+i, 100+i, 210+i, 150+i], 0.9, 1, "手牌_观战方", "spectator"),
        ]
        result = dt_system.process_frame(detections)
        print(f"   第{i+1}帧: 共识分数 {result['consensus_score']:.3f}")
    
    # 测试异常检测（突然出现新卡牌）
    print("测试异常检测（突然出现新卡牌）...")
    anomaly_detections = [
        CardDetection("二", [100, 100, 150, 150], 0.9, 1, "手牌_观战方", "spectator"),
        CardDetection("三", [160, 100, 210, 150], 0.9, 1, "手牌_观战方", "spectator"),
        CardDetection("四", [220, 100, 270, 150], 0.9, 1, "手牌_观战方", "spectator"),  # 新卡牌
    ]
    anomaly_result = dt_system.process_frame(anomaly_detections)
    
    print(f"✅ 异常检测结果:")
    print(f"   - 检测到异常: {len(anomaly_result['anomalies'])}个")
    print(f"   - 应用修正: {anomaly_result['corrections_applied']}个")
    print(f"   - 最终共识分数: {anomaly_result['consensus_score']:.3f}")
    
    for anomaly in anomaly_result['anomalies']:
        print(f"   - {anomaly['type']}: {anomaly['description']}")
    
    print()
    return dt_system

def demo_anylabeling_export():
    """演示AnyLabeling格式导出"""
    print("📤 演示5: AnyLabeling格式导出")
    print("-" * 40)
    
    dt_system = create_digital_twin_system()
    
    # 处理一帧数据
    detections = [
        CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
        CardDetection("三", [160, 100, 210, 150], 0.92, 6, "吃碰区_观战方", "spectator"),
    ]
    result = dt_system.process_frame(detections)
    
    # 导出AnyLabeling格式
    anylabeling_data = dt_system.export_to_anylabeling_format(result, 640, 320)
    
    print(f"✅ AnyLabeling格式导出:")
    print(f"   - 版本: {anylabeling_data['version']}")
    print(f"   - 标注数量: {len(anylabeling_data['shapes'])}")
    print(f"   - 图像尺寸: {anylabeling_data['imageWidth']}x{anylabeling_data['imageHeight']}")
    print(f"   - 包含元数据: {'✅' if 'digital_twin_metadata' in anylabeling_data else '❌'}")
    
    # 显示标注详情
    for i, shape in enumerate(anylabeling_data['shapes']):
        print(f"   标注{i+1}: {shape['label']} (区域{shape['group_id']}, ID: {shape['attributes']['digital_twin_id']})")
    
    print()
    return anylabeling_data

def demo_session_statistics():
    """演示会话统计"""
    print("📊 演示6: 会话统计")
    print("-" * 40)
    
    dt_system = create_digital_twin_system()
    
    # 处理多帧数据
    for i in range(3):
        detections = [
            CardDetection("二", [100, 100, 150, 150], 0.9, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160, 100, 210, 150], 0.9, 1, "手牌_观战方", "spectator"),
            CardDetection("四", [220, 100, 270, 150], 0.9, 1, "手牌_观战方", "spectator"),
        ]
        dt_system.process_frame(detections)
    
    # 获取统计信息
    stats = dt_system.get_session_statistics()
    
    print(f"✅ 会话统计:")
    print(f"   - 会话ID: {stats['session_id'][:8]}...")
    print(f"   - 处理帧数: {stats['total_frames_processed']}")
    print(f"   - 追踪卡牌总数: {stats['total_cards_tracked']}")
    print(f"   - 虚拟卡牌数: {stats['virtual_cards_created']}")
    print(f"   - 区域流转次数: {stats['region_transitions']}")
    print(f"   - 异常检测次数: {stats['anomalies_detected']}")
    print(f"   - 修正应用次数: {stats['corrections_applied']}")
    print(f"   - 共识分数: {stats['consensus_score']:.3f}")
    print(f"   - 物理约束验证: {'✅ 通过' if stats['physical_constraints_valid'] else '❌ 失败'}")
    
    print()

def main():
    """主演示函数"""
    print("🚀 数字孪生系统V2.0 功能演示")
    print("=" * 50)
    print()
    
    # 运行所有演示
    demo_basic_usage()
    demo_physical_constraints()
    demo_frame_inheritance()
    demo_consensus_validation()
    demo_anylabeling_export()
    demo_session_statistics()
    
    print("🎉 演示完成！")
    print("=" * 50)
    print()
    print("📚 更多信息:")
    print("- 技术报告: docs/development/数字孪生系统V2重构报告.md")
    print("- 源代码: src/core/digital_twin_v2.py")
    print("- 测试脚本: tests/test_digital_twin_v2.py")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试素材使用示例
展示如何利用不同的测试素材进行各种测试
"""

import sys
import os
import cv2
import json
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector
from src.core.data_validator import DataValidationPipeline

class TestMaterialUsage:
    """测试素材使用示例类"""
    
    def __init__(self):
        """初始化"""
        self.base_path = Path("legacy_assets/ceshi")
        self.detector = None
        self.validator = None
        
    def setup_components(self):
        """设置组件"""
        print("🔧 初始化检测器和验证器...")
        
        # 初始化检测器
        model_path = "best.pt"
        if os.path.exists(model_path):
            self.detector = CardDetector(model_path, enable_validation=True)
            print("✅ 检测器初始化成功")
        else:
            print("❌ 模型文件不存在，跳过检测器初始化")
        
        # 初始化验证器
        self.validator = DataValidationPipeline()
        print("✅ 验证器初始化成功")
    
    def example_1_quick_validation_with_tupian(self):
        """示例1：使用tupian进行快速验证"""
        print("\n" + "="*60)
        print("📋 示例1：使用tupian进行快速验证")
        print("="*60)
        
        tupian_path = self.base_path / "tupian"
        if not tupian_path.exists():
            print("❌ tupian目录不存在")
            return
        
        # 获取关键帧
        key_frames = {
            "游戏开始": "frame_00000.jpg",
            "打鸟选择": "frame_00025.jpg",
            "牌局进行中": "frame_00247.jpg",
            "牌局结束": "frame_00371.jpg"
        }
        
        print("🎯 测试不同游戏状态的检测效果...")
        
        for state_name, frame_file in key_frames.items():
            frame_path = tupian_path / frame_file
            if not frame_path.exists():
                print(f"   ⚠️ {frame_file} 不存在，跳过")
                continue
            
            print(f"\n🖼️  测试 {state_name} ({frame_file}):")
            
            # 读取图片
            image = cv2.imread(str(frame_path))
            if image is None:
                print(f"   ❌ 无法读取图片")
                continue
            
            # 检测（如果检测器可用）
            if self.detector:
                detections = self.detector.detect_image(image)
                print(f"   📊 检测到 {len(detections)} 个对象")
                
                # 显示前3个检测结果
                for i, det in enumerate(detections[:3]):
                    label = det.get('label', 'unknown')
                    conf = det.get('confidence', 0.0)
                    print(f"      {i+1}. {label} (置信度: {conf:.3f})")
            else:
                print("   ⚠️ 检测器未初始化，跳过检测")
        
        print("\n✅ 快速验证完成")
        print("💡 用途：快速验证基础功能是否正常，适合回归测试")
    
    def example_2_temporal_consistency_with_calibration_gt(self):
        """示例2：使用calibration_gt测试时间一致性"""
        print("\n" + "="*60)
        print("📋 示例2：使用calibration_gt测试时间一致性")
        print("="*60)
        
        images_path = self.base_path / "calibration_gt" / "images"
        if not images_path.exists():
            print("❌ calibration_gt/images目录不存在")
            return
        
        # 选择连续的几帧进行测试
        test_frames = ["frame_00050.jpg", "frame_00051.jpg", "frame_00052.jpg", "frame_00053.jpg"]
        
        print("🔄 测试连续帧的时间一致性...")
        
        previous_detections = None
        
        for frame_file in test_frames:
            frame_path = images_path / frame_file
            if not frame_path.exists():
                print(f"   ⚠️ {frame_file} 不存在，跳过")
                continue
            
            print(f"\n🖼️  处理 {frame_file}:")
            
            # 读取图片
            image = cv2.imread(str(frame_path))
            if image is None:
                continue
            
            # 检测
            if self.detector:
                detections = self.detector.detect_image(image)
                print(f"   📊 检测到 {len(detections)} 个对象")
                
                # 与上一帧对比
                if previous_detections is not None:
                    consistency_score = self._calculate_consistency(previous_detections, detections)
                    print(f"   🔗 与上一帧一致性: {consistency_score:.3f}")
                
                previous_detections = detections
            else:
                print("   ⚠️ 检测器未初始化，跳过检测")
        
        print("\n✅ 时间一致性测试完成")
        print("💡 用途：验证连续帧处理的稳定性，测试数据验证层效果")
    
    def example_3_state_analysis_with_zhuangtaiquyu(self):
        """示例3：使用zhuangtaiquyu进行状态分析"""
        print("\n" + "="*60)
        print("📋 示例3：使用zhuangtaiquyu进行状态分析")
        print("="*60)
        
        labels_path = self.base_path / "zhuangtaiquyu" / "labels" / "train"
        if not labels_path.exists():
            print("❌ zhuangtaiquyu/labels/train目录不存在")
            return
        
        print("🎯 分析状态区域和物理卡牌ID...")
        
        # 分析不同区域的标注
        region_stats = {}
        physical_id_stats = {}
        
        for region_dir in labels_path.iterdir():
            if not region_dir.is_dir():
                continue
            
            region_id = region_dir.name
            label_files = list(region_dir.glob("*.json"))[:5]  # 只分析前5个文件
            
            region_annotations = 0
            region_physical_ids = set()
            
            for label_file in label_files:
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    shapes = data.get('shapes', [])
                    region_annotations += len(shapes)
                    
                    for shape in shapes:
                        label = shape.get('label', '')
                        if len(label) > 1:
                            physical_id = label[0]
                            card_label = label[1:]
                            region_physical_ids.add(physical_id)
                            
                            # 统计物理ID
                            if physical_id not in physical_id_stats:
                                physical_id_stats[physical_id] = set()
                            physical_id_stats[physical_id].add(card_label)
                
                except Exception as e:
                    print(f"   ⚠️ 无法解析 {label_file.name}: {e}")
            
            region_stats[region_id] = {
                "annotations": region_annotations,
                "physical_ids": len(region_physical_ids),
                "files_analyzed": len(label_files)
            }
        
        # 显示分析结果
        print(f"\n📊 区域分析结果:")
        for region_id, stats in sorted(region_stats.items()):
            print(f"   区域 {region_id}: {stats['annotations']} 个标注, {stats['physical_ids']} 个物理ID")
        
        print(f"\n🎯 物理卡牌ID分析:")
        for physical_id, card_labels in sorted(physical_id_stats.items()):
            print(f"   物理ID {physical_id}: {len(card_labels)} 种卡牌 {list(card_labels)[:5]}")
        
        print("\n✅ 状态分析完成")
        print("💡 用途：验证状态转换逻辑，测试数字孪生功能")
    
    def example_4_performance_test_with_shipin(self):
        """示例4：使用shipin进行性能测试"""
        print("\n" + "="*60)
        print("📋 示例4：使用shipin进行性能测试")
        print("="*60)
        
        shipin_path = self.base_path / "shipin"
        if not shipin_path.exists():
            print("❌ shipin目录不存在")
            return
        
        video_files = list(shipin_path.glob("*.mp4"))
        if not video_files:
            print("❌ 没有找到视频文件")
            return
        
        video_file = video_files[0]
        print(f"🎬 测试视频: {video_file.name}")
        
        # 分析视频信息
        cap = cv2.VideoCapture(str(video_file))
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"   📊 视频信息: {width}x{height}, {fps:.1f}fps, {frame_count}帧")
        
        # 性能测试：处理前10帧
        print(f"\n⚡ 性能测试：处理前10帧...")
        
        import time
        processing_times = []
        
        for i in range(min(10, frame_count)):
            ret, frame = cap.read()
            if not ret:
                break
            
            start_time = time.time()
            
            # 检测（如果检测器可用）
            if self.detector:
                detections = self.detector.detect_image(frame)
                detection_count = len(detections)
            else:
                detection_count = 0
            
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            print(f"   帧 {i+1}: {processing_time:.3f}秒, {detection_count}个检测")
        
        cap.release()
        
        # 性能统计
        if processing_times:
            avg_time = np.mean(processing_times)
            max_time = np.max(processing_times)
            min_time = np.min(processing_times)
            
            print(f"\n📈 性能统计:")
            print(f"   平均处理时间: {avg_time:.3f}秒")
            print(f"   最大处理时间: {max_time:.3f}秒")
            print(f"   最小处理时间: {min_time:.3f}秒")
            print(f"   理论最大FPS: {1/avg_time:.1f}")
        
        print("\n✅ 性能测试完成")
        print("💡 用途：评估实时处理能力，进行性能优化")
    
    def _calculate_consistency(self, detections1: list, detections2: list) -> float:
        """计算两帧检测结果的一致性"""
        if not detections1 or not detections2:
            return 0.0
        
        # 简单的一致性计算：基于检测数量和标签
        labels1 = [det.get('label', '') for det in detections1]
        labels2 = [det.get('label', '') for det in detections2]
        
        common_labels = set(labels1) & set(labels2)
        all_labels = set(labels1) | set(labels2)
        
        if not all_labels:
            return 1.0
        
        return len(common_labels) / len(all_labels)
    
    def run_all_examples(self):
        """运行所有示例"""
        print("🚀 测试素材使用示例")
        print("展示如何利用不同的测试素材进行各种测试")
        
        # 设置组件
        self.setup_components()
        
        # 运行示例
        self.example_1_quick_validation_with_tupian()
        self.example_2_temporal_consistency_with_calibration_gt()
        self.example_3_state_analysis_with_zhuangtaiquyu()
        self.example_4_performance_test_with_shipin()
        
        print("\n" + "="*60)
        print("🎉 所有示例运行完成！")
        print("="*60)
        
        print("\n📚 更多信息:")
        print("   📖 详细文档: docs/testing/测试素材详细介绍.md")
        print("   🔧 分析工具: python tools/dataset_analyzer.py")
        print("   🧪 测试脚本: examples/test_material_usage.py")

def main():
    """主函数"""
    # 检查基础路径
    base_path = "legacy_assets/ceshi"
    if not os.path.exists(base_path):
        print(f"❌ 测试素材目录不存在: {base_path}")
        print("请确保在项目根目录运行此脚本")
        return
    
    # 运行示例
    usage_demo = TestMaterialUsage()
    usage_demo.run_all_examples()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
简单检测演示 - 快速开始示例

这个示例展示了如何使用跑胡子AI系统进行基本的卡牌检测和数字孪生处理。
适合新用户快速了解系统的基本功能。

运行方法：
    python examples/quick_start/simple_detection_demo.py
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def main():
    """主演示函数"""
    print("🎯 跑胡子AI系统 - 简单检测演示")
    print("=" * 50)
    
    try:
        # 步骤1：导入核心模块
        print("1. 导入核心模块...")
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("   ✅ 模块导入成功")
        
        # 步骤2：创建数字孪生系统
        print("\n2. 创建数字孪生系统...")
        dt_system = create_digital_twin_system()
        print("   ✅ 系统创建成功")
        print(f"   📋 系统类型: {type(dt_system).__name__}")
        
        # 步骤3：检查系统功能
        print("\n3. 检查系统功能...")
        has_process_frame = hasattr(dt_system, 'process_frame')
        has_dual_output = hasattr(dt_system, 'export_synchronized_dual_format')
        
        print(f"   📊 帧处理功能: {'✅ 可用' if has_process_frame else '❌ 不可用'}")
        print(f"   🔄 双轨输出功能: {'✅ 可用' if has_dual_output else '❌ 不可用'}")
        
        if not (has_process_frame and has_dual_output):
            print("   ⚠️ 系统功能不完整，请检查安装")
            return False
        
        # 步骤4：创建测试数据
        print("\n4. 创建测试数据...")
        test_detections = [
            CardDetection(
                card_value="二",
                bbox=[100, 100, 150, 150],
                confidence=0.95,
                class_id=1,
                region_name="手牌_观战方",
                perspective="spectator"
            ),
            CardDetection(
                card_value="三",
                bbox=[200, 100, 250, 150],
                confidence=0.90,
                class_id=2,
                region_name="手牌_观战方",
                perspective="spectator"
            ),
            CardDetection(
                card_value="四",
                bbox=[300, 100, 350, 150],
                confidence=0.88,
                class_id=3,
                region_name="出牌区_观战方",
                perspective="spectator"
            )
        ]
        print(f"   ✅ 测试数据创建成功: {len(test_detections)} 张卡牌")
        
        # 显示测试数据详情
        for i, detection in enumerate(test_detections, 1):
            print(f"      卡牌{i}: {detection.card_value} (置信度: {detection.confidence:.2f})")
        
        # 步骤5：数字孪生处理
        print("\n5. 数字孪生处理...")
        result = dt_system.process_frame(test_detections)
        
        # 检查处理结果
        digital_twin_cards = result.get('digital_twin_cards', [])
        consensus_score = result.get('consensus_score', 0)
        
        print(f"   ✅ 处理完成")
        print(f"   📊 输入卡牌: {len(test_detections)} 张")
        print(f"   🎯 数字孪生卡牌: {len(digital_twin_cards)} 张")
        print(f"   🤝 共识分数: {consensus_score:.3f}")
        
        # 显示数字孪生卡牌详情
        if digital_twin_cards:
            print("   📋 数字孪生卡牌详情:")
            for i, card in enumerate(digital_twin_cards[:3], 1):  # 只显示前3张
                print(f"      {i}. {card.card_value} -> ID: {card.twin_id} (区域: {card.region_name})")
            if len(digital_twin_cards) > 3:
                print(f"      ... 还有 {len(digital_twin_cards) - 3} 张卡牌")
        
        # 步骤6：双轨输出演示
        print("\n6. 双轨输出演示...")
        dual_result = dt_system.export_synchronized_dual_format(
            result, 
            image_width=640, 
            image_height=320, 
            image_path='demo_frame.jpg'
        )
        
        # 检查双轨输出结果
        rlcard_format = dual_result.get('rlcard_format', {})
        anylabeling_format = dual_result.get('anylabeling_format', {})
        consistency_validation = dual_result.get('consistency_validation', {})
        
        print("   ✅ 双轨输出完成")
        
        # RLCard格式统计
        hand_cards = len(rlcard_format.get('hand', []))
        public_cards = len(rlcard_format.get('public', []))
        print(f"   🎮 RLCard格式: 手牌 {hand_cards} 张, 公共牌 {public_cards} 张")
        
        # AnyLabeling格式统计
        shapes_count = len(anylabeling_format.get('shapes', []))
        image_width = anylabeling_format.get('imageWidth', 0)
        image_height = anylabeling_format.get('imageHeight', 0)
        print(f"   🏷️ AnyLabeling格式: {shapes_count} 个标注 ({image_width}x{image_height})")
        
        # 一致性验证
        consistency_score = consistency_validation.get('consistency_score', 0)
        is_consistent = consistency_validation.get('is_consistent', False)
        print(f"   🔍 一致性验证: 分数 {consistency_score:.3f}, 状态 {'✅ 一致' if is_consistent else '❌ 不一致'}")
        
        # 步骤7：结果总结
        print("\n7. 演示结果总结")
        print("   " + "=" * 40)
        print(f"   📊 处理统计:")
        print(f"      • 输入检测: {len(test_detections)} 张卡牌")
        print(f"      • 数字孪生: {len(digital_twin_cards)} 张卡牌")
        print(f"      • RLCard输出: {hand_cards + public_cards} 张卡牌")
        print(f"      • AnyLabeling输出: {shapes_count} 个标注")
        
        print(f"   🎯 质量指标:")
        print(f"      • 共识分数: {consensus_score:.3f}")
        print(f"      • 一致性分数: {consistency_score:.3f}")
        print(f"      • 一致性状态: {'✅ 通过' if is_consistent else '❌ 失败'}")
        
        # 成功判断
        success = (
            len(digital_twin_cards) > 0 and
            consistency_score >= 0.8 and
            is_consistent
        )
        
        if success:
            print("\n🎉 演示完全成功！")
            print("   ✅ 所有功能正常工作")
            print("   ✅ 质量指标达标")
            print("   ✅ 系统运行稳定")
        else:
            print("\n⚠️ 演示基本成功，但有改进空间")
            if len(digital_twin_cards) == 0:
                print("   📋 建议: 检查数字孪生处理逻辑")
            if consistency_score < 0.8:
                print("   📋 建议: 优化一致性算法")
            if not is_consistent:
                print("   📋 建议: 检查双轨输出同步机制")
        
        # 下一步建议
        print("\n📚 下一步学习建议:")
        print("   1. 运行 examples/quick_start/dual_format_demo.py - 学习双格式输出")
        print("   2. 查看 examples/advanced/batch_processing_demo.py - 学习批量处理")
        print("   3. 阅读 docs/user_guide/ - 深入了解系统功能")
        print("   4. 尝试 examples/calibration_gt/processing_example.py - 真实数据处理")
        
        return success
        
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {e}")
        print("   📋 解决方案:")
        print("   1. 检查是否在项目根目录运行")
        print("   2. 确认已安装所有依赖: pip install -r requirements.txt")
        print("   3. 检查Python路径配置")
        return False
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        print("   📋 请检查:")
        print("   1. 系统环境配置")
        print("   2. 依赖包版本")
        print("   3. 错误日志详情")
        
        # 显示详细错误信息
        import traceback
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    success = main()
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"演示结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 退出码
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
深度架构分析脚本

分析当前数字孪生系统的架构和流程，重点理解：
1. frame_00028的16区域分配逻辑（暗牌场景）
2. frame_00060的16区域需求（明牌场景）
3. 暗牌vs明牌的处理差异
4. 空间顺序分配的实现机制
5. 当前代码逻辑的缺陷和修复方案

作者：AI助手
日期：2025-07-25
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepArchitectureAnalyzer:
    """深度架构分析器"""
    
    def __init__(self):
        self.output_dir = Path("output")
        self.digital_twin_dir = self.output_dir / "calibration_gt_final_with_digital_twin" / "labels"
        self.src_dir = Path("src")
        
        # 关键分析帧
        self.analysis_frames = {
            28: "暗牌分配成功案例",
            60: "明牌分配目标场景"
        }
        
        logger.info("深度架构分析器初始化完成")
    
    def analyze_frame_28_allocation_logic(self) -> Dict[str, Any]:
        """分析frame_00028的分配逻辑（暗牌场景）"""
        logger.info("🔍 分析frame_00028的分配逻辑")
        
        analysis = {
            "frame": 28,
            "scenario_type": "暗牌分配成功案例",
            "transition_analysis": {},
            "allocation_pattern": {},
            "spatial_distribution": {},
            "key_insights": {}
        }
        
        # 分析27→28的转换
        frame_27_data = self._load_frame_data(27)
        frame_28_data = self._load_frame_data(28)
        
        if frame_27_data and frame_28_data:
            analysis["transition_analysis"] = self._analyze_27_to_28_transition(frame_27_data, frame_28_data)
            analysis["allocation_pattern"] = self._analyze_28_allocation_pattern(frame_28_data)
            analysis["spatial_distribution"] = self._analyze_28_spatial_distribution(frame_28_data)
            analysis["key_insights"] = self._extract_28_key_insights(analysis)
        
        return analysis
    
    def _load_frame_data(self, frame_num: int) -> Optional[Dict[str, Any]]:
        """加载帧数据"""
        frame_file = self.digital_twin_dir / f"frame_{frame_num:05d}.json"
        if not frame_file.exists():
            return None
        
        try:
            with open(frame_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载frame_{frame_num:05d}.json失败: {e}")
            return None
    
    def _analyze_27_to_28_transition(self, frame_27_data: Dict[str, Any], frame_28_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析27→28的转换"""
        transition = {
            "source_analysis": {},
            "target_analysis": {},
            "transition_type": "UNKNOWN",
            "allocation_mechanism": "UNKNOWN"
        }
        
        # 分析源区域（区域7）
        region_7_prev = self._extract_region_cards(frame_27_data, 7)
        region_7_curr = self._extract_region_cards(frame_28_data, 7)
        
        transition["source_analysis"] = {
            "region_7_prev": {
                "count": len(region_7_prev),
                "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_7_prev]
            },
            "region_7_curr": {
                "count": len(region_7_curr),
                "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_7_curr]
            },
            "region_7_change": len(region_7_curr) - len(region_7_prev)
        }
        
        # 分析目标区域（区域16）
        region_16_prev = self._extract_region_cards(frame_27_data, 16)
        region_16_curr = self._extract_region_cards(frame_28_data, 16)
        
        transition["target_analysis"] = {
            "region_16_prev": {
                "count": len(region_16_prev),
                "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_16_prev]
            },
            "region_16_curr": {
                "count": len(region_16_curr),
                "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_16_curr]
            },
            "region_16_change": len(region_16_curr) - len(region_16_prev)
        }
        
        # 确定转换类型
        transition["transition_type"] = self._determine_transition_type_28(transition)
        transition["allocation_mechanism"] = self._determine_allocation_mechanism_28(transition)
        
        return transition
    
    def _extract_region_cards(self, data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
        """提取指定区域的卡牌"""
        if not data or 'shapes' not in data:
            return []
        
        return [shape for shape in data['shapes'] if shape.get('group_id') == region_id]
    
    def _determine_transition_type_28(self, transition: Dict[str, Any]) -> str:
        """确定frame_28的转换类型"""
        region_7_change = transition["source_analysis"]["region_7_change"]
        region_16_change = transition["target_analysis"]["region_16_change"]
        
        if region_7_change < 0 and region_16_change > 0:
            return "7→16_TRANSFER"  # 7→16流转
        elif region_16_change > 0 and region_7_change == 0:
            return "NEW_ALLOCATION"  # 新分配
        elif region_16_change == 0:
            return "INHERITANCE"  # 继承
        else:
            return "COMPLEX_CHANGE"
    
    def _determine_allocation_mechanism_28(self, transition: Dict[str, Any]) -> str:
        """确定frame_28的分配机制"""
        transition_type = transition.get("transition_type", "UNKNOWN")
        
        if transition_type == "7→16_TRANSFER":
            return "TRANSFER_INHERITANCE"  # 流转继承
        elif transition_type == "NEW_ALLOCATION":
            return "SPATIAL_ALLOCATION"  # 空间分配
        else:
            return "UNKNOWN"
    
    def _analyze_28_allocation_pattern(self, frame_28_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析frame_28的分配模式"""
        region_16_cards = self._extract_region_cards(frame_28_data, 16)
        
        pattern = {
            "total_cards": len(region_16_cards),
            "card_types": {"明牌": 0, "暗牌": 0},
            "id_pattern": {},
            "label_pattern": {},
            "allocation_strategy": "UNKNOWN"
        }
        
        # 分析卡牌类型
        for card in region_16_cards:
            label = card.get('label', '')
            twin_id = card.get('attributes', {}).get('digital_twin_id', '')
            
            if '暗' in label or '暗' in twin_id:
                pattern["card_types"]["暗牌"] += 1
            else:
                pattern["card_types"]["明牌"] += 1
        
        # 分析ID模式
        ids = [card.get('attributes', {}).get('digital_twin_id') for card in region_16_cards]
        pattern["id_pattern"] = {
            "unique_ids": list(set([id for id in ids if id])),
            "id_diversity": len(set([id for id in ids if id])) / len(ids) if ids else 0,
            "has_sequential_pattern": self._check_sequential_pattern(ids),
            "has_spatial_pattern": self._check_spatial_pattern(region_16_cards)
        }
        
        # 分析标签模式
        labels = [card.get('label', '') for card in region_16_cards]
        pattern["label_pattern"] = {
            "unique_labels": list(set(labels)),
            "label_diversity": len(set(labels)) / len(labels) if labels else 0
        }
        
        # 确定分配策略
        pattern["allocation_strategy"] = self._determine_allocation_strategy_28(pattern)
        
        return pattern
    
    def _check_sequential_pattern(self, ids: List[str]) -> bool:
        """检查是否为顺序模式"""
        if not ids:
            return False
        
        # 提取数字前缀
        numbers = []
        for id_str in ids:
            if id_str and id_str[0].isdigit():
                numbers.append(int(id_str[0]))
        
        if len(numbers) != len(ids):
            return False
        
        # 检查是否为连续序列
        numbers.sort()
        expected = list(range(1, len(numbers) + 1))
        return numbers == expected
    
    def _check_spatial_pattern(self, cards: List[Dict[str, Any]]) -> bool:
        """检查是否有空间模式"""
        if len(cards) < 2:
            return False
        
        # 简化检查：如果卡牌有不同的Y坐标，认为有空间模式
        y_positions = []
        for card in cards:
            points = card.get('points', [])
            if points:
                y_coords = [point[1] for point in points]
                y_positions.append(max(y_coords))
        
        return len(set(y_positions)) > 1
    
    def _determine_allocation_strategy_28(self, pattern: Dict[str, Any]) -> str:
        """确定frame_28的分配策略"""
        id_diversity = pattern["id_pattern"]["id_diversity"]
        has_sequential = pattern["id_pattern"]["has_sequential_pattern"]
        has_spatial = pattern["id_pattern"]["has_spatial_pattern"]
        
        if id_diversity > 0.8 and not has_sequential:
            return "DIVERSE_INHERITANCE"  # 多样化继承
        elif has_sequential and has_spatial:
            return "SPATIAL_SEQUENTIAL"  # 空间顺序分配
        elif id_diversity > 0.5:
            return "MIXED_ALLOCATION"  # 混合分配
        else:
            return "UNIFORM_ALLOCATION"  # 统一分配
    
    def _analyze_28_spatial_distribution(self, frame_28_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析frame_28的空间分布"""
        region_16_cards = self._extract_region_cards(frame_28_data, 16)
        
        spatial = {
            "cards_with_positions": [],
            "spatial_order": [],
            "y_distribution": {},
            "x_distribution": {}
        }
        
        # 提取位置信息
        for i, card in enumerate(region_16_cards):
            points = card.get('points', [])
            if points:
                y_coords = [point[1] for point in points]
                x_coords = [point[0] for point in points]
                
                card_info = {
                    "index": i,
                    "label": card.get('label', ''),
                    "twin_id": card.get('attributes', {}).get('digital_twin_id', ''),
                    "y_position": max(y_coords),  # 下方位置
                    "x_position": min(x_coords),  # 左侧位置
                    "y_center": sum(y_coords) / len(y_coords),
                    "x_center": sum(x_coords) / len(x_coords)
                }
                spatial["cards_with_positions"].append(card_info)
        
        # 按空间顺序排序（从下到上，从左到右）
        spatial["spatial_order"] = sorted(
            spatial["cards_with_positions"], 
            key=lambda x: (-x["y_position"], x["x_position"])
        )
        
        return spatial
    
    def _extract_28_key_insights(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """提取frame_28的关键洞察"""
        return {
            "successful_features": [
                "多样化ID分配（6个不同ID）",
                "暗牌和明牌混合处理",
                "空间位置合理分布",
                "ID继承机制正常工作"
            ],
            "allocation_characteristics": [
                "使用多样化继承策略",
                "保持ID的唯一性",
                "支持暗牌标记",
                "空间分布合理"
            ],
            "vs_frame_60_differences": [
                "frame_28: 暗牌+明牌混合，多样化ID",
                "frame_60: 纯明牌，需要顺序分配",
                "frame_28: 继承为主",
                "frame_60: 分配为主"
            ]
        }

    def analyze_frame_60_requirements(self) -> Dict[str, Any]:
        """分析frame_60的需求（明牌场景）"""
        logger.info("🔍 分析frame_60的需求")

        analysis = {
            "frame": 60,
            "scenario_type": "明牌顺序分配需求",
            "current_state": {},
            "expected_state": {},
            "gap_analysis": {},
            "requirements": {}
        }

        # 分析当前状态
        frame_59_data = self._load_frame_data(59)
        frame_60_data = self._load_frame_data(60)

        if frame_59_data and frame_60_data:
            analysis["current_state"] = self._analyze_60_current_state(frame_59_data, frame_60_data)
            analysis["expected_state"] = self._define_60_expected_state()
            analysis["gap_analysis"] = self._analyze_60_gap(analysis["current_state"], analysis["expected_state"])
            analysis["requirements"] = self._define_60_requirements(analysis["gap_analysis"])

        return analysis

    def _analyze_60_current_state(self, frame_59_data: Dict[str, Any], frame_60_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析frame_60的当前状态"""
        current = {
            "source_region_3": {},
            "target_region_16": {},
            "transition_result": {}
        }

        # 分析源区域3
        region_3_cards = self._extract_region_cards(frame_59_data, 3)
        current["source_region_3"] = {
            "count": len(region_3_cards),
            "cards": region_3_cards,
            "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_3_cards],
            "labels": [card.get('label', '') for card in region_3_cards]
        }

        # 分析目标区域16
        region_16_cards = self._extract_region_cards(frame_60_data, 16)
        current["target_region_16"] = {
            "count": len(region_16_cards),
            "cards": region_16_cards,
            "ids": [card.get('attributes', {}).get('digital_twin_id') for card in region_16_cards],
            "labels": [card.get('label', '') for card in region_16_cards],
            "spatial_order": self._get_spatial_order(region_16_cards)
        }

        # 分析转换结果
        current["transition_result"] = {
            "inheritance_success": self._check_inheritance_success(current),
            "spatial_allocation_success": self._check_spatial_allocation_success(current),
            "overall_success": False
        }

        return current

    def _get_spatial_order(self, cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取空间顺序"""
        cards_with_pos = []
        for card in cards:
            points = card.get('points', [])
            if points:
                y_coords = [point[1] for point in points]
                x_coords = [point[0] for point in points]

                card_info = {
                    "card": card,
                    "twin_id": card.get('attributes', {}).get('digital_twin_id', ''),
                    "label": card.get('label', ''),
                    "y_position": max(y_coords),
                    "x_position": min(x_coords)
                }
                cards_with_pos.append(card_info)

        # 按从下到上，从左到右排序
        return sorted(cards_with_pos, key=lambda x: (-x["y_position"], x["x_position"]))

    def _check_inheritance_success(self, current: Dict[str, Any]) -> bool:
        """检查继承是否成功"""
        source_ids = current["source_region_3"]["ids"]
        target_ids = current["target_region_16"]["ids"]

        if not source_ids:
            return False

        source_id = source_ids[0]
        return all(id == source_id for id in target_ids if id)

    def _check_spatial_allocation_success(self, current: Dict[str, Any]) -> bool:
        """检查空间分配是否成功"""
        spatial_order = current["target_region_16"]["spatial_order"]

        if len(spatial_order) != 4:
            return False

        # 检查是否为1二、2二、3二、4二的顺序
        expected_ids = ["1二", "2二", "3二", "4二"]
        actual_ids = [item["twin_id"] for item in spatial_order]

        return actual_ids == expected_ids

    def _define_60_expected_state(self) -> Dict[str, Any]:
        """定义frame_60的期望状态"""
        return {
            "source_region_3": {
                "count": 1,
                "base_id": "1二",
                "base_label": "二"
            },
            "target_region_16": {
                "count": 4,
                "spatial_allocation": {
                    "position_1_bottom": "1二",
                    "position_2": "2二",
                    "position_3": "3二",
                    "position_4_top": "4二"
                },
                "allocation_rule": "从下到上按空间顺序分配递增ID"
            },
            "allocation_mechanism": "SPATIAL_SEQUENTIAL_ALLOCATION"
        }

    def _analyze_60_gap(self, current: Dict[str, Any], expected: Dict[str, Any]) -> Dict[str, Any]:
        """分析frame_60的差距"""
        return {
            "inheritance_gap": {
                "current": "所有卡牌继承相同ID（1二）",
                "expected": "按空间顺序分配不同ID（1二、2二、3二、4二）",
                "gap": "缺少空间顺序分配逻辑"
            },
            "allocation_mechanism_gap": {
                "current": "使用继承机制",
                "expected": "使用空间顺序分配机制",
                "gap": "需要切换到分配模式"
            },
            "spatial_logic_gap": {
                "current": "没有空间顺序处理",
                "expected": "从下到上的空间顺序分配",
                "gap": "缺少空间排序和递增ID分配"
            }
        }

    def _define_60_requirements(self, gap_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """定义frame_60的修复需求"""
        return {
            "core_requirement": "实现多卡牌场景的空间顺序分配",
            "specific_requirements": [
                "检测1→4的多卡牌分配场景",
                "提取基础标签（'二'）",
                "按空间位置排序（从下到上）",
                "分配递增ID（1二、2二、3二、4二）",
                "保持明牌属性"
            ],
            "technical_requirements": [
                "添加多卡牌场景检测逻辑",
                "实现空间排序算法",
                "实现递增ID分配算法",
                "集成到现有流转机制中"
            ]
        }

    def analyze_current_code_logic(self) -> Dict[str, Any]:
        """分析当前代码逻辑"""
        logger.info("🔍 分析当前代码逻辑")

        analysis = {
            "region_transitioner_analysis": {},
            "id_assigner_analysis": {},
            "processing_flow_analysis": {},
            "logic_gaps": {}
        }

        # 分析流转器逻辑
        analysis["region_transitioner_analysis"] = self._analyze_region_transitioner_logic()

        # 分析ID分配器逻辑
        analysis["id_assigner_analysis"] = self._analyze_id_assigner_logic()

        # 分析处理流程
        analysis["processing_flow_analysis"] = self._analyze_processing_flow()

        # 识别逻辑缺陷
        analysis["logic_gaps"] = self._identify_logic_gaps(analysis)

        return analysis

    def _analyze_region_transitioner_logic(self) -> Dict[str, Any]:
        """分析流转器逻辑"""
        transitioner_file = self.src_dir / "modules" / "region_transitioner.py"

        analysis = {
            "file_exists": transitioner_file.exists(),
            "current_logic": {},
            "missing_logic": {},
            "modification_points": {}
        }

        if transitioner_file.exists():
            try:
                with open(transitioner_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                analysis["current_logic"] = {
                    "has_7_to_16": "7→16" in content,
                    "has_3_to_16": "3→16" in content,
                    "has_4_to_16": "4→16" in content,
                    "has_multi_card_detection": "len(region_16_cards) > len(" in content,
                    "has_spatial_sorting": "spatial" in content.lower() or "空间" in content,
                    "has_sequential_allocation": "sequential" in content.lower() or "顺序" in content
                }

                analysis["missing_logic"] = {
                    "multi_card_scenario_detection": not analysis["current_logic"]["has_multi_card_detection"],
                    "spatial_sequential_allocation": not (analysis["current_logic"]["has_spatial_sorting"] and analysis["current_logic"]["has_sequential_allocation"]),
                    "明牌_vs_暗牌_handling": "明牌" not in content
                }

            except Exception as e:
                analysis["error"] = f"读取文件失败: {e}"

        return analysis

    def _analyze_id_assigner_logic(self) -> Dict[str, Any]:
        """分析ID分配器逻辑"""
        assigner_file = self.src_dir / "modules" / "basic_id_assigner.py"

        analysis = {
            "file_exists": assigner_file.exists(),
            "spatial_allocation_support": False,
            "sequential_allocation_support": False,
            "coordination_with_transitioner": False
        }

        if assigner_file.exists():
            try:
                with open(assigner_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                analysis["spatial_allocation_support"] = "spatial" in content.lower() or "空间" in content
                analysis["sequential_allocation_support"] = "sequential" in content.lower() or "顺序" in content
                analysis["coordination_with_transitioner"] = "allocated" in content

            except Exception as e:
                analysis["error"] = f"读取文件失败: {e}"

        return analysis

    def _analyze_processing_flow(self) -> Dict[str, Any]:
        """分析处理流程"""
        return {
            "current_flow": [
                "1. 原始检测",
                "2. ID分配器（基础分配）",
                "3. 流转器（跨区域流转）",
                "4. 继承器（同区域继承）"
            ],
            "flow_issues": [
                "流转器只处理继承，不处理分配",
                "ID分配器不支持空间顺序分配",
                "缺少多卡牌场景的特殊处理"
            ],
            "required_flow": [
                "1. 原始检测",
                "2. 流转器（检测多卡牌场景并处理）",
                "3. ID分配器（处理剩余卡牌）",
                "4. 继承器（同区域继承）"
            ]
        }

    def _identify_logic_gaps(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """识别逻辑缺陷"""
        return {
            "critical_gaps": [
                "缺少多卡牌场景检测（1→4）",
                "缺少空间顺序分配逻辑",
                "缺少明牌vs暗牌的处理差异",
                "流转器只做继承，不做分配"
            ],
            "architectural_issues": [
                "流转器和分配器职责不清",
                "没有专门的多卡牌分配机制",
                "空间逻辑分散在不同组件中"
            ],
            "fix_strategy": [
                "在流转器中添加多卡牌分配逻辑",
                "实现空间顺序分配算法",
                "明确组件职责分工"
            ]
        }

    def generate_fix_proposal(self) -> Dict[str, Any]:
        """生成修复方案"""
        logger.info("🔧 生成修复方案")

        # 执行所有分析
        frame_28_analysis = self.analyze_frame_28_allocation_logic()
        frame_60_analysis = self.analyze_frame_60_requirements()
        code_analysis = self.analyze_current_code_logic()

        proposal = {
            "problem_diagnosis": {
                "core_issue": "当前3→16流转只做ID继承，不做空间顺序分配",
                "specific_problem": "frame_60需要1→4的多卡牌空间分配，但当前逻辑只做1→1的ID继承",
                "root_cause": "缺少多卡牌场景的检测和空间顺序分配逻辑"
            },

            "solution_strategy": {
                "approach": "在流转器中添加多卡牌分配逻辑",
                "core_principle": "检测多卡牌场景，切换到空间顺序分配模式",
                "implementation_location": "region_transitioner.py的3→16流转逻辑中"
            },

            "detailed_fix_plan": {
                "step_1": {
                    "name": "添加多卡牌场景检测",
                    "description": "在3→16流转中检测1→4的多卡牌场景",
                    "logic": "if len(region_16_cards) > len(region_3_cards) and len(region_3_cards) == 1:"
                },
                "step_2": {
                    "name": "实现空间顺序分配",
                    "description": "按从下到上的空间顺序分配1二、2二、3二、4二",
                    "logic": "spatial_sort + sequential_id_allocation"
                },
                "step_3": {
                    "name": "保持明牌属性",
                    "description": "确保分配的卡牌保持明牌属性",
                    "logic": "preserve_card_attributes"
                }
            },

            "implementation_details": {
                "modification_point": "_handle_special_7_to_16_transition方法中的3→16逻辑",
                "new_logic_insertion": "在找到matching_card_3之后，检查是否为多卡牌场景",
                "fallback_logic": "如果不是多卡牌场景，使用原有的继承逻辑"
            },

            "validation_plan": {
                "test_cases": [
                    "frame_60: 验证1二2二3二4二的空间分配",
                    "frame_35: 确保7→16功能不受影响",
                    "其他3→16场景: 确保正常继承不受影响"
                ]
            }
        }

        return proposal

def main():
    """主函数"""
    print("🔍 深度架构分析")
    print("="*60)
    print("分析目标:")
    print("1. frame_28的暗牌分配成功案例")
    print("2. frame_60的明牌分配需求")
    print("3. 当前代码逻辑的缺陷")
    print("4. 修复方案设计")
    print()

    analyzer = DeepArchitectureAnalyzer()

    try:
        # 执行深度分析
        frame_28_analysis = analyzer.analyze_frame_28_allocation_logic()
        frame_60_analysis = analyzer.analyze_frame_60_requirements()
        code_analysis = analyzer.analyze_current_code_logic()
        fix_proposal = analyzer.generate_fix_proposal()

        # 生成综合报告
        comprehensive_report = {
            "frame_28_analysis": frame_28_analysis,
            "frame_60_analysis": frame_60_analysis,
            "code_analysis": code_analysis,
            "fix_proposal": fix_proposal
        }

        # 保存报告
        output_file = Path("output") / "deep_architecture_analysis_report.json"
        output_file.parent.mkdir(exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, ensure_ascii=False, indent=2, default=str)

        print(f"📊 深度架构分析报告已保存: {output_file}")
        print()

        # 显示关键发现
        print("🔍 关键发现:")
        print(f"  frame_28成功特征: {frame_28_analysis['key_insights']['successful_features'][0]}")
        print(f"  frame_60核心需求: {frame_60_analysis['requirements']['core_requirement']}")
        print(f"  当前逻辑缺陷: {code_analysis['logic_gaps']['critical_gaps'][0]}")
        print()

        # 显示修复方案
        problem = fix_proposal["problem_diagnosis"]
        solution = fix_proposal["solution_strategy"]
        print("🔧 修复方案:")
        print(f"  核心问题: {problem['core_issue']}")
        print(f"  解决策略: {solution['approach']}")
        print(f"  实施位置: {solution['implementation_location']}")
        print()

        # 显示实施计划
        plan = fix_proposal["detailed_fix_plan"]
        print("📋 实施计划:")
        for step_key, step_info in plan.items():
            print(f"  {step_info['name']}: {step_info['description']}")
        print()

        print("✅ 深度分析完成！详细信息请查看生成的JSON报告。")
        print()
        print("💡 核心洞察: 需要在3→16流转中添加多卡牌场景的空间顺序分配逻辑！")

    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        print(f"❌ 分析失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())

# 🚀 快速开始指南

## 5分钟快速体验跑胡子AI系统

### 📋 前置要求

- Python 3.10+
- NVIDIA GPU (推荐RTX 5060或更高)
- 8GB+ RAM

### ⚡ 一键安装

```bash
# 1. 克隆项目
git clone https://github.com/your-username/phz-ai-simple.git
cd phz-ai-simple

# 2. 创建虚拟环境
python -m venv env
.\env\Scripts\activate  # Windows
# source env/bin/activate  # Linux/Mac

# 3. 安装依赖
pip install -r requirements.txt
```

### 🎯 核心功能快速测试

#### 1. 卡牌检测测试 (30秒)
```bash
# 使用GPU检测单张图像
python -m src.core.detect --source data/calibration_gt/images/frame_001.jpg --visualize --device 0
```

#### 2. 双轨输出验证 (1分钟)
```bash
# 验证RLCard+AnyLabeling同步输出
python quick_dual_test.py
```

#### 3. 数字孪生系统演示 (2分钟)
```bash
# 完整的数字孪生功能演示
python examples/digital_twin_v2_demo.py
```

#### 4. 实时屏幕检测 (2分钟)
```bash
# 实时屏幕捕获和检测
python -m src.main --config src/config/config.json --device 0
```

### 📊 预期结果

#### ✅ 成功指标
- **检测速度**: 30+ FPS (GPU) / 3+ FPS (CPU)
- **检测精度**: F1分数 >97%
- **双轨一致性**: 一致性分数 >95%
- **内存使用**: <4GB GPU显存

#### 🎯 输出示例
```json
{
  "detection_results": {
    "cards_detected": 12,
    "processing_time": "33ms",
    "confidence_avg": 0.96
  },
  "dual_format": {
    "rlcard_format": "✅ AI决策用格式",
    "anylabeling_format": "✅ 人工审核用格式",
    "consistency_score": 1.000
  }
}
```

### 🔧 常见问题

#### GPU不可用
```bash
# 使用CPU模式 (较慢但可用)
python -m src.core.detect --source your_image.jpg --device cpu
```

#### 模型文件缺失
```bash
# 下载预训练模型
# 模型文件应放在: models/yolov8l.pt
```

#### 依赖安装失败
```bash
# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 📚 下一步

- 📖 **详细使用**: 查看 [docs/user_guide/](docs/user_guide/) 获取完整使用指南
- 🔧 **配置调优**: 参考 [docs/user_guide/configuration.md](docs/user_guide/configuration.md)
- 🧪 **高级功能**: 探索 [docs/user_guide/advanced_features.md](docs/user_guide/advanced_features.md)
- 🚨 **问题排查**: 查看 [docs/user_guide/troubleshooting.md](docs/user_guide/troubleshooting.md)

### 🎉 成功！

如果上述测试都通过，恭喜您已成功部署跑胡子AI系统！

---

**💡 提示**: 首次运行可能需要下载模型文件，请确保网络连接正常。

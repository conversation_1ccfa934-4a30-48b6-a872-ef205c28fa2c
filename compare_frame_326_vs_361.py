#!/usr/bin/env python3
"""
对比分析frame_00326和frame_00361的流转情况

分析为什么frame_00326能正常流转，而frame_00361不能
"""

import json
import os
from typing import Dict, List, Any

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的JSON数据"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    try:
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件不存在: {frame_path}")
        return {}
    except Exception as e:
        print(f"❌ 加载文件失败: {frame_path}, 错误: {e}")
        return {}

def extract_region_cards(data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    return [shape for shape in data['shapes'] 
            if shape.get('group_id') == region_id]

def get_digital_twin_id(card: Dict[str, Any]) -> str:
    """获取卡牌的数字孪生ID"""
    # 优先从attributes中获取
    if 'attributes' in card and 'digital_twin_id' in card['attributes']:
        return card['attributes']['digital_twin_id']
    # 备用从twin_id字段获取
    if 'twin_id' in card:
        return card['twin_id']
    return 'None'

def analyze_frame_325_326_transition():
    """分析frame_00325→frame_00326的流转情况"""
    print("🔍 分析frame_00325→frame_00326的流转情况")
    print("="*80)
    
    # 加载数据
    frame_325_data = load_frame_data(325)
    frame_326_data = load_frame_data(326)
    
    if not frame_325_data or not frame_326_data:
        print("❌ 无法加载测试数据")
        return
    
    print("\n📋 Frame_00325状态分析：")
    
    # 分析frame_00325的各区域状态
    region_1_cards_325 = extract_region_cards(frame_325_data, 1)
    region_3_cards_325 = extract_region_cards(frame_325_data, 3)
    region_6_cards_325 = extract_region_cards(frame_325_data, 6)
    
    print(f"区域1（观战手牌区）: {len(region_1_cards_325)}张")
    for card in region_1_cards_325:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        if '三' in label or '叁' in label:
            print(f"  - 标签: {label}, ID: {twin_id} ⭐")
        else:
            print(f"  - 标签: {label}, ID: {twin_id}")
    
    print(f"\n区域3（观战抓牌区）: {len(region_3_cards_325)}张")
    for card in region_3_cards_325:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id} ⭐")
    
    print(f"\n区域6（观战吃碰区）: {len(region_6_cards_325)}张")
    for card in region_6_cards_325:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    print("\n📋 Frame_00326状态分析：")
    
    # 分析frame_00326的各区域状态
    region_1_cards_326 = extract_region_cards(frame_326_data, 1)
    region_3_cards_326 = extract_region_cards(frame_326_data, 3)
    region_6_cards_326 = extract_region_cards(frame_326_data, 6)
    
    print(f"区域1（观战手牌区）: {len(region_1_cards_326)}张")
    san_cards_326 = []
    for card in region_1_cards_326:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        if '三' in label or '叁' in label:
            san_cards_326.append(card)
            print(f"  - 标签: {label}, ID: {twin_id} ⭐")
    
    print(f"\n区域3（观战抓牌区）: {len(region_3_cards_326)}张")
    for card in region_3_cards_326:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    print(f"\n区域6（观战吃碰区）: {len(region_6_cards_326)}张")
    # 按Y坐标排序（从下到上）
    region_6_cards_326.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    for i, card in enumerate(region_6_cards_326):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        y_pos = card.get('points', [[0,0]])[0][1]
        print(f"  位置{i+1}: 标签: {label}, ID: {twin_id}, Y: {y_pos:.1f} ⭐")
    
    # 分析流转情况
    print("\n🎯 流转分析：")
    
    # 查找frame_00325区域3中的2三
    san_2_in_325 = None
    for card in region_3_cards_325:
        if card.get('label') == '2三':
            san_2_in_325 = card
            break
    
    if san_2_in_325:
        san_2_id = get_digital_twin_id(san_2_in_325)
        print(f"✅ Frame_00325区域3中发现2三: ID={san_2_id}")
        
        # 查找frame_00326区域6中的三类卡牌
        san_cards_in_326_region6 = [card for card in region_6_cards_326 if '三' in card.get('label', '')]
        
        print(f"📋 Frame_00326区域6中的三类卡牌: {len(san_cards_in_326_region6)}张")
        for card in san_cards_in_326_region6:
            label = card.get('label', 'None')
            twin_id = get_digital_twin_id(card)
            print(f"  - 标签: {label}, ID: {twin_id}")
        
        # 检查是否有2三的继承
        san_2_inherited = False
        for card in san_cards_in_326_region6:
            if get_digital_twin_id(card) == san_2_id:
                san_2_inherited = True
                print(f"✅ 2三成功从区域3流转到区域6: ID={san_2_id}")
                break
        
        if not san_2_inherited:
            print(f"❌ 2三未能从区域3流转到区域6")
    else:
        print("❌ Frame_00325区域3中未发现2三")
    
    return {
        'frame_325': {
            'region_1': region_1_cards_325,
            'region_3': region_3_cards_325,
            'region_6': region_6_cards_325
        },
        'frame_326': {
            'region_1': region_1_cards_326,
            'region_3': region_3_cards_326,
            'region_6': region_6_cards_326
        }
    }

def analyze_frame_360_361_transition():
    """分析frame_00360→frame_00361的流转情况"""
    print("\n🔍 分析frame_00360→frame_00361的流转情况")
    print("="*80)
    
    # 加载数据
    frame_360_data = load_frame_data(360)
    frame_361_data = load_frame_data(361)
    
    if not frame_360_data or not frame_361_data:
        print("❌ 无法加载测试数据")
        return
    
    print("\n📋 Frame_00360状态分析：")
    
    # 分析frame_00360的各区域状态
    region_1_cards_360 = extract_region_cards(frame_360_data, 1)
    region_3_cards_360 = extract_region_cards(frame_360_data, 3)
    region_6_cards_360 = extract_region_cards(frame_360_data, 6)
    
    print(f"区域1（观战手牌区）: {len(region_1_cards_360)}张")
    for card in region_1_cards_360:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        if '柒' in label:
            print(f"  - 标签: {label}, ID: {twin_id} ⭐")
    
    print(f"\n区域3（观战抓牌区）: {len(region_3_cards_360)}张")
    for card in region_3_cards_360:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id} ⭐")
    
    print(f"\n区域6（观战吃碰区）: {len(region_6_cards_360)}张")
    for card in region_6_cards_360:
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        print(f"  - 标签: {label}, ID: {twin_id}")
    
    print("\n📋 Frame_00361状态分析：")
    
    # 分析frame_00361的各区域状态
    region_1_cards_361 = extract_region_cards(frame_361_data, 1)
    region_3_cards_361 = extract_region_cards(frame_361_data, 3)
    region_6_cards_361 = extract_region_cards(frame_361_data, 6)
    
    print(f"区域1（观战手牌区）: {len(region_1_cards_361)}张")
    print(f"区域3（观战抓牌区）: {len(region_3_cards_361)}张")
    
    print(f"\n区域6（观战吃碰区）: {len(region_6_cards_361)}张")
    # 按Y坐标排序（从下到上）
    region_6_cards_361.sort(key=lambda card: card.get('points', [[0,0]])[0][1], reverse=True)
    
    for i, card in enumerate(region_6_cards_361):
        label = card.get('label', 'None')
        twin_id = get_digital_twin_id(card)
        y_pos = card.get('points', [[0,0]])[0][1]
        if '柒' in label:
            print(f"  位置{i+1}: 标签: {label}, ID: {twin_id}, Y: {y_pos:.1f} ⭐")
        else:
            print(f"  位置{i+1}: 标签: {label}, ID: {twin_id}, Y: {y_pos:.1f}")
    
    # 分析流转情况
    print("\n🎯 流转分析：")
    
    # 查找frame_00360区域3中的2柒
    qi_2_in_360 = None
    for card in region_3_cards_360:
        if card.get('label') == '2柒':
            qi_2_in_360 = card
            break
    
    if qi_2_in_360:
        qi_2_id = get_digital_twin_id(qi_2_in_360)
        print(f"✅ Frame_00360区域3中发现2柒: ID={qi_2_id}")
        
        # 查找frame_00361区域6中的柒类卡牌
        qi_cards_in_361_region6 = [card for card in region_6_cards_361 if '柒' in card.get('label', '')]
        
        print(f"📋 Frame_00361区域6中的柒类卡牌: {len(qi_cards_in_361_region6)}张")
        for card in qi_cards_in_361_region6:
            label = card.get('label', 'None')
            twin_id = get_digital_twin_id(card)
            print(f"  - 标签: {label}, ID: {twin_id}")
        
        # 检查是否有2柒的继承
        qi_2_inherited = False
        for card in qi_cards_in_361_region6:
            if get_digital_twin_id(card) == qi_2_id:
                qi_2_inherited = True
                print(f"✅ 2柒成功从区域3流转到区域6: ID={qi_2_id}")
                break
        
        if not qi_2_inherited:
            print(f"❌ 2柒未能从区域3流转到区域6")
    else:
        print("❌ Frame_00360区域3中未发现2柒")

def compare_scenarios():
    """对比两个场景的差异"""
    print("\n💡 对比分析：frame_00326成功 vs frame_00361失败")
    print("="*80)
    
    print("\n📋 相似点：")
    print("1. 都是3→6的流转场景")
    print("2. 都是从区域3的抓牌区流转到区域6的吃碰区")
    print("3. 都涉及相同基础标签的卡牌（三类/柒类）")
    
    print("\n📋 关键差异分析：")
    
    print("\n🎯 Frame_00325→Frame_00326（成功案例）：")
    print("- 区域3: 2三 → 消失")
    print("- 区域6: 新增 1三、2三、1叁（从下到上排列）")
    print("- 结果: 2三成功从区域3流转到区域6")
    
    print("\n🎯 Frame_00360→Frame_00361（失败案例）：")
    print("- 区域3: 2柒 → 消失")
    print("- 区域6: 新增多张卡牌，但只有1柒，缺少2柒")
    print("- 结果: 2柒未能从区域3流转到区域6")
    
    print("\n🔍 可能的原因分析：")
    
    print("\n1. 处理时间差异：")
    print("   - Frame_00326可能在SimpleInheritor优化之前处理")
    print("   - Frame_00361可能在SimpleInheritor优化之后处理")
    
    print("\n2. 区域6的前一帧状态差异：")
    print("   - Frame_00325的区域6: 0张卡牌（空区域）")
    print("   - Frame_00360的区域6: 6张卡牌（已有卡牌）")
    print("   - 这可能影响SimpleInheritor的处理逻辑")
    
    print("\n3. 标签匹配差异：")
    print("   - '三'类标签可能有更好的匹配逻辑")
    print("   - '柒'类标签可能在某些情况下匹配失败")
    
    print("\n4. 代码版本差异：")
    print("   - 两个帧可能使用了不同版本的处理逻辑")
    print("   - Frame_00326可能使用了更早期的、更宽松的流转逻辑")

def main():
    """主函数"""
    print("🔍 Frame_00326 vs Frame_00361 流转对比分析")
    print("="*80)
    print("分析为什么frame_00326能正常流转，而frame_00361不能")
    print("="*80)
    
    # 分析两个场景
    analyze_frame_325_326_transition()
    analyze_frame_360_361_transition()
    
    # 对比分析
    compare_scenarios()
    
    print("\n📊 总结")
    print("="*80)
    print("✅ Frame_00326确实能够正常进行3→6流转")
    print("❌ Frame_00361无法进行3→6流转")
    print("🔧 关键差异可能在于区域6的前一帧状态和处理逻辑的版本差异")
    print("💡 建议深入分析SimpleInheritor在不同情况下的行为差异")

if __name__ == "__main__":
    main()
